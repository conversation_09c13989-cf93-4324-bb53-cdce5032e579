{"name": "abcyun-hippy", "version": "1.6.4", "private": true, "main": "src/main.tsx", "author": "字节星球", "license": "", "description": "ABC诊所管家移动APP", "repository": "", "scripts": {"dev": "turbo run dev", "build": "turbo run build", "dev:all": "pnpm -r run dev", "build:all": "pnpm -r run build", "hippy:debug": "hippy-debug", "hippy:dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider webpack --config ./scripts/hippy-webpack.dev.js", "hippy:dev3": "node ./scripts/env-polyfill.js hippy-dev -c ./scripts/hippy-webpack.ohos.dev.js", "hippy:vendor": "webpack --config ./scripts/build-vender.js", "hippy:build": "webpack --config ./scripts/build-hippy.js", "ossZipUpload": "webpack --config ./scripts/oss-zip-upload.js", "eslint": "eslint . --max-warnings=0", "tsc": "tsc --build tsconfig.json && rm -rf dist_tsc", "stack_decode": "cd tools/stack_decoder && npm install && npm start", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{tsx,ts}": ["eslint --fix", "eslint --max-warnings=0"]}, "hostMinVersion": "2.4.0.0000", "hostMaxVersion": "9999.9.9.9999", "keywords": ["Hippy", "React"], "dependencies": {"@hippy/hippy-dynamic-import-plugin": "^2.0.0", "@app/abc-mobile-ui": "workspace:*", "@app/theme": "workspace:*", "@hippy/react": "file:third-party/@hippy/react", "@hippy/rmc-list-view": "latest", "@hippy/rmc-pull-to-refresh": "latest", "animated-scroll-to": "^2.2.0", "@types/node": "^22.14.1", "big.js": "^6.2.2", "crypto-js": "^4.2.0", "google-libphonenumber": "^3.2.33", "i18next": "^23.4.2", "lodash": "^4.17.15", "moment": "^2.29.4", "react": "^17.0.2", "react-dom": "^17.0.2", "react-reconciler": "file:third-party/react-reconciler", "react-router": "~5.1.2", "react-router-dom": "~5.1.2", "reflect-metadata": "^0.1.13", "regenerator-runtime": "^0.13.3", "rxjs": "^6.5.4", "tslib": "^2.8.1", "url": "^0.11.4"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/plugin-proposal-class-properties": "^7.7.4", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/plugin-proposal-optional-chaining": "^7.9.0", "@babel/polyfill": "^7.12.0", "@babel/preset-env": "^7.7.6", "@babel/preset-react": "^7.7.4", "@babel/preset-typescript": "^7.8.3", "@hippy/debug-server-next": "latest", "@hippy/hippy-react-refresh-webpack-plugin": "^0.5.5", "@hippy/rejection-tracking-polyfill": "^1.0.0", "@types/crypto-js": "^4.2.2", "@types/google-libphonenumber": "^7.4.30", "@types/lodash": "^4.14.149", "@types/react": "^16.8.25", "@types/react-reconciler": "^0.18.0", "@types/big.js": "^6.2.2", "@typescript-eslint/eslint-plugin": "^3.9.1", "@typescript-eslint/parser": "^3.9.1", "abc-fed-build-tool": "^0.7.9", "babel-loader": "^8.1.0", "bezier-easing": "^2.1.0", "case-sensitive-paths-webpack-plugin": "^2.2.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^5.1.1", "cross-env": "^7.0.3", "cross-env-os": "^7.1.1", "css-loader": "^3.4.0", "eslint": "^7.7.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.1.0", "file-loader": "^5.0.2", "html-webpack-plugin": "^5.6.3", "husky": "^8.0.0", "lerna": "^4.0.0", "lint-staged": "^10.3.0", "plop": "^4.0.1", "prettier": "^2.0.5", "react-refresh": "^0.11.0", "rimraf": "^6.0.1", "source-map": "^0.7.3", "source-map-loader": "^0.2.4", "style-loader": "^1.0.2", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^6.2.1", "turbo": "^1.13.0", "typescript": "^3.8.3", "unicode-loader": "^1.0.7", "url-loader": "^3.0.0", "webpack": "5.101.0", "webpack-cli": "5.1.4", "webpack-dev-server": "^4.15.2", "webpack-oss": "^2.1.6", "webpackbar": "^6.0.1"}, "engines": {"pnpm": "~7.33.5"}}