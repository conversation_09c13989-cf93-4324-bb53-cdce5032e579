import React from "react";
import { View, ScrollView } from "@hippy/react";
import DemoURLProtocols from "./demo-url-dispatcher";
import { UrlRoute } from "../url-dispatcher/url-router";
import { Route } from "../url-dispatcher/router-decorator";
import { BasePage } from "./base-page";

import { Colors, Sizes } from "@app/theme";
import {
    AbcAssetImageDemo,
    AbcContentEmptyDemo,
    AbcIconfontDemo,
    AbcQrCodeDemo,
    AbcFlexDemo,
    AbcGridDemo,
    AbcDividerDemo,
    AbcRadioBoxDemo,
    AbcRadioButtonGroupDemo,
    AbcCollapseDemo,
    AbcBadgeDemo,
    AbcStepperDemo,
} from "../../../packages/abc-mobile-ui/src/components/component-demo";

export class DemoView extends BasePage {
    renderContent(): JSX.Element {
        return (
            <ScrollView style={{ flex: 1, backgroundColor: Colors.white }}>
                <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp48), { flex: 1 }]}>{this.props.children}</View>
            </ScrollView>
        );
    }
}
@Route({ path: DemoURLProtocols.AbcAssetImage })
export class AssetImageRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcAssetImageDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcContentEmpty })
export class AbcContentEmptyRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcContentEmptyDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcQrCode })
export class AbcQrCodeRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcQrCodeDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcIconfont })
export class AbcIconfontRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcIconfontDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcFlex })
export class AbcFlexRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcFlexDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcGrid })
export class AbcGridRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcGridDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcDivider })
export class AbcDividerRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcDividerDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcRadio })
export class AbcRadioRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcRadioBoxDemo />
                <View style={{ marginTop: Sizes.dp24 }}>
                    <AbcRadioButtonGroupDemo></AbcRadioButtonGroupDemo>
                </View>
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcCollapse })
export class AbcCollapseRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcCollapseDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcBadge })
export class AbcBadgeRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcBadgeDemo />
            </DemoView>
        );
    }
}

@Route({ path: DemoURLProtocols.AbcStepper })
export class AbcStepperRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        return (
            <DemoView>
                <AbcStepperDemo />
            </DemoView>
        );
    }
}
