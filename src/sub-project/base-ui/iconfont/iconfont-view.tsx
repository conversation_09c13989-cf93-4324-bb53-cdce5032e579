/**
 * Created by he<PERSON><PERSON> on 2020/3/14.
 */

//import createIconSet from 'create-icon-set';

import React from "react";
import { ClickableProps, Style } from "@hippy/react";
import { iconfontMap } from "./iconfont-map";

import { Color, Colors, Sizes } from "../../theme";
import { LogUtils } from "../../common-base-module/log";
import { AbcText } from "../views/abc-text";

const glyphs = new Map<string, string>();
iconfontMap.glyphs.forEach((item) => {
    glyphs.set(item.name, String.fromCharCode(item.unicode_decimal));
});

interface IconFontViewPros extends ClickableProps {
    name: string;
    size: number | string;
    fontFamily: string | "iconfont";
    color: Color;
    style?: Style;
}

export default class IconFontView extends React.Component<IconFontViewPros> {
    static defaultProps = {
        size: Sizes.dp24,
        fontFamily: "iconfont",
        color: Colors.T3,
    };

    constructor(props: IconFontViewPros) {
        super(props);
    }

    render(): JSX.Element {
        const { fontFamily, name, size, color, style, ...props } = this.props;
        return (
            <AbcText
                style={{
                    ...style,
                    fontFamily: fontFamily,
                    fontSize: Number(size),
                    color: color,
                }}
                {...props}
                accessibilityLabel={name}
            >
                {glyphs.get(name) || ""}
            </AbcText>
        );
    }

    static dropDown(): JSX.Element {
        LogUtils.d(`dropDown()`);
        return <IconFontView name="Dropdown_Triangle" size={Sizes.dp14} color={Colors.P1} />;
    }
}

interface RightArrowViewProps {
    color?: Color;
    size?: number;
}

export class RightArrowView extends React.Component<RightArrowViewProps> {
    render(): JSX.Element {
        const { color, size } = this.props;
        return <IconFontView name={"Arrow_Right"} size={size ?? Sizes.dp16} color={color ?? Colors.P1} />;
    }
}

export class DropdownArrowView extends React.Component {
    render(): JSX.Element {
        return <IconFontView name={"Dropdown_Triangle"} size={Sizes.dp14} color={Colors.P1} />;
    }
}

interface TreeDotViewProps extends RightArrowViewProps {}

export class TreeDotView extends React.Component<TreeDotViewProps> {
    render(): JSX.Element {
        const { color, size } = this.props;
        return <IconFontView name={"three_dot"} size={size ?? Sizes.dp20} color={color ?? Colors.black} />;
    }
}

export class PositiveSelectView extends React.Component<RightArrowViewProps> {
    render(): JSX.Element {
        const { size } = this.props;
        return <IconFontView name={"Positive_Selected"} size={size ?? Sizes.dp14} color={Colors.mainColor} />;
    }
}
