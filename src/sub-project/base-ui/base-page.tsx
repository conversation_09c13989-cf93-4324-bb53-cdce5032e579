/**
 * Created by he<PERSON><PERSON> on 2020/3/12.
 */
import React from "react";
import { Text, View } from "@hippy/react";

import AppBar from "./app-bar";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { DisposableTracker } from "../common-base-module/cleanup/disposable";
import { AbcEmptyItemView } from "./views/empty-view";
import { ABCNetworkError } from "../net";
import { ABCNetworkErrorView } from "./views/network-error-view";
import { applyMixins, errorSummary } from "../common-base-module/utils";
import { LoadingView } from "./views/loading-view";
import SafeAreaView from "./safe_area_view";
import { BaseComponent, getComponentErrorHandler } from "./base-component";
import { ABCNavigator } from "./views/abc-navigator";
import { ABCDeviceLoginError, ABCError } from "../common-base-module/common-error";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>ider } from "../bloc";
import { Theme<PERSON>anager } from "../theme/themes";
import { statBeginPage, statEndPage } from "../base-business/stat/stat-manager";
import { ignore } from "../common-base-module/global";
import { KeyboardListenerViewFillBlack } from "./views/keyboard-listener-view";
import { runtimeConstants } from "../data/constants/runtime-constants-manager";
import { AbcStatManagerInstance } from "../base-business/stat/abc-stat-manager";

export class BasePage<P = {}, S = {}, SS = any> extends BaseComponent<P, S, SS> {
    startTime: number;
    constructor(props: P) {
        super(props);

        ThemeManager.themeObserver
            .subscribe((/*ignore*/) => {
                this.setState({});
            })
            .addToDisposableBag(this);
        this.startTime = Date.now();
    }

    componentDidMount(): void {
        super.componentDidMount();
        const name = this.pageName() ?? this.getAppBarTitle() ?? this.constructor.name;
        name && statBeginPage(name);
        const endTime = Date.now();
        const renderTime = endTime - this.startTime;
        AbcStatManagerInstance.savePerformanceDetail({
            title: name,
            moduleName: "渲染耗时",
            value: renderTime,
        });
    }

    pageName(): string | undefined {
        return;
    }

    render(): JSX.Element {
        let _content: JSX.Element | undefined;
        try {
            _content = this.renderContent();
        } catch (e) {
            const handler = getComponentErrorHandler();
            handler && handler(e, `from page ${this.constructor.name}`);

            _content = (
                <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                    <Text style={[TextStyles.t16NR2, { marginBottom: Sizes.dp16 }]}>
                        {`显示出错 联系ABC${runtimeConstants.PRODUCT_NAME}`}
                    </Text>
                    <Text>错误详情:{errorSummary(e)}</Text>
                </View>
            );
        }

        return (
            <SafeAreaView
                airTestKey={this.pageName() ?? this.getAppBarTitle() ?? this.constructor.name}
                style={{ backgroundColor: this.getBackgroundColor(), flex: 1 }}
                showStatusBar={this.getShowStatusBar()}
                statusBarColor={this.getStatusBarColor()}
                showBottomSafeArea={this.getShowBottomSafeArea()}
                bottomSafeAreaColor={this.getBottomSafeAreaColor()}
                touchEmptyBlurTextInput={this.getTouchEmptyBlurTextInput()}
            >
                {this.getAppBar()}
                {_content}
            </SafeAreaView>
        );
    }

    getAppBar(): JSX.Element | undefined {
        return (
            <AppBar
                title={this.getAppBarTitle()}
                titleColor={this.getAppBarTitleColor()}
                bottomLine={this.getAppBarBottomLine()}
                customTitle={this.getAPPBarCustomTitle()}
                showBackBtn={this.getAppBarBackIconStatus()}
                leftPart={this.getLeftAppBarIcons()}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                bgColor={this.getAppBarBgColor()}
                backBtnColor={this.getAppBarBtnColor()}
                backIcon={this.getAppBarBackIcon()}
                onLongClick={this.onLongClick.bind(this)}
            />
        );
    }

    onBackClick(): void {
        ABCNavigator.pop();
    }

    renderContent(): JSX.Element | undefined {
        return;
    }

    getBackgroundColor(): Color {
        return Colors.window_bg;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.white;
    }

    getShowBottomSafeArea(): boolean {
        return true;
    }

    getShowStatusBar(): boolean {
        return true;
    }

    getStatusBarColor(): Color {
        return Colors.white;
    }

    getAppBarBackIcon(): JSX.Element | undefined {
        return;
    }

    getAppBarBackIconStatus(): boolean {
        return true;
    }

    getAppBarTitle(): string {
        return "";
    }

    getAppBarTitleColor(): Color {
        return Colors.black;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return undefined;
    }

    getLeftAppBarIcons(): JSX.Element[] {
        return [];
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [];
    }

    getAppBarBottomLine(): boolean {
        return true;
    }

    getAppBarBgColor(): Color {
        return Colors.white;
    }

    getAppBarBtnColor(): Color {
        return Colors.black;
    }

    onLongClick(): void {
        return;
    }

    getFillKeyboardContent(): boolean {
        debugger;
        return false;
    }

    getTouchEmptyBlurTextInput(): boolean {
        return false;
    }

    componentWillUnmount(): void {
        super.componentWillUnmount();

        const name = this.pageName();
        name && statEndPage(name);
    }
}

export interface BasePage extends DisposableTracker {}

applyMixins(BasePage, [DisposableTracker]);

export enum ABCNetworkPageContentStatus {
    none,
    loading,
    empty,
    show_data,
    loadingMore, //将会在底部显示"正在加载更多"
    error, //加载失败，点击重试
    refresh, //重新加载
    noMoreData,
}

export abstract class NetworkViewMixin<P = {}, S = {}, SS = any> {
    get error(): any {
        return this._error;
    }

    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    set error(value: any) {
        this._error = value;
    }

    get counter(): number {
        if (!this._counter) this._counter = 0;
        return this._counter;
    }

    set counter(value: number) {
        this._counter = value;
    }

    private _counter = 1;

    private _contentStatus = ABCNetworkPageContentStatus.show_data;
    set contentStatus(value: ABCNetworkPageContentStatus) {
        this._contentStatus = value;
    }

    get contentStatus(): ABCNetworkPageContentStatus {
        if (this._contentStatus == undefined) {
            this._contentStatus = ABCNetworkPageContentStatus.show_data;
        }

        return this._contentStatus;
    }

    private _error: any;

    get isEmptyContent(): boolean {
        return true;
    }

    reloadData(): void {
        return;
    }

    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    setContentStatus(status: ABCNetworkPageContentStatus, error?: any): void {
        if (status == this.contentStatus) {
            return;
        }

        this.contentStatus = status;
        this._error = error;

        this.setState({});
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/explicit-module-boundary-types
    setState(state: any): void {
        ignore(state);
        throw new Error("不应该调用到NetworkView.setState this = " + this.constructor.name + ", state = ");
    }

    emptyContent(): JSX.Element {
        // return <ABCEmptyView tips={"没有找到相关结果"} />;
        return <AbcEmptyItemView tips={"没有找到相关结果"} />;
    }

    errorContent(): JSX.Element {
        let body;
        let error = this._error;
        while (error instanceof ABCError || error instanceof ABCDeviceLoginError) {
            error = error.detailError;
        }

        if (error instanceof ABCNetworkError) {
            body = <ABCNetworkErrorView error={error} />;
        } else {
            body = (
                <View style={{ alignItems: "center", paddingHorizontal: Sizes.dp16 }}>
                    <Text style={[TextStyles.t14NT3, { marginTop: 80 }]}>{errorSummary(error)}</Text>
                    <Text style={[TextStyles.t14NT3, { marginTop: 16 }]}>点击刷新</Text>
                </View>
            );
        }

        return (
            <View style={{ flex: 1 }} onClick={() => this.reloadData()}>
                {body}
            </View>
        );
    }

    renderContent(): JSX.Element {
        return <View />;
    }

    _buildLoadingBody(): JSX.Element {
        return (
            <View style={[ABCStyles.absoluteFill, ABCStyles.centerChild, { zIndex: 999, backgroundColor: Colors.white }]}>
                <View
                    style={{
                        alignItems: "center",
                        justifyContent: "center",
                        width: 80,
                        height: 80,
                        borderRadius: 6,
                        backgroundColor: "#091E42A3",
                    }}
                >
                    <LoadingView size={24} />
                </View>
            </View>
        );
    }

    _buildBody(): JSX.Element {
        let body;
        const contentStatus = this.contentStatus;
        if (contentStatus == ABCNetworkPageContentStatus.empty) {
            body = this.emptyContent();
        } else if (contentStatus == ABCNetworkPageContentStatus.error && this.isEmptyContent) {
            body = this.errorContent();
        } else {
            body = (
                <View style={{ flex: 1 }}>
                    {this.contentStatus == ABCNetworkPageContentStatus.loading && this.isEmptyContent && this._buildLoadingBody()}
                    {this.renderContent()}
                    {contentStatus == ABCNetworkPageContentStatus.noMoreData && (
                        <View
                            style={{
                                justifyContent: "center",
                                alignItems: "center",
                                paddingTop: 12,
                                paddingBottom: 8,
                            }}
                        >
                            <Text style={[TextStyles.t14NT4]}>没有更多数据了</Text>
                        </View>
                    )}
                </View>
            );
        }

        return body;
    }
}

export class BaseNetworkPage<P = {}, S = {}, SS = any> extends BasePage<P, S, SS> {
    render(): JSX.Element {
        // @ts-ignore
        const body = this._buildBody();

        return (
            <SafeAreaView
                airTestKey={this.pageName() ?? this.getAppBarTitle() ?? this.constructor.name}
                style={{ backgroundColor: this.getBackgroundColor(), flex: 1 }}
                showStatusBar={this.getShowStatusBar()}
                statusBarColor={this.getStatusBarColor()}
                bottomSafeAreaColor={this.getBottomSafeAreaColor()}
                touchEmptyBlurTextInput={this.getTouchEmptyBlurTextInput()}
            >
                {this.getAppBar()}
                {body}
                {this.getFillKeyboardContent() && <KeyboardListenerViewFillBlack />}
            </SafeAreaView>
        );
    }
}

// @ts-ignore
export interface BaseNetworkPage extends NetworkViewMixin {}

applyMixins(BaseNetworkPage, [NetworkViewMixin], ["setState"]);

export class NetworkView<P = {}, S = {}, SS = any> extends BaseComponent<P, S, SS> {
    render(): JSX.Element {
        return this._buildBody();
    }
}

// @ts-ignore
export interface NetworkView extends NetworkViewMixin {}

applyMixins(NetworkView, [NetworkViewMixin], ["setState"]);

/**
 * 基于bloc的Page基类
 */
// @ts-ignore
export class BaseBlocPage<P = {}, BLOC extends Bloc<any, any>, S = {}, SS = any> extends BasePage<P, S, SS> {
    bloc!: BLOC;

    constructor(props: P) {
        super(props);
    }

    // 是否有bloc的所有权，如果有，将会在退出时，负责释放
    protected takeBlocOwnership(): boolean {
        return true;
    }

    render(): JSX.Element {
        return (
            <SafeAreaView
                airTestKey={this.pageName() ?? this.getAppBarTitle() ?? this.constructor.name}
                style={{ backgroundColor: this.getBackgroundColor(), flex: 1 }}
                showStatusBar={this.getShowStatusBar()}
                statusBarColor={this.getStatusBarColor()}
                showBottomSafeArea={this.getShowBottomSafeArea()}
                bottomSafeAreaColor={this.getBottomSafeAreaColor()}
                touchEmptyBlurTextInput={this.getTouchEmptyBlurTextInput()}
            >
                {this.getAppBar()}
                {this._renderContent()}
            </SafeAreaView>
        );
    }

    public componentWillUnmount(): void {
        super.componentWillUnmount();
        if (this.takeBlocOwnership() && this.bloc) {
            this.bloc.dispose();
        }
    }

    private _renderContent(): JSX.Element {
        return (
            <BlocProvider bloc={this.bloc}>
                <BlocBuilder
                    bloc={this.bloc}
                    condition={() => true}
                    build={() => {
                        return this.renderContent();
                    }}
                />
            </BlocProvider>
        );
    }
}

// @ts-ignore
export class BaseBlocNetworkPage<P = {}, BLOC extends Bloc<any, any>, S = {}, SS = any> extends BaseNetworkPage<P, S, SS> {
    bloc!: BLOC;

    constructor(props: P) {
        super(props);
    }

    // 是否有bloc的所有权，如果有，将会在退出时，负责释放
    protected takeBlocOwnership(): boolean {
        return true;
    }

    public componentWillUnmount(): void {
        super.componentWillUnmount();
        if (this.takeBlocOwnership() && this.bloc) {
            this.bloc.dispose();
        }
    }

    public render(): JSX.Element {
        // @ts-ignore
        return (
            <SafeAreaView
                airTestKey={this.pageName() ?? this.getAppBarTitle() ?? this.constructor.name}
                style={{ backgroundColor: this.getBackgroundColor(), flex: 1 }}
                showStatusBar={this.getShowStatusBar()}
                statusBarColor={this.getStatusBarColor()}
                bottomSafeAreaColor={this.getBottomSafeAreaColor()}
                touchEmptyBlurTextInput={this.getTouchEmptyBlurTextInput()}
                showBottomSafeArea={this.getShowBottomSafeArea()}
            >
                {this.getAppBar()}
                {this._renderContent()}
            </SafeAreaView>
        );
    }

    private _renderContent(): JSX.Element {
        return (
            <BlocProvider bloc={this.bloc}>
                <BlocBuilder bloc={this.bloc} condition={() => true} build={() => this._buildBody()} />
            </BlocProvider>
        );
    }
}

// @ts-ignore
export class BaseBlocNetworkView<P = {}, BLOC extends Bloc<any, any>, S = {}, SS = any> extends NetworkView<P, S, SS> {
    bloc!: BLOC;

    constructor(props: P) {
        super(props);
    }

    //是否有bloc的所有权，如果有，将会在退出时，负责释放
    protected takeBlocOwnership(): boolean {
        return true;
    }

    public componentWillUnmount(): void {
        super.componentWillUnmount();
        if (this.takeBlocOwnership() && this.bloc) {
            this.bloc.dispose();
        }
    }

    public render(): JSX.Element {
        return (
            <BlocProvider bloc={this.bloc}>
                <BlocBuilder bloc={this.bloc} condition={() => true} build={() => this._buildBody()} />
            </BlocProvider>
        );
    }
}
