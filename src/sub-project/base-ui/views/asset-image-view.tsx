/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-20
 *
 * @description
 *
 */

import React from "react";
import { Image, Style } from "@hippy/react";
import { ThemeManager, ThemeType } from "../../theme/themes";
import { Colors, flattenStyles } from "../../theme";

const kImageRoot = "assets/images/";

const themesImagesRoot = new Map();
themesImagesRoot.set(ThemeType.normal, "default-theme");
themesImagesRoot.set(ThemeType.chineseMedicine, "chinese-medicine-theme");

function imageRoot(ignoreTheme: boolean) {
    const theme = ThemeManager.getCurrentTheme();
    return kImageRoot + (ignoreTheme ? "default-theme" : themesImagesRoot.get(theme)) + "/";
}

function assetImagePath(name: string, options?: { ext?: string; ignoreTheme: boolean }): string {
    return imageRoot(options?.ignoreTheme ?? true) + name + "." + (options?.ext ?? "png");
}

/**
 * 将会根据本地资源名，拼出带有scheme的地址，在一些View backgroundUrl里需要使用
 * @param name
 * @param options 用于指定可选参数,扩展名，是否忽略主题
 */
function fullAssetImagePath(name: string, options?: { ext?: string; ignoreTheme: boolean }): string {
    const url = assetImagePath(name, options);
    if (url && !/^(http|https):\/\//.test(url) && url.indexOf("assets") > -1) {
        if (process.env.NODE_ENV === "development") {
            const addStr1 = "http://"; // do not change this, otherwise js-min went wrong
            return `${addStr1}127.0.0.1:${process.env.PORT}/${url}`;
        }
        const addStr2 = "hpfile://"; // do not change this, otherwise js-min went wrong
        return `${addStr2}./${url}`;
    }
    return url;
}

interface AssetImageViewProps {
    name: string;
    ext?: string;
    style: Style | Style[];
    ignoreTheme?: boolean; //default true
    src?: string;
    onClick?: () => void;

    resizeMode?: "cover" | "contain" | "stretch" | "repeat" | "center";
    onLoad?: (nativeEvent: { width: number; height: number; url: string }) => void;
}

export class AssetImageView extends React.Component<AssetImageViewProps> {
    static defaultProps = {
        name: "",
        style: {
            width: 24,
            height: 24,
        },
        ignoreTheme: true,
    };

    public render(): JSX.Element {
        const { name, style, ext, ignoreTheme, src, onLoad, ...otherProps } = this.props;
        let url = assetImagePath(name, {
            ignoreTheme: ignoreTheme!,
            ext: ext,
        });
        if (src) {
            url = src;
        }
        return (
            <Image
                accessibilityLabel={name}
                // @ts-ignore
                style={flattenStyles([{ backgroundColor: Colors.transparent }, style])}
                {...otherProps}
                // source={{ uri: 'http://img.qdaily.com/article/article_show/20180226115511QR0IMWjcBZmo8FaV.gif' }}
                source={{ uri: url }}
                //@ts-ignore
                onLoad={onLoad}
            />
        );
    }
}

export { assetImagePath, fullAssetImagePath };
