/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-31
 *
 * @description
 */
import React from "react";
import { Dimensions, Image, StyleSheet, Text, View, ViewPager } from "@hippy/react";
import { BasePage } from "../base-page";
import { LogUtils } from "../../common-base-module/log";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import { ABCNavigator } from "./abc-navigator";
import { UrlUtils } from "../../common-base-module/utils";
import { AbcPinchImageView } from "./abc-pinch-image-view";
import { BaseComponent } from "../base-component";
import { Subscription } from "rxjs";
import { fromPromise } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { ImageUtils } from "../utils/image-utils";
import { Version } from "../utils/version-utils";
import { AppInfo } from "../../base-business/config/app-info";
import { StringUtils } from "../utils/string-utils";
import { AttachmentItem } from "../../base-business/data/beans";
import { AbcView } from "./abc-view";

// @ts-ignore
const styles = StyleSheet.create({
    viewPagerContainer: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: Colors.black,
    },

    pageIndicatorContainer: {
        position: "absolute",
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: "center",
        alignItems: "center",
    },
});

interface ImageGalleryViewPageProps {
    images: AttachmentItem[];
    initialPage?: number;
    mutable?: boolean;
    onDelete?: (index: number) => Promise<boolean>;
    isShowCopyImgBtn?: boolean; //是否显示复制图片到病历按钮
    onCopyImgToMedicalRecord?(selectItem?: AttachmentItem): void; //复制图片到病历
}

interface PageItemProps {
    select: boolean;
    image: string;
    useImageSignUrl?: boolean;
}

class PageItem extends BaseComponent<PageItemProps> {
    private readonly _image: string;

    private _pinchImageView: AbcPinchImageView | null = null;

    constructor(props: PageItemProps) {
        super(props);
        this._image = UrlUtils.tripOSSCompressParams(this.props.image);
    }

    componentWillReceiveProps(nextProps: Readonly<PageItemProps>) {
        if (!nextProps.select && this.props.select) {
            this._pinchImageView?.resetToInitialState();
        }
    }

    render() {
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center", overflow: "hidden" }}>
                <AbcPinchImageView src={this._image} style={{ flex: 1 }} maxScale={10} ref={(ref) => (this._pinchImageView = ref)} />
            </View>
        );
    }
}

//deprecated，兼容2.1.0.0100之前老版本, 新版本后，删除
class PageItemDeprecated extends React.Component<PageItemProps> {
    displaySize = { width: 280, height: 320 };
    getImageSizeSubscription: Subscription;

    private readonly _image: string;

    constructor(props: PageItemProps) {
        super(props);

        this._image = UrlUtils.tripOSSCompressParams(this.props.image);

        const { width: screenWidth, scale } = Dimensions.get("screen");
        let { height: screenHeight } = Dimensions.get("screen");

        screenHeight -= 60;

        this.getImageSizeSubscription = fromPromise(ImageUtils.getImageSize(this._image)).subscribe((result) => {
            const widthInDp = result.width / scale;
            const heightInDp = result.height / scale;
            const aspect = widthInDp / heightInDp;

            let targetWidth = screenWidth;
            let targetHeight = targetWidth / aspect;

            if (targetHeight > screenHeight) {
                targetHeight = screenHeight;
                targetWidth = targetHeight * aspect;
            }

            this.displaySize = { width: targetWidth, height: targetHeight };
            this.setState({});
        });
    }

    render() {
        return (
            <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
                <Image source={{ uri: this._image }} style={this.displaySize} />
            </View>
        );
    }

    componentWillUnmount(): void {
        if (super.componentWillUnmount) {
            super.componentWillUnmount();
        }

        this.getImageSizeSubscription.unsubscribe();
    }
}

interface ImageGalleryViewPageState {
    images: AttachmentItem[];
    currentPage: number;
}

export class ImageGalleryViewPage extends BasePage<ImageGalleryViewPageProps, ImageGalleryViewPageState> {
    static defaultProps = {
        images: [],
        initialPage: 0,
        mutable: false,
    };
    private _viewPager: ViewPager | null = null;

    constructor(props: ImageGalleryViewPageProps) {
        super(props);
        LogUtils.d("ImageGalleryViewPage.constructor");

        this.state = {
            currentPage: this.props.initialPage!,
            images: this.props.images,
        };
    }

    getAppBarTitle(): string {
        const { currentPage, images } = this.state;
        return `${currentPage + 1}/${images.length}`;
    }

    getAppBarTitleColor(): Color {
        return Colors.white;
    }

    getStatusBarColor(): Color {
        return Colors.black;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.black;
    }

    getAppBarBtnColor(): Color {
        return Colors.white;
    }

    getAppBarBgColor(): Color {
        return Colors.black;
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    getRightAppBarIcons(): JSX.Element[] {
        if (this.props.mutable) {
            return [
                <Text key={0} style={TextStyles.t16NW} onClick={() => this._onDelete()}>
                    删除
                </Text>,
            ];
        }
        return [];
    }

    renderContent(): JSX.Element {
        const { currentPage } = this.state;
        const { images } = this.state;
        const { isShowCopyImgBtn } = this.props;
        const lessThan210Version = new Version(AppInfo.appVersion).compareTo(new Version("2.1.0.0100")) < 0;
        const pages = images.map((image, index) => {
            if (!StringUtils.checkIsImage(image.fileName) && !StringUtils.checkIsImage(image.useImageSignUrl ? image.signUrl : image.url))
                return (
                    <View
                        style={[
                            ABCStyles.centerChild,
                            {
                                flex: 1,
                                marginHorizontal: Sizes.dp4,
                                backgroundColor: Colors.window_bg,
                            },
                        ]}
                    >
                        <Text style={[TextStyles.t16NT2.copyWith({ lineHeight: Sizes.dp24 }), { textAlign: "center" }]}>不支持预览</Text>
                    </View>
                );
            if (lessThan210Version)
                return (
                    <PageItemDeprecated
                        image={image?.useImageSignUrl ? image.signUrl ?? "" : image.url ?? ""}
                        useImageSignUrl={image.useImageSignUrl}
                        key={`${image.url}${index}`}
                        select={index == currentPage}
                    />
                );
            return (
                <PageItem
                    image={image?.useImageSignUrl ? image.signUrl ?? "" : image.url ?? ""}
                    useImageSignUrl={image.useImageSignUrl}
                    key={`${image.url}${index}`}
                    select={index == currentPage}
                />
            );
        });

        return (
            <View style={{ flex: 1 }}>
                <ViewPager
                    ref={(ref) => (this._viewPager = ref)}
                    initialPage={currentPage!}
                    style={styles.viewPagerContainer}
                    onPageSelected={(event) => this._onPageSelect(event.position)}
                >
                    {pages}
                </ViewPager>
                {isShowCopyImgBtn && (
                    <View
                        style={[
                            styles.pageIndicatorContainer,
                            {
                                bottom: Sizes.dp16,
                                backgroundColor: Colors.transparent,
                            },
                        ]}
                    >
                        <AbcView
                            style={[
                                Sizes.paddingLTRB(Sizes.dp6, Sizes.dp12),
                                {
                                    backgroundColor: Colors.mainColor,
                                    height: Sizes.dp32,
                                    borderRadius: Sizes.dp4,
                                },
                                ABCStyles.rowAlignCenter,
                            ]}
                            onClick={() => this.props.onCopyImgToMedicalRecord?.(images[currentPage])}
                        >
                            <Text
                                style={[
                                    TextStyles.t14MM.copyWith({
                                        color: Colors.white,
                                        lineHeight: Sizes.dp20,
                                    }),
                                ]}
                            >{`添加到病历`}</Text>
                        </AbcView>
                    </View>
                )}
            </View>
        );
    }

    private _onPageSelect(index: number) {
        this.setState({
            currentPage: index,
        });
    }

    private async _onDelete() {
        const { images } = this.state;
        let { currentPage } = this.state;
        if (images.length == 0) return;

        const _delete = await this.props.onDelete?.(currentPage);
        if (!_delete) return;

        images.splice(this.state.currentPage, 1);

        //全部删除完后退出
        if (images.length == 0) {
            ABCNavigator.pop();
            return;
        }

        if (currentPage >= images.length) currentPage = images.length - 1;

        this.setState({
            images: images,
            currentPage: currentPage,
        });
    }
}
