/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/1
 *
 * @description 自定义数字键盘
 */

import { ABCStyleSheet, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcTextInput, KeyboardBuilder } from "../abc-text-input";
import _ from "lodash";
import { Text, View } from "@hippy/react";
import { GridView } from "../grid-view";
import { AbcButton } from "../abc-button";
import { IconFontView, SizedBox } from "../../index";
import React from "react";
import { BaseComponent } from "../../base-component";
import { pxToDp } from "../../utils/ui-utils";

const styles = ABCStyleSheet.create({
    gridItemContainer: {
        height: pxToDp(44),
        backgroundColor: Colors.white,
        justifyContent: "center",
        alignItems: "center",
    },
    defaultUnit: {
        padding: 0,
        flex: 1,
        height: pxToDp(44),
        borderWidth: 0.5,
        borderColor: Colors.white,
        borderRadius: pxToDp(3),
        backgroundColor: Colors.white,
    },
    selectUnit: {
        borderColor: Colors.mainColor,
    },
});

const kKeyboardHeight = pxToDp(206);

export class NumberKeyboardBuilder implements KeyboardBuilder {
    selectUnit?: string;
    unitList?: string[];
    onChangeUnit?: (unit: string) => void;
    startUnitList?: string;

    constructor(options?: { selectUnit?: string; unitList?: string[]; onChangeUnit?: (unit: string) => void; startUnitList?: string }) {
        const { selectUnit, unitList, onChangeUnit, startUnitList } = options ?? {};
        this.selectUnit = selectUnit;
        this.unitList = unitList;
        this.startUnitList = startUnitList;
        this.onChangeUnit = onChangeUnit;
    }

    build(textInput: AbcTextInput): JSX.Element {
        return (
            <NumberKeyboard textInput={textInput} selectUnit={this.selectUnit} unitList={this.unitList} onChangeUnit={this.onChangeUnit} />
        );
    }

    keyboardHeight(): number {
        return kKeyboardHeight;
    }
}

interface NumberKeyboardProps {
    textInput: AbcTextInput;
    selectUnit?: string;
    unitList?: string[];
    onChangeUnit?: (unit: string) => void;
}

export class NumberKeyboard extends BaseComponent<NumberKeyboardProps> {
    private readonly _gridItems: JSX.Element[];

    constructor(props: NumberKeyboardProps) {
        super(props);

        const characters = _.range(1, 10).map((value) => value.toString());
        characters.push(".");
        characters.push("0");

        this._gridItems = characters.map((item) => this._renderNumberItem(item));

        this._gridItems.push(this._renderDeleteItem());
    }

    public render(): JSX.Element {
        const { textInput } = this.props;
        return (
            <View
                accessibilityLabel={"数字键盘"}
                style={{
                    height: kKeyboardHeight,
                    backgroundColor: Colors.keyboardBg,
                    flexDirection: "row",
                    padding: pxToDp(6),
                }}
            >
                <GridView
                    itemHeight={pxToDp(44)}
                    crossAxisCount={3}
                    crossAxisSpacing={pxToDp(6)}
                    mainAxisSpacing={pxToDp(6)}
                    style={{ flex: 1 }}
                    rowStyle={{ alignItems: "stretch" }}
                >
                    {this._gridItems}
                </GridView>
                <View style={{ width: pxToDp(81), marginLeft: pxToDp(6) }}>
                    {this._renderUnitList()}
                    <AbcButton
                        text={"确认"}
                        style={{ flex: 1 }}
                        textStyle={TextStyles.t18MW}
                        showShadow={true}
                        onClick={() => {
                            textInput.finishEditing();
                        }}
                    />
                </View>
            </View>
        );
    }

    private _renderNumberItem(number: string): JSX.Element {
        const { textInput } = this.props;
        return (
            <AbcButton
                style={styles.gridItemContainer}
                pressColor={Colors.buttonPressed}
                key={number}
                continuousClick={true}
                showShadow={true}
                onClick={() => textInput.insertText(number.toString())}
            >
                <Text style={TextStyles.t22NT1.copyWith({ lineHeight: Sizes.dp32 })}>{number}</Text>
            </AbcButton>
        );
    }

    private _renderDeleteItem(): JSX.Element {
        const { textInput } = this.props;
        return (
            <AbcButton
                style={styles.gridItemContainer}
                pressColor={Colors.buttonPressed}
                key={"Delete"}
                continuousClick={true}
                showShadow={true}
                onClick={() => textInput.deleteCurrentChar()}
            >
                <IconFontView name={"delete"} size={pxToDp(20)} color={Colors.t2} />
            </AbcButton>
        );
    }

    private _renderUnitItem(unit: string, select: boolean): JSX.Element {
        const { onChangeUnit } = this.props;
        const _style = { ...styles.defaultUnit };
        if (select) Object.assign(_style, styles.selectUnit);
        return (
            <AbcButton
                key={unit}
                text={unit}
                style={_style}
                textStyle={select ? TextStyles.t16NM : TextStyles.t16NT4}
                pressColor={Colors.buttonPressed}
                continuousClick={true}
                showShadow={true}
                onClick={() => {
                    if (select) return;
                    onChangeUnit?.(unit);
                }}
            />
        );
    }

    private _renderUnitList(): JSX.Element {
        const { unitList, selectUnit } = this.props;
        if (unitList && unitList?.length > 4) return <View />;
        const _rowFirst: JSX.Element[] = [];
        const _rowSecond: JSX.Element[] = [];
        unitList?.forEach((item, index, self) => {
            if (index < 2) {
                _rowFirst.push(this._renderUnitItem(item, selectUnit == item || item == selectUnit?.replace("/次", "")));
                if (index < self.length - 1) _rowFirst.push(<SizedBox key={index.toString()} width={pxToDp(6)} />);
            } else {
                _rowSecond.push(this._renderUnitItem(item, selectUnit == item || item == selectUnit?.replace("/次", "")));
                if (index < self.length - 1) _rowSecond.push(<SizedBox key={index.toString()} width={pxToDp(6)} />);
            }
        });
        return (
            <View>
                <View style={{ flexDirection: "row" }}>{_rowFirst}</View>
                {_rowFirst.length ? <SizedBox height={pxToDp(6)} /> : <View />}
                <View style={{ flexDirection: "row" }}>{_rowSecond}</View>
                {_rowSecond.length ? <SizedBox height={pxToDp(6)} /> : <View />}
            </View>
        );
    }
}
