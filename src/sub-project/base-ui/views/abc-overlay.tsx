/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/7/16
 */
import { Subject } from "rxjs";
import _ from "lodash";
import { ABCNavigator } from "./abc-navigator";

export enum OverlayViewKey {
    debugLogPage = "debugLogPage",
    abcPayAdDialog = "abcPayAdDialog", //ABC支付开通提醒弹窗
    expirePaymentDialog = "expirePaymentDialog", // 即将过期或已过期缴费提醒弹窗
    expirePaymentTips = "expirePaymentTips", //// 即将过期或已过期缴费提醒内容
    abcDoubleElevenDialog = "abcDoubleElevenDialog", // 双十一活动弹窗
    abcDoubleTwelfthDialog = "abcDoubleTwelfthDialog", // 双十二活动弹窗
    uiComponentsPage = "uiComponentsPage", // 通用弹窗
}

export class AbcOverlay {
    get list(): JSX.Element[] {
        return [...this._list.values()];
    }

    get keys(): string[] {
        return [...this._list.keys()];
    }

    _list: Map<string, JSX.Element> = new Map<string, JSX.Element>();

    componentShouldUpdate: Subject<number> = new Subject<number>();

    /**
     * 添加 overlay View
     * @waring 建议公共的overlay key定义到enum OverlayViewKe中，重复的key会导致替换调对应的view，
     *         未定义的key 需要外部进行维护
     * @param overlayView
     * @param key
     */
    show(overlayView: JSX.Element, key: string): void {
        this.list.push(overlayView);
        this._list.set(key, overlayView);
        ABCNavigator.getInstance()!.overlayList = [...this._list.values()];
    }

    hide(key: string | (() => string)): void {
        let _key = "";
        if (_.isFunction(key)) {
            _key = key();
        } else {
            _key = key;
        }
        this._list.delete(_key);
        ABCNavigator.getInstance()!.overlayList = [...this._list.values()];
    }
}

const abcOverlay = new AbcOverlay();
export default abcOverlay;
