/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/18
 *
 * @description
 */
import { BaseComponent } from "../base-component";
import { ABCNavigator, TransitionType } from "./abc-navigator";
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { ABCStyles, ABCStyleSheet, Colors, createShadowStyle, Sizes, TextStyles } from "../../theme";
import { Point } from "../ui-events";
import { SafeAreaView, SizedBox } from "../index";
import { pxToDp } from "../utils/ui-utils";
import { RichArrowView } from "./arrow-view";
import { AbcView } from "./abc-view";
import { DeviceUtils } from "../utils/device-utils";
import { GridView } from "./grid-view";

export class MenuItem<T> {
    value: T;
    text?: string;
    textStyle?: Style;
    icon?: JSX.Element;

    constructor(options: { value: T; text?: string; icon?: JSX.Element; textStyle?: Style }) {
        const { value, text, icon, textStyle } = options;
        this.value = value;
        this.text = text;
        this.textStyle = textStyle;
        this.icon = icon;
    }
}

const kItemHeight = pxToDp(56);

const styles = ABCStyleSheet.create({
    topLine: {
        ...ABCStyles.topLine,
        borderColor: "#FFFFFF33",
    },
});

interface ArrowPosition extends Point {
    isBottom?: boolean;
}

interface PopMenuPoint {
    x?: number;
    y?: number;
    l_x?: number;
    l_b?: number;
}

interface PopMenuProps {
    menus: MenuItem<any>[];
    point: PopMenuPoint;
    arrowPosition?: ArrowPosition;
    showArrow?: boolean;
    onChange?(index?: any): void;
}

export class PopMenu extends BaseComponent<PopMenuProps> {
    static show<T>(menus: MenuItem<T>[], anchor: PopMenuPoint, arrowPosition?: ArrowPosition): Promise<T | undefined> {
        return ABCNavigator.navigateToPage(<PopMenu menus={menus} point={anchor} arrowPosition={arrowPosition} />, {
            transitionType: TransitionType.none,
            dismissWhenTouchOutside: false,
        });
    }

    constructor(props: PopMenuProps) {
        super(props);
    }

    render(): JSX.Element {
        const { point, menus, arrowPosition, onChange } = this.props;
        const horizontalPadding = menus.length > 1 ? Sizes.dp10 : Sizes.dp16;
        return (
            <SafeAreaView style={{ flex: 1 }} statusBarColor={"transparent"}>
                <AbcView style={{ flex: 1 }} onClick={() => (!!onChange ? onChange?.() : ABCNavigator.pop())}>
                    <View
                        style={{
                            position: "absolute",
                            right: point.x,
                            top: point.y,
                        }}
                    >
                        <View
                            style={{
                                position: "absolute",
                                top: -Sizes.dp6,
                                right: arrowPosition?.x ?? Sizes.dp18,
                            }}
                        >
                            <RichArrowView
                                rotate={0}
                                size={Sizes.dp14}
                                borderColor={Colors.popMenuBg}
                                backgroundColor={Colors.popMenuBg}
                                borderSize={0}
                            />
                        </View>
                        <View
                            style={{
                                borderRadius: Sizes.dp4,
                                paddingLeft: horizontalPadding,
                                backgroundColor: Colors.popMenuBg,
                            }}
                        >
                            {menus.map((menu, index) => {
                                const { icon, text, value, textStyle } = menu;
                                return (
                                    <AbcView
                                        key={index.toString()}
                                        style={[
                                            ABCStyles.rowAlignCenter,
                                            index > 0 ? styles.topLine : {},
                                            {
                                                height: kItemHeight,
                                                paddingRight: horizontalPadding,
                                            },
                                        ]}
                                        onClick={() => {
                                            !!onChange ? onChange?.(value) : ABCNavigator.pop(value);
                                        }}
                                    >
                                        {!!icon && <View style={{ paddingRight: Sizes.dp10 }}>{icon}</View>}
                                        <Text style={[TextStyles.t16NW, textStyle]}>{text ?? ""}</Text>
                                    </AbcView>
                                );
                            })}
                        </View>
                    </View>
                </AbcView>
            </SafeAreaView>
        );
    }
}

// PopMenu重构 TODO 浮窗缺少投影
export class AbcPopMenu extends BaseComponent<PopMenuProps> {
    static show<T>(
        menus: MenuItem<T>[],
        anchor: PopMenuPoint,
        arrowPosition?: ArrowPosition,
        options?: { showArrow?: boolean; fullGrid?: boolean }
    ): Promise<T | undefined> {
        return ABCNavigator.navigateToPage(
            React.createElement(options?.fullGrid ? AbcPopMenu_FullGrid : this, {
                menus: menus,
                point: anchor,
                arrowPosition: arrowPosition,
                showArrow: options?.showArrow ?? true,
            }),
            {
                transitionType: TransitionType.none,
                dismissWhenTouchOutside: false,
            }
        );
    }

    constructor(props: PopMenuProps) {
        super(props);
    }

    render(): JSX.Element {
        const { point, menus, arrowPosition, showArrow, onChange } = this.props;
        const horizontalPadding = Sizes.dp16;
        const __style: Style = {};
        if (point.l_x != undefined) {
            __style.left = point.l_x;
        }
        if (point.l_b != undefined) {
            __style.bottom = point.l_b;
        }
        if (point.x != undefined) {
            __style.right = point.x;
        }
        if (point.y != undefined) {
            __style.top = point.y;
        }
        return (
            <SafeAreaView style={{ flex: 1 }} statusBarColor={"transparent"}>
                <AbcView style={{ flex: 1 }} onClick={() => (!!onChange ? onChange?.() : ABCNavigator.pop())}>
                    <View
                        style={[
                            {
                                position: "absolute",
                            },
                            __style,
                            {
                                ...createShadowStyle(
                                    {
                                        shadowColor: Colors.S1,
                                        shadowOpacity: 0.12,
                                        shadowRadius: Sizes.dp8,
                                    },
                                    true
                                ),
                            },
                        ]}
                    >
                        {!!showArrow && (
                            <View
                                style={[
                                    {
                                        position: "absolute",

                                        right: arrowPosition?.x ?? Sizes.dp18,
                                        zIndex: DeviceUtils.isAndroid() ? 10 : undefined,
                                    },
                                    !!arrowPosition?.isBottom ? { bottom: -Sizes.dp6 } : { top: -Sizes.dp6 },
                                ]}
                            >
                                <RichArrowView
                                    rotate={!!arrowPosition?.isBottom ? 180 : 0}
                                    size={Sizes.dp14}
                                    borderColor={Colors.S1}
                                    backgroundColor={Colors.S2}
                                    borderSize={0}
                                />
                            </View>
                        )}
                        <View
                            style={[
                                {
                                    borderRadius: Sizes.dp4,
                                    paddingLeft: horizontalPadding,
                                    backgroundColor: Colors.S2,
                                    borderColor: "rgba(0,0,0,0.12)",
                                },
                                DeviceUtils.isAndroid()
                                    ? {
                                          borderWidth: Sizes.dpHalf,
                                      }
                                    : {},
                            ]}
                        >
                            {menus.map((menu, index) => {
                                const { icon, text, value, textStyle } = menu;
                                return (
                                    <AbcView
                                        key={index.toString()}
                                        style={[
                                            ABCStyles.rowAlignCenter,
                                            index > 0 ? ABCStyles.topLine : {},
                                            {
                                                height: kItemHeight,
                                                marginRight: horizontalPadding,
                                            },
                                        ]}
                                        onClick={() => {
                                            !!onChange ? onChange?.(value) : ABCNavigator.pop(value);
                                        }}
                                    >
                                        {!!icon && <View style={{ paddingRight: Sizes.dp10 }}>{icon}</View>}
                                        <Text style={[TextStyles.t16NW, textStyle]}>{text ?? ""}</Text>
                                    </AbcView>
                                );
                            })}
                        </View>
                    </View>
                </AbcView>
            </SafeAreaView>
        );
    }
}

class AbcPopMenu_FullGrid extends AbcPopMenu {
    render(): JSX.Element {
        const { point, menus, arrowPosition, showArrow } = this.props;
        // const horizontalPadding = Sizes.dp16;
        const __style: Style = {};
        __style.left = 0;
        if (point.l_b != undefined) {
            __style.bottom = point.l_b;
        }
        __style.right = 0;
        if (point.y != undefined) {
            __style.top = point.y;
        }
        return (
            <SafeAreaView style={{ flex: 1 }} statusBarColor={"transparent"} bottomSafeAreaColor={"rgba(0,0,0,0.2)"}>
                <AbcView style={{ flex: 1 }} onClick={() => ABCNavigator.pop()}>
                    <View
                        style={[
                            {
                                position: "absolute",
                            },
                            __style,
                        ]}
                    >
                        {!!showArrow && (
                            <View
                                style={{
                                    position: "absolute",
                                    top: -Sizes.dp6,
                                    right: arrowPosition?.x ?? Sizes.dp18,
                                    zIndex: DeviceUtils.isAndroid() ? 10 : undefined,
                                }}
                            >
                                <RichArrowView
                                    rotate={0}
                                    size={Sizes.dp14}
                                    borderColor={Colors.S1}
                                    backgroundColor={Colors.S2}
                                    borderSize={0}
                                />
                            </View>
                        )}
                        <View
                            style={[
                                {
                                    borderBottomLeftRadius: Sizes.dp4,
                                    borderBottomRightRadius: Sizes.dp4,
                                    // paddingRight: horizontalPadding,
                                    backgroundColor: Colors.S2,
                                    borderColor: "rgba(0,0,0,0.12)",
                                },
                                DeviceUtils.isAndroid()
                                    ? {
                                          borderWidth: Sizes.dpHalf,
                                      }
                                    : {},
                            ]}
                        >
                            <GridView
                                crossAxisCount={Math.min(menus.length, 3)}
                                itemHeight={Sizes.dp72}
                                style={{ paddingTop: Sizes.dp12, paddingBottom: Sizes.dp16 }}
                            >
                                {menus.map((menu, index) => {
                                    const { icon, text, value, textStyle } = menu;
                                    return (
                                        <AbcView
                                            key={index.toString()}
                                            style={[
                                                Sizes.paddingLTRB(0, Sizes.dp12, 0, Sizes.dp8),
                                                {
                                                    alignItems: "center",
                                                },
                                            ]}
                                            onClick={() => {
                                                ABCNavigator.pop(value);
                                            }}
                                        >
                                            {!!icon && (
                                                <View
                                                    style={
                                                        {
                                                            /* paddingRight: Sizes.dp10,*/
                                                        }
                                                    }
                                                >
                                                    {icon}
                                                </View>
                                            )}
                                            <SizedBox height={Sizes.dp5} />
                                            <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp25 }), textStyle]}>
                                                {text ?? ""}
                                            </Text>
                                        </AbcView>
                                    );
                                })}
                            </GridView>
                        </View>
                    </View>
                    <View style={[ABCStyles.absoluteFillObject, { backgroundColor: 0x33000000, zIndex: -1, top: 100 }]} />
                </AbcView>
            </SafeAreaView>
        );
    }
}
