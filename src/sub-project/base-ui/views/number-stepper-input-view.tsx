/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/16
 *
 * @description
 */
import { BaseComponent } from "../base-component";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import React from "react";
import { Style, View } from "@hippy/react";
import { IconFontView, SizedBox } from "../index";
import { MinMaxLimitFormat, PrecisionLimitFormatter } from "../utils/formatter";
import { CustomInput } from "../input/custom-input";
import { AbcTextInput, TextFormatter } from "./abc-text-input";
import { NumberUtils } from "../../common-base-module/utils";
import _ from "lodash";

interface NumberStepperInputViewProps {
    size?: { width?: number; height?: number };
    minCount: number;
    maxCount: number;
    value?: number;
    onChanged?: (value: number, textInput?: AbcTextInput | null, isBtn?: boolean) => void;
    onUnitChanged?: (unit: string) => void;
    floatPrecisionLength?: number; //default 0
    borderColor?: Color;
    style?: Style;
    unit?: string; //单位
    unitList?: string[]; //单位列表
    alwaysHideUnit?: boolean; //false, 在编辑框中不显示单位
    onEndEditing?: () => void;

    customInput?: () => JSX.Element;
    stepCount?: number; //  每次加减的值
    disable?: boolean; // 是否可编辑（true--禁用，false--可编辑）
}

export class NumberStepperInputView extends BaseComponent<NumberStepperInputViewProps> {
    static defaultProps = {
        size: { height: Sizes.dp24 },
        minCount: 0,
        maxCount: 0,
        floatPrecisionLength: 0,
    };

    _currentValue?: number;
    _error = false;
    _textInput: CustomInput | null = null;
    protected formatters: TextFormatter[];

    constructor(props: NumberStepperInputViewProps) {
        super(props);

        this._currentValue = this.props.value;

        const { maxCount, minCount, floatPrecisionLength } = this.props;
        this.formatters = [PrecisionLimitFormatter(floatPrecisionLength ?? 0), MinMaxLimitFormat(minCount, maxCount)];
    }

    public componentWillReceiveProps(nextProps: Readonly<NumberStepperInputViewProps> /*, nextContext: any*/): void {
        this._currentValue = nextProps.value;

        const { maxCount: oldMaxCount, minCount: oldMinCount, floatPrecisionLength: oldFloatPrecisionLength } = this.props;
        const { maxCount, minCount, floatPrecisionLength } = nextProps;
        if (oldMaxCount != maxCount || oldMinCount != minCount || oldFloatPrecisionLength != floatPrecisionLength)
            this.formatters = [PrecisionLimitFormatter(floatPrecisionLength ?? 0), MinMaxLimitFormat(minCount, maxCount)];
    }

    public focus(): void {
        this._textInput?.focus();
    }

    public render(): JSX.Element {
        const {
            maxCount,
            minCount,
            size,

            style,
            customInput,
            unit,
            unitList,
            alwaysHideUnit,
            borderColor,
        } = this.props;
        const height = size!.height;
        const halfHeight = height! / 2;
        const iconWidth = Sizes.dp32;

        const maxEnable = (this._currentValue ?? 0.0) < maxCount;
        const minEnable = (this._currentValue ?? 0.0) > minCount;
        return (
            <View
                style={[
                    style ?? {},
                    {
                        ...ABCStyles.rowAlignCenter,
                        borderWidth: 1,
                        borderRadius: halfHeight,
                        borderColor: borderColor ?? Colors.P1,
                        height: height,
                    },
                ]}
                onClick={() => {
                    return;
                }}
            >
                <View
                    style={{
                        justifyContent: "center",
                        alignItems: "center",
                        borderRightWidth: 1,
                        borderColor: Colors.P1,
                        width: iconWidth,
                        height: height,
                    }}
                    onClick={() => minEnable && this._onAdd(-1)}
                >
                    <IconFontView name={"Minus"} size={Sizes.dp16} color={!minEnable ? Colors.T2 : Colors.black} />
                </View>

                {customInput ? (
                    customInput()
                ) : (
                    <CustomInput
                        ref={(ref) => (this._textInput = ref)}
                        borderType={"none"}
                        style={{ flex: 1, textAlign: "center" }}
                        formatter={this.formatters}
                        value={_.isNil(this._currentValue) || isNaN(this._currentValue) ? "" : this._currentValue.toString()}
                        onBlur={() => this._onBlur()}
                        onChange={(text) => this._onChangeText(text)}
                        unit={unit}
                        unitList={unitList}
                        error={this._error}
                        alwaysHideUnit={alwaysHideUnit}
                        onChangeUnit={(unit) => this.props.onUnitChanged?.(unit)}
                        onEndEditing={() => this.props.onEndEditing?.()}
                    />
                )}

                <View
                    style={{
                        justifyContent: "center",
                        alignItems: "center",
                        borderLeftWidth: 1,
                        borderColor: Colors.P1,
                        height: height,
                        width: iconWidth,
                    }}
                    onClick={() => maxEnable && this._onAdd(1)}
                >
                    <IconFontView name={"Plus"} size={Sizes.dp16} color={!maxEnable ? Colors.T2 : Colors.black} />
                </View>
            </View>
        );
    }

    protected _onAdd(i: number): void {
        const { minCount, maxCount } = this.props;
        const current = this._currentValue ?? 0.0;
        /**
         * js 加减精度问题
         */
        let destValue = Number(NumberUtils.toFixedWithoutZero(current + i, 6));

        if (destValue < minCount) {
            destValue = minCount;
        }

        if (destValue > maxCount) {
            destValue = maxCount;
        }

        this._currentValue = destValue;
        if (destValue != current) {
            this._error = false;
            this._textInput?.setValue(destValue.toString(), { syncDefaultValue: true });
            this.props.onChanged?.(destValue, null, true);
            this.setState({});
        }
    }

    protected _onBlur(): void {
        this._valueValidate();
    }

    protected _valueValidate(): void {
        const count = parseFloat(this._textInput?.value ?? "") ?? 0;
        const { minCount, maxCount } = this.props;

        this._error = isNaN(count) || count > maxCount || count < minCount;
    }

    protected _onChangeText(text: string): void {
        this._valueValidate();
        const doubleValue = parseFloat(text);
        if (doubleValue != this._currentValue) {
            this._currentValue = doubleValue;
            this.props.onChanged?.(this._currentValue);
        }
    }
}

export class NewStyleNumberStepperInputView extends NumberStepperInputView {
    static defaultProps = {
        size: { height: Sizes.dp28 },
        minCount: 0,
        maxCount: 0,
        floatPrecisionLength: 0,
        stepCount: 1,
        disable: false,
    };
    render(): JSX.Element {
        const {
            maxCount,
            minCount,
            size,

            style,
            customInput,
            unit,
            unitList,
            alwaysHideUnit,
            stepCount,
            disable,
        } = this.props;
        const height = size!.height;
        const iconWidth = Sizes.dp28;

        const maxEnable = (this._currentValue ?? 0.0) < maxCount && !disable;
        const minEnable = (this._currentValue ?? 0.0) > minCount && !disable;
        return (
            <View
                style={[
                    style ?? {},
                    {
                        ...ABCStyles.rowAlignCenter,
                        height: height,
                    },
                ]}
                onClick={() => {
                    return;
                }}
            >
                <View
                    style={{
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: Colors.whiteSmoke,
                        width: iconWidth,
                        height: height,
                        borderTopLeftRadius: Sizes.dp4,
                        borderBottomLeftRadius: Sizes.dp4,
                        borderTopRightRadius: 1,
                        borderBottomRightRadius: 1,
                    }}
                    onClick={() => minEnable && this._onAdd(-stepCount!)}
                >
                    <IconFontView name={"Minus"} size={Sizes.dp9} color={!minEnable ? Colors.T2 : Colors.black} />
                </View>
                <SizedBox width={Sizes.dp2} />

                {customInput ? (
                    customInput()
                ) : (
                    <CustomInput
                        ref={(ref) => (this._textInput = ref)}
                        borderType={"none"}
                        style={{ flex: 1, textAlign: "center" }}
                        formatter={this.formatters}
                        value={_.isNil(this._currentValue) || isNaN(this._currentValue) ? "" : this._currentValue.toString()}
                        onBlur={() => this._onBlur()}
                        onChange={(text) => this._onChangeText(text)}
                        unit={unit}
                        unitList={unitList}
                        error={this._error}
                        alwaysHideUnit={alwaysHideUnit}
                        onChangeUnit={(unit) => this.props.onUnitChanged?.(unit)}
                        onEndEditing={() => this.props.onEndEditing?.()}
                        textStyle={{ backgroundColor: Colors.whiteSmoke, ...TextStyles.t16NB }}
                        disable={disable}
                    />
                )}
                <SizedBox width={Sizes.dp2} />
                <View
                    style={{
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: Colors.whiteSmoke,
                        height: height,
                        width: iconWidth,
                        borderTopRightRadius: Sizes.dp4,
                        borderBottomRightRadius: Sizes.dp4,
                    }}
                    onClick={() => maxEnable && this._onAdd(stepCount!)}
                >
                    <IconFontView name={"add"} size={Sizes.dp9} color={!maxEnable ? Colors.T2 : Colors.black} />
                </View>
            </View>
        );
    }
}
