/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/4/20
 */
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { IconFontView } from "../index";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import _ from "lodash";
import { BaseComponent } from "../base-component";
import { AbcView } from "./abc-view";

interface AbcCheckboxProps {
    check?: boolean;
    enable?: boolean;
    checkColor?: Color; //check图标的颜色
    checkIconBgColor?: Color; //check图标区域背影色
    size?: number;
    text?: string;
    textStyle?: Style;
    border?: Boolean;
    style?: Style;
    stateless?: boolean; //是否内部控制状态
    onChange?: (check: boolean) => void;
    numberOfLines?: number;
    airTestKey?: string;
}

export class AbcCheckbox extends BaseComponent<AbcCheckboxProps, any> {
    constructor(props: AbcCheckboxProps) {
        super(props);
        this.state = {
            _checked: props.check,
        };
    }

    static defaultProps = {
        check: false,
        enable: true,
        size: Sizes.dp16,
    };

    protected _changeChecked(): void {
        const { enable, onChange, stateless, check } = this.props;
        if (!enable) return;
        else {
            //计算check值
            let newValue = !this.state._checked;
            //如果设置了无状态模式，则根据属性计算check值
            if (stateless) {
                newValue = !check;
            }
            this.setState({
                _checked: newValue,
            });
            if (onChange) onChange(newValue);
        }
    }

    componentWillReceiveProps(nextProps: Readonly<AbcCheckboxProps> /*, nextContext: any*/): void {
        if (this.props.check != nextProps.check) {
            this.setState({ _checked: nextProps.check });
        }
    }

    public renderCheckIcon(): JSX.Element {
        const { stateless, check, checkColor, size } = this.props;
        let { _checked } = this.state;

        if (stateless) {
            _checked = check;
        }
        return _checked ? (
            <IconFontView name={"Positive_Selected"} size={(size ?? Sizes.dp14) - 2} color={checkColor || Colors.mainColor} />
        ) : (
            <View />
        );
    }

    render(): JSX.Element {
        const { text, size, checkIconBgColor, textStyle, border, enable, style, airTestKey } = this.props;
        const lineHeight = textStyle?.lineHeight ?? Sizes.dp16;
        const fontSize = textStyle?.fontSize ?? Sizes.dp16;
        let borderStyle;
        if (border) {
            //@ts-ignore
            borderStyle = {
                ...Sizes.paddingLTRB(Sizes.dp14, 0),
                borderWidth: 1,
                borderColor: Colors.P1,
                borderRadius: Sizes.dp14,
            };
        }
        return (
            <AbcView
                style={[
                    {
                        ...borderStyle,
                        flexDirection: "row",
                        alignItems: "center",
                    },
                    style ?? {},
                ]}
                onClick={enable ? this._changeChecked.bind(this) : undefined}
                airTestKey={airTestKey}
            >
                <View
                    style={{
                        marginVertical: (lineHeight - fontSize) / 2,
                        width: size,
                        height: size,
                        alignItems: "center",
                        borderWidth: 1,
                        borderColor: Colors.P1,
                        borderRadius: Sizes.dp2,
                        backgroundColor: checkIconBgColor ?? (enable ? Colors.white : Colors.P5),
                    }}
                >
                    {this.renderCheckIcon()}
                </View>
                {_.isNil(text) ? (
                    <View />
                ) : (
                    <Text
                        style={[
                            TextStyles.t16NT1,
                            Sizes.marginLTRB(Sizes.dp3, 0),
                            { color: enable ? Colors.T1 : Colors.T2 },
                            {
                                ...textStyle,
                            },
                        ]}
                        numberOfLines={this.props.numberOfLines}
                    >
                        {text}
                    </Text>
                )}
            </AbcView>
        );
    }
}

export enum IndeterminateCheckboxStatus {
    none,
    some,
    all,
}

interface AbcCheckboxProps {
    checkStatus?: IndeterminateCheckboxStatus;
}

export class AbcIndeterminateCheckbox extends AbcCheckbox {
    renderCheckIcon(): JSX.Element {
        const { checkColor, checkStatus } = this.props;
        const { _checked } = this.state;
        if (_checked && checkStatus == IndeterminateCheckboxStatus.all) {
            return <IconFontView name={"Positive_Selected"} size={Sizes.dp14} color={checkColor || Colors.mainColor} />;
        } else if (_checked && checkStatus == IndeterminateCheckboxStatus.some) {
            return <IconFontView name={"Minus"} size={Sizes.dp14} color={checkColor || Colors.mainColor} />;
        } else {
            return <View />;
        }
    }
}

export class AbcIndeterminateNewCheckbox extends AbcCheckbox {
    renderCheckIcon(): JSX.Element {
        const { checkColor, checkStatus } = this.props;
        const { _checked } = this.state;
        if (_checked && checkStatus == IndeterminateCheckboxStatus.all) {
            return (
                <View style={{ justifyContent: "center", width: Sizes.dp19, height: Sizes.dp19 }}>
                    <IconFontView name={"Chosen"} size={Sizes.dp16} color={checkColor || Colors.mainColor} />
                </View>
            );
        } else if (_checked && checkStatus == IndeterminateCheckboxStatus.some) {
            return (
                <View style={{ justifyContent: "center", width: Sizes.dp19, height: Sizes.dp19 }}>
                    <IconFontView name={"half"} size={Sizes.dp19} color={checkColor || Colors.mainColor} />
                </View>
            );
        } else {
            return (
                <View style={{ justifyContent: "center", width: Sizes.dp19, height: Sizes.dp19 }}>
                    <View
                        style={{
                            width: Sizes.dp16,
                            height: Sizes.dp16,
                            borderWidth: Sizes.dp1,
                            borderRadius: Sizes.dp8,
                            borderColor: Colors.P1,
                            backgroundColor: !_checked ? Colors.whiteSmoke : undefined,
                        }}
                    />
                </View>
            );
        }
    }

    render(): JSX.Element {
        const { text, textStyle, border, enable, style } = this.props;
        let borderStyle;
        if (border) {
            //@ts-ignore
            borderStyle = {
                ...Sizes.paddingLTRB(Sizes.dp14, 0),
                borderWidth: 1,
                borderColor: Colors.P1,
                borderRadius: Sizes.dp14,
            };
        }
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        ...borderStyle,
                    },
                    style ?? {},
                ]}
                onClick={enable ? this._changeChecked.bind(this) : undefined}
            >
                {this.renderCheckIcon()}
                {_.isNil(text) ? (
                    <View />
                ) : (
                    <Text
                        style={[
                            TextStyles.t16NT1,
                            Sizes.marginLTRB(Sizes.dp3, 0),
                            { color: enable ? Colors.T1 : Colors.T2 },
                            {
                                ...textStyle,
                            },
                        ]}
                        numberOfLines={this.props.numberOfLines}
                    >
                        {text}
                    </Text>
                )}
            </View>
        );
    }
}
