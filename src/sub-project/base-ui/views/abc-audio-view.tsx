import React from "react";
import { ClickableProps, LayoutableProps, Style, TouchableProps } from "@hippy/react";
import { LogUtils } from "../../common-base-module/log";
import { AbcUIManagerModule } from "../abc-ui-manager-module";

interface TimeUpdateEvent {
    currentTime: number; //单位ms
}

interface AbcAudioViewProps extends LayoutableProps, ClickableProps, TouchableProps {
    src: string;
    emitTimeupdateRate?: number; //发送timeupdate事件的频率,默认250ms一次,音位ms
    style?: Style | Style[];
    onPause?: () => void; //暂时事件
    onPlaying?: () => void; //播放事件
    //播放结束
    onEnded?: () => void;
    onError?: (error: any) => void; //播放出错
    onTimeupdate?: (currentTime: TimeUpdateEvent) => void; //通知当前播放进度
}

/**
 * 播放音频
 */
class AbcAudioView extends React.Component<AbcAudioViewProps, {}> {
    private instance?: HTMLDivElement | null;
    private _isPlaying = false;

    /**
     * 开始播放
     */
    play(src?: string): void {
        LogUtils.d("AbcAudioView.play src = " + src);
        AbcUIManagerModule.callUIFunction(this.instance, "play", [src]);
    }

    /**
     * 暂停播放
     */
    pause(): void {
        LogUtils.d("AbcAudioView.pause");
        AbcUIManagerModule.callUIFunction(this.instance, "pause", []);
    }

    /**
     *
     */
    isPlaying(): boolean {
        return this._isPlaying;
    }

    render(): JSX.Element {
        const { ...nativeProps } = this.props;

        nativeProps.onEnded = this._onEnded.bind(this);
        nativeProps.onPlaying = this._onPlay.bind(this);
        nativeProps.onPause = this._onPause.bind(this);
        nativeProps.onTimeupdate = this._onTimeupdate.bind(this);
        // nativeProps.onPlayStart = true;
        // nativeProps.onPlayStart = true;
        // nativeProps.onPlayProgress =

        return (
            <div
                // @ts-ignore
                nativeName="ABCAudioView"
                ref={(ref: any) => {
                    this.instance = ref;
                }}
                {...nativeProps}
            />
        );
    }

    private _onPlay() {
        LogUtils.d("AbcAudioView._onPlay = this.props.onPlaying = " + this.props.onPlaying);
        this._isPlaying = true;

        this.props.onPlaying!();
    }

    private _onPause() {
        this._isPlaying = false;
        this.props.onPause?.();
    }

    private _onEnded() {
        LogUtils.d("AbcAudioView._onEnd = this.props.onEnded = " + this.props.onEnded);
        this._isPlaying = false;
        this.props.onEnded?.();
    }

    private _onTimeupdate(evt: TimeUpdateEvent) {
        LogUtils.d("AbcAudioView._onTimeupdate = this.props._onTimeupdate = " + JSON.stringify(evt));
        this.props.onTimeupdate?.(evt);
    }
}

export default AbcAudioView;
