/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON> on 2020/6/17
 *
 * @description
 *
 */
import { BaseComponent } from "../base-component";
import React from "react";
import { Style, View } from "@hippy/react";
import { Color, Colors } from "../../theme";

interface SwitchProps {
    value: boolean;
    style?: Style;
    activeColor?: Color;
    deActiveColor?: Color;

    onChanged?: (on: boolean) => void;
}

export class SwitchView extends BaseComponent<SwitchProps> {
    static defaultProps = {
        activeColor: Colors.mainColor,
        deActiveColor: Colors.T4,
        style: { width: 48, height: 24 },
    };

    _value: boolean;

    constructor(props: SwitchProps) {
        super(props);
        this._value = this.props.value;
    }

    public componentWillReceiveProps(nextProps: Readonly<SwitchProps> /*, nextContext: any*/): void {
        this._value = nextProps.value;
    }

    public render(): JSX.Element {
        const { activeColor, deActiveColor, style } = this.props;
        let height = 24;
        if (typeof style?.height === "number") height = style.height;

        const ballSize = height - 4;
        return (
            <View
                style={[
                    style!,
                    {
                        backgroundColor: this._value ? activeColor : deActiveColor,
                        height: height,
                        borderRadius: height / 2,

                        alignItems: this._value ? "flex-end" : "flex-start",
                        justifyContent: "center",
                        paddingHorizontal: 2,
                    },
                ]}
                onClick={() => this._onClick()}
            >
                <View
                    style={{
                        borderRadius: ballSize / 2,
                        width: ballSize,
                        height: ballSize,
                        backgroundColor: Colors.white,
                    }}
                />
            </View>
        );
    }

    private _onClick() {
        this._value = !this._value;
        this.props.onChanged?.(this._value);

        this.setState({});
    }
}
