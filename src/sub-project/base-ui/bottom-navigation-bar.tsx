/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/23
 *
 * @description
 */
import { BaseComponent } from "./base-component";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import React from "react";
import { Text, View } from "@hippy/react";
import { TextStyle } from "../theme/text-styles";
import { AbcButton } from "./views/abc-button";
import { UIUtils } from "./utils";
import { Badge } from "./badge/badge";

interface BottomNavigationBarProps {
    items: BottomNavigationBarItem[];
    currentIndex?: number;
    selectedItemColor?: Color; //Colors.mainColor

    titleStyle?: TextStyle;
    //选择了不同项
    onSelectChanged?: (index: number) => void;
}

export interface BottomNavigationBarItem {
    title: string;
    icon: JSX.Element;
    activeIcon: JSX.Element;
    type?: BottomNavigationBarType; //fixed
    badge?: number;
}

enum BottomNavigationBarType {
    //固定宽度
    fixed,
}

const paddingHorizontal = 0;

export class BottomNavigationBar extends BaseComponent<BottomNavigationBarProps> {
    static defaultProps = {
        currentIndex: 0,
        fixed: BottomNavigationBarType.fixed,
    };

    private _currentIndex: number;

    constructor(props: BottomNavigationBarProps) {
        super(props);

        this._currentIndex = this.props.currentIndex!;
    }

    public setCurrentIndex(index: number): void {
        this._currentIndex = index;
        this.setState({});

        this.props.onSelectChanged?.(index);
    }

    render(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.topLine,
                    {
                        borderColor: Colors.dividerLineColor,
                        flexDirection: "row",
                        justifyContent: "space-between",
                        paddingHorizontal: paddingHorizontal,
                        backgroundColor: Colors.white,
                    },
                ]}
            >
                {this.props.items.map((item, index) => this._renderItem(item, index))}
            </View>
        );
    }

    private _renderItem(item: BottomNavigationBarItem, index: number): JSX.Element {
        const { titleStyle, selectedItemColor = Colors.mainColor } = this.props;
        const width = (UIUtils.getScreenWidth() - 2 * paddingHorizontal) / Math.max(1, this.props.items.length);
        const select = index == this._currentIndex;
        return (
            <AbcButton
                continuousClick={true}
                key={index}
                style={{
                    alignItems: "center",
                    backgroundColor: Colors.transparent,
                    width: width,
                    padding: 0,
                    paddingTop: Sizes.dp10,
                    paddingBottom: Sizes.dp3,
                }}
                pressColor={Colors.buttonPressed}
                onClick={() => this._onSelectChanged(index)}
            >
                <Badge dot={Boolean(item.badge)} position={{ top: -Sizes.dp2, right: -Sizes.dp2 }}>
                    {!select && item.icon}
                    {select && item.activeIcon}
                    <Text style={[TextStyles.t10NT3, titleStyle ?? {}, select ? { color: selectedItemColor } : {}]}>{item.title}</Text>
                </Badge>
            </AbcButton>
        );
    }

    public _onSelectChanged(index: number): void {
        this.setCurrentIndex(index);
    }
}
