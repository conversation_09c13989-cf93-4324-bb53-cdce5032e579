/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-27
 *
 * @description
 *
 */
import { Style, StyleSheet, Text, View } from "@hippy/react";
import * as React from "react";
import { ABCStyles, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import _ from "lodash";
import IconFontView from "../iconfont/iconfont-view";
import { ListViewHelper } from "../list-view-helper";
import { LogUtils } from "../../common-base-module/log";
import { ABCNavigator, BottomSheetOptions } from "../views/abc-navigator";
import { AbcLoadingButton } from "../views/abc-button";
import { BaseComponent } from "../base-component";
import { SafeAreaBottomView } from "../safe_area_view";
import { AbcView } from "../views/abc-view";
import { AnyType } from "../../common-base-module/common-types";
import { ignore } from "../../common-base-module/global";
import { DeviceUtils } from "../utils/device-utils";
import { AssetImageView } from "../views/asset-image-view";
import { AbcToolBar, AbcToolBarButtonStyle1, AbcToolBarButtonStyle2 } from "../abc-app-library/tool-bar-button/tool-bar";
import { pxToDp } from "../utils/ui-utils";
import { AbcText } from "../views/abc-text";

export function showBottomSheet<T>(view: JSX.Element, options?: BottomSheetOptions): Promise<T> {
    const content_view = (
        <AbcView style={{ flexShrink: 1, backgroundColor: DeviceUtils.isOhos() ? Colors.white : undefined }} tagName={"bottomsheet"}>
            {view}
        </AbcView>
    );
    return ABCNavigator.showBottomSheet(content_view, options);
}

export function showRightSheet<T>(view: JSX.Element): Promise<T> {
    return ABCNavigator.showRightSheet(view);
}

// @ts-ignore
const styles = StyleSheet.create({
    titleBarContainer: {
        height: Sizes.listItemHeight,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        borderBottomWidth: Sizes.dpHalf,
        borderColor: Colors.dividerLineColor,
        backgroundColor: Colors.white,
        borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
        paddingLeft: Sizes.listHorizontalMargin,
    },
    abcTitleBarContainer: {
        height: Sizes.dp57,
        borderBottomWidth: Sizes.dpHalf,
        justifyContent: "center",
        alignItems: "center",
        borderColor: Colors.dividerLineColor,
        backgroundColor: Colors.white,
        borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
    },
    itemContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
    },
    selectIconStyle: { marginRight: Sizes.listHorizontalMargin },
});

/**
 * 弹出选择项bottom sheet
 * @param option bottom sheet参数
 */
export function showOptionsBottomSheet(option: {
    title: string; //标题
    needTitleBar?: boolean; // 是否需要头部 titleBar 栏
    options?: string[]; //选项
    optionDescs?: string[]; //描述信息
    optionsWidgets?: JSX.Element[]; //自定义选项组件
    multiSelect?: boolean; //是否多选, default false
    initialSelectIndexes?: Set<number>; //初始选中项
    showConfirmBtn?: boolean;
    showResetBtn?: boolean;
    emptyText?: string;
    alwaysShowSelectedBloc?: boolean; //非选择状态时占位

    showTopRadius?: boolean; // 开启顶部左右圆角 默认6像素

    height?: number;
    titleStyle?: Style;
    titlePosition?: "left" | "center" | "right";
    showCloseButton?: boolean;
    clearOnClick?: () => void;
    canOnClickClear?: boolean;
}): Promise<number[] | undefined> {
    return showBottomSheet(<OptionChooseBottomSheet {...option} />);
}

interface OptionChooseBottomSheetProps {
    title: string; //标题
    needTitleBar?: boolean; // 是否需要头部 titleBar 栏
    options?: string[]; //选项
    optionDescs?: string[]; //描述信息
    optionsWidgets?: JSX.Element[]; //自定义选项组件
    multiSelect?: boolean; //是否多选, default false
    initialSelectIndexes?: Set<number>; //初始选中项
    showConfirmBtn?: boolean;
    showResetBtn?: boolean; // 是否显示重置按钮
    showCheckBox?: boolean;
    height?: number;
    emptyText?: string;
    onCheckChanged?: (indexes: Set<number>, toggleIndex: number) => void;

    alwaysShowSelectedBloc?: boolean;
    rawOptions?: AnyType[];
    optionBuilder?: (option: AnyType, index: number, checked: boolean) => JSX.Element;

    showTopRadius?: boolean;

    titleStyle?: Style;
    titlePosition?: "left" | "center" | "right";
    showCloseButton?: boolean;
    clearOnClick?: () => void;
    canOnClickClear?: boolean;
}

export class OptionChooseBottomSheet extends BaseComponent<OptionChooseBottomSheetProps> {
    static onlyShowItem = "onlyShowItem";

    _currentSelectOptions = new Set<number>();

    _options?: JSX.Element[];
    _rawOptions?: AnyType[];

    constructor(props: OptionChooseBottomSheetProps) {
        super(props);

        const { initialSelectIndexes, emptyText } = this.props;
        if (initialSelectIndexes && initialSelectIndexes.size != 0) {
            this._currentSelectOptions = new Set(initialSelectIndexes.keys());
        }

        const { options } = this.props;
        const { optionDescs } = this.props;
        let { optionsWidgets } = this.props;

        if (_.isEmpty(optionsWidgets) && options) {
            optionsWidgets = options!.map((option: string, index) => {
                const desc = optionDescs && optionDescs.length > index ? optionDescs[index] : "";
                if (_.isEmpty(desc)) {
                    return (
                        <View
                            key={index}
                            style={{
                                ...ABCStyles.rowAlignCenter,
                                height: Sizes.listItemHeight,
                                paddingHorizontal: Sizes.listHorizontalMargin,
                            }}
                            onClick={() => this._onClickItem(index)}
                        >
                            <Text
                                style={[
                                    TextStyles.t16NB,
                                    {
                                        flex: 1,
                                    },
                                ]}
                            >
                                {option}
                            </Text>
                        </View>
                    );
                } else {
                    return (
                        <View
                            key={index}
                            style={{
                                height: Sizes.listItemHeight + Sizes.dp18,
                                paddingHorizontal: Sizes.listHorizontalMargin,
                            }}
                            onClick={() => this._onClickItem(index)}
                        >
                            <Text
                                style={[
                                    TextStyles.t16NB,
                                    {
                                        flex: 1,
                                        marginTop: Sizes.dp14,
                                    },
                                ]}
                            >
                                {option}
                            </Text>
                            <Text
                                style={[
                                    TextStyles.t12NT4,
                                    {
                                        flex: 1,
                                    },
                                ]}
                            >
                                {desc}
                            </Text>
                        </View>
                    );
                }
            });
        }

        if (_.isEmpty(optionsWidgets) && emptyText) {
            optionsWidgets = [
                <Text
                    key={"empty"}
                    style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp64 }), { flex: 1, textAlign: "center" }]}
                    onClick={() => ignore()}
                >
                    {emptyText}
                </Text>,
            ];
        }

        this._options = optionsWidgets;

        this._rawOptions = this.props.rawOptions;
    }

    setSelectIndexes(indexes: Set<number>): void {
        this._currentSelectOptions = indexes;

        this.setState({});
    }

    setOptions(options: JSX.Element[]): void {
        this._options = options;
        this.setState({});
    }

    setRawOptions(options: AnyType): void {
        this._rawOptions = options;
        this.setState({});
    }

    render(): JSX.Element {
        const {
            title,
            showConfirmBtn,
            optionBuilder,
            alwaysShowSelectedBloc,
            titlePosition,
            titleStyle,
            showCloseButton,
            clearOnClick,
            canOnClickClear,
            showResetBtn,
            needTitleBar = true,
        } = this.props;

        let _titlePosition = BottomSheetHelper.createTitleBar(title);
        if (titlePosition == "center") {
            _titlePosition = BottomSheetHelper.abcTitleBar({
                title: title,
                showCloseButton: showCloseButton,
                titleStyle: titleStyle,
                clearOnClick: clearOnClick,
                canOnClickClear: canOnClickClear,
            });
        }
        let optionsWidgets = this._options?.map((item, index) => {
            //某些项只做展示
            const reg = RegExp(OptionChooseBottomSheet.onlyShowItem);
            const __isExistOnlyShowItem = reg.test((item.key ?? "").toString());
            return (
                <AbcView key={index} onClick={() => (__isExistOnlyShowItem ? {} : this._onClickItem(index))} style={styles.itemContainer}>
                    {item}
                    {this._currentSelectOptions.has(index) ? (
                        <IconFontView
                            name={"Positive_Selected"}
                            size={Sizes.dp14}
                            color={Colors.mainColor}
                            style={styles.selectIconStyle}
                        />
                    ) : (
                        !!alwaysShowSelectedBloc && <View style={[styles.selectIconStyle, { width: Sizes.dp14 }]} />
                    )}
                </AbcView>
            );
        });

        if (!optionsWidgets && this._rawOptions && optionBuilder) {
            optionsWidgets = this._rawOptions.map((option, index) => (
                <AbcView key={index} onClick={() => this._onClickItem(index)} style={{ alignItems: "stretch" }}>
                    {optionBuilder(option, index, this._currentSelectOptions.has(index))}
                </AbcView>
            ));
        }

        return (
            <AbcView
                style={{
                    backgroundColor: Colors.white,
                    flex: 1,
                    height: this.props.height ?? pxToDp(375),
                    borderTopLeftRadius: this.props.showTopRadius ? 6 : undefined, // 这里圆角仅支持纯数字否则底部可能会透出
                    borderTopRightRadius: this.props.showTopRadius ? 6 : undefined,
                    overflow: this.props.showTopRadius ? "hidden" : undefined,
                }}
                onClick={() => ABCNavigator.pop()}
            >
                {needTitleBar && _titlePosition}
                <View style={{ flex: 1 }}>
                    {ListViewHelper.build({
                        children: optionsWidgets!,
                        style: { flex: 1 },
                    })}
                    {(showResetBtn || showConfirmBtn) && (
                        <AbcToolBar toolbarHeight={Sizes.dp80}>
                            {showResetBtn && <AbcToolBarButtonStyle2 key={"reset"} text={"重置"} onClick={() => this._onReset()} />}
                            {showConfirmBtn && <AbcToolBarButtonStyle1 key={"confirm"} text={"确定"} onClick={() => this._onConfirm()} />}
                        </AbcToolBar>
                    )}
                </View>
                <SafeAreaBottomView />
            </AbcView>
        );
    }

    private _onClickItem(index: number): void {
        const { multiSelect, onCheckChanged, showConfirmBtn } = this.props;
        if (this._currentSelectOptions.has(index)) {
            if (multiSelect) {
                this._currentSelectOptions.delete(index);
            }
        } else {
            if (!multiSelect) this._currentSelectOptions.clear();

            this._currentSelectOptions.add(index);
        }

        onCheckChanged?.(this._currentSelectOptions, index);

        if (!showConfirmBtn) {
            this._onConfirm();
        } else {
            this.setState({});
        }
    }

    private _onConfirm() {
        ABCNavigator.pop(Array.from(this._currentSelectOptions.values()));
    }
    private _onReset() {
        this._currentSelectOptions = new Set<number>();
        ABCNavigator.pop(Array.from(this._currentSelectOptions.values()));
    }
}

export function showModel(options: { title: string; view: JSX.Element | JSX.Element[]; showRadius?: boolean }): Promise<any> {
    const view = (
        <View
            style={[
                {
                    backgroundColor: Colors.white,
                },
                !!options.showRadius
                    ? { overflow: "hidden", borderTopRightRadius: Math.floor(Sizes.dp6), borderTopLeftRadius: Math.floor(Sizes.dp6) }
                    : {},
            ]}
        >
            {BottomSheetHelper.createTitle(options.title)}
            <View>{options.view}</View>
            <SafeAreaBottomView />
        </View>
    );
    return showBottomSheet(view);
}

export class TopSheetHelper {
    static createTitleBar(title: string, callback?: () => void): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    {
                        justifyContent: "center",
                        backgroundColor: Colors.white,
                        height: Sizes.dp44,
                    },
                ]}
            >
                <View style={[styles.backButton, { position: "absolute", left: 0 }]}>
                    <BottomSheetCloseButton
                        onTap={() => {
                            ABCNavigator.pop();
                        }}
                    />
                </View>
                <View>
                    <Text style={TextStyles.t18NB}>{title}</Text>
                </View>
                <View style={{ position: "absolute", right: Sizes.dp10, flexDirection: "row" }}>
                    <AbcLoadingButton
                        style={{ height: Sizes.dp26, paddingHorizontal: Sizes.dp10 }}
                        onClick={() => {
                            callback && callback();
                        }}
                    >
                        <Text style={TextStyles.t14NW}>完成</Text>
                    </AbcLoadingButton>
                </View>
            </View>
        );
    }
}

export class BottomSheetHelper {
    static createTitleBar(title: string, showCloseButton?: boolean): JSX.Element {
        return (
            <View style={styles.titleBarContainer}>
                <Text style={TextStyles.t14NT4}>{title}</Text>
                <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
                {showCloseButton && <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />}
            </View>
        );
    }

    static createTitle(title: string, showCloseButton?: boolean, titleStyle?: Style): JSX.Element {
        return (
            <View
                style={[
                    styles.titleBarContainer,
                    {
                        justifyContent: "center",
                    },
                ]}
            >
                <Text style={titleStyle ?? TextStyles.t16NB}>{title}</Text>
                {showCloseButton && <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />}
            </View>
        );
    }

    static abcTitleBar(options: {
        title: string | JSX.Element;
        titleStyle?: Style;
        showCloseButton?: boolean;
        clearOnClick?: () => void;
        canOnClickClear?: boolean;
    }): JSX.Element {
        let titleView = options.title;
        if (_.isString(options.title)) {
            titleView = <Text style={[options.titleStyle ?? TextStyles.t18MT1]}>{options.title}</Text>;
        }
        return (
            <View style={styles.abcTitleBarContainer}>
                {options.clearOnClick && (
                    <View style={{ position: "absolute", left: 0 }}>
                        <AbcText
                            style={[
                                TextStyles.t16NM.copyWith({
                                    lineHeight: Sizes.dp22,
                                    color: !!options.canOnClickClear ? Colors.mainColor : Colors.bdColor,
                                }),
                                { padding: Sizes.listHorizontalMargin },
                            ]}
                            onClick={!!options.canOnClickClear ? () => options.clearOnClick?.() : undefined}
                        >
                            {"清除"}
                        </AbcText>
                    </View>
                )}
                {titleView ?? <View />}
                {options.showCloseButton && (
                    <View style={{ position: "absolute", right: 0, top: Sizes.dp6 }}>
                        <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
                    </View>
                )}
            </View>
        );
    }

    static createBottomBar(params: {
        text?: string;
        hideWhenKeyboardShow?: boolean;
        onClick?: () => void;
        lineHeight?: number;
    }): JSX.Element {
        return (
            <AbcToolBar hideWhenKeyboardShow={params.hideWhenKeyboardShow} lineHeight={params?.lineHeight}>
                <AbcToolBarButtonStyle1 text={params.text ?? "确定"} onClick={() => params.onClick?.()} />
            </AbcToolBar>
        );
    }
}

class BottomSheetCloseButtonProps {
    style?: Style | Style[];
    onTap?(): void;
}

export class BottomSheetCloseButton extends React.Component<BottomSheetCloseButtonProps> {
    render(): JSX.Element {
        return (
            <AbcView onClick={this._onTap.bind(this)} style={{ paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp14 }}>
                <AssetImageView
                    name={"delete_circle_grey"}
                    style={[{ width: Sizes.dp18, height: Sizes.dp18 }, flattenStyles(this.props.style)]}
                />
            </AbcView>
        );
    }

    private _onTap(): void {
        LogUtils.d("BottomSheetCloseButton.onTap");
        if (this.props.onTap) this.props.onTap();
    }
}

/**
 * 弹出选择项right sheet
 * @param option bottom sheet参数
 */

export function showOptionsRightSheet(option: {
    options?: string[]; //选项
    optionsWidgets?: JSX.Element[]; //自定义选项组件
    multiSelect?: boolean; //是否多选, default false
    initialSelectIndexes?: Set<number>; //初始选中项
}): Promise<[number]> {
    let optionsWidgets = option.optionsWidgets;
    if (_.isEmpty(optionsWidgets) && option.options) {
        const initialSelectIndexes = option.initialSelectIndexes;
        optionsWidgets = option.options!.map((option: string, index) => {
            return (
                <View
                    key={index}
                    style={{
                        height: Sizes.listItemHeight,
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        justifyContent: "center",
                        flexDirection: "row",
                        alignItems: "center",
                    }}
                    onClick={() => {
                        ABCNavigator.pop([index]);
                    }}
                >
                    <Text
                        style={[
                            TextStyles.t16NB,
                            {
                                flexGrow: 1,
                                alignItems: "center",
                            },
                        ]}
                    >
                        {option}
                    </Text>
                    {initialSelectIndexes?.has(index) && <IconFontView name={"Positive_Selected"} size={14} color={Colors.mainColor} />}
                </View>
            );
        });
    }

    const view = (
        <View
            style={{
                position: "absolute",
                right: 0,
                backgroundColor: Colors.white,
                width: 140,
                height: 352,
                top: 120,
            }}
            onClick={() => ABCNavigator.pop()}
        >
            {ListViewHelper.build({
                children: optionsWidgets!,
            })}
        </View>
    );

    return showRightSheet(view);
}
