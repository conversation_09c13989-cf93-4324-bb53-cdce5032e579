/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-29
 *
 * @description
 */
import { BaseComponent } from "../base-component";
import React from "react";
import { Text, View } from "@hippy/react";
import { showDialog } from "./dialog-builder";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import _ from "lodash";
import { ABCNavigator } from "../views/abc-navigator";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView } from "../index";
import { AccessibilityLabelType } from "../../data/constants/accessibility-label-type";

interface ToastProps {
    tips: string;
    duration?: number | "LONG" | "SHORT";
    success?: boolean;
    warning?: boolean;
}

/**
 *
 */
export class Toast extends BaseComponent<ToastProps> {
    private isPop = false;
    static show(
        tips: string,
        options?: {
            success?: boolean;
            warning?: boolean;
            autoBlurText?: boolean;
            duration?: number | "LONG" | "SHORT";
        }
    ): Promise<boolean | undefined> {
        return showDialog(<Toast tips={tips} success={options?.success} warning={options?.warning} duration={options?.duration} />, {
            routeName: "Toast",
            alignItems: "center",
            autoBlurText: options?.autoBlurText,
        });
    }

    constructor(props: ToastProps) {
        super(props);

        const { duration } = this.props;
        let durationNum = 1000; //short
        if (duration == "LONG") {
            durationNum = 3000; //
        } else if (_.isNumber(duration)) {
            durationNum = duration;
        }

        delayed(durationNum)
            .subscribe(() => {
                this.isPop = true;
                ABCNavigator.pop(true);
            })
            .addToDisposableBag(this);
    }
    componentWillUnmount(): void {
        super.componentWillUnmount();
        if (!this.isPop) {
            ABCNavigator.pop(true);
        }
    }

    render(): JSX.Element {
        const { tips, success, warning } = this.props;
        let accessibilityLabel = `${AccessibilityLabelType.TOAST}-`;
        if (warning) {
            accessibilityLabel += "warning-";
        }
        return (
            <View
                accessibilityLabel={`${accessibilityLabel}${tips}`}
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        backgroundColor: Colors.toast_bg,
                        padding: Sizes.dp16,
                        borderRadius: Sizes.dp4,
                        marginHorizontal: Sizes.dp20,
                    },
                ]}
            >
                {warning ? (
                    // <AssetImageView name={"inventory_alarm"} style={{ width: Sizes.dp18, height: Sizes.dp18, marginRight: Sizes.dp6 }} />
                    <IconFontView size={Sizes.dp18} name={"Attention"} color={Colors.Y2} />
                ) : undefined}
                {success ? <IconFontView name={"Chosen"} size={Sizes.dp14} color={Colors.mainColor} /> : undefined}
                <Text style={[TextStyles.t16NW, { marginLeft: Sizes.dp6 }]}>{tips}</Text>
            </View>
        );
    }
}
