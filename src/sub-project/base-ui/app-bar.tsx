/**
 * Created by <PERSON><PERSON><PERSON> on 2020/3/12.
 */
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import React from "react";
import { Style, StyleSheet, Text, View } from "@hippy/react";
import { LogUtils } from "../common-base-module/log";

import IconFontView from "./iconfont/iconfont-view";
import { ABCNavigator } from "./views/abc-navigator";
import { BaseComponent } from "./base-component";
import { SearchInput } from "./searchBar/search-bar";
import { SizedBox, Spacer } from "./index";
import { KeyboardType } from "./views/abc-text-input";
import { AbcView } from "./views/abc-view";
import { switchMap } from "rxjs/operators";
import { of, Subject } from "rxjs";
import { DeviceUtils } from "./utils/device-utils";

// @ts-ignore
const styles = StyleSheet.create({
    container: {
        backgroundColor: "white",
        height: Sizes.dp44,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        borderBottomWidth: 1,
        borderColor: Colors.dividerLineColor,
    },
    backButton: {
        left: 0,
    },
    appBarIcon: {
        paddingLeft: 0,
        paddingRight: 0,
    },
});

interface AppBarPros {
    title?: string;
    titleColor?: Color;
    bgColor?: Color;
    showBackBtn?: boolean;
    backBtnColor?: Color;
    backIcon?: JSX.Element;
    bottomLine?: boolean;
    customTitle?: JSX.Element;
    leftPart: JSX.Element[];
    rightPart: JSX.Element[];
    onBackClick?: () => void;
    onLongClick?: () => void;
}

export default class AppBar extends React.Component<AppBarPros> {
    static defaultProps = {
        title: "",
        titleColor: Colors.black,
        bgColor: Colors.white,
        backBtnColor: Colors.black,
        bottomLine: true,
        leftPart: [],
        rightPart: [],
        showBackBtn: true,
    };

    constructor(props: AppBarPros) {
        super(props);
        LogUtils.d(`AppBar props = ${props}, title = ${props.title}`);
    }

    render(): JSX.Element {
        const { title, bgColor, backBtnColor, bottomLine, leftPart, rightPart, customTitle, titleColor, backIcon, showBackBtn } =
            this.props;
        return (
            <View
                style={[
                    styles.container,
                    {
                        backgroundColor: bgColor,
                        borderBottomWidth: bottomLine ? 1 : 0,
                    },
                ]}
            >
                {ABCNavigator.canPop() &&
                    showBackBtn &&
                    (backIcon ?? (
                        <IconFontView
                            name={"back"}
                            size={Sizes.dp20}
                            color={backBtnColor}
                            style={{ paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 }}
                            onClick={() => this._onBackClick()}
                        />
                    ))}
                <View style={{ flexDirection: "row" }}>
                    {leftPart.map((item, index) => (
                        <View key={index.toString()} style={styles.appBarIcon}>
                            {item}
                        </View>
                    ))}
                </View>
                <Spacer />
                <View
                    style={[ABCStyles.absoluteFillObject, ABCStyles.centerChild, { zIndex: -1 }]}
                    onClick={() => true}
                    onLongClick={() => {
                        this.props.onLongClick?.();
                    }}
                >
                    {customTitle ? (
                        customTitle
                    ) : (
                        <Text style={[TextStyles.t16MB, titleColor ? { color: titleColor } : {}]}>{title ?? ""}</Text>
                    )}
                </View>
                <Spacer />

                <View style={{ paddingRight: Sizes.dp10, flexDirection: "row" }}>
                    {rightPart.map((item, index) => (
                        <View key={index.toString()} style={styles.appBarIcon}>
                            {item}
                        </View>
                    ))}
                </View>
            </View>
        );
    }

    private _onBackClick(): void {
        const { onBackClick } = this.props;
        if (onBackClick) {
            onBackClick();
            return;
        }

        ABCNavigator.pop();
    }
}

interface AppSearchBarProps {
    placeholder?: string;
    placeholderColor?: Color;
    leftIconColor?: Color;
    inputStyle?: Style;
    searchContainerStyle?: Style;
    defaultValue?: string;
    autoFocus?: boolean;
    title?: string;
    bgColor?: Color;
    backBtnColor?: Color;
    bottomLine?: false;
    keyboardType?: KeyboardType;
    enableDefaultToolBar?: boolean;

    onBackClick?: () => void;

    rightPart?: JSX.Element[];
    onChangeText?: (text: string) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    maxLength?: number;
    showBackIcon?: boolean; //是否显示返回按钮
    style?: Style | Style[];
    onTriggerScan?: (code?: string) => void;
    isNeedAutoFillText?: boolean; //是否需要自动回显搜索内容
    isShowScanIcon?: boolean; //是否显示扫一扫按钮
    qrScanGenerator?: Function; //扫码查询的接口
    qrScanRsp?: (rsp?: any) => void;
}

/**
 * 搜索AppBar
 */
export class AppSearchBar extends BaseComponent<AppSearchBarProps> {
    static defaultProps = {
        title: "",
        bgColor: Colors.white,
        backBtnColor: Colors.black,
        bottomLine: true,
        rightPart: [],
        enableDefaultToolBar: true,
        showBackIcon: true,
    };
    private _searchText: SearchInput | null = null;
    private _qrScanSearchTrigger: Subject<string> = new Subject<string>();

    constructor(props: AppSearchBarProps) {
        super(props);
    }

    componentDidMount(): void {
        this._qrScanSearchTrigger
            .pipe(
                switchMap((code) => {
                    if (!code) return of(null);
                    return this.props.qrScanGenerator?.({ keyword: code }).toObservable();
                })
            )
            .subscribe((rsp) => {
                this.props.qrScanRsp?.(rsp);
            })
            .addToDisposableBag(this);
    }

    componentWillReceiveProps(nextProps: Readonly<AppSearchBarProps>): void {
        if (!!nextProps.defaultValue && this.props.defaultValue != nextProps.defaultValue) {
            this._searchText?.setText(nextProps.defaultValue);
        }
    }

    public setSearchText(text: string): void {
        this._searchText?.setText(text);
    }

    render(): JSX.Element {
        const { bgColor, backBtnColor, bottomLine, rightPart, showBackIcon, style } = this.props;
        return (
            <View
                style={[
                    styles.container,
                    {
                        backgroundColor: bgColor,
                        borderBottomWidth: bottomLine ? 1 : 0,
                    },
                    flattenStyles(style),
                ]}
            >
                {showBackIcon && (
                    <AbcView style={styles.backButton} onClick={() => this._onBackClick()}>
                        <IconFontView
                            name="back"
                            size={Sizes.dp20}
                            color={backBtnColor}
                            style={{
                                paddingLeft: DeviceUtils.isOhos() ? Sizes.dp16 : Sizes.dp0,
                                paddingHorizontal: DeviceUtils.isOhos() ? Sizes.dp0 : Sizes.dp16,
                                paddingVertical: Sizes.dp12,
                            }}
                        />
                    </AbcView>
                )}
                {this._renderSearchInput()}
                <View style={{ marginRight: Sizes.dp10, flexDirection: "row" }}>
                    {rightPart && <SizedBox width={Sizes.dp16} />}
                    {rightPart?.map((item, index) => (
                        <View key={index.toString()} style={styles.appBarIcon}>
                            {item}
                        </View>
                    ))}
                </View>
            </View>
        );
    }

    private _renderSearchInput(): JSX.Element {
        const {
            placeholder,
            defaultValue,
            autoFocus,
            keyboardType,
            enableDefaultToolBar,
            maxLength,
            placeholderColor,
            leftIconColor,
            inputStyle,
            searchContainerStyle,
            isNeedAutoFillText,
            isShowScanIcon,
        } = this.props;
        return (
            <View style={{ flex: 1 }}>
                <SearchInput
                    leftIconColor={leftIconColor}
                    inputStyle={inputStyle}
                    searchContainerStyle={searchContainerStyle}
                    maxLength={maxLength}
                    ref={(ref) => (this._searchText = ref)}
                    placeholder={placeholder}
                    value={defaultValue}
                    autoFocus={autoFocus}
                    keyboardType={keyboardType}
                    enableDefaultToolBar={enableDefaultToolBar}
                    onChange={(value) => this.props.onChangeText?.(value)}
                    onFocus={() => this.props.onFocus?.()}
                    onBlur={() => this.props.onBlur?.()}
                    placeholderColor={placeholderColor}
                    isShowScanIcon={isShowScanIcon}
                    onTriggerScan={(code) => {
                        this._qrScanSearchTrigger.next(code);
                    }}
                    isNeedAutoFillText={isNeedAutoFillText}
                    scanIconStyle={{ marginRight: Sizes.dp12 }}
                />
            </View>
        );
    }

    private _onBackClick(): void {
        const { onBackClick } = this.props;
        if (onBackClick) {
            onBackClick();
            return;
        }

        ABCNavigator.pop();
    }
}
