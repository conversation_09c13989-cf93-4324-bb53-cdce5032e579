/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-31
 *
 * @description
 */

import { Image } from "@hippy/react";
import { Completer } from "../../common-base-module/async/completer";
import { LogUtils } from "../../common-base-module/log";
import { AbcPlatformMix } from "../../base-business/native-modules/abc-platform-mix";

export class ImageUtils {
    static getImageSize(url: string): Promise<{ width: number; height: number }> {
        LogUtils.d("ImageUtils.getImageSize");
        const completer = new Completer<{ width: number; height: number }>();

        if (url.startsWith("/")) {
            AbcPlatformMix.getImageSize(url)
                .then((size) => {
                    completer.resolve(size);
                })
                .catch((error) => completer.reject(error));
        } else {
            Image.getSize(
                url,
                (width, height) => completer.resolve({ width: width, height: height }),
                (err) => completer.reject(err)
            );
        }

        return completer.promise;
    }
}
