/**
 * create by dengjie
 * desc:
 * create date 2020/4/28
 */

import _ from "lodash";
import { NumberUtils } from "../../common-base-module/utils";
import abcI18Next from "../../language/config";

export class ABCUtils {
    static _chineseWord: Array<string> = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    static _chineseRadix: Array<string> = ["", "十", "百", "千", "万", "亿", "十", "百", "千"];
    static _circledNums: Array<string> = ["①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩", "⑪", "⑫", "⑬", "⑭", "⑮", "⑯"];

    // static formatDatetimeAsRecent(datetime: Date, year?: boolean, time?: boolean): string {
    //     year = year ?? true;
    //     time = time ?? true;
    //
    //     if (datetime == null) {
    //         return '';
    //     }
    //     let now = new Date();
    //     let todayEnd =new Date(now.getFullYear(), now.getMonth(), now.getDay(), 23, 59, 59, 999);
    //     var dayFormat = new Date((year ? 'yyyy-' : '') + 'MM-dd');
    //
    //     var diff = todayEnd.difference(datetime);
    //     var day = '';
    //     if (diff.inDays <= 0) {
    //         day = '今天';
    //     } else if (diff.inDays <= 1) {
    //         day = '昨天';
    //     } else if (diff.inDays <= 2) {
    //         day = '前天';
    //     } else {
    //         day = dayFormat.format(datetime);
    //     }
    //
    //     if (!time) {
    //         return day;
    //     }
    //
    //     var timeFormat = DateFormat('HH:mm');
    //     return `${day} ${timeFormat.format(datetime)}`;
    // }

    static toChineseNum(num: number): string {
        if (num >= 10000) {
            return "";
        }
        const charArr: string[] = [...(num + "")],
            len = charArr.length,
            outStrArr: string[] = [];
        charArr.forEach((_i, j) => {
            const i = Number(_i);
            if (len !== 2 || i != 1 || j != 0) {
                outStrArr.push(this._chineseWord[i]);
            }
            if (i > 0) {
                outStrArr.push(this._chineseRadix[len - j - 1]);
            }
        });

        return outStrArr.join("").replace(/零+/g, "零").replace(/零+$/, "");
    }

    static toCircledNum(i: number): string {
        if (i == null) {
            return "";
        }
        // if (i > 0 && i < this._circledNums.length) {
        //     return this._circledNums[i - 1];
        // } else {
        //     return this._circledNums[i - 1];
        // }
        return this._circledNums[i - 1];
    }

    static formatPrice(price: number): string {
        if (price == null) {
            return Number(0).toFixed(2);
        }
        return NumberUtils.formatMaxFixed(price, 2);
    }

    /**
     * 格式化金额，支持 2 - 4 位小数
     * @param num
     * @param toFixed2
     */
    static formatMoney(num?: number, toFixed2 = true): string {
        if (num === null || num === undefined) return "";
        num = Number((+num).toFixed(5));
        let numStr = "" + num;

        // 是否为负数
        let isMinus = false;
        if (/^-/.test(numStr)) {
            isMinus = true;
            numStr = numStr.slice(1);
        }

        if (toFixed2) {
            numStr = Number(numStr).toFixed(2);
        } else {
            const decimal = numStr.split(".")[1];
            const decimalLength = decimal ? decimal.length : 0;
            if (decimalLength > 5) {
                numStr = Number(numStr).toFixed(5);
            } else if (decimalLength > 2) {
                numStr = Number(numStr).toFixed(decimalLength);
            } else {
                numStr = Number(numStr).toFixed(2);
            }
        }

        numStr = (isMinus ? "-" : "") + numStr;
        return numStr;
    }

    static isEmpty<T>(list: Array<T> | undefined): boolean {
        return list == null || list.length == 0;
    }

    static isNotEmpty<T>(list: Array<T>): boolean {
        return !_.isEmpty(list);
    }

    static first<T>(list: Array<T>): T | undefined {
        if (_.isEmpty(list)) {
            return undefined;
        }

        return list[0];
    }

    static last<T>(list: Array<T>): T | undefined {
        if (_.isEmpty(list)) {
            return;
        }

        return [...list].pop();
    }

    /**
     * 格式化库存价格 （默认保留2位小数 超过两位使用原始值）
     * @param options
     */
    static inventoryPriceWithRMB(options: { price: number; showSymbols?: boolean }): string {
        const { price, showSymbols = true } = options;
        let priceStr;
        if (Number.isInteger(price)) {
            priceStr = price.toFixed(2).toString();
        } else {
            const decimalPart = price.toString().split(".")[1];
            if (decimalPart && decimalPart.length > 2) {
                priceStr = price.toString();
            } else {
                priceStr = price.toFixed(2);
            }
        }

        return `${showSymbols ? abcI18Next.t("¥") : ""}${priceStr}`;
    }

    static formatPriceWithRMB(price: number, showPlus = true, treatZeroAsPositive = true, scale = 2): string {
        const priceAbs = Math.abs(price);

        let priceStr;
        if (price == undefined) {
            priceStr = NumberUtils.formatMaxFixed(0, scale);
        } else {
            priceStr = NumberUtils.formatMaxFixed(priceAbs, scale);
        }

        let sign = price < 0 ? "-" : "";

        if (showPlus) {
            sign = price > 0 || (treatZeroAsPositive && price == 0) ? "+" : "-";
        }
        return `${sign}${abcI18Next.t("¥")}${priceStr}`;
    }

    static paddingMoney(money: string | number, toFixed = false): string {
        if (money === "" || money === null || money === undefined || _.isNaN(money)) return toFixed ? (0).toFixed(2) : "0";

        // 中药默认支持5位小数
        money = Number(money).toFixed(5);
        // 后面的0抹去
        money = money.match(/\-?[0-9]+\.[0-9]{2}/) + money.substring(money.length - 3).replace(/0+$/, "");

        // 是否为负数
        let isMinus = false;
        if (/^\-/.test(money)) {
            isMinus = true;
            money = ("" + money).slice(1);
        }

        const integer = money.split(".")[0];
        const decimal = money.split(".")[1];

        let result = "";
        // while (integer.length > 3) {
        //     result = ',' + integer.slice(-3) + result;
        //     integer = integer.slice(0, integer.length - 3);
        // }
        if (integer) {
            result = integer + result;
        }

        const _result = (isMinus ? "-" : "") + `${result}.${decimal}`;

        if (toFixed) {
            return (+_result).toFixed(2);
        }
        return _result;
    }
}
