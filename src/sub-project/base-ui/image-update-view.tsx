/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/11/26
 */
import React from "react";
import { Image, ScrollView, Style, View } from "@hippy/react";
import { BaseComponent } from "./base-component";
import { ABCStyles, Colors, flattenStyles, Sizes } from "../theme";
import { ABCNavigator } from "./views/abc-navigator";
import { ImageGalleryViewPage } from "./views/image-gallery-view-page";
import { showQueryDialog } from "./dialog/dialog-builder";
import { LayoutEvent } from "./ui-events";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { StringUtils } from "./utils/string-utils";
import { AttachmentItem } from "../base-business/data/beans";
import { AbcView } from "./views/abc-view";
import { IconFontView } from "./index";
import { AssetImageView } from "./views/asset-image-view";
import { WebviewPage } from "./webview-page";
import { DeviceUtils } from "./utils/device-utils";

interface ImageUpdateViewProps {
    imageList?: AttachmentItem[];
    ableUpdate?: boolean;
    imageSize?: number;
    contentStyle?: Style | Style[];

    showAddIcon?: boolean; // 显示添加图标
    onDelete?(index: number): void;

    onUpdate?(): void;
}

export class ImageUpdateView extends BaseComponent<ImageUpdateViewProps> {
    private _scrollViewRef?: ScrollView | null;
    private _lastRef?: LayoutEvent;

    constructor(props: ImageUpdateViewProps) {
        super(props);
    }

    static defaultProps = {
        imageSize: Sizes.dp48,
        showAddIcon: true,
    };

    _showImageGalleryViewPage(attachments: Array<AttachmentItem>, attachment: number, mutable: boolean): void {
        const images = attachments.map((item) => item);
        ABCNavigator.navigateToPage(
            <ImageGalleryViewPage images={images} initialPage={attachment} mutable={mutable} onDelete={this._onDeleteImage.bind(this)} />
        ).then();
    }

    async _onDeleteImage(index: number): Promise<boolean> {
        const _select = await showQueryDialog("确认删除图片？", "");
        if (_select) {
            this.props.onDelete?.(index);
            return true;
        }
        return false;
    }

    componentDidUpdate(prevProps: Readonly<ImageUpdateViewProps>): void {
        if (
            prevProps.ableUpdate != this.props.ableUpdate ||
            (prevProps.imageList && this.props.imageList && prevProps.imageList.length < this.props.imageList.length)
        ) {
            delayed(300).subscribe(() => {
                this._scrollViewRef?.scrollTo(this._lastRef?.layout.x ?? 0, 0, true);
            });
        }
    }

    render(): JSX.Element {
        const { ableUpdate, onUpdate, imageSize, contentStyle, showAddIcon } = this.props;
        return (
            <ScrollView
                horizontal={true}
                contentContainerStyle={{
                    flex: 1,
                    marginHorizontal: 2,
                    ...flattenStyles(contentStyle),
                }}
                showsHorizontalScrollIndicator={false}
                ref={(ref) => (this._scrollViewRef = ref)}
            >
                <View style={{ height: 1 }} collapsable={false} ref={"firstChild"} />
                {this.props.imageList?.map((item, index, self) => {
                    if (
                        !StringUtils.checkIsImage(item.fileName) &&
                        !StringUtils.checkIsImage(item.useImageSignUrl ? item.signUrl : item.url)
                    ) {
                        return (
                            <View
                                style={{
                                    borderWidth: 1,
                                    borderColor: Colors.dividerLineColor,
                                    borderRadius: Sizes.dp4,
                                    width: imageSize,
                                    height: imageSize,
                                    margin: Sizes.dp4,
                                    backgroundColor: Colors.white,
                                    justifyContent: "center",
                                    alignItems: "center",
                                }}
                                key={index}
                            >
                                <AssetImageView
                                    name={
                                        DeviceUtils.isIOS()
                                            ? StringUtils.getFileNameType(item.fileName)
                                            : StringUtils.getFileNameType(item.fileName) + "_gray"
                                    }
                                    style={{
                                        width: Sizes.dp26,
                                        height: Sizes.dp30,
                                    }}
                                    key={index}
                                    onClick={() => {
                                        DeviceUtils.isIOS() && ABCNavigator.navigateToPage(<WebviewPage uri={item?.url ?? ""} />);
                                    }}
                                />
                            </View>
                        );
                    }
                    return (
                        <View key={index}>
                            <Image
                                src={encodeURI((item?.useImageSignUrl ? item.signUrl : item.url) ?? "")}
                                style={{
                                    ...flattenStyles(ABCStyles.borderOne),
                                    width: imageSize,
                                    height: imageSize,
                                    margin: Sizes.dp4,
                                    borderColor: Colors.dividerLineColor,
                                    borderRadius: Sizes.dp6,
                                }}
                                resizeMode={"cover"}
                                onClick={() => {
                                    const onlyImgList = self?.filter(
                                        (t) =>
                                            StringUtils.checkIsImage(t.fileName) ||
                                            StringUtils.checkIsImage(t.useImageSignUrl ? t.signUrl : t.url)
                                    );
                                    const _index = onlyImgList?.findIndex((it) => it.url == self[index].url);
                                    this._showImageGalleryViewPage(onlyImgList, _index, !!ableUpdate);
                                }}
                            />
                        </View>
                    );
                })}
                {showAddIcon && (
                    <AbcView
                        style={{
                            width: imageSize,
                            height: imageSize,
                            marginHorizontal: Sizes.dp4,
                            borderColor: Colors.P5,
                            borderWidth: Sizes.dp1,
                            borderRadius: Sizes.dp6,
                            justifyContent: "center",
                            alignItems: "center",
                            margin: Sizes.dp4,
                        }}
                        onClick={() => {
                            onUpdate?.();
                        }}
                    >
                        <IconFontView name={"Plus"} color={Colors.P3} size={Sizes.dp20} />
                    </AbcView>
                )}
                <View
                    onLayout={(ref) => {
                        //@ts-ignore
                        this._lastRef = ref;
                    }}
                    collapsable={false}
                    style={{ height: Sizes.dp1 }}
                />
            </ScrollView>
        );
    }
}
