/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/7/20
 */
import React from "react";
import { View } from "@hippy/react";
import { ABCNetworkPageContentStatus, BaseNetworkPage } from "./base-page";
import { AbcWebview } from "./abc-webview";
import { LogUtils } from "../common-base-module/log";
import { DeviceUtils } from "./utils/device-utils";
import { TreeDotView } from "./iconfont/iconfont-view";
import { Colors, Sizes } from "../theme";
import { AbcView } from "./views/abc-view";
import { ShareToOtherApplicationView } from "./dialog/share-to-other-application-view";
import { Version } from "./utils/version-utils";
import { AppInfo } from "../base-business/config/app-info";
import { wxLoginHelper } from "../login/wx-login-helper";
import { ShareUtils } from "../common-base-module/utils/share-utils";

interface WebviewPageProps {
    title?: string;
    uri: string;
    isShowShareIcon?: boolean;
    desc?: string;
}

export class WebviewPage extends BaseNetworkPage<WebviewPageProps> {
    constructor(props: WebviewPageProps) {
        super(props);
    }

    getAppBarTitle(): string {
        const { title } = this.props;
        return title ?? "";
    }
    static defaultProps = {
        isShowShareIcon: false,
    };

    async _jumpToOtherApplication(): Promise<void> {
        const shareConfig = {
            url: this.props.uri,
            title: this.props?.title,
            desc: this.props?.desc,
        };
        await ShareToOtherApplicationView.show({
            items: [ShareUtils.createDefaultWxShareItem(shareConfig)],
            defaultConfig: shareConfig,
        });
    }
    getRightAppBarIcons(): JSX.Element[] {
        const { isShowShareIcon } = this.props;
        if (!isShowShareIcon) return [];
        // 是否支持分享到微信
        const hasShareImgToWechat = new Version(AppInfo.appVersion).compareTo(new Version("2.3.0.0100")) < 0;
        // 是否安装微信
        const isWeChatInstalled = wxLoginHelper._weChatInstalled;
        if (hasShareImgToWechat || !isWeChatInstalled) return [];
        return [
            <AbcView key={"pdf_setting"} style={{ marginRight: Sizes.dp8 }} onClick={() => this._jumpToOtherApplication()}>
                <TreeDotView color={Colors.T1} />
            </AbcView>,
        ];
    }

    componentDidMount(): void {
        DeviceUtils.isOhos() ? "" : this.setContentStatus(ABCNetworkPageContentStatus.loading);
    }

    renderContent(): JSX.Element | undefined {
        const { uri } = this.props;
        LogUtils.d("ignore." + uri);
        return (
            <View style={{ flexGrow: 1 }}>
                <AbcWebview
                    source={{ uri: encodeURI(uri) }}
                    onLoadStart={(ignore) => {
                        LogUtils.d("ignore." + ignore.url);
                    }}
                    onLoadEnd={
                        (/*ignore*/) => {
                            this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                        }
                    }
                    style={{ flexGrow: 1 }}
                />
            </View>
        );
    }
}
