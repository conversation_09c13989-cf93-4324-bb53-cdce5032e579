import React from "react";
import { Dimensions, ScrollView, Style, StyleSheet, Text, View } from "@hippy/react";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { LogUtils } from "../../common-base-module/log";
import _ from "lodash";
import { UniqueKey } from "../keys";
import { AbcTextInput, KeyboardBuilder, KeyboardType, SimpleFinishPanel, TextFormatter } from "../views/abc-text-input";
import { AbcView } from "../views/abc-view";
import { DeviceUtils } from "../utils/device-utils";
import { NumberKeyboardBuilder } from "../views/keyboards/number-keyboard";
import { IconFontView } from "../index";
import FontWeight from "../../theme/font-weights";

interface CustomInputProps {
    borderType?: "all" | "bottom" | "none" | "boxBorder";

    type?: "input" | "sheet" | "phone-pad" | "input-text" | "select" | string; //input
    border?: boolean;
    unit?: string;
    unitList?: Array<string>;
    disable?: boolean;
    value?: string | number | undefined;
    placeholder?: string;
    title?: string;
    autoFocus?: boolean | null;
    customPanelBuilder?: JSX.Element;
    customKeyboardBuilder?: KeyboardBuilder;
    enableDefaultToolBar?: boolean;

    alwaysShowUtil?: boolean; // 强制显示单位
    alwaysHideUnit?: boolean; //在编辑框里不显示单位
    disablePlaceholderText?: string; //空值展示信息

    onFocus?(): void;

    onBlur?: (text: string) => void;

    formatter?: TextFormatter | TextFormatter[];

    style?: Style;
    textStyle?: Style;
    containerStyle?: Style;
    error?: boolean;
    warring?: boolean;

    onChange?: (value: string) => void;

    onChangeUnit?(unit: string): void;

    resizeMode?: boolean;

    onEndEditing?(text: string): void;

    placeholderColor?: Color; //提示文字颜色
    selectTextOnFocus?: boolean;
    disableColor?: Color; //禁用条件下，value及unit颜色
    sheetTypeTextAlign?: "left" | "center" | "right"; //在type为sheet下文字对齐方式
    notExistValue?: boolean; //不存在值，只是提示文字（用于无值时，提示文字的颜色判断）
    disableStyle?: Style | Style[]; //禁用的文字样式

    disableOnClick?: () => void; //禁用时的点击事件

    /**
     * 输入框前补充内容
     */
    startView?: () => JSX.Element;
    unitStyle?: Style; //单位unit的样式

    startUnitListView?: JSX.Element; // 单位列表前补充视图
    unitListItemStyle?: Style | Style[]; // 重构单位列表项目样式

    unitInPageLength?: number; // 页面上单位显示几位长度（不涉及键盘上单位，不从外面截取传入的原因是无法与键盘上的单位匹配选中）
}

//@ts-ignore
const inputBaseStyle = StyleSheet.create({
    container: {
        height: Sizes.dp28,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        overflow: "hidden",
    },
    border: {
        ...Sizes.paddingLTRB(1, 1),
        borderWidth: 1,
        borderColor: Colors.transparent,
        borderRadius: Sizes.dp14,
    },
    borderDefault: {
        borderColor: Colors.P1,
    },
    borderError: {
        borderColor: Colors.errorBorder,
    },
    borderWarring: {
        borderColor: Colors.Y2,
    },
    borderErrorSelect: {
        ...Sizes.paddingLTRB(0, 0),
        borderWidth: 2,
    },
    borderSelected: {
        ...Sizes.paddingLTRB(0, 0),
        borderWidth: 2,
        borderColor: Colors.mainColor,
    },
    disableContainer: {
        backgroundColor: Colors.D2,
        borderColor: Colors.D2,
    },
    disableContent: {
        backgroundColor: Colors.D2,
    },
    textInput: {
        ...TextStyles.t14NB,
        flex: 1,
        height: Sizes.dp28,
        // minWidth: 40,
        textAlign: "center",
        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : 0,
    },
});

//@ts-ignore
const bottomInputBorderStyle = StyleSheet.create({
    container: {
        height: Sizes.dp28,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        overflow: "hidden",
    },
    border: {
        borderBottomWidth: 1,
        borderColor: Colors.transparent,
    },
    borderErrorSelect: {
        borderBottomWidth: 1,
    },
    borderSelected: {
        borderBottomWidth: 1,
        borderColor: Colors.mainColor,
    },
    disableContainer: {
        backgroundColor: Colors.white,
        borderColor: Colors.transparent,
    },
    disableContent: {
        backgroundColor: Colors.white,
    },
    borderDefault: {
        borderColor: Colors.P1,
    },
    borderError: {
        borderColor: Colors.errorBorder,
        backgroundColor: Colors.errorBorderBg,
    },

    borderWarring: {
        borderColor: Colors.Y2,
    },
    textInput: {
        ...TextStyles.t14NB,
        flex: 1,
        height: Sizes.dp28,
        // minWidth: 40,
        textAlign: "center",
        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : 0,
    },
});

//@ts-ignore
const noneInputBorderStyle = StyleSheet.create({
    container: {
        height: Sizes.dp28,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        overflow: "hidden",
    },
    border: {
        borderBottomWidth: 1,
        borderColor: Colors.transparent,
    },
    borderErrorSelect: {
        borderBottomWidth: 1,
    },
    borderSelected: {
        borderBottomWidth: 1,
    },
    disableContainer: {
        backgroundColor: Colors.white,
        borderColor: Colors.transparent,
    },
    disableContent: {
        backgroundColor: Colors.white,
    },
    borderDefault: {
        borderColor: Colors.transparent,
    },
    borderError: {
        borderColor: Colors.errorBorder,
    },
    borderWarring: {
        borderColor: Colors.Y2,
    },
    textInput: {
        ...TextStyles.t14NB,
        flex: 1,
        height: Sizes.dp28,
        // minWidth: 40,
        textAlign: "center",
        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : 0,
    },
});

//@ts-ignore
const borderBoxBorderStyle = StyleSheet.create({
    container: {
        height: Sizes.dp28,
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        overflow: "hidden",
        backgroundColor: Colors.whiteSmoke,
        ...Sizes.paddingLTRB(Sizes.dp6, 1),
    },
    border: {
        borderWidth: 0.5,
        borderColor: Colors.transparent,
        borderRadius: Sizes.dp4,
    },
    borderDefault: {
        borderColor: Colors.transparent,
    },
    borderError: {
        borderColor: Colors.errorBorder,
        backgroundColor: Colors.errorBorderBg,
    },
    borderWarring: {
        borderColor: Colors.Y2,
    },
    borderErrorSelect: {
        borderWidth: 0.5,
    },
    borderSelected: {
        borderWidth: 0.5,
        borderColor: Colors.mainColor,
        backgroundColor: Colors.mainColor_08,
    },
    disableContainer: {
        backgroundColor: Colors.whiteSmoke,
        borderColor: Colors.D2,
    },
    disableContent: {
        backgroundColor: Colors.whiteSmoke,
    },
    textInput: {
        ...TextStyles.t14NB.copyWith({ lineHeight: Sizes.dp18 }),
        flex: 1,
        height: Sizes.dp28,
        // minWidth: 40,
        textAlign: "center",
        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : 0,
    },
});

export class CustomInput extends React.Component<CustomInputProps, any> {
    inputBaseStyle: any;

    public _textInput?: AbcTextInput | null;

    private keyBoardType: KeyboardType = "numeric";

    constructor(props: CustomInputProps) {
        super(props);

        switch (props.borderType) {
            case "bottom": {
                this.inputBaseStyle = bottomInputBorderStyle;
                break;
            }
            case "none": {
                this.inputBaseStyle = noneInputBorderStyle;
                break;
            }
            case "boxBorder": {
                this.inputBaseStyle = borderBoxBorderStyle;
                break;
            }
            case "all":
            default: {
                this.inputBaseStyle = inputBaseStyle;
            }
        }
        this.state = {
            selected: false,
            focus: false,
            showMenu: false,
            keyBoardHeight: 0,
            keyboardTopMarginTop: 0,
            showUnit: Boolean(props.value),
        };
        this._renderBorder();
    }

    static defaultProps = {
        enableDefaultToolBar: true,
        borderType: "all",
        type: "input",
        value: "",
        border: true,
        sheetTypeTextAlign: "center",
    };

    public focus(): void {
        this._textInput?.focus();
    }

    public setValue(value: string, options?: { shouldChange?: boolean; syncDefaultValue?: boolean }): void {
        this._textInput?.setValue(value, options);
    }

    get value(): string | undefined {
        return this._textInput?.value;
    }

    private _renderBorder(): void {
        const { border } = this.props;
        if (border) {
            this.inputBaseStyle.border = {
                ...this.inputBaseStyle.border,
                ...this.inputBaseStyle.borderDefault,
            };
        }
    }

    private _renderInput(): JSX.Element {
        const {
            value,
            disable,
            placeholder,
            style,
            textStyle,
            resizeMode,
            formatter,
            autoFocus,
            customPanelBuilder,
            customKeyboardBuilder,
            enableDefaultToolBar,
            placeholderColor,
            selectTextOnFocus,
        } = this.props;
        let _customKeyboardBuilder: KeyboardBuilder | undefined;
        if (this.keyBoardType == "numeric") {
            _customKeyboardBuilder = new NumberKeyboardBuilder({
                selectUnit: this.props.unit,
                unitList: this.props.unitList,
                onChangeUnit: this.props.onChangeUnit,
            });
        }
        return (
            <AbcTextInput
                accessibilityLabel={`abc-input-${placeholder}`}
                style={{
                    ...this.inputBaseStyle.textInput,
                    textAlign: style?.textAlign ?? "center",
                    ...flattenStyles(textStyle),
                }}
                autoFocus={autoFocus ?? false}
                defaultValue={_.isNumber(value) ? value.toString() : value}
                resizeMode={resizeMode}
                editable={!disable}
                ref={(ref) => {
                    this._textInput = ref;
                }}
                syncTextOnBlur={true}
                enableDefaultToolBar={enableDefaultToolBar}
                returnKeyType={"done"}
                keyboardType={this.keyBoardType ?? "numeric"}
                maxLength={this.keyBoardType == "phone-pad" ? 11 : undefined}
                multiline={this.props.type == "sheet"} //当前是选择面板时，需要设置为多行，解决：ios16默认拉起了工具条
                numberOfLines={1}
                placeholder={placeholder}
                placeholderTextColor={placeholderColor ?? Colors.T3}
                textShareMenuVisible={false}
                onKeyboardWillShow={this.handleKeyBoardShow.bind(this)}
                keyboardDistanceFromTextField={Sizes.dp80}
                onFocus={() => {
                    if (this.props.disable) return;
                    this.setState({
                        selected: true,
                        focus: true,
                        showMenu: true,
                    });
                    if (this.props.type == "input") {
                        if (!!value && value == "-") {
                            this._textInput?.setValue("");
                        }
                    }
                    this.props.onFocus?.();
                }}
                onBlur={() => {
                    this.setState({
                        selected: false,
                        focus: false,
                        showMenu: false,
                    });
                    this.props.onBlur?.(this._textInput?.value ?? "");
                }}
                formatter={formatter}
                onChangeText={this._handleInputChange.bind(this)}
                customPanelBuilder={(textInput) => {
                    if (customPanelBuilder) return customPanelBuilder;
                    if (this.keyBoardType == "numeric") {
                        return (
                            <_UnitPanel
                                selectItem={this.props.unit}
                                unitList={this.props.unitList ?? []}
                                textInput={textInput}
                                onChange={this._handleUnitChange.bind(this)}
                                startUnitListView={this.props.startUnitListView}
                                unitListItemStyle={this.props.unitListItemStyle}
                            />
                        );
                    } else {
                        return (
                            <_DefaultPanel
                                key={UniqueKey()}
                                selectItem={this.props.unit}
                                unitList={this.props.unitList ?? []}
                                textInput={textInput}
                                onChange={this._handleUnitChange.bind(this)}
                            />
                        );
                    }
                }}
                customKeyboardBuilder={customKeyboardBuilder ?? _customKeyboardBuilder}
                onEndEditing={(text) => this.props.onEndEditing?.(text)}
                selectTextOnFocus={selectTextOnFocus}
            />
        );
    }

    private _renderUnit(): JSX.Element | undefined {
        const {
            unit,
            disable,
            alwaysShowUtil,
            alwaysHideUnit,
            textStyle,
            warring,
            disableColor,
            unitStyle,
            unitInPageLength = 0,
        } = this.props;
        const _focused = this._textInput?.focused;
        const value = this._textInput ? this._textInput?.value : this.props.value;
        if (alwaysHideUnit) return;
        if ((alwaysShowUtil && unit != "适量") || (unit && (_focused || (value != undefined && value !== "")) && unit != "适量")) {
            // let {unit, disable} = this.props;
            // const value = this._textInput ? this._textInput?.value : this.props.value;
            // if (unit && value != undefined && value !== '' && unit != '适量') {
            return (
                <Text
                    style={[
                        flattenStyles(textStyle ?? TextStyles.t16NT1),
                        {
                            alignSelf: "center",
                            marginRight: Sizes.dp3,
                            color: disable ? (disableColor ? disableColor : Colors.t3) : warring ? Colors.Y2 : Colors.black,
                            maxWidth: Sizes.dp44,
                        },
                        flattenStyles(unitStyle),
                    ]}
                    onClick={() => {
                        if (this._textInput) this._textInput.focus();
                    }}
                    numberOfLines={1}
                >
                    {(unitInPageLength && !!unit ? unit?.slice(0, unitInPageLength) : unit) ?? ""}
                </Text>
            );
        }
    }

    private _renderContent(): JSX.Element {
        const {
            type,
            value,
            disable,
            placeholder,
            disablePlaceholderText,
            style,
            disableColor,
            sheetTypeTextAlign,
            notExistValue,
            textStyle,
            disableStyle,
            disableOnClick,
        } = this.props;
        if (disable)
            return (
                <AbcView style={{ alignSelf: "center", flex: 1 }} onClick={disableOnClick}>
                    <Text
                        ellipsizeMode={"tail"}
                        numberOfLines={1}
                        style={[
                            disableColor
                                ? TextStyles.t12NT2.copyWith({ color: disableColor })
                                : TextStyles.t12NT2.copyWith({ color: Colors.t4 }),
                            { flex: 1, textAlign: style?.textAlign ?? "center" },
                            flattenStyles(disableStyle),
                        ]}
                    >
                        {Boolean(value) ? value!.toString() : disablePlaceholderText ?? "--"}
                    </Text>
                </AbcView>
            );
        switch (type) {
            case "sheet": {
                return (
                    <AbcView
                        style={[
                            { flex: 1, alignSelf: "center", justifyContent: "center" },
                            DeviceUtils.isAndroid() ? { position: "relative" } : {},
                        ]}
                        onClick={() => {
                            this._textInput?.focus();
                        }}
                    >
                        {/*安卓上height=0无法拉起*/}
                        <View
                            style={[
                                { zIndex: -100, bottom: -10, left: -1000 },
                                { position: "absolute", height: DeviceUtils.isIOS() ? 0 : 1 },
                            ]}
                            collapsable={false}
                        >
                            {this._renderInput()}
                        </View>
                        <Text
                            numberOfLines={1}
                            style={[
                                flattenStyles(textStyle ?? TextStyles.t14NT1),
                                notExistValue ? { color: Colors.T4 } : {},
                                { textAlign: sheetTypeTextAlign },
                            ]}
                        >
                            {value || (placeholder as string)}
                        </Text>
                    </AbcView>
                );
            }
            case "select": {
                return (
                    <AbcView
                        style={[
                            { flex: 1, alignSelf: "center", justifyContent: "center" },
                            DeviceUtils.isAndroid() ? { position: "relative" } : {},
                        ]}
                        onClick={() => {
                            this._textInput?.focus();
                        }}
                    >
                        {/*安卓上height=0无法拉起*/}
                        <View
                            style={[
                                DeviceUtils.isAndroid() ? { zIndex: -100, bottom: -10, left: -1000 } : {},
                                { position: "absolute", height: DeviceUtils.isIOS() ? 0 : 1 },
                            ]}
                            collapsable={false}
                        >
                            {this._renderInput()}
                        </View>
                        <View
                            style={[
                                ABCStyles.centerChild,
                                { flexDirection: "row", justifyContent: sheetTypeTextAlign != "center" ? undefined : "center" },
                            ]}
                        >
                            <Text
                                numberOfLines={1}
                                style={[
                                    flattenStyles(textStyle ?? TextStyles.t14NT1),
                                    notExistValue ? { color: Colors.T4 } : {},
                                    { textAlign: sheetTypeTextAlign },
                                    this.state.selected
                                        ? textStyle
                                            ? { color: Colors.mainColor, fontWeight: FontWeight.medium }
                                            : TextStyles.t14MM
                                        : {},
                                ]}
                            >
                                {value || (placeholder as string)}
                            </Text>
                            <View style={[ABCStyles.rowAlignCenter, { marginLeft: Sizes.dp4 }]}>
                                <IconFontView
                                    name="arrow_down"
                                    size={Sizes.dp12}
                                    color={this.state.selected ? Colors.mainColor : Colors.T6}
                                />
                            </View>
                        </View>
                    </AbcView>
                );
            }
            case "phone-pad": {
                this.keyBoardType = "phone-pad";
                return this._renderInput();
            }
            // case "default": {
            //     this.keyBoardType = "default";
            //     return this._renderInput()
            // }
            case "input-text": {
                this.keyBoardType = "default";
                return this._renderInput();
            }
            case "input":
            default: {
                return this._renderInput();
            }
        }
    }

    render(): JSX.Element {
        // const { height = Sizes.dp28 } = this.props.style ?? {};
        const { startView } = this.props;
        return (
            <AbcView
                style={[
                    this.props?.style ? { ...this.props?.style } : { width: Sizes.dp48 },
                    // DeviceUtils.isAndroid() && !!height ? { height: (height as number) + 1 } : {}, // 安卓底部线条会显示不完全
                ]}
                onClick={() => {
                    this._textInput?.focus();
                }}
            >
                <View
                    style={[
                        this.inputBaseStyle.container,
                        this.inputBaseStyle.border,
                        this.state.selected && (this.props.error ? this.inputBaseStyle.borderSelected : this.inputBaseStyle.borderSelected),
                        this.props.disable ? this.inputBaseStyle.disableContainer : {},
                        this.props.error ? this.inputBaseStyle.borderError : {},
                        this.props.warring ? this.inputBaseStyle.borderWarring : {},
                        this.props.containerStyle,
                        this.props.type == "select" ? { borderWidth: 0 } : {},
                    ]}
                >
                    {!!startView && startView()}
                    {this._renderContent()}
                    {this._renderUnit()}
                </View>
            </AbcView>
        );
    }

    private _showSheet(): void {
        // @ts-ignore
        this._handleInputChange();
    }

    private handleKeyBoardShow(options: { keyboardHeight: number }) {
        LogUtils.d(options.keyboardHeight.toString());
        this.setState({
            keyBoardHeight: options.keyboardHeight,
            showMenu: true,
            focus: true,
        });
    }

    private _handleInputChange(value?: string): void {
        this.setState({
            showUnit: Boolean(value),
        });
        this.props.onChange?.(value ?? "");
    }

    private _handleUnitChange(value?: string): void {
        // this._textInput.blur();
        if (value) {
            this.props.onChangeUnit!(value);
            if (value == "适量") this.setValue("适量");
            else if (this.value == "适量") this.setValue("");
        }
    }
}

const width = Dimensions.get("screen").width;

function splitUnitGroup(array: any[], subGroupLength: number) {
    let index = 0;
    const newArray: any[] = [];
    while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)));
    }
    return newArray;
}

interface _UnitPanelProps {
    selectItem?: any;
    textInput: AbcTextInput;
    unitList: any[];
    startUnitListView?: any; //单位列表前补充视图
    unitListItemStyle?: Style | Style[]; // 重构单位列表项目样式
    onChange?(value: string): void;
}

class _UnitPanel extends React.Component<_UnitPanelProps> {
    private readonly _itemWidth: number | undefined;
    protected _renderList: Array<Array<any>> = [[]];

    constructor(props: _UnitPanelProps) {
        super(props);
        const { unitList } = this.props;
        if (Array.isArray(unitList)) {
            if (unitList.length > 4) {
                this._itemWidth = (width - Sizes.listHorizontalMargin * 2 - 3 * 8) / 4;
                this._renderList = splitUnitGroup(unitList, 4);
            } else if (unitList.length > 0) {
                this._itemWidth = (width - Sizes.listHorizontalMargin * 2 - (unitList.length - 1) * 8) / unitList.length;
                this._renderList[0] = unitList;
            } else {
                this._renderList = [];
            }
        }
    }

    _handleChange(item: string) {
        this.props.onChange?.(item);
    }

    _renderItemMargin() {
        return <View style={{ width: Sizes.dp4 }} />;
    }

    _renderItem(item: string) {
        const { unitListItemStyle } = this.props;
        return (
            <View
                style={[
                    {
                        width: this._itemWidth,
                        height: Sizes.dp36,
                        borderRadius: Sizes.dp3,
                        alignItems: "center",
                        justifyContent: "center",
                    },
                    this.props.selectItem == item || item == this.props.selectItem?.replace("/次", "")
                        ? {
                              backgroundColor: Colors.white,
                          }
                        : {},
                    flattenStyles(unitListItemStyle),
                ]}
                onClick={() => {
                    this._handleChange(item);
                }}
            >
                <Text style={TextStyles.t13NT9.copyWith({ color: Colors.T1 })}>{item}</Text>
            </View>
        );
    }

    render() {
        const { unitList, startUnitListView } = this.props;

        // 单位数量小于指定数目时，会使用键盘自带的单位面板
        if (unitList.length < 4) return <View />;

        return (
            <View>
                {/*<NumberFastInput textInput={this.props.textInput}/>*/}
                {this._renderList.length ? (
                    <ScrollView
                        horizontal={true}
                        showsHorizontalScrollIndicator={false}
                        style={{
                            paddingHorizontal: Sizes.dp5,
                            paddingVertical: Sizes.dp6,
                            backgroundColor: Colors.panelBg,
                            ...ABCStyles.bottomLine,
                            borderColor: Colors.P3,
                        }}
                        onMomentumScrollEnd={() => {
                            return;
                        }}
                    >
                        {unitList.map((item, index, self) => {
                            return (
                                <View key={index} style={{ flexDirection: "row" }}>
                                    {!!startUnitListView && index == 0 && startUnitListView}
                                    {this._renderItem(item)}
                                    {index < self.length - 1 && this._renderItemMargin()}
                                </View>
                            );
                        })}
                    </ScrollView>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

class _DefaultPanel extends _UnitPanel {
    render(): any {
        const { unitList } = this.props;
        return (
            <View>
                {DeviceUtils.isIOS() && <SimpleFinishPanel />}
                {this._renderList.length ? (
                    <ScrollView
                        horizontal={true}
                        style={{
                            paddingHorizontal: Sizes.listHorizontalMargin,
                            paddingTop: 4,
                            paddingBottom: 12,
                            backgroundColor: Colors.white,
                        }}
                    >
                        {unitList.map((item, index, self) => {
                            return (
                                <View key={index} style={{ flexDirection: "row" }}>
                                    {this._renderItem(item)}
                                    {index < self.length - 1 && this._renderItemMargin()}
                                </View>
                            );
                        })}
                    </ScrollView>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}
