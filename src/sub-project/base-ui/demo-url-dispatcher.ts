export default class DemoURLProtocols {
    static ABC_SCHEME = "abcyun";

    // assetImage组件
    static AbcAssetImage = `${DemoURLProtocols.ABC_SCHEME}://demo/abcAssetImage`;
    // assetImage组件
    static AbcContentEmpty = `${DemoURLProtocols.ABC_SCHEME}://demo/abcContentEmpty`;

    // AbcQrCode
    static AbcQrCode = `${DemoURLProtocols.ABC_SCHEME}://demo/abcQrCode`;

    // AbcIconfont
    static AbcIconfont = `${DemoURLProtocols.ABC_SCHEME}://demo/abcIconfont`;

    // AbcFlex
    static AbcFlex = `${DemoURLProtocols.ABC_SCHEME}://demo/abcFlex`;

    // AbcGrid
    static AbcGrid = `${DemoURLProtocols.ABC_SCHEME}://demo/abcGrid`;

    // AbcDivider
    static AbcDivider = `${DemoURLProtocols.ABC_SCHEME}://demo/abcDivider`;

    // AbcRadio
    static AbcRadio = `${DemoURLProtocols.ABC_SCHEME}://demo/abcRadio`;

    // AbcCollapse
    static AbcCollapse = `${DemoURLProtocols.ABC_SCHEME}://demo/abcCollapse`;

    // AbcBadge
    static AbcBadge = `${DemoURLProtocols.ABC_SCHEME}://demo/abcBadge`;

    // AbcStepper
    static AbcStepper = `${DemoURLProtocols.ABC_SCHEME}://demo/abcStepper`;
}
