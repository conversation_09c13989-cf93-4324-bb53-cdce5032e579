import React from "react";
import { UIUtils } from "../utils";
import { Filters } from "./search-bar-bean";
import { showRightSheet } from "../dialog/bottom_sheet";
import { AbcTextInput, KeyboardType } from "../views/abc-text-input";
import { Style, StyleSheet, Text, View } from "@hippy/react";
import { ABCStyles, Colors, FontSizes, Sizes, TextStyles } from "../../theme";
import { IconFontView, SizedBox } from "../index";
import { AbcView } from "../views/abc-view";
import { FilterView } from "./search-bar";
import { AbcText } from "../views/abc-text";
import { ABCNavigator } from "../views/abc-navigator";
import { CommonScanPage } from "./common-scan-page";
import { of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { BaseComponent } from "../base-component";
import { DeviceUtils } from "../utils/device-utils";

// @ts-ignore
const SearchInputStyle = StyleSheet.create({
    container: {
        flex: 1,
        ...Sizes.paddingLTRB(Sizes.dp12, 0),
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: Colors.P4,
    },
    contentInput: {
        flex: 1,
        height: Sizes.dp36,
        fontSize: FontSizes.size14,
        backgroundColor: Colors.P4,
        underlineColorAndroid: Colors.P4,
    },
});

interface NewSearchInputProps {
    editable?: boolean;
    autoFocus?: boolean;
    value?: string;
    disable?: Boolean;
    borderRadius?: number;
    border?: number;
    placeholder?: string;
    keyboardType?: KeyboardType;
    inputStyle?: Style;
    enableDefaultToolBar?: boolean;
    onChange?: (value: string) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    maxLength?: number;
    onTriggerScan?(code?: string): void;
    isShowScanIcon?: boolean;
}

interface NewSearchInputState {
    value: any;
    clearVisible: boolean;
}

export class NewSearchInput extends React.Component<NewSearchInputProps, NewSearchInputState> {
    _textInput?: AbcTextInput | null;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        disable: false,
        border: 0,
        borderRadius: Sizes.dp6,
        editable: true,
        enableDefaultToolBar: true,
    };

    constructor(props: NewSearchInputProps) {
        super(props);
        this.state = {
            value: props.value,
            clearVisible: false,
        };
    }

    public setText(text: string): void {
        this._textInput?.setValue(text, { shouldChange: true });
    }

    public setTextNotUpdate(text: string): void {
        if (text) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: text });
        this._textInput?.setValue(text, { shouldChange: false });
    }

    public handleTextChange(value: string): void {
        if (value) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: value });
        this.props.onChange?.(value);
    }

    private _clearInputValue(): void {
        this.setState({ value: "" });
        if (this._textInput) {
            this.handleTextChange("");
            this._textInput.clear();
        }
    }

    _renderRightIcon(): JSX.Element | boolean {
        return this.state.clearVisible && <IconFontView name="cross_small" size={Sizes.dp18} onClick={this._clearInputValue.bind(this)} />;
    }

    _renderLeftIcon(): JSX.Element {
        return <IconFontView name="Search" size={Sizes.dp14} color={Colors.T6} style={{ paddingRight: Sizes.dp6 }} />;
    }

    _renderInput(): JSX.Element {
        const { value } = this.state;
        const { placeholder, autoFocus, keyboardType, inputStyle, editable, enableDefaultToolBar, maxLength } = this.props;
        return (
            <AbcTextInput
                maxLength={maxLength}
                editable={editable}
                autoFocus={autoFocus ?? false}
                multiline={false}
                placeholder={placeholder}
                placeholderTextColor={Colors.T6}
                defaultValue={value}
                keyboardType={keyboardType}
                enableDefaultToolBar={enableDefaultToolBar}
                style={{ ...SearchInputStyle.contentInput, ...inputStyle }}
                ref={(ref) => {
                    this._textInput = ref!;
                }}
                onFocus={() => {
                    this.props.onFocus?.();
                }}
                onBlur={() => {
                    this.props.onBlur?.();
                }}
                onChangeText={this.handleTextChange.bind(this)}
            />
        );
    }

    async _renderScan(): Promise<void> {
        const { onTriggerScan } = this.props;
        let loopStatus = true;
        do {
            await CommonScanPage.scan({
                callback(arg1?: { action?: "user_cancel" | "click_tips"; code?: string }) {
                    if (arg1?.action == "user_cancel") {
                        loopStatus = false;
                    } else if (!!arg1?.code) {
                        loopStatus = false;
                        onTriggerScan?.(arg1?.code);
                    }
                },
            });
        } while (loopStatus);
    }

    render(): JSX.Element {
        const { borderRadius, border, editable, inputStyle, isShowScanIcon } = this.props;
        return (
            <View
                style={[
                    SearchInputStyle.container,
                    {
                        borderRadius: borderRadius,
                        borderWidth: border,
                        backgroundColor: inputStyle?.backgroundColor,
                    },
                ]}
            >
                {this._renderLeftIcon()}
                <View style={{ flexGrow: 1 }}>
                    {this._renderInput()}
                    {!editable && (
                        <AbcView
                            style={{
                                position: "absolute",
                                left: 0,
                                right: 0,
                                top: 0,
                                bottom: 0,
                                backgroundColor: Colors.transparent,
                            }}
                            onClick={() => {
                                this.handleTextChange(this.state.value);
                            }}
                        />
                    )}
                </View>
                {!!isShowScanIcon && (
                    <IconFontView
                        style={{ marginBottom: -Sizes.dp2 }}
                        name={"scan"}
                        color={Colors.T1}
                        size={Sizes.dp18}
                        onClick={() => {
                            this._renderScan();
                        }}
                    />
                )}
                {/*<View style={{ borderWidth: 1, borderColor: Colors.red }}> {this._renderRightIcon()}</View>*/}
            </View>
        );
    }
}

interface NewSearchInputProps {
    showClear?: boolean;
    filters?: Filters;
    editable?: boolean;

    onFilterChange?(arg1: Filters): void;
    isShowFilterIcon?: boolean; //是否显示时间筛选按钮
    qrScanGenerator?: Function; //扫码查询的接口
    qrScanRsp?: (rsp?: any) => void;
    isShowScanIcon?: boolean; //是否显示扫码按钮
}

interface AbcSearchBarWithFilterStates {
    activeFilterView: boolean;
}

export class AbcSearchBarWithFilter extends BaseComponent<NewSearchInputProps, AbcSearchBarWithFilterStates> {
    protected _searchView?: NewSearchInput | null;
    private _qrScanSearchTrigger: Subject<string> = new Subject<string>();
    constructor(props: NewSearchInputProps) {
        super(props);
        this.state = {
            activeFilterView: false,
        };
    }

    protected _hasFilter = false;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        editable: true,
        border: 0,
        borderRadius: Sizes.dp4,
        isShowFilterIcon: true,
    };
    viewHeight: any;

    componentDidMount(): void {
        this._qrScanSearchTrigger
            .pipe(
                switchMap((code) => {
                    if (!code) return of(null);
                    return this.props.qrScanGenerator?.({ keyword: code }).toObservable();
                })
            )
            .subscribe((rsp) => {
                this.props.qrScanRsp?.(rsp);
            })
            .addToDisposableBag(this);
    }

    public render(): JSX.Element {
        const filterNames = this.props.filters?.getEnableTitleNames().join(" ");
        let showClearIcon = false;
        this.props.filters?.getCurrentItems().forEach((item) => {
            if (item && !item.alwaysShow) {
                showClearIcon = true;
            }
        });
        const { isShowFilterIcon, isShowScanIcon } = this.props;
        return (
            <View
                style={[Boolean(filterNames) ? {} : ABCStyles.bottomLine, { backgroundColor: Colors.panelBg }]}
                collapsable={false}
                ref={(ref) => (this._iconRef = ref)}
                onLayout={(layoutInfo) => {
                    //@ts-ignore
                    this.viewHeight = layoutInfo.layout.height;
                }}
            >
                <View
                    style={{
                        flexDirection: "row",
                        marginVertical: Sizes.dp10,
                    }}
                >
                    <SizedBox width={Sizes.dp16} />
                    <NewSearchInput
                        inputStyle={{}}
                        editable={this.props.editable}
                        ref={(ref) => {
                            this._searchView = ref;
                            this._searchInput = ref?._textInput;
                        }}
                        onChange={() => {
                            return;
                        }}
                        isShowScanIcon={isShowScanIcon}
                        onTriggerScan={(code) => {
                            this._qrScanSearchTrigger.next(code);
                        }}
                        {...this.props}
                    />
                    <SizedBox width={Sizes.dp16} />
                    {isShowFilterIcon && (
                        <AbcView
                            style={{
                                alignSelf: "center",
                            }}
                            onClick={this._handleFilterClick.bind(this)}
                        >
                            <IconFontView
                                style={{
                                    ...Sizes.paddingLTRB(0, Sizes.dp6, Sizes.dp17, Sizes.dp6),
                                }}
                                name={"filter"}
                                size={Sizes.dp16}
                                color={this.state.activeFilterView ? Colors.mainColor : Colors.black}
                            />
                        </AbcView>
                    )}
                </View>

                {Boolean(filterNames) && (
                    <View style={{ backgroundColor: Colors.white }}>
                        <View
                            style={[
                                ABCStyles.rowAlignCenter,
                                Sizes.paddingLTRB(Sizes.dp16, 0, Sizes.dp8, 0),
                                {
                                    height: Sizes.dp44,
                                    backgroundColor: Colors.theme2Mask8,
                                },
                            ]}
                        >
                            <Text style={[TextStyles.t14MM, { lineHeight: Sizes.dp20 }]}>{`${filterNames}`}</Text>
                            <AbcView
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    ABCStyles.absoluteFill,
                                    {
                                        left: undefined,
                                        right: Sizes.dp8,
                                        paddingHorizontal: Sizes.dp8,
                                    },
                                ]}
                                onClick={() => {
                                    this.props.filters?.fillSelectAttrs(this.props.filters?.getDefaultFilter());
                                    this._hasFilter = false;
                                    this.forceUpdate();
                                    this.props.onFilterChange?.(this.props.filters?.getDefaultFilter() ?? new Filters());
                                }}
                            >
                                {showClearIcon && <IconFontView name={"close1"} size={Sizes.dp18} color={Colors.mainColor} />}
                            </AbcView>
                        </View>
                    </View>
                )}
            </View>
        );
    }

    _searchInput?: AbcTextInput | null;

    _iconRef: View | null = null;

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<NewSearchInputProps>): void {
        this._searchView?.setTextNotUpdate(nextProps.value ?? "");
    }

    protected async _handleFilterClick(): Promise<void> {
        const layout = await UIUtils.measureInWindow(this._iconRef!);
        this.setState({ activeFilterView: true });
        //滑动列表到顶部，android上会有露底问题，需要再减去一定高度
        let outcroppingHeight = 0;
        if (DeviceUtils.isAndroid()) {
            outcroppingHeight = Sizes.dp14;
        }
        const select: Filters = await showRightSheet(
            <FilterView
                showClear={this.props.showClear ?? false}
                filters={this.props.filters}
                height={layout.y + this.viewHeight - UIUtils.safeStatusHeight() - outcroppingHeight}
                width={UIUtils.getScreenWidth()}
                borderRadiusStyle={{ borderBottomLeftRadius: Sizes.dp6, borderBottomRightRadius: Sizes.dp6 }}
            />
        );

        this.setState({ activeFilterView: false });
        if (!select) return;
        this.props.filters?.fillSelectAttrs(select);
        //选中项并且选中项中含有非默认项
        this._hasFilter = !!select.filters.length && !!select.filters[0].filters?.find((it) => !it.isDefault);
        this.forceUpdate();
        this.props.onFilterChange?.(select);
    }
}

/**
 * 含取消按钮的搜索Bar
 * 按医生名称搜索使用
 * 处方模板搜索使用
 */
export class SearchBarWithButton extends React.Component<NewSearchInputProps, any> {
    protected _searchView?: NewSearchInput | null;
    constructor(props: NewSearchInputProps) {
        super(props);
    }

    protected _hasFilter = false;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        editable: true,
        border: 0,
        borderRadius: 4,
    };
    viewHeight: any;

    protected async _handleButtonClick(): Promise<void> {
        // const layout = await UIUtils.measureInWindow(this._iconRef!);
        // const select: Filters = await showRightSheet(
        //     <FilterView
        //         showClear={this.props.showClear ?? false}
        //         filters={this.props.filters}
        //         height={layout.y + this.viewHeight - UIUtils.safeStatusHeight()}
        //     />
        // );
        //
        // if (!select) return;
        // this.props.filters?.fillSelectAttrs(select);
        // this._hasFilter = !!select.filters.length;
        // this.forceUpdate();
        // this.props.onFilterChange?.(select);
    }

    _searchInput?: AbcTextInput | null;

    _iconRef: View | null = null;

    public render(): JSX.Element {
        return (
            <View
                style={{ backgroundColor: Colors.white }}
                collapsable={false}
                ref={(ref) => (this._iconRef = ref)}
                onLayout={(layoutInfo) => {
                    //@ts-ignore
                    this.viewHeight = layoutInfo.layout.height;
                }}
            >
                <View
                    style={{
                        flexDirection: "row",
                        marginVertical: Sizes.dp8,
                    }}
                >
                    <SizedBox width={Sizes.dp16} />
                    <NewSearchInput
                        inputStyle={{ backgroundColor: Colors.bg1 }}
                        editable={this.props.editable}
                        ref={(ref) => {
                            this._searchView = ref;
                            this._searchInput = ref?._textInput;
                        }}
                        onChange={() => {
                            return;
                        }}
                        {...this.props}
                    />
                    <SizedBox width={Sizes.dp12} />
                    <AbcView
                        style={{
                            alignSelf: "center",
                        }}
                        onClick={this._handleButtonClick.bind(this)}
                    >
                        <AbcText
                            numberOfLines={1}
                            style={[TextStyles.t16NT6.copyWith({ color: Colors.t2 }), { marginRight: Sizes.dp16 }]}
                            onClick={() => ABCNavigator.pop()}
                        >
                            取消
                        </AbcText>
                    </AbcView>
                </View>
            </View>
        );
    }
}
