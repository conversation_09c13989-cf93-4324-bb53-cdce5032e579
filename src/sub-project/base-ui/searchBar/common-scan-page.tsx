import { BaseComponent } from "../base-component";
import { Permission, PermissionStatus, PermissionType } from "../../common-base-module/permission/permission";
import { Toast } from "../dialog/toast";
import { BarcodeScanner } from "../camera/barcode-scanner";

interface CommonScanPageProps {
    callback?(arg1?: { action?: "user_cancel" | "click_tips"; code?: string }): void;
}
export class CommonScanPage extends BaseComponent {
    static async scan(options?: CommonScanPageProps): Promise<void> {
        const hasPermissions = await Permission.checkPermission(PermissionType.camera, true);
        if (hasPermissions == PermissionStatus.denied || hasPermissions == PermissionStatus.permanentlyDenied) {
            await Toast.show("无相机使用权限", { warning: true });
            options?.callback?.({ action: "user_cancel" });
            return;
        }
        const result = await BarcodeScanner.scan({ title: "扫码", selectTips: "" })
            .then((barcode) => {
                return barcode;
            })
            .catchIgnore();

        if (result) {
            options?.callback?.({ code: result });
        } else {
            options?.callback?.({ action: "user_cancel" });
        }
    }
}
