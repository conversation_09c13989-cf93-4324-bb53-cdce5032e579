import React from "react";
import { Animation, Dimensions, ScrollView, Style, StyleSheet, Text, View } from "@hippy/react";
import { DividerLine, IconFontView, SafeAreaView, SizedBox, ToolBar, ToolBarButtonStyle1, <PERSON>lBarButtonStyle2, Unique<PERSON>ey } from "../index";
import { ABCStyles, Color, Colors, flattenStyles, FontSizes, Sizes, TextStyles } from "../../theme";
import { FilterGroup, FilterItem, Filters, TimeFilterItem } from "./search-bar-bean";
import { showRightSheet } from "../dialog/bottom_sheet";
import { ABCUtils } from "../utils/utils";
import { ABCNavigator } from "../views/abc-navigator";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { AbcTextInput, KeyboardType, ToggleSystemKeyboardPanel } from "../views/abc-text-input";
import { BaseComponent } from "../base-component";
import { AbcView } from "../views/abc-view";
import { _RangeTimePicker, TimePicker } from "../picker/time-picker";
import { TimeUtils } from "../../common-base-module/utils";
import _ from "lodash";
import { UIUtils } from "../utils";
import { AbcButton } from "../views/abc-button";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { AlphabetKeyboardBuilder } from "../views/keyboards/alphabet-keyboard";
import abcOverlay from "../views/abc-overlay";
import { AssetImageView } from "../views/asset-image-view";
import { AbcCalendar } from "../abc-app-library/calendar/calendar-static";
import { Toast } from "../dialog/toast";
import { Range } from "../utils/value-holder";
import { AbcTag } from "../tag/abc-tag";
import { pxToDp } from "../utils/ui-utils";
import { AgeWheelHelper } from "../picker/age-picker";
import Utils from "../../../utils";
import { CommonScanPage } from "./common-scan-page";
import { of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { reportPointApi } from "../../views/statistical-points";

export enum CommonFilterId {
    all = 100000,
    //时间相关 1-
    timeToday = CommonFilterId.all + 1,
    timeYesterday = CommonFilterId.all + 2,
    timeBeforeYesterday = CommonFilterId.all + 3,
    timeThisWeek = CommonFilterId.all + 4,
    timeThisMonth = CommonFilterId.all + 5,
    timeThisYear = CommonFilterId.all + 6,
    timeRange = CommonFilterId.all + 9,
    time = CommonFilterId.all + 10,
    recentThreeDays = CommonFilterId.all + 11,
    timeTomorrow = CommonFilterId.all + 12,
    timeAfterTomorrow = CommonFilterId.all + 13,
    timeWithCalendar = CommonFilterId.all + 14,

    //status相关 100-
    allStatus = CommonFilterId.all + 100,

    //others 10000-
    others = CommonFilterId.all + 10000,

    asyncFilter = 200000,
    otherTimeFilter = 900000,
}

const __searchInput = "searchInput";

export enum FilterGroupId {
    time = 100, //时间模块

    statEmployee = 200, //统计-人员筛选
    otherTime = 300, //其他时间筛选
    billingDepartment = 400, //开单科室
}

export enum departmentFilterId {
    billingDepartmentId = 10000, // 开单科室
}

export const departmentNotSpecifiedItem = Object.freeze({
    id: "1",
    name: "未指定科室",
});

// @ts-ignore
const SearchInputStyle = StyleSheet.create({
    container: {
        flex: 1,
        ...Sizes.paddingLTRB(Sizes.dp4, 0),
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: Colors.bg1,
    },
    contentInput: {
        flex: 1,
        height: Sizes.dp36,
        fontSize: FontSizes.size16,
        ...Sizes.marginLTRB(Sizes.dp4, 0),
        backgroundColor: Colors.bg1,
        underlineColorAndroid: Colors.bg1,
    },
});

interface SearchInputProps {
    editable?: boolean;
    autoFocus?: boolean;
    value?: string;
    disable?: Boolean;
    borderRadius?: number;
    border?: number;
    placeholder?: string;
    placeholderTextColor?: string;
    keyboardType?: KeyboardType;
    style?: Style | Style[];
    inputStyle?: Style;
    enableDefaultToolBar?: boolean;
    onChange?: (value: string) => void;
    onFocus?: () => void;
    onBlur?: () => void;
    maxLength?: number;
    placeholderColor?: Color; //提示文字颜色
    searchContainerStyle?: Style; //整个搜索框样式布局
    leftIconColor?: Color; //左边搜索图标颜色
    selectTextOnFocus?: boolean;

    canUseCustomKeyboard?: boolean;
    rightIconStyle?: Style; //右侧图标的样式
    hasKanbanMode?: boolean; //是否为看板模式（true:看板，false:列表）
    onSwitchMode?: (value: boolean) => void; //切换模式（看板/列表）
    onTriggerScan?: (code?: string) => void; //扫码
    isNeedAutoFillText?: boolean; //是否需要自动填充文本
    isShowScanIcon?: boolean; //是否显示扫码图标
    showLeftIcon?: boolean;
    scanIconStyle?: Style;
}

interface SearchInputState {
    value: any;
    clearVisible: boolean;
}

export class SearchInput extends React.Component<SearchInputProps, SearchInputState> {
    _textInput?: AbcTextInput | null;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        disable: false,
        border: 0,
        borderRadius: 4,
        editable: true,
        enableDefaultToolBar: true,
        isNeedAutoFillText: false,
        showLeftIcon: true,
    };

    constructor(props: SearchInputProps) {
        super(props);
        this.state = {
            value: props.value,
            clearVisible: false,
        };
    }

    componentDidMount(): void {
        this.showInputToolBar();
    }

    componentWillUnmount(): void {
        abcOverlay.hide(__searchInput);
    }

    public setText(text: string): void {
        this._textInput?.setValue(text, { shouldChange: true });
    }

    public setTextNotUpdate(text: string): void {
        if (text) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: text });
        this._textInput?.setValue(text, { shouldChange: false });
    }

    public handleTextChange(value: string): void {
        if (value) {
            this.setState({ clearVisible: true });
        } else {
            this.setState({ clearVisible: false });
        }
        this.setState({ value: value });
        this.props.onChange?.(value);
    }

    public _clearInputValue(): void {
        this.setState({ value: "" });
        if (this._textInput) {
            this.handleTextChange("");
            this._textInput.clear();
        }
    }

    async _renderScan(): Promise<void> {
        const { onTriggerScan, isNeedAutoFillText } = this.props;
        let loopStatus = true;
        do {
            await CommonScanPage.scan({
                callback: (arg1?: { action?: "user_cancel" | "click_tips"; code?: string }) => {
                    if (arg1?.action == "user_cancel") {
                        loopStatus = false;
                    } else if (!!arg1?.code) {
                        if (isNeedAutoFillText) this._textInput?.setValue(arg1?.code);
                        loopStatus = false;
                        onTriggerScan?.(arg1?.code);
                    }
                },
            });
        } while (loopStatus);
    }

    _renderRightIcon(): JSX.Element | boolean {
        const { rightIconStyle, isShowScanIcon, scanIconStyle } = this.props;
        const { value } = this.state;
        const rightView: JSX.Element[] = [];
        if (!!isShowScanIcon && !value) {
            rightView.push(
                <IconFontView
                    style={{ marginBottom: -Sizes.dp2, ...scanIconStyle }}
                    name={"scan"}
                    color={Colors.T1}
                    size={Sizes.dp18}
                    onClick={() => {
                        this._renderScan();
                    }}
                />
            );
        }
        if (this.state.clearVisible) {
            rightView.push(
                <AssetImageView
                    name={"cross_circle"}
                    style={{ width: Sizes.dp18, height: Sizes.dp18, marginRight: Sizes.dp12, ...rightIconStyle }}
                    onClick={this._clearInputValue.bind(this)}
                />
            );
        }
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                {rightView?.map((item, index) => {
                    return <View key={index}>{item}</View>;
                })}
            </View>
        );
    }

    showInputToolBar(): void {
        if (this.props.canUseCustomKeyboard) {
            abcOverlay.show(this._renderInputToolBar(), __searchInput);
        }
    }

    _renderLeftIcon(): JSX.Element {
        const { leftIconColor, showLeftIcon } = this.props;
        if (!showLeftIcon) return <View />;
        return <IconFontView name={"Search"} size={Sizes.dp14} color={leftIconColor ?? Colors.T6} style={{ marginLeft: Sizes.dp8 }} />;
    }

    _renderInput(): JSX.Element {
        const { value } = this.state;
        const {
            placeholder,
            autoFocus,
            keyboardType,
            inputStyle,
            editable,
            enableDefaultToolBar,
            maxLength,
            placeholderTextColor,
            selectTextOnFocus,
            canUseCustomKeyboard,
        } = this.props;
        return (
            <AbcTextInput
                maxLength={maxLength}
                editable={editable}
                autoFocus={autoFocus ?? false}
                multiline={false}
                placeholder={placeholder}
                placeholderTextColor={placeholderTextColor ?? Colors.T6}
                defaultValue={value}
                keyboardType={keyboardType}
                enableDefaultToolBar={canUseCustomKeyboard ? false : enableDefaultToolBar}
                selectTextOnFocus={selectTextOnFocus}
                style={{ ...SearchInputStyle.contentInput, ...inputStyle }}
                customKeyboardBuilder={canUseCustomKeyboard ? new AlphabetKeyboardBuilder() : undefined}
                ref={(ref) => {
                    this._textInput = ref!;
                }}
                onFocus={() => {
                    this.showInputToolBar();
                    this.props.onFocus?.();
                }}
                onBlur={() => {
                    abcOverlay.hide(__searchInput);
                    this.props.onBlur?.();
                }}
                onChangeText={this.handleTextChange.bind(this)}
            />
        );
    }

    _renderInputToolBar(): JSX.Element {
        return (
            <View style={{ position: "absolute", bottom: 0, left: 0, right: 0, zIndex: 100 }}>
                <ToggleSystemKeyboardPanel textInput={this._textInput} />
            </View>
        );
    }

    render(): JSX.Element {
        const { borderRadius, border, editable, style, searchContainerStyle } = this.props;
        return (
            <View
                style={[
                    SearchInputStyle.container,
                    {
                        borderRadius: borderRadius,
                        borderWidth: border,
                    },
                    flattenStyles(style),
                    flattenStyles(searchContainerStyle),
                ]}
            >
                {this._renderLeftIcon()}
                <View style={{ flexGrow: 1 }}>
                    {this._renderInput()}
                    {!editable && (
                        <AbcView
                            style={{
                                position: "absolute",
                                left: 0,
                                right: 0,
                                top: 0,
                                bottom: 0,
                                backgroundColor: Colors.transparent,
                            }}
                            onClick={() => {
                                this.handleTextChange(this.state.value);
                            }}
                        />
                    )}
                </View>
                {this._renderRightIcon()}
            </View>
        );
    }
}

interface SearchInputProps {
    showClear?: boolean;
    filters?: Filters;
    editable?: boolean;

    onFilterChange?(arg1: Filters): void;
    isShowFilterIcon?: boolean; //是否显示时间筛选按钮
    filterCondition?: boolean; //筛选条件
    style?: Style | Style[];
    qrScanGenerator?: Function; //扫码查询的接口
    qrScanRsp?: (rsp?: any) => void;
    isShowScanIcon?: boolean; //是否显示扫码按钮
}

export class SearchBarWithFilter extends BaseComponent<SearchInputProps, any> {
    protected _searchView?: SearchInput | null;
    private _qrScanSearchTrigger: Subject<string> = new Subject<string>();
    constructor(props: SearchInputProps) {
        super(props);
    }

    protected _hasFilter = false;

    static defaultProps = {
        placeholder: "请输入搜索内容",
        editable: true,
        border: 0,
        borderRadius: 4,
        isShowFilterIcon: true,
    };
    viewHeight: any;
    hasInit = false;
    protected async _handleFilterClick(): Promise<void> {
        const layout = await UIUtils.measureInWindow(this._iconRef!);
        const select: Filters = await showRightSheet(
            <FilterView
                showClear={this.props.showClear ?? false}
                filters={this.props.filters}
                height={layout.y + this.viewHeight - UIUtils.safeStatusHeight()}
            />
        );
        if (!select) return;
        this.props.filters?.fillSelectAttrs(select);
        this._hasFilter = !!select.filters.length;
        this.forceUpdate();
        this.props.onFilterChange?.(select);
    }

    _searchInput?: AbcTextInput | null;

    _iconRef: View | null = null;

    UNSAFE_componentWillReceiveProps(nextProps: Readonly<SearchInputProps>): void {
        this._searchView?.setTextNotUpdate(nextProps.value ?? "");
    }

    componentDidMount(): void {
        this._qrScanSearchTrigger
            .pipe(
                switchMap((code) => {
                    if (!code) return of(null);
                    return this.props.qrScanGenerator?.({ keyword: code }).toObservable();
                })
            )
            .subscribe((rsp) => {
                this.props.qrScanRsp?.(rsp);
            })
            .addToDisposableBag(this);
    }

    public render(): JSX.Element {
        const filterNames = this.props.filters?.getEnableTitleNames().join(" ");
        let showClearIcon = false;
        this.props.filters?.getCurrentItems().forEach((item) => {
            if (item && !item.alwaysShow) {
                showClearIcon = true;
            }
        });
        const { isShowFilterIcon, style, isShowScanIcon } = this.props;
        return (
            <View
                style={[ABCStyles.bottomLine, flattenStyles(style)]}
                collapsable={false}
                ref={(ref) => (this._iconRef = ref)}
                onLayout={(layoutInfo) => {
                    if (this.hasInit && !!this.viewHeight) return;
                    //@ts-ignore
                    this.viewHeight = layoutInfo.layout.height;
                    this.hasInit = true;
                }}
            >
                <View
                    style={{
                        flexDirection: "row",
                        marginVertical: Sizes.dp8,
                    }}
                >
                    <SizedBox width={Sizes.dp16} />
                    <SearchInput
                        editable={this.props.editable}
                        ref={(ref) => {
                            this._searchView = ref;
                            this._searchInput = ref?._textInput;
                        }}
                        onChange={() => {
                            return;
                        }}
                        isShowScanIcon={isShowScanIcon}
                        onTriggerScan={(code) => {
                            this._qrScanSearchTrigger.next(code);
                        }}
                        {...this.props}
                    />
                    <SizedBox width={Sizes.dp12} />
                    {isShowFilterIcon && (
                        <AbcView
                            style={{
                                alignSelf: "center",
                            }}
                            onClick={this._handleFilterClick.bind(this)}
                        >
                            <IconFontView
                                style={{ ...Sizes.paddingLTRB(0, Sizes.dp6, Sizes.dp12, Sizes.dp6) }}
                                name={"filter"}
                                size={Sizes.dp16}
                                color={this._hasFilter ? Colors.mainColor : Colors.black}
                            />
                        </AbcView>
                    )}
                </View>
                {Boolean(filterNames) && (
                    <View
                        style={[
                            {
                                height: Sizes.dp32,
                                backgroundColor: Colors.theme4,
                                justifyContent: "center",
                                alignItems: "center",
                            },
                        ]}
                    >
                        <Text style={TextStyles.t14MM}>{`${filterNames}`}</Text>
                        <AbcView
                            style={[
                                ABCStyles.rowAlignCenter,
                                ABCStyles.absoluteFill,
                                {
                                    left: undefined,
                                    paddingHorizontal: Sizes.dp8,
                                },
                            ]}
                            onClick={() => {
                                this.props.filters?.fillSelectAttrs(this.props.filters?.getDefaultFilter());
                                this._hasFilter = false;
                                this.forceUpdate();
                                this.props.onFilterChange?.(this.props.filters?.getDefaultFilter() ?? new Filters());
                            }}
                        >
                            {/*{showClearIcon && <IconFontView name={"cross_circle"} size={Sizes.dp18} />}*/}
                            {showClearIcon && <IconFontView name={"close1"} size={Sizes.dp18} />}
                        </AbcView>
                    </View>
                )}
            </View>
        );
    }
}

export interface FilterViewProps {
    showClear?: boolean;
    height?: number;
    width?: number;
    borderRadiusStyle?: Style;
    filters?: Filters;
    currentFilterGroup?: FilterGroup;
    itemBottomLine?: boolean;
}
export class FilterView<P extends FilterViewProps = {}> extends React.Component<P> {
    protected showTimePicker = false;
    protected _checkFilterTemp?: Filters;
    protected _shouldCheckMore = false;
    protected filters?: Filters;
    protected _animation: Animation;
    protected _bgAnimation: Animation;
    protected _animationDuration = 250;

    private readonly _useCalendar: boolean = false;

    constructor(props: P) {
        super(props);
        const filterGroups: Array<FilterGroup> = [];
        this.filters = JsonMapper.deserialize(Filters, props.filters);
        this.filters?.filters.forEach((group) => {
            //其它组
            const filters: Array<FilterItem> = [];
            group.filters?.forEach((filter) => {
                if (ABCUtils.isNotEmpty(filter.disableWhenSelects!)) {
                    for (const itemId of filter.disableWhenSelects!) {
                        if (this.filters!.getFilterItemById(itemId)?.select ?? false) {
                            return;
                        }
                    }
                }
                if (filter.select) filters.push(filter);
            });
            filterGroups.push(group.copyFrom({ filters: filters }));
        });

        this._shouldCheckMore = props.currentFilterGroup ? false : (props.filters?.filters?.length ?? 0) > 1;

        this._useCalendar = props.currentFilterGroup?.useCalendar ?? false;

        this._checkFilterTemp = JsonMapper.deserialize(Filters, { filters: filterGroups });

        this._animation = new Animation({
            startValue: -Dimensions.get("window").height + (props.height ?? 0), // 开始值
            toValue: 0, // 动画结束值
            duration: this._animationDuration, // 动画持续时长
            delay: 100, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });

        this._bgAnimation = new Animation({
            startValue: 0, // 开始值
            toValue: 1, // 动画结束值
            duration: this._animationDuration, // 动画持续时长
            delay: 0, // 至动画真正开始的延迟时间
            mode: "timing", // 动画模式
            timingFunction: "linear", // 动画缓动函数
        });
    }

    async _handleCheckItem(item: FilterItem, group: FilterGroup, disable: boolean): Promise<void> {
        if (disable) return;

        const _shouldCheckMore = this._shouldCheckMore;
        if (item.id == CommonFilterId.time) {
            if (!_shouldCheckMore) {
                this.showTimePicker = true;
                this.setState({});
            }
            const select = await TimePicker.show(item.date ?? new Date(), { maxDate: item.maxDate ?? new Date() });
            if (select) {
                item.title = select.format("yyyy-MM-dd");
                item.date = select;
            } else {
                if (item.title == "指定时间") {
                    item.select = false;
                    item.date = undefined;
                }
                if (!_shouldCheckMore) {
                    this.navigatorPopWithAnimation();
                }
                return;
            }
        } else if (item.asyncChange) {
            const asyncItem = await item.asyncChange(item);
            if (asyncItem) {
                item = asyncItem;
            } else {
                return;
            }
        }

        item.select = item.supportCleanup ? !item.select : true;
        const currentGroup = group;
        const filterGroups: Array<FilterGroup> = [];

        this.filters?.filters.forEach((group) => {
            if (currentGroup == group) {
                if (item.exclusive) {
                    // 当选择选择组里的元素具有排它性,那这一组只保留当前选择项
                    if (item.select) {
                        filterGroups.push(
                            JsonMapper.deserialize(FilterGroup, {
                                filters: [item.copyWith({ select: item.select })],
                            })
                        );
                    }
                    //  如果要满足清除操作，则需要将其他项清空
                    group.filters?.forEach((sub) => {
                        if (sub.id != item.id) {
                            sub.select = false;
                        }
                    });
                } else {
                    const filters: Array<FilterItem> = [];
                    group.filters?.forEach((filter) => {
                        if (filter.select && !filter.exclusive) filters?.push(filter);
                    });
                    group.id = currentGroup.id;
                    filterGroups.push(group.copyFrom({ filters: filters }));
                }
            } else {
                // 其它组
                const filters: Array<FilterItem> = [];
                group.filters?.forEach((filter) => {
                    if (ABCUtils.isNotEmpty(filter.disableWhenSelects!)) {
                        for (const itemId of filter.disableWhenSelects!) {
                            if (this.filters!.getFilterItemById(itemId)?.select ?? false) {
                                return;
                            }
                        }
                    }
                    if (filter.select && this._checkFilterTemp?.getFilterItemById(filter.id!)?.select) filters.push(filter);
                });
                filterGroups.push(group.copyFrom({ filters: filters }));
            }
        });
        if (!_shouldCheckMore) {
            this.navigatorPopWithAnimation(JsonMapper.deserialize(Filters, { filters: filterGroups }));
        } else {
            this._checkFilterTemp = JsonMapper.deserialize(Filters, { filters: filterGroups });
            this.forceUpdate();
        }
    }

    _handleSubmitFilter(): void {
        this.navigatorPopWithAnimation(this._checkFilterTemp);
    }

    componentDidMount(): void {
        this._animation.start();
        this._bgAnimation.start();
    }

    componentWillUnmount(): void {
        this._animation.destroy();
        this._bgAnimation.destroy();
    }

    updateAnimation(): void {
        this._animation.updateAnimation({
            startValue: 0,
            toValue: -Dimensions.get("window").height + (this.props.height ?? 0),
            duration: this._animationDuration,
        });
        this._bgAnimation.updateAnimation({
            startValue: 1,
            toValue: 0,
            duration: this._animationDuration,
        });
        this._animation.start();
        this._bgAnimation.start();
    }

    reportPointApi(filter: Filters): void {
        filter.filters.map((group) => {
            group.filters?.map((item) => {
                if (item.select && !!item.statPoint) {
                    reportPointApi(item.statPoint);
                }
            });
        });
    }

    navigatorPopWithAnimation(filters?: Filters): void {
        this.updateAnimation();
        delayed(this._animationDuration).subscribe(() => {
            !!filters && this.reportPointApi(filters);
            ABCNavigator.pop(filters);
        });
    }

    public render(): JSX.Element {
        const _screenHeight = Dimensions.get("screen").height;
        const _top = this.props.height ?? 0;
        if (this.showTimePicker) return <View />;
        return (
            <SafeAreaView
                statusBarColor={Colors.transparent}
                style={{
                    height: _screenHeight,
                }}
            >
                <AbcView
                    style={{ flexGrow: 1 }}
                    onClick={() => {
                        this.navigatorPopWithAnimation();
                    }}
                >
                    <View
                        style={[
                            {
                                position: "relative",
                                height: _screenHeight - _top,
                                top: _top,
                                // backgroundColor: 0x33000000,
                                overflow: "hidden",
                            },
                        ]}
                    >
                        {this._useCalendar ? this.createCalendarPanelView() : this.createDefaultPanelView()}
                        <View
                            //@ts-ignore
                            style={{
                                height: Dimensions.get("window").height - (this.props.height ?? 0),
                                position: "absolute",
                                top: 0,
                                left: 0,
                                right: 0,
                                backgroundColor: 0x33000000,
                                zIndex: -1,
                                opacity: this._bgAnimation,
                            }}
                        />
                    </View>
                </AbcView>
            </SafeAreaView>
        );
    }

    protected createDefaultPanelView(): JSX.Element {
        const menuItems: JSX.Element[] = [];
        let deliverCount = 0;
        const clearButtons: JSX.Element[] = [];
        const { itemBottomLine, currentFilterGroup, borderRadiusStyle } = this.props;

        for (const group of this.filters!.filters) {
            if (currentFilterGroup && group.id != currentFilterGroup.id) continue;

            if (menuItems.length > 0) {
                deliverCount++;
                menuItems.push(<DividerLine key={UniqueKey()} lineHeight={10} color={Colors.D2} />);
            }

            for (const item of group.filters!) {
                let disable = false;
                if (ABCUtils.isNotEmpty(item.disableWhenSelects!)) {
                    for (const itemId of item.disableWhenSelects!) {
                        if (this.filters!.getFilterItemById(itemId)?.select ?? false) {
                            disable = true;
                            break;
                        }
                    }
                }

                if (!!item.beforeLineTitle) {
                    menuItems.push(
                        <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10)]} onClick={() => ({})}>
                            <Text style={[TextStyles.t12NT6.copyWith({ lineHeight: Sizes.dp17 })]}>{item.beforeLineTitle}</Text>
                        </View>
                    );
                }

                menuItems.push(
                    <AbcView
                        style={[
                            itemBottomLine ? ABCStyles.bottomLine : {},
                            {
                                height: Sizes.listItemHeight,
                                paddingHorizontal: Sizes.listHorizontalMargin,
                                justifyContent: "center",
                                flexDirection: "row",
                                alignItems: "center",
                            },
                        ]}
                        key={item.id}
                        onClick={this._handleCheckItem.bind(this, item, group, disable)}
                    >
                        <Text
                            style={[
                                disable
                                    ? TextStyles.t16NT4.copyWith({ fontWeight: item.select ? "500" : "normal" })
                                    : TextStyles.t16NT1.copyWith({ fontWeight: item.select ? "500" : "normal" }),
                                {
                                    flexGrow: 1,
                                    alignItems: "center",
                                    color: item.select ? Colors.mainColor : Colors.T1,
                                },
                            ]}
                        >
                            {item.displayTitle!}
                        </Text>
                        {item.select && this._checkFilterTemp?.getFilterItemById(item.id!)?.select && (
                            <IconFontView name={"Positive_Selected"} size={Sizes.dp14} color={Colors.mainColor} />
                        )}
                    </AbcView>
                );
                if (!!item.endLineTitle) {
                    menuItems.push(
                        <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10)]} onClick={() => ({})}>
                            <Text style={[TextStyles.t12NT6.copyWith({ lineHeight: Sizes.dp17 })]}>{item.endLineTitle}</Text>
                        </View>
                    );
                }
            }
        }

        if (this.props.showClear) {
            if (this._shouldCheckMore) {
                deliverCount++;
                clearButtons.push(<DividerLine key={UniqueKey()} lineHeight={Sizes.dp8} color={Colors.D2} />);
                clearButtons.push(
                    <View
                        style={[
                            ABCStyles.topLine,
                            {
                                height: Sizes.listItemHeight,
                                justifyContent: "center",
                                flexDirection: "row",
                            },
                        ]}
                        key={"clear"}
                    >
                        <AbcButton
                            style={{ flex: 1, borderRadius: 0, backgroundColor: Colors.white }}
                            text={"重置"}
                            textStyle={TextStyles.t16NM}
                            onClick={() => {
                                this.navigatorPopWithAnimation(this.filters?.getDefaultFilter());
                            }}
                        />
                        <AbcButton
                            style={{ flex: 1, borderRadius: 0, backgroundColor: Colors.mainColor }}
                            text={"确定"}
                            textStyle={TextStyles.t16NM.copyWith({ color: Colors.white })}
                            onClick={() => {
                                this._handleSubmitFilter();
                            }}
                        />
                    </View>
                );
            } else {
                clearButtons.push(
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                height: Sizes.listItemHeight,
                                paddingHorizontal: Sizes.listHorizontalMargin,
                            },
                            ABCStyles.topLine,
                        ]}
                        key={"clear"}
                        onClick={() => {
                            this.navigatorPopWithAnimation(this.filters?.getDefaultFilter(this.props.currentFilterGroup));
                        }}
                    >
                        <Text
                            style={[
                                TextStyles.t16NM,
                                {
                                    flexGrow: 1,
                                    alignItems: "center",
                                },
                            ]}
                        >
                            重置
                        </Text>
                    </View>
                );
            }
        }

        const _screenHeight = Dimensions.get("screen").height;
        const _top = this.props.height ?? 0;
        if (this.showTimePicker) return <View />;
        return (
            <View
                style={{
                    ...borderRadiusStyle,
                    position: "absolute",
                    right: 0,
                    backgroundColor: Colors.white,
                    width: this.props.width ?? Sizes.dp140,
                    height: Math.min(
                        _screenHeight - _top - _screenHeight / 3,
                        (menuItems.length + clearButtons.length - deliverCount) * Sizes.listItemHeight + deliverCount * Sizes.dp8
                    ),
                    transform: [
                        {
                            translateY: this._animation,
                        },
                    ],
                }}
            >
                <ScrollView showsVerticalScrollIndicator={false}>{menuItems}</ScrollView>
                {clearButtons}
            </View>
        );
    }

    protected createCalendarPanelView(): JSX.Element {
        const { borderRadiusStyle, currentFilterGroup } = this.props;
        const firstFilter =
            currentFilterGroup?.filters?.find((it) => it.id == CommonFilterId.timeWithCalendar) ?? currentFilterGroup!.filters![0];
        return (
            <View
                style={{
                    ...borderRadiusStyle,
                    position: "absolute",
                    right: 0,
                    backgroundColor: Colors.white,
                    width: this.props.width ?? Sizes.dp140,
                    transform: [
                        {
                            translateY: this._animation,
                        },
                    ],
                }}
            >
                <AbcCalendar.Component
                    date={firstFilter.date}
                    onChange={(date) => {
                        firstFilter.date = date;
                        firstFilter.title = date.format("MM-dd");
                        if (TimeUtils.isToday(date)) {
                            Object.assign(firstFilter, {
                                title: "今天",
                                isDefault: true,
                            });
                        } else {
                            Object.assign(firstFilter, {
                                isDefault: false,
                            });
                        }

                        const group = this.filters?.filters.find((it) => it.id == currentFilterGroup?.id);

                        this._handleCheckItem(firstFilter, group!, false);
                    }}
                />
            </View>
        );
    }
}

interface TimeSelectViewProps {
    expandFilter?: FilterGroup;

    defaultFilterInfo?: Filters;

    maxDate?: Date;

    onChange?(arg1?: Filters): void;
}

export class TimeSelectView extends BaseComponent<TimeSelectViewProps> {
    private filters!: Filters;

    defaultFilterItem?: Filters;

    constructor(props: TimeSelectViewProps) {
        super(props);
        this.createFilter();
    }

    private createFilter(): void {
        this.filters = JsonMapper.deserialize(Filters, {
            filters: [
                JsonMapper.deserialize(FilterGroup, {
                    filters: [
                        JsonMapper.deserialize(FilterItem, {
                            title: "昨天",
                            id: CommonFilterId.timeYesterday,
                            exclusive: true,
                        }),
                        JsonMapper.deserialize(FilterItem, {
                            title: "前天",
                            id: CommonFilterId.timeBeforeYesterday,
                            exclusive: true,
                        }),
                        JsonMapper.deserialize(TimeFilterItem, {
                            title: "指定时间",
                            id: CommonFilterId.time,
                            exclusive: true,
                        }),
                    ],
                }),
            ],
        });

        if (this.props.expandFilter) {
            this.filters.filters.unshift(this.props.expandFilter);
        }

        this.filters.filters.forEach((item1, index1) => {
            item1.filters?.forEach((item2) => {
                item2.select = false;
                if (!this.defaultFilterItem?.filters.length) return;
                if (!this.defaultFilterItem?.filters[index1].filters?.length) return;
                if (_.isNil(this.defaultFilterItem?.filters[index1]?.filters?.[0].id)) return;
                item2.select = item2.id == this.defaultFilterItem?.filters[index1].filters?.[0].id;
                if (item2.id == CommonFilterId.time && this.defaultFilterItem?.filters[index1].filters?.[0].id == CommonFilterId.time) {
                    item2.title = this.defaultFilterItem?.filters[index1].filters?.[0].title;
                    item2.date = this.defaultFilterItem?.filters[index1].filters?.[0].date;
                }
            });
        });
    }

    componentWillReceiveProps(/*nextProps: Readonly<TimeSelectViewProps>*/): void {
        this.defaultFilterItem = this.props.defaultFilterInfo;
        this.createFilter();
    }

    private async _showFilterView(): Promise<void> {
        const select: Filters = await showRightSheet(<FilterView filters={this.filters} height={Sizes.dp48} showClear={true} />);
        if (!select) return;
        this.defaultFilterItem = select;
        for (const item1 of this.defaultFilterItem.filters) {
            for (const item2 of item1.filters ?? []) {
                switch (item2?.id) {
                    case CommonFilterId.time: {
                        const select = await TimePicker.show(item2.date ?? new Date(), { maxDate: this.props.maxDate });
                        if (select) {
                            Object.assign(item2, {
                                title: select.format("yyyy-MM-dd"),
                                date: select,
                            });
                        } else {
                            this.defaultFilterItem = undefined;
                        }
                        break;
                    }
                    case CommonFilterId.timeYesterday: {
                        Object.assign(item2, { date: TimeUtils.yesterday() });
                        break;
                    }
                    case CommonFilterId.timeBeforeYesterday: {
                        Object.assign(item2, { date: TimeUtils.beforeYesterday() });
                        break;
                    }
                }
            }
        }
        this.createFilter();
        this.props.onChange?.(this.defaultFilterItem);
        this.forceUpdate();
    }

    public render(): JSX.Element {
        const filterName: (string | undefined)[] = [];
        this.defaultFilterItem?.filters.forEach((item) => {
            item.filters?.forEach((item) => {
                filterName.push(item.title);
            });
        });
        return (
            <AbcView
                style={[ABCStyles.rowAlignCenter]}
                onClick={() => {
                    this._showFilterView();
                }}
            >
                <Text style={[TextStyles.t14NT1]}>{`${filterName.length == 0 ? "今天" : filterName.join(" ")}`}</Text>
                {IconFontView.dropDown()}
            </AbcView>
        );
    }
}

enum RangePickerCurrentType {
    start = 1,
    end = 2,
}

export class FilterPanelViewV2 extends FilterView<FilterViewProps> {
    private _showWheelPanel = false;
    private _showWheelPanelType?: RangePickerCurrentType;
    private _bgColorAnimation: Animation;

    constructor(props: FilterViewProps) {
        super(props);
        //强制使用"确定"按钮选中
        this._shouldCheckMore = true;
        this._bgColorAnimation = new Animation({
            startValue: "#00000000",
            toValue: "#00000033",
            valueType: "color", // 颜色动画需显式指定color单位
            duration: this._animationDuration,
            delay: 0,
            mode: "timing",
            timingFunction: "linear",
        });
    }

    get isStartTime(): boolean {
        return this._showWheelPanelType == RangePickerCurrentType.start;
    }

    get isEndTime(): boolean {
        return this._showWheelPanelType == RangePickerCurrentType.end;
    }

    async _handleCheckItem(item: FilterItem, group: FilterGroup, disable: boolean, updateWheelStatus = true): Promise<void> {
        if (updateWheelStatus) {
            this._showWheelPanel = false;
            this._showWheelPanelType = undefined;
        }
        super._handleCheckItem(item, group, disable);

        //选中time的其他项时清空range
        const timeRange = this.filters?.getFilterItemById(CommonFilterId.asyncFilter);
        const _timeRange = this._checkFilterTemp?.getFilterItemById(CommonFilterId.asyncFilter);
        if (!_timeRange?.select) {
            timeRange!.timeRange = undefined;
            this.forceUpdate();
        }
    }

    _handleSubmitFilter(): void {
        //选中time的其他项时清空range
        const _timeRange = this._checkFilterTemp?.getFilterItemById(CommonFilterId.asyncFilter);
        if (_timeRange?.select && !(_timeRange.timeRange && _timeRange.timeRange.start && _timeRange.timeRange.end)) {
            Toast.show("请选择完整时间段", { warning: true });
            return;
        }
        super._handleSubmitFilter();
    }

    componentDidMount(): void {
        super.componentDidMount();
        this._bgColorAnimation.start();
    }

    componentWillUnmount(): void {
        super.componentWillUnmount();
        this._bgColorAnimation.destroy();
    }

    updateAnimation(): void {
        super.updateAnimation();
        this._bgColorAnimation.updateAnimation({
            startValue: "#00000033",
            toValue: "#00000000",
            duration: this._animationDuration,
        });
        this._bgColorAnimation.start();
    }

    render(): JSX.Element {
        const menuItems: JSX.Element[] = [];
        const { currentFilterGroup } = this.props;

        for (const group of this.filters!.filters) {
            if (currentFilterGroup && group.id != currentFilterGroup.id) continue;

            if (group.id == FilterGroupId.time) {
                menuItems.push(this._renderNormalTimePanelView(group));
                continue;
            }

            menuItems.push(this._renderNormalPanelView(group));
        }

        const _screenHeight = Dimensions.get("screen").height;
        const _top = this.props.height ?? 0;
        if (this.showTimePicker) return <View />;
        return (
            <SafeAreaView
                statusBarColor={Colors.transparent}
                style={{
                    height: _screenHeight,
                }}
            >
                <AbcView
                    style={{ flexGrow: 1 }}
                    onClick={() => {
                        this.navigatorPopWithAnimation();
                    }}
                >
                    <View
                        style={[
                            {
                                height: _screenHeight - _top - (Utils.isiPhoneX() ? 32 : 0),
                                top: _top,
                                backgroundColor: this._bgColorAnimation,
                                overflow: "hidden",
                            },
                        ]}
                    >
                        <View style={{ flex: 1 }}>
                            <View
                                style={{
                                    right: 0,
                                    backgroundColor: Colors.white,
                                    width: this.props.width ?? Sizes.dp140,
                                    borderBottomLeftRadius: Sizes.dp6,
                                    borderBottomRightRadius: Sizes.dp6,
                                    overflow: "hidden",
                                    flexShrink: 1,
                                    marginBottom: Sizes.dp12,
                                    transform: [
                                        {
                                            translateY: this._animation,
                                        },
                                    ],
                                }}
                                onClick={() => ""}
                            >
                                <ScrollView contentContainerStyle={{ paddingBottom: Sizes.dp24 }} showsVerticalScrollIndicator={false}>
                                    {menuItems}
                                </ScrollView>
                                {this._renderButtonGroup()}
                            </View>
                        </View>

                        {this._renderWheelPanel()}
                    </View>
                </AbcView>
            </SafeAreaView>
        );
    }

    private _updateSelectTimeRange(time: Date): void {
        const timeRange = this.filters?.getFilterItemById(CommonFilterId.asyncFilter);
        if (!timeRange?.select) return;
        timeRange.timeRange = timeRange.timeRange ?? new Range<Date>();
        if (this.isStartTime) {
            timeRange.timeRange.start = time;
            timeRange.title = `${time?.format("yyyy-MM-dd")} - ${timeRange.timeRange.end?.format("yyyy-MM-dd")}`;
        } else if (this.isEndTime) {
            timeRange.timeRange.end = time;
            timeRange.title = `${timeRange.timeRange.start?.format("yyyy-MM-dd")} - ${time?.format("yyyy-MM-dd")}`;
        }

        const timeGroup = this.filters?.filters.find((group) => group.id == FilterGroupId.time);
        this._handleCheckItem(timeRange, timeGroup!, false, false);
    }

    private _renderFilterGroupPanel(title = "", list: FilterItem[] = [], group: FilterGroup, width?: number): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp4)]}>
                <Text style={[TextStyles.t14MT1.copyWith({ lineHeight: Sizes.dp20 })]}>{`${title}`}</Text>
                <SizedBox height={Sizes.dp4} />
                <View style={[{ flexDirection: "row", flexWrap: "wrap", marginLeft: -Sizes.dp2 }]}>
                    {list?.map((item, index) => {
                        let disable = false;
                        if (ABCUtils.isNotEmpty(item.disableWhenSelects!)) {
                            for (const itemId of item.disableWhenSelects!) {
                                if (this.filters!.getFilterItemById(itemId)?.select ?? false) {
                                    disable = true;
                                    break;
                                }
                            }
                        }
                        return (
                            <AbcTag
                                key={index}
                                text={item.displayTitle}
                                style={{ width: width }}
                                checked={item.select && this._checkFilterTemp?.getFilterItemById(item.id!)?.select}
                                onClick={this._handleCheckItem.bind(this, item, group, disable)}
                                textStyle={{
                                    color:
                                        item.select && this._checkFilterTemp?.getFilterItemById(item.id!)?.select
                                            ? Colors.mainColor
                                            : Colors.T6,
                                }}
                            />
                        );
                    })}
                </View>
            </View>
        );
    }

    private _renderAsyncTimeFilterPane(title = "", list: FilterItem[] = [], group: FilterGroup): JSX.Element {
        const timeRange = _.first(list);
        if (!timeRange) return <View />;
        let disable = false;
        if (ABCUtils.isNotEmpty(timeRange.disableWhenSelects!)) {
            for (const itemId of timeRange.disableWhenSelects!) {
                if (this.filters!.getFilterItemById(itemId)?.select ?? false) {
                    disable = true;
                    break;
                }
            }
        }
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp4)]}>
                <Text style={[TextStyles.t14MT1.copyWith({ lineHeight: Sizes.dp20 })]}>{`${title}`}</Text>
                <SizedBox height={Sizes.dp4} />
                <View style={[{ flexDirection: "row", flexWrap: "wrap", alignItems: "center", marginLeft: -Sizes.dp2 }]}>
                    <AbcTag
                        key={"isStartTime"}
                        checked={this.isStartTime}
                        style={{ width: Sizes.dp136 }}
                        text={TimeUtils.formatDate(timeRange?.timeRange?.start, "yyyy-MM-dd", "选择开始时间")}
                        onClick={() => {
                            this._handleCheckItem(timeRange, group, disable);
                            this._showWheelPanel = true;
                            this._showWheelPanelType = RangePickerCurrentType.start;
                            this.forceUpdate();
                        }}
                        textStyle={{ color: this.isStartTime ? Colors.mainColor : Colors.T6 }}
                    />
                    -
                    <AbcTag
                        key={"isEndTime"}
                        checked={this.isEndTime}
                        style={{ width: Sizes.dp136 }}
                        text={TimeUtils.formatDate(timeRange?.timeRange?.end, "yyyy-MM-dd", "选择结束时间")}
                        onClick={() => {
                            this._handleCheckItem(timeRange, group, disable);
                            this._showWheelPanel = true;
                            this._showWheelPanelType = RangePickerCurrentType.end;
                            this.forceUpdate();
                        }}
                        textStyle={{
                            color: this.isEndTime ? Colors.mainColor : Colors.T6,
                        }}
                    />
                </View>
            </View>
        );
    }

    private _renderNormalTimePanelView(group: FilterGroup): JSX.Element {
        const asyncFilter: FilterItem[] | undefined = group?.filters?.filter((item) => item.id == CommonFilterId.asyncFilter);
        const normalTimeList: FilterItem[] | undefined = group?.filters?.filter((item) => item.id != CommonFilterId.asyncFilter);
        return (
            <View key={"time"}>
                {this._renderFilterGroupPanel(
                    group?.name,
                    normalTimeList ?? [],
                    group,
                    Sizes.dp48 + Sizes.dp14 * (group?.name?.length ?? 0)
                )}
                {asyncFilter && this._renderAsyncTimeFilterPane("自定义时间", asyncFilter ?? [], group)}
            </View>
        );
    }

    private _renderNormalPanelView(group: FilterGroup): JSX.Element {
        return <View key={group.id ?? UniqueKey()}>{this._renderFilterGroupPanel(group?.name, group.filters ?? [], group)}</View>;
    }

    private _renderWheelPanel(): JSX.Element {
        if (!this._showWheelPanel) return <View style={{ height: pxToDp(248) }} />;
        const timeRange = this.filters?.getFilterItemById(CommonFilterId.asyncFilter);
        if (!timeRange?.select) return <View />;
        return (
            <View style={{ backgroundColor: Colors.white, height: pxToDp(248) }} onClick={() => ""}>
                {AgeWheelHelper.creatTitleBar(
                    () => {
                        let time = new Date();
                        const timeRange = this.filters?.getFilterItemById(CommonFilterId.asyncFilter);
                        if (timeRange) {
                            timeRange.timeRange = timeRange.timeRange ?? new Range<Date>();
                            if (this.isStartTime) {
                                time = timeRange.timeRange.start ?? new Date();
                            } else if (this.isEndTime) {
                                time = timeRange.timeRange.end ?? new Date();
                            }
                        }
                        this._updateSelectTimeRange(time);
                        this._showWheelPanel = false;
                        this._showWheelPanelType = undefined;
                        this.forceUpdate();
                    },
                    () => {
                        this._showWheelPanel = false;
                        this._showWheelPanelType = undefined;
                        this.forceUpdate();
                    }
                )}
                <_RangeTimePicker
                    minDate={this.isStartTime ? undefined : timeRange.timeRange?.start}
                    maxDate={this.isEndTime ? new Date() : timeRange.timeRange?.end ?? new Date()}
                    height={pxToDp(200)}
                    onChange={(time) => {
                        this._updateSelectTimeRange(time);
                    }}
                />
            </View>
        );
    }

    private _renderButtonGroup(): JSX.Element {
        const clearButtons: JSX.Element[] = [];
        clearButtons.push(
            <ToolBarButtonStyle2
                key={"reset"}
                text={"重置"}
                onClick={() => {
                    this.navigatorPopWithAnimation(this.filters?.getDefaultFilter(this.props.currentFilterGroup));
                }}
            />
        );
        clearButtons.push(
            <ToolBarButtonStyle1
                key={"submit"}
                text={"确定"}
                onClick={() => {
                    this._handleSubmitFilter();
                }}
            />
        );
        return <ToolBar>{clearButtons}</ToolBar>;
    }
}
