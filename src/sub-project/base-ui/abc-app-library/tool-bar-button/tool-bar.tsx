/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/8/3
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { BaseComponent } from "../../base-component";
import _ from "lodash";
import { Text, View } from "@hippy/react";
import DividerLine from "../../divider-line";
import { ABCStyles, Color, Colors, FontSizes, FontWeights, Sizes, TextStyles } from "../../../theme";
import { KeyboardListenerView } from "../../views/keyboard-listener-view";
import { AbcView } from "../../views/abc-view";
import { ThemeManager } from "../../../theme/themes";
import { GridView } from "../../views/grid-view";
import { AbcPopMenu, MenuItem } from "../../views/pop-menu";
import { TreeDotView } from "../../iconfont/iconfont-view";
import UiUtils from "../../utils/ui-utils";

function _createDefaultStyle() {
    return {
        fontSize: FontSizes.size16,
        fontWeight: FontWeights.normal,
        fontColor: Colors.black,
        backgroundColor: Colors.white,
        borderColor: Colors.mainColor,
        lineHeight: Sizes.dp22,
    };
}

let defaultStyle: AbcToolBarButtonStyle = _createDefaultStyle();
ThemeManager.themeObserver.subscribe((/*themeType*/) => {
    defaultStyle = _createDefaultStyle();
});

interface AbcToolBarProps {
    hideWhenKeyboardShow?: boolean;

    showCount?: number;
    showMoreBtn?: boolean;
    direction?: "left" | "right";
    lineHeight?: number;
    toolbarHeight?: number;
}

export class AbcToolBar extends BaseComponent<AbcToolBarProps> {
    private _showMoreBtnRef?: View | null;
    private _allButtons: JSX.Element[] = [];

    constructor(props: AbcToolBarProps) {
        super(props);
    }

    render(): JSX.Element {
        const { hideWhenKeyboardShow, showCount, showMoreBtn, lineHeight, toolbarHeight = Sizes.toolbarHeight } = this.props;
        const buttons: JSX.Element[] = this.createBtnGroup();

        const count = showMoreBtn ? showCount ?? 5 : buttons.length;

        let content = (
            <View>
                <DividerLine style={{ marginBottom: !!lineHeight ? Sizes.dp12 : 0 }} lineHeight={lineHeight} />
                <View
                    style={{
                        height: toolbarHeight,
                        backgroundColor: Colors.white,
                        paddingLeft: Sizes.dp12,
                        paddingRight: Sizes.dp12,
                    }}
                >
                    <GridView crossAxisCount={count} itemHeight={Sizes.dp44} crossAxisSpacing={Sizes.dp8}>
                        {buttons}
                    </GridView>
                </View>
            </View>
        );

        if (hideWhenKeyboardShow) content = <KeyboardListenerView>{content}</KeyboardListenerView>;

        return content;
    }

    protected createShowMoreBtn(): JSX.Element {
        const { backgroundColor, borderColor } = defaultStyle;
        return (
            <View
                collapsable={false}
                ref={(ref) => {
                    this._showMoreBtnRef = ref;
                }}
            >
                <AbcView
                    style={{
                        backgroundColor: backgroundColor,
                        height: Sizes.dp44,
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "row",
                        borderColor: borderColor,
                        borderWidth: 1,
                        borderRadius: 3,
                    }}
                    onClick={() => this._showMoreBtn()}
                >
                    <TreeDotView color={Colors.mainColor} />
                </AbcView>
            </View>
        );
    }

    protected createBtnGroup(): JSX.Element[] {
        const { children, showCount, showMoreBtn, direction } = this.props;
        const buttons: JSX.Element[] = [];
        if (_.isArray(children)) {
            (children as JSX.Element[]).forEach((button) => {
                if (button) buttons.push(button);
            });
        } else if (children != undefined) {
            buttons.push(children as JSX.Element);
        }

        this._allButtons = buttons;
        if (showMoreBtn && buttons.length > (showCount ?? 5)) {
            const newBtns: JSX.Element[] = buttons.slice(0, (showCount ?? 5) - 1);
            newBtns.push(this.createShowMoreBtn());
            return newBtns;
        }

        if (direction == "right") {
            const li = (showCount ?? 5) - buttons.length;
            if (li > 0) {
                for (let index = 0; index < li; index++) {
                    buttons.unshift(<View />);
                }
            }
        }

        return buttons;
    }

    private async _showMoreBtn(): Promise<void> {
        const { showCount } = this.props;
        const hideChilds = this._allButtons.slice((showCount ?? 5) - 1);
        const layout = await UiUtils.measureInWindow(this._showMoreBtnRef);
        const menuItems: MenuItem<JSX.Element>[] = [];
        hideChilds.forEach((node) => {
            menuItems.push(
                new MenuItem({
                    value: node,
                    text: node.props.text,
                    textStyle: TextStyles.t16NT1,
                })
            );
        });
        const { y, height } = layout;
        const select = await AbcPopMenu.show(
            menuItems,
            { x: Sizes.dp10, y: y - Sizes.dp56 * hideChilds.length - height - Sizes.dp12 },
            { x: Sizes.dp20, y: 0, isBottom: true }
        );
        if (!!select) {
            select.props.onClick();
        }
    }
}

interface AbcToolBarButtonStyle {
    width?: number;
    fontSize?: number;
    fontWeight?: string;
    fontColor?: Color;
    backgroundColor?: Color;
    borderColor?: Color;
    textAlign?: "left" | "center" | "right";
}

interface AbcToolBarButtonProps {
    text: string;
    onClick?: () => void;
    style?: AbcToolBarButtonStyle;
    showIndicator?: boolean; //是否显示loading
    disable?: boolean;
}

export class AbcToolBarButton extends React.Component<AbcToolBarButtonProps> {
    constructor(props: AbcToolBarButtonProps) {
        super(props);
    }

    render(): JSX.Element {
        const { text, showIndicator, style } = this.props;
        const { width, fontSize, fontWeight, fontColor, backgroundColor, borderColor, ...otherStyle } = style
            ? { ...defaultStyle, ...style }
            : defaultStyle;

        return (
            <View
                style={{
                    flex: width == undefined ? 1 : undefined,
                    width: width,
                }}
            >
                <AbcView
                    style={{
                        backgroundColor: backgroundColor,
                        height: Sizes.dp44,
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "row",
                        borderColor: borderColor,
                        borderWidth: 1,
                        borderRadius: 4,
                    }}
                    onClick={() => !showIndicator && this.props.onClick?.()}
                >
                    <Text
                        style={[
                            otherStyle ? otherStyle : {},
                            {
                                color: fontColor,
                                fontSize: fontSize,
                                fontWeight: fontWeight,
                            },
                        ]}
                    >
                        {text}
                    </Text>
                </AbcView>
                {showIndicator && <View style={[ABCStyles.absoluteFill, { backgroundColor: "rgba(255,255,255, 0.7)" }]} />}
            </View>
        );
    }
}

export class AbcToolBarButtonStyle1 extends BaseComponent<AbcToolBarButtonProps> {
    constructor(props: AbcToolBarButtonProps) {
        super(props);
    }

    render(): JSX.Element {
        const { text, style, disable, onClick, ...otherProps } = this.props;

        let bgColor = Colors.mainColor;
        if (disable) {
            bgColor = Colors.bdColor;
        } else if (onClick) {
            bgColor = Colors.mainColor;
        } else {
            bgColor = Colors.P5;
        }
        return (
            <AbcToolBarButton
                {...otherProps}
                style={{
                    fontColor: onClick || disable ? Colors.white : Colors.T2,
                    backgroundColor: bgColor,
                    borderColor: bgColor,
                    ...style,
                }}
                text={text}
                onClick={() => !disable && onClick?.()}
            />
        );
    }
}

export class AbcToolBarButtonStyle2 extends BaseComponent<AbcToolBarButtonProps> {
    constructor(props: AbcToolBarButtonProps) {
        super(props);
    }

    /**
     *
     */
    render(): JSX.Element {
        const { text, style, disable, ...otherProps } = this.props;

        return (
            <AbcToolBarButton
                {...otherProps}
                style={{
                    fontColor: disable ? Colors.T4 : Colors.mainColor,
                    // backgroundColor: Colors.D2,
                    borderColor: disable ? Colors.bdColor : Colors.mainColor,
                    ...style,
                }}
                text={text}
                onClick={() => !disable && this.props.onClick?.()}
            />
        );
    }
}
