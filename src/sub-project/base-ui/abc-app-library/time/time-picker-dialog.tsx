/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/7/25
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-component";
import { BottomSheetHelper } from "../dialog";
import { showBottomSheet } from "../../dialog/bottom_sheet";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcTimeBaseProps } from "./date/bean";
import { TimeUtils } from "../../../common-base-module/utils";
import { AbcWheel } from "../wheel/abc-wheel";
import { Range } from "../../utils/value-holder";
import { ABCNavigator } from "../../views/abc-navigator";
import moment from "moment";
import { pxToDp } from "../../utils/ui-utils";
import { SafeAreaBottomView } from "../../safe_area_view";

export interface TimeRangePickerDialogProps extends AbcTimeBaseProps<Range<Date>> {
    /**
     * 开始时间 补充显示内容
     */
    subTitle?: {
        date: Date;
        render: () => JSX.Element;
    }[];

    titleSuffix?: {};

    /**
     * 是否限制为当前时间开始
     */
    limitNow?: boolean;

    minDate?: Date;
    maxDate?: Date;

    special?: boolean; //  解决初始异常问题

    //排除项时间
    exclusionList?: Date[];
    //排除时间项提示文案
    exclusionSubText?: string;
    /**
     * 包含时间项处理方式
     * @return boolean false拦截后续操作，true不做变动
     */
    exclusionValidate?: () => boolean | Promise<boolean>;
}

interface TimeRangePickerDialogStates {
    date: Range<Date>;
}

export class TimeRangePickerDialog extends BaseComponent<TimeRangePickerDialogProps, TimeRangePickerDialogStates> {
    dataList: Date[] = [];
    private _startWheel?: AbcWheel | null;
    private _endWheel?: AbcWheel | null;
    private _initDayTime = new Date();

    constructor(props: TimeRangePickerDialogProps) {
        super(props);
        this._initDayTime = props.date?.start || props.date?.end || this._initDayTime;
        this.init();
        if (!!props.date) {
            const startIndex = this.findTimeIndex(props.date.start);
            let endIndex = this.findTimeIndex(props.date.end, !props.special);
            if (startIndex == endIndex) {
                endIndex = Math.min(startIndex + 1, this.dataList.length - 1);
            }
            this.state = {
                date: new Range<Date>(this.dataList[startIndex], this.dataList[endIndex]),
            };
        } else {
            const currentTime = new Date(`${this._initDayTime.format("yyyy/MM/dd")} ${new Date().format("HH:mm:ss")}`);
            const startIndex = this.findTimeIndex(currentTime);
            let endIndex = this.findTimeIndex(currentTime, true);
            if (startIndex == endIndex) {
                endIndex = Math.min(startIndex + 1, this.dataList.length - 1);
            }
            this.state = {
                date: new Range<Date>(this.dataList[startIndex], this.dataList[endIndex]),
            };
        }
    }

    static async show<T>(params?: TimeRangePickerDialogProps): Promise<Range<Date>> {
        return showBottomSheet(<TimeRangePickerDialog {...params} />);
    }

    /**
     * 根据时间查找在时间列表中的位置
     * <AUTHOR>
     * @date 2022/7/26
     * @param {Date} time 时间
     * @param {Boolean}  before 选中时间前/后
     * @returns {number} 索引
     */
    findTimeIndex(time?: Date, before = false): number {
        const minDateIndex = this.dataList.findIndex((it) => it.getTime() > (time?.getTime() ?? 0));
        if (before) {
            return Math.max(minDateIndex, 0);
        }
        return Math.max(minDateIndex - 1, 0);
    }

    checkRangeTime(isStart = true): void {
        const { date } = this.state;
        if (date.start!.getTime() < date.end!.getTime()) return;

        if (isStart) {
            const endIndex = this.findTimeIndex(date.start, true);
            date.end = this.dataList[endIndex];
            this._endWheel?.scrollToItemWithIndex(endIndex);
        } else {
            const startIndex = Math.max(this.findTimeIndex(date.end) - 1, 0);
            date.start = this.dataList[startIndex];
            this._startWheel?.scrollToItemWithIndex(startIndex);
        }
        this.setState({ date });
    }

    renderStartRowView(data: Date): JSX.Element {
        const { exclusionList, exclusionSubText } = this.props;
        const hasExclusion = exclusionList?.find((item) => item.format("HH:mm") == data.format("HH:mm"));
        const selectedIndex = data.getTime() == this.state.date.start?.getTime();

        const extContent = this.props.subTitle?.find((item) => item.date.getTime() == data.getTime());
        return (
            <View style={[{ paddingVertical: Sizes.dp6, marginVertical: Sizes.dp4, position: "relative" }]}>
                {!!extContent && (
                    <View style={{ position: "absolute", top: Sizes.dp10, bottom: Sizes.dp6, left: 0 }}>{extContent.render()}</View>
                )}
                <Text
                    style={[
                        TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp22, color: selectedIndex ? Colors.T1 : Colors.t2 }),
                        { textAlign: "center" },
                    ]}
                >
                    {data.format("HH:mm")}
                </Text>
                {!!exclusionSubText && !!hasExclusion && (
                    <Text
                        style={[
                            {
                                position: "absolute",
                                right: Sizes.dp12,
                                top: Sizes.dp6,
                                bottom: Sizes.dp6,
                            },
                            TextStyles.t14NT3.copyWith({ lineHeight: Sizes.dp22, color: Colors.t3 }),
                        ]}
                    >
                        {exclusionSubText}
                    </Text>
                )}
            </View>
        );
    }

    renderEndRowView(data: Date): JSX.Element {
        const { exclusionList, exclusionSubText } = this.props;
        const selectedIndex = data.getTime() == this.state.date.end?.getTime();
        const hasExclusion = exclusionList?.find((item) => item.format("HH:mm") == data.format("HH:mm"));
        return (
            <View style={[{ paddingVertical: Sizes.dp6, marginVertical: Sizes.dp4 }]}>
                <Text
                    style={[
                        TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp22, color: selectedIndex ? Colors.T1 : Colors.t2 }),
                        { textAlign: "center" },
                    ]}
                >
                    {data.format("HH:mm")}
                </Text>
                {!!exclusionSubText && !!hasExclusion && (
                    <Text
                        style={[
                            {
                                position: "absolute",
                                right: Sizes.dp12,
                                top: Sizes.dp6,
                                bottom: Sizes.dp6,
                            },
                            TextStyles.t14NT3.copyWith({ lineHeight: Sizes.dp22, color: Colors.t3 }),
                        ]}
                    >
                        {exclusionSubText}
                    </Text>
                )}
            </View>
        );
    }

    render(): JSX.Element {
        const _startList = this.dataList.map((item) => this.renderStartRowView(item));
        const _endList = this.dataList.map((item) => this.renderEndRowView(item));
        const { date } = this.state;

        const startDateIndex = this.findTimeIndex(date.start);
        const endDateIndex = this.findTimeIndex(date.end);

        return (
            <View>
                <View style={[ABCStyles.dialogBaseTopRadius, { backgroundColor: Colors.white, height: pxToDp(400) }]}>
                    {BottomSheetHelper.createDefaultHandleBar({
                        onClick: async () => {
                            const { exclusionValidate, exclusionList } = this.props;
                            let shouldFinish = true;
                            if (
                                exclusionList?.some((exclusionDate) =>
                                    moment(exclusionDate).isBetween(
                                        moment(this.state.date.start),
                                        moment(this.state.date.end),
                                        undefined,
                                        "[]"
                                    )
                                )
                            ) {
                                if (!!exclusionValidate) {
                                    shouldFinish = await exclusionValidate();
                                }
                            }
                            if (shouldFinish) {
                                ABCNavigator.pop(this.state.date);
                            }
                        },
                    })}
                    {this.renderHeaderView()}
                    <View style={{ flexShrink: 1 }}>
                        <View style={{ flexDirection: "row" }}>
                            <AbcWheel
                                ref={(ref) => {
                                    this._startWheel = ref;
                                }}
                                topOffsetHeight={Sizes.dp60}
                                dataList={_startList}
                                initialIndex={startDateIndex}
                                onSelectChanged={(index) => {
                                    date.start = this.dataList[Math.min(index, this.dataList.length - 2)];
                                    this._startWheel?.scrollToItemWithIndex(Math.min(index, this.dataList.length - 2));
                                    this.setState({ date });
                                    this.checkRangeTime();
                                }}
                            />
                            <AbcWheel
                                ref={(ref) => {
                                    this._endWheel = ref;
                                }}
                                topOffsetHeight={Sizes.dp60}
                                dataList={_endList}
                                initialIndex={endDateIndex}
                                onSelectChanged={(index) => {
                                    date.end = this.dataList[Math.max(index, 1)];
                                    this._endWheel?.scrollToItemWithIndex(Math.max(index, 1));
                                    this.setState({ date });
                                    this.checkRangeTime(false);
                                }}
                            />
                        </View>
                    </View>
                    <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
                </View>
            </View>
        );
    }

    /**
     * 初始化相关的列表数据
     * <AUTHOR>
     * @date 2022/7/25
     */
    protected init(): void {
        const {
            intervalStep = 15,
            limitNow = false,
            minDate = moment(this._initDayTime).hour(6).minute(0).toDate(),
            maxDate = moment(this._initDayTime).hour(23).minute(0).toDate(),
            exclusionList,
            exclusionSubText,
        } = this.props;
        let nowDayStart = TimeUtils.getStartOfDate(this._initDayTime);
        const nowDayEnd = TimeUtils.getEndOfDate(this._initDayTime);
        let itemDate;

        if (!!minDate) {
            const hour = minDate.getHours(),
                minute = minDate.getMinutes();
            nowDayStart.setHours(hour);
            nowDayStart.setMinutes(Math.floor(minute / intervalStep) * intervalStep);
        }

        if (!!maxDate) {
            const hour = maxDate.getHours(),
                minute = maxDate.getMinutes();
            nowDayEnd.setHours(hour);
            nowDayEnd.setMinutes(Math.floor(minute / intervalStep) * intervalStep);
            nowDayEnd.setSeconds(0);
            nowDayEnd.setMilliseconds(0);
        }

        if (limitNow) {
            const startTime = Math.max(minDate.getTime() - 60 * 1000, new Date().getTime());
            nowDayStart = new Date(
                Math.ceil((startTime - nowDayStart.getTime()) / (intervalStep * 60 * 1000)) * intervalStep * 60 * 1000 +
                    nowDayStart.getTime()
            );
        }
        let count = 0;
        while (nowDayEnd.getTime() - (itemDate?.getTime() ?? 0) > 0) {
            itemDate = new Date(nowDayStart.getTime() + count * intervalStep * 60 * 1000);
            count++;
            if (!!exclusionList?.map((item) => item.getTime()).includes(itemDate.getTime()) && !exclusionSubText) {
                continue;
            } else {
                this.dataList.push(itemDate);
            }
        }
    }

    protected renderHeaderView(): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp12 }]}>
                <View style={[ABCStyles.centerChild, { flex: 1 }]}>
                    <Text style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })]}>开始时间</Text>
                </View>
                <View style={[ABCStyles.centerChild, { flex: 1 }]}>
                    <Text style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })]}>结束时间</Text>
                </View>
            </View>
        );
    }
}
