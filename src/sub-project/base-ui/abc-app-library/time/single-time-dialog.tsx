import { Style, StyleSheet, Text, View } from "@hippy/react";
import { ABCStyles, Colors, flattenStyles, Sizes, TextStyles } from "../../../theme";
import { pxToDp } from "../../utils/ui-utils";
import { BottomSheetHelper } from "../dialog";
import { ABCNavigator } from "../../views/abc-navigator";
import { AbcWheel } from "../wheel/abc-wheel";
import { SafeAreaBottomView } from "../../safe_area_view";
import React from "react";
import _ from "lodash";
import { showBottomSheet } from "../../dialog/bottom_sheet";
import { BaseComponent } from "../../base-component";
import { AbcTag } from "../common/abc-tag";
import { Spacer } from "../../index";
import sizes from "../../../theme/sizes";
interface SingleTimeDialogProps {
    style?: Style | Style[];
    hourTime?: string;
    minutesTime?: string;
    enable?: boolean;
    hourIntervalStep?: number;
    minutesIntervalStep?: number;
    onChange?(hourTime?: string, minutesTime?: string): void;
}
interface SingleTimeDialogState {
    selected: boolean;
}
interface SingleTimePickerDialogState {
    hourTime?: string;
    minutesTime?: string;
}
const defaultStyle = StyleSheet.create({
    abcTag: {
        backgroundColor: Colors.whiteSmoke,
        borderWidth: Sizes.dpHalf,
        borderColor: Colors.transparent,
        borderRadius: Sizes.dp4,
        ...sizes.marginLTRB(0),
        ...Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8),
    },
    abcTagSelected: {
        backgroundColor: Colors.theme2_08,
        borderColor: Colors.mainColor,
    },
    abcErrorTag: {
        borderColor: Colors.errorBorder,
    },
});
export class SingleTimeDialog extends BaseComponent<SingleTimeDialogProps, SingleTimeDialogState> {
    constructor(props: SingleTimeDialogProps) {
        super(props);
        this.state = {
            selected: false,
        };
    }
    createAbcTagStyle(): Style {
        const { style } = this.props;
        let _abcTagStyle = Object.assign(defaultStyle.abcTag, { ...flattenStyles(style) });
        if (this.state.selected) {
            _abcTagStyle = Object.assign(
                { ...defaultStyle.abcTag },
                {
                    ...defaultStyle.abcTagSelected,
                }
            );
        }

        return _abcTagStyle;
    }
    render(): JSX.Element {
        const { enable, hourTime, minutesTime } = this.props;
        const text = !!hourTime && !!minutesTime ? `${hourTime}:${minutesTime}` : "选择时间";
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                <AbcTag
                    checked={true}
                    style={[this.createAbcTagStyle()]}
                    textStyle={[
                        TextStyles.t16NT1.copyWith({
                            lineHeight: Sizes.dp22,
                            color: !!enable && !!hourTime && !!minutesTime ? Colors.T1 : Colors.T4,
                        }),
                        { textAlign: "center" },
                    ]}
                    text={text}
                    onClick={() => {
                        !!enable && this.handleTimeClick();
                    }}
                    textNumberOfLines={1}
                />
                <Spacer />
            </View>
        );
    }
    public async handleTimeClick(): Promise<void> {
        const { onChange } = this.props;
        this.setState({ selected: true });
        const result = await SingleTimePickerDialog.show({
            ...this.props,
        });
        this.setState({ selected: false });
        if (!result) return;
        if (!!result?.hourTime || !!result?.minutesTime) {
            onChange?.(result?.hourTime, result?.minutesTime);
        }
    }
}
export class SingleTimePickerDialog extends BaseComponent<SingleTimeDialogProps, SingleTimePickerDialogState> {
    private _startWheel: AbcWheel | null = null;
    private _endWheel: AbcWheel | null = null;
    private hourList: string[] = [];
    private timeList: string[] = [];
    static async show<T>(params?: SingleTimeDialogProps): Promise<{ hourTime?: string; minutesTime?: string }> {
        return showBottomSheet(<SingleTimePickerDialog {...params} />);
    }
    constructor(props: SingleTimeDialogProps) {
        super(props);
        this.hourList = _.range(0, 24, props.hourIntervalStep ?? 1).map((item) => {
            if (item.toString()?.length < 2) {
                return "0" + item;
            }
            return item.toString();
        });
        this.timeList = _.range(0, 60, props?.minutesIntervalStep ?? 10).map((item) => {
            if (item.toString()?.length < 2) {
                return "0" + item;
            }
            return item.toString();
        });
        this.state = {
            hourTime: (() => {
                if (!props?.hourTime) return;
                if (props?.hourTime?.length < 2) {
                    return "0" + props?.hourTime;
                }
                return props?.hourTime;
            })(),
            minutesTime: (() => {
                if (!props?.minutesTime) return;
                if (props?.minutesTime?.length < 2) {
                    return "0" + props?.minutesTime;
                }
                return props?.minutesTime;
            })(),
        };
    }
    renderMinutesRowView(minutes: string): JSX.Element {
        const selectedIndex = minutes == this.state.minutesTime;
        return (
            <View style={[{ paddingVertical: Sizes.dp6, marginVertical: Sizes.dp4 }]}>
                <Text
                    style={[
                        TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp22, color: selectedIndex ? Colors.T1 : Colors.t2 }),
                        { textAlign: "center" },
                    ]}
                >
                    {minutes}
                </Text>
            </View>
        );
    }
    renderStartRowView(hour: string): JSX.Element {
        const selectedIndex = hour == this.state.hourTime;
        return (
            <View style={[{ paddingVertical: Sizes.dp6, marginVertical: Sizes.dp4, position: "relative" }]}>
                <Text
                    style={[
                        TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp22, color: selectedIndex ? Colors.T1 : Colors.t2 }),
                        { textAlign: "center" },
                    ]}
                >
                    {hour}
                </Text>
            </View>
        );
    }
    render(): JSX.Element {
        const _startList = this.hourList.map((item) => this.renderStartRowView(item));

        const _endList = this.timeList.map((item) => this.renderMinutesRowView(item));

        const startDateIndex = this.hourList?.findIndex((item) => item == this.state.hourTime);
        const endDateIndex = this.timeList?.findIndex((item) => item == this.state.minutesTime);
        if (endDateIndex < 0) {
            this.setState({
                minutesTime: this.timeList[0],
            });
        }

        return (
            <View>
                <View style={[ABCStyles.dialogBaseTopRadius, { backgroundColor: Colors.white, height: pxToDp(400) }]}>
                    {BottomSheetHelper.createDefaultHandleBar({
                        onClick: async () => {
                            ABCNavigator.pop({
                                hourTime: this.state.hourTime,
                                minutesTime: this.state.minutesTime,
                            });
                        },
                    })}
                    <View style={{ flexShrink: 1 }}>
                        <View style={{ flexDirection: "row" }}>
                            <AbcWheel
                                ref={(ref) => {
                                    this._startWheel = ref;
                                }}
                                topOffsetHeight={Sizes.dp60}
                                dataList={_startList}
                                initialIndex={startDateIndex}
                                onSelectChanged={(index) => {
                                    const hour = this.hourList[Math.max(index, 0)];
                                    this._startWheel?.scrollToItemWithIndex(Math.max(index, 0));
                                    this.setState({ hourTime: hour });
                                }}
                            />
                            <AbcWheel
                                ref={(ref) => {
                                    this._endWheel = ref;
                                }}
                                topOffsetHeight={Sizes.dp60}
                                dataList={_endList}
                                initialIndex={endDateIndex}
                                onSelectChanged={(index) => {
                                    const minutes = this.timeList[Math.max(index, 0)];
                                    this._endWheel?.scrollToItemWithIndex(Math.max(index, 0));
                                    this.setState({ minutesTime: minutes });
                                }}
                            />
                        </View>
                    </View>
                    <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
                </View>
            </View>
        );
    }
}
