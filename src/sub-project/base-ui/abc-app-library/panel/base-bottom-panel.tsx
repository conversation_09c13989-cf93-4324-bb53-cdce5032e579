/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/12/29
 */
import React from "react";
import { View } from "@hippy/react";
import { Sizes } from "../../../theme";
import { AbcView } from "../../views/abc-view";
import { ABCNavigator, TransitionType } from "../../views/abc-navigator";
import { UIUtils } from "../../utils";

export function showBottomPanel<T>(
    view: JSX.Element,
    options?: { topMaskHeight?: number; bg?: string; borderRadius?: number; showMask?: boolean; onCancel?: () => void }
): Promise<T> {
    const outSideView = (
        <View style={{ flex: 1, backgroundColor: options?.bg ?? "", position: "relative" }}>
            {/*<BlurView style={ABCStyles.absoluteFill} blurType={"light"} blurAmount={5} />*/}
            <AbcView
                style={{ height: (options?.topMaskHeight ?? Sizes.dp44) + UIUtils.safeStatusHeight() }}
                onClick={() => options?.onCancel?.() ?? ABCNavigator.pop()}
            />
            <View
                style={{
                    flex: 1,
                    borderTopLeftRadius: options?.borderRadius ?? Sizes.dp6,
                    borderTopRightRadius: options?.borderRadius ?? Sizes.dp6,
                    overflow: "hidden",
                }}
            >
                {view}
            </View>
        </View>
    );
    return ABCNavigator.navigateToPage(outSideView, {
        backgroundColor: options?.showMask ?? true ? 0x33000000 : undefined,
        transitionType: TransitionType.inFromBottom,
        enableBackgroundAnimation: true,
    });
}
