/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/3/9
 * @Copyright 成都字节流科技有限公司© 2023
 */
import React, { useRef } from "react";
import { Style, Text, View } from "@hippy/react";
import moment from "moment";
import {
    PageViewColValue,
    PageViewSort,
    RegistrationKanBanViewProps,
    RegistrationKanBanViewV3,
    TimeSegItem,
} from "../registrationKanBanView";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { AbcScrollView2 } from "../../../base-ui/views/abc-scroll-view2";
import { DEFAULT_DOCTOR_ID, RegistrationDetail, RegistrationFormItem, RegistrationStatusV2 } from "../../data/bean";
import { TimeUtils } from "../../../common-base-module/utils";
import { BaseComponent } from "../../../base-ui/base-component";
import { AbcView } from "../../../base-ui/views/abc-view";
import { IconFontView, SizedBox, Spacer } from "../../../base-ui";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { Toast } from "../../../base-ui/dialog/toast";
import { ABCNavigator, TransitionType } from "../../../base-ui/views/abc-navigator";
import { DentistryInvoicePage } from "../../dentistry/dentistry-invoice-page";
import { Range } from "../../../base-ui/utils/value-holder";
import { AppointmentInvoicePage } from "../../dentistry/appointment/appointment-invoice-page";
import { AppointmentInvoiceSummaryDialog } from "../../dentistry/appointment/appointment-invoice-summary-dialog";
import { RegistrationInvoiceSummaryDialog } from "../../registration-invoice-summary-dialog";
import _ from "lodash";
import { FlexibleTimeShowMoreDialog } from "./flexible-time-show-more-dialog";
import { UIUtils } from "../../../base-ui/utils";
import { layoutDayEvents } from "./common";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { delayed } from "../../../common-base-module/rxjs-ext/rxjs-ext";
import { AssetImageView } from "../../../base-ui/views/asset-image-view";

interface RegistrationKanBanFlexibleTimeModeViewProps extends RegistrationKanBanViewProps {}
export class RegistrationKanBanFlexibleTimeModeView extends RegistrationKanBanViewV3<RegistrationKanBanFlexibleTimeModeViewProps> {
    readonly DATA_GRID_HEIGHT = Sizes.dp22;

    dataGridHeight = Sizes.dp22;

    protected _abcScrollYViewRef?: AbcScrollView2 | null;
    private _firstLoad = true;

    constructor(props: RegistrationKanBanFlexibleTimeModeViewProps) {
        super(props);
        this.scrollOffsetSyncName = "RegistrationKanBanFlexibleTimeModeView";
        const tableRowHeader: Map<string, number[]> = new Map();
        const _now = moment();
        this.__headerList.forEach((item) => {
            tableRowHeader.set(item.timeFrom, [1, 0, 0]);
            if (_now.isBetween(_now.clone().set({ ...item.momentDateObj.timeFrom }), _now.clone().set({ ...item.momentDateObj.timeTo }))) {
                tableRowHeader.set(item.timeFrom, [1, 0, 1]);
            }
        });
        this.rowHeaderDisplayList = tableRowHeader;
    }

    componentWillReceiveProps(nextProps: Readonly<RegistrationKanBanFlexibleTimeModeViewProps>): void {
        //如果当前props内容变化了，那么就重新计算
        if (
            this.props.dailyViews?.length !== nextProps.dailyViews?.length ||
            this.props.dailyViews?.[0].reserveDate?.toDateString() !== nextProps.dailyViews?.[0].reserveDate?.toDateString()
        ) {
            // this._firstLoad = true;
        }
    }

    renderScrollRowHeaderView(): JSX.Element {
        return (
            <View style={[{ width: Sizes.dp51 }]}>
                <View style={{ height: Sizes.dp48 }} />
                <AbcScrollView2
                    bounces={false}
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    horizontal={false}
                    scrollOffsetSyncGroup={this.scrollOffsetSyncName}
                    style={[{ width: Sizes.dp51 }]}
                    scrollEventThrottle={1}
                    ref={(ref) => {
                        this._abcScrollYViewRef = ref;
                    }}
                >
                    {[...this.rowHeaderDisplayList.keys()]
                        // .filter((item, index) => !(index % 4))
                        .map((item, index, self) => {
                            const _heightCount = this.rowHeaderDisplayList.get(item) ?? [4, 0];

                            const _lastItem = self[Math.max(index - 1, 0)];
                            const _lastHeightCount = this.rowHeaderDisplayList.get(_lastItem) ?? [4, 0];
                            let isNow = false;
                            if (!!_lastHeightCount[2]) {
                                isNow = true;
                            }
                            const height = this.dataGridHeight * Math.max(_heightCount[0], 1) + this.dataDescHeight * _heightCount[1];
                            return (
                                <View
                                    key={index}
                                    style={[
                                        {
                                            height,
                                            width: Sizes.dp51,
                                        },
                                    ]}
                                >
                                    {(!(index % 4) || isNow) && (
                                        <Text
                                            style={[
                                                TextStyles.t11NT2.copyWith({
                                                    color: isNow ? Colors.mainColor : Colors.t2,
                                                    lineHeight: Sizes.dp16,
                                                }),
                                                { marginTop: 0, marginLeft: Sizes.dp16 },
                                            ]}
                                        >
                                            {`${item}`}
                                        </Text>
                                    )}
                                </View>
                            );
                        })}
                </AbcScrollView2>
            </View>
        );
    }

    renderTabPanelDefaultView(list: PageViewColValue[]): JSX.Element {
        const dataGridWidth = this.DATA_GRID_WIDTH * Math.max(1, 3 / list.length);
        return (
            <View
                style={{ flexDirection: "row" }}
                // @ts-ignore
                onAttachedToWindow={() => {
                    if (this._firstLoad) {
                        //生成当前时间线信息
                        const _now = new Date().format("HH:mm");
                        let currentTimeLineIndex = 0;
                        this.__headerList.findIndex((item, index) => {
                            if (_now > item.timeFrom && _now <= item.timeTo) {
                                currentTimeLineIndex = index;
                            }
                        });
                        this._onScrollToContentTrigger.next(currentTimeLineIndex);
                        this._firstLoad = false;
                    }
                }}
            >
                {/*整体横向→*/}
                {list?.map((pv) => {
                    const employee = pv.data;
                    //每一列↓
                    return (
                        <FixedOrderModeColItemView
                            key={`${employee.reserveDate?.toDateString()}${employee.employeeId}`}
                            dataGridWidth={dataGridWidth}
                            dataGridHeight={this.dataGridHeight}
                            rowHeaderDisplayList={this.__headerList}
                            pageViewColValue={pv}
                            onHandleReg={(_registrationFormItem) => {
                                this.handleCreateReg(_registrationFormItem);
                            }}
                            onHandleReviewRegDetail={(regDetail) => {
                                this.handleReviewRegDetail(regDetail);
                            }}
                        />
                    );
                })}
            </View>
        );
    }

    initLeftHeaderList(_props: RegistrationKanBanFlexibleTimeModeViewProps): void {
        const tableColHeader: Map<string | Date, number> = new Map();
        const list: PageViewColValue[] = [];

        if (((_props ?? this.props).dailyViews?.length ?? 0) > 1) {
            (_props ?? this.props).dailyViews?.forEach((dv) => {
                tableColHeader.set(dv.reserveDate!, 1);
                dv.dailyEmployeeList?.map((employee) => {
                    employee.reserveDate = dv.reserveDate;
                    list.push({
                        data: employee,
                        headerKeyStr: employee.reserveDate,
                        notRefundedCount: 0,
                    });
                });
            });
        } else {
            (_props ?? this.props).dailyViews?.map((dv) => {
                dv?.dailyEmployeeList?.map((employee) => {
                    employee.reserveDate = dv.reserveDate;
                    const count = employee.registrationList?.length ?? 0;

                    const _employeeName = employee.employeeId === DEFAULT_DOCTOR_ID ? "不指定" : employee.employeeName;
                    tableColHeader.set(_employeeName ?? "", count);

                    list.push({
                        data: employee,
                        headerKeyStr: _employeeName,
                        notRefundedCount: count,
                    });
                });
            });
        }

        ///必须得
        this.colHeaderDisplayList = tableColHeader;

        const pageViewDataList: Map<PageViewSort, PageViewColValue[]> = new Map();
        _.chunk(list, 3).map((item, index) => {
            pageViewDataList.set(index, item);
        });
        this.pageViewDataList = pageViewDataList;
        this.dataGridWidth = this.DATA_GRID_WIDTH * Math.max(1, 3 / this.colHeaderDisplayList.size);
    }

    handleReviewRegDetail(detail: RegistrationDetail): void {
        if (this.isAppointment) {
            AppointmentInvoiceSummaryDialog.show({
                id: detail?.id,
                statusName: detail?.registrationFormItem?.statusName,
            });
        } else if (this.isRegistration) {
            const itemInfo = detail?.registrationFormItem;
            RegistrationInvoiceSummaryDialog.show({
                id: detail.id,
                doctorId: itemInfo?.doctorId,
                oldReserveInfo: itemInfo?.oldReserveInfo,
                statusName: itemInfo?.statusName,
            });
        }
    }

    handleCreateReg(regFormItem: RegistrationFormItem): void {
        const { canModifyRegistration } = this.props;
        if (TimeUtils.getTodayStart().getTime() > (regFormItem.reserveDate?.getTime() ?? 0)) {
            Toast.show("过去时间不可预约");
            return;
        }
        regFormItem.isReserved = (regFormItem.reserveDate?.getTime() ?? 0) > TimeUtils.getTodayEnd().getTime() ? 1 : 0;
        if (this.isAppointment) {
            if (!canModifyRegistration) {
                Toast.show("暂无预约权限", { warning: true }); // 无新增、修改挂号预约权限不可点击预约
                return;
            }
            ABCNavigator.navigateToPage(
                <AppointmentInvoicePage registrationFormItem={regFormItem} fromKanbanEntry={this.props?.viewMode} />,
                { transitionType: TransitionType.inFromBottom }
            );
        } else if (this.isRegistration) {
            if (!canModifyRegistration) {
                Toast.show("暂无挂号权限", { warning: true }); // 无新增、修改挂号预约权限不可点击挂号
                return;
            }
            ABCNavigator.navigateToPage<RegistrationDetail>(
                <DentistryInvoicePage registrationFormItem={regFormItem} fromKanbanEntry={this.props?.viewMode} />,
                { transitionType: TransitionType.inFromBottom }
            );
        }
    }
}

interface FlexibleTimeDragViewProps {
    mode?: "relative" | "absolute";
    beginRange?: string;
    dataGridHeight: number;
    dataGridWidth: number;

    detail: RegistrationDetail;

    coverLayoutMap?: Map<[string, string], [string, string, string][][]>;

    coverLayoutV2Map?: Map<
        [string, string],
        {
            start: string;
            end: string;
            regId: string;
            _column: number;
            _columnCnt: number;
            _columnSpan: number;
        }[]
    >;
    onChange?(detail: RegistrationDetail): void;
}
export class FlexibleTimeDragView extends BaseComponent<FlexibleTimeDragViewProps> {
    dataGridHeight = Sizes.dp22;

    dataGridWidth = 0;
    private showContent = true;
    private textMaxNumberLine = 3;

    ///初始化生成时间相关数据，避免计算位置信息重复处理
    endMoment: moment.Moment;
    startMoment: moment.Moment;
    startDiff = 0;
    ///end

    isMinMaxWidth = false;
    isMinMaxHeight = false;

    constructor(props: FlexibleTimeDragViewProps) {
        super(props);
        this.dataGridHeight = props.dataGridHeight;
        this.dataGridWidth = props.dataGridWidth;

        const reserveStart = props.detail.registrationFormItem?.reserveStart;
        const reserveDate = moment(props.detail.registrationFormItem?.reserveDate).format("YYYY-MM-DD");
        const reserveEnd = props.detail.registrationFormItem?.reserveEnd;
        const dateStartMoment = moment(`${reserveDate} ${props.beginRange ?? "06:00"}`);
        this.startMoment = moment(`${reserveDate} ${reserveStart}`);
        this.endMoment = moment(`${reserveDate} ${reserveEnd}`);
        this.startDiff = this.startMoment.diff(dateStartMoment, "minutes");
    }

    get statusStyle(): { color: string; backgroundColor: string } {
        //预约用红色
        if (this.props.detail.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn) {
            return {
                color: Colors.Y2,
                backgroundColor: Colors.Y7,
            };
        } else if (this.props.detail.registrationFormItem?.statusV2 == RegistrationStatusV2.diagnosed) {
            return {
                color: Colors.T1Mask30,
                backgroundColor: Colors.B10,
            };
        }
        return {
            color: Colors.B1,
            backgroundColor: Colors.B10,
        };
    }

    get registrationDetail(): RegistrationDetail | undefined {
        return this.props.detail;
    }

    computedViewLayout(
        registrationList: RegistrationDetail,
        coverLayoutMap: Map<
            [string, string],
            {
                start: string;
                end: string;
                regId: string;
                _column: number;
                _columnCnt: number;
                _columnSpan: number;
            }[]
        >
    ): { width?: number; height: number; top?: number } {
        const layout = {
            height: this.dataGridHeight,
            width: this.dataGridWidth,
        };

        {
            const diff = this.startDiff;
            if (!!diff) {
                Object.assign(layout, { top: (Math.max(0, diff) / 15) * this.dataGridHeight });
            }
        }

        {
            const diff = this.endMoment.diff(this.startMoment, "minutes");
            if (!!diff) {
                layout.height = Math.max(1, (diff + Math.min(0, this.startDiff)) / 15) * this.dataGridHeight;
            }
        }

        if (!!coverLayoutMap.size) {
            for (const coverMapItem of [...coverLayoutMap.values()]) {
                const _coverMapItemIndex = coverMapItem.find((item) => {
                    return item.regId === registrationList.id;
                });
                if (!!_coverMapItemIndex) {
                    const { _column, _columnSpan, _columnCnt } = _coverMapItemIndex;
                    layout.width = (this.dataGridWidth - 20) / _columnCnt;
                    Object.assign(layout, {
                        left: Math.max(0, _column - 1) * layout.width,
                        width: layout.width * Math.max(1, _columnSpan),
                    });
                }
            }
        }
        this.showContent = layout.width > Sizes.dp16;
        this.textMaxNumberLine = Math.max(Math.floor(layout.height / this.dataGridHeight) - 1, 1);
        this.isMinMaxWidth = layout.width <= Sizes.dp24;
        this.isMinMaxHeight = layout.height <= this.dataGridHeight;
        return layout;
    }

    renderPatientView(): JSX.Element {
        if (!this.showContent) return <View />;
        const { color } = this.statusStyle;
        const { patient } = this.registrationDetail ?? {};
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, { flexShrink: 1 }]}>
                <Text
                    style={[
                        DeviceUtils.isAndroid() ? { paddingTop: Sizes.dp3 } : {},
                        TextStyles.t12BB.copyWith({ color: color, lineHeight: Sizes.dp12 }),
                    ]}
                    numberOfLines={this.isMinMaxWidth ? this.textMaxNumberLine + (this.isMinMaxHeight ? 0 : 1) : 1}
                    ellipsizeMode={"clip"}
                >
                    {patient?.name ?? ""}
                </Text>
                {this.registrationDetail?.registrationFormItem?.registrationCategory == 2 && (
                    <AssetImageView
                        name={"bian"}
                        style={{ marginLeft: Sizes.dp2, marginTop: -Sizes.dp1, width: Sizes.dp14, height: Sizes.dp14 }}
                    />
                )}
                <Spacer />
                <SizedBox width={Sizes.dp2} />
            </View>
        );
    }

    renderReserveTimeView(): JSX.Element {
        if (!this.showContent) return <View />;
        const { color } = this.statusStyle;
        const { registrationFormItem } = this.registrationDetail ?? {};
        return (
            <View style={{ flex: 1 }}>
                <Text
                    style={TextStyles.t11NT2.copyWith({ color: color, lineHeight: Sizes.dp12 })}
                    numberOfLines={this.textMaxNumberLine}
                    ellipsizeMode={"clip"}
                >
                    {`${registrationFormItem?.reserveStart ?? ""}  ${this.reserveProductDisplay()}`}
                </Text>
            </View>
        );
    }

    reserveProductDisplay(): string {
        const { registrationFormItem } = this.registrationDetail ?? {};
        let str = `${registrationFormItem?.registrationProducts?.map((item) => item.displayName).join("、") ?? ""}`;
        if (!!str) {
            str += " ";
        }
        str += `${registrationFormItem?.visitSourceRemark ?? ""}`;
        return str;
    }

    createBasePosition(): Style {
        const { mode = "absolute" } = this.props;
        if (mode === "absolute" && !!this.props.coverLayoutV2Map) {
            return {
                ...this.computedViewLayout(this.props.detail, this.props.coverLayoutV2Map),
                position: "absolute",
            };
        } else {
            return {
                position: "relative",
                width: this.dataGridWidth,
                ...this.computedViewLayout(this.props.detail, new Map()),
            };
        }
    }

    render(): JSX.Element {
        const { backgroundColor } = this.statusStyle;
        const layout = this.createBasePosition();
        return (
            <View
                style={[
                    layout,
                    {
                        paddingHorizontal: Sizes.dp2,
                    },
                ]}
            >
                <AbcView
                    style={[
                        Sizes.paddingLTRB(Sizes.dp4, 0),
                        {
                            flex: 1,
                            backgroundColor: backgroundColor,
                            borderRadius: Sizes.dp2,
                        },
                    ]}
                    onClick={() => {
                        // 打开预约详情
                        this.props.onChange?.(this.props.detail);
                    }}
                >
                    {!this.isMinMaxHeight ? (
                        <>
                            <View style={[ABCStyles.rowAlignCenter, { paddingTop: Sizes.dp3 }]}>{this.renderPatientView()}</View>
                            <View style={[ABCStyles.rowAlignCenter, { flexWrap: "wrap", marginTop: Sizes.dp2 }]}>
                                {!this.isMinMaxWidth && this.renderReserveTimeView()}
                            </View>
                        </>
                    ) : (
                        <>
                            <View style={[ABCStyles.rowAlignCenter, { paddingTop: Sizes.dp3 }]}>
                                {this.renderPatientView()}
                                {!this.isMinMaxWidth && this.renderReserveTimeView()}
                            </View>
                        </>
                    )}
                </AbcView>
                <View style={{ height: Sizes.dp2 }} />
            </View>
        );
    }
}

interface FixedOrderModeColItemViewProps {
    dataGridWidth: number;
    dataGridHeight: number;
    rowHeaderDisplayList: TimeSegItem[];
    pageViewColValue: PageViewColValue;

    onHandleReg(grid: RegistrationFormItem): void;
    onHandleReviewRegDetail(grid: RegistrationDetail): void;
}
export const FixedOrderModeColItemView: React.FC<FixedOrderModeColItemViewProps> = (props) => {
    const {
        dataGridWidth,
        dataGridHeight,
        rowHeaderDisplayList,
        pageViewColValue: { data: employee },

        onHandleReg,
        onHandleReviewRegDetail,
    } = props;

    //计算重叠格子内容位置信息
    const testRangeListLayoutMap: Map<[string, string], { start: string; end: string; regId: string }[]> = new Map();
    const coverRangeCountMap: Map<[string, string], string[]> = new Map<[string, string], string[]>();
    props.rowHeaderDisplayList.forEach((item, index, self) => {
        if (!(index % 4)) {
            coverRangeCountMap.set([item.timeFrom, self[index + 3].timeTo], []);
        }
    });
    employee.registrationList
        ?.sort((a, b) => {
            //moment生成两个时间段的时间差
            const _a_s = moment(a.registrationFormItem?.reserveStart ?? "", "LT"),
                _a_e = moment(a.registrationFormItem?.reserveEnd ?? "", "LT"),
                _b_s = moment(b.registrationFormItem?.reserveStart ?? "", "LT"),
                _b_e = moment(b.registrationFormItem?.reserveEnd ?? "", "LT");
            return _a_e.diff(_a_s) - _b_e.diff(_b_s);
        })
        ?.sort((a, b) => ((a.registrationFormItem?.reserveStart ?? 0) > (b.registrationFormItem?.reserveStart ?? 0) ? 1 : -1))
        ?.forEach((regDetail) => {
            const keysTestLayout = [...testRangeListLayoutMap.keys()];

            const reserveStart = regDetail.registrationFormItem?.reserveStart ?? "";
            const reserveEnd = regDetail.registrationFormItem?.reserveEnd ?? "";

            //生成格子数据统计
            const rangeKey = [...coverRangeCountMap.keys()];
            rangeKey
                .filter((_key) => {
                    //项目开始时间<格子结束时间 && 项目结束时间>格子开始时间
                    const _g_start = _key[0],
                        _g_end = _key[1];
                    return _g_end > reserveStart && reserveEnd > _g_start;
                })
                .forEach((_key) => {
                    const count = coverRangeCountMap.get(_key) ?? [];
                    count.push(regDetail.id ?? "");
                    coverRangeCountMap.set(_key, count);
                });
            //end 格子数据统计
            if (!keysTestLayout.length) {
                testRangeListLayoutMap.set(
                    [reserveStart, reserveEnd],
                    [{ start: reserveStart, end: reserveEnd, regId: regDetail.id ?? "" }]
                );
            } else {
                let __hasChange = false;
                keysTestLayout.forEach((key, index, self) => {
                    const _start = key[0],
                        _end = key[1];

                    if (_start > reserveEnd || reserveStart > _end) {
                        if (!__hasChange && index == self.length - 1) {
                            testRangeListLayoutMap.set(
                                [reserveStart, reserveEnd],
                                [{ start: reserveStart, end: reserveEnd, regId: regDetail.id ?? "" }]
                            );
                        }
                    } else {
                        const list = testRangeListLayoutMap.get(key);
                        const listTestLayout = testRangeListLayoutMap.get(keysTestLayout[index]) ?? [];
                        let _newKey = key;
                        if (!!list) {
                            listTestLayout.push({ start: reserveStart, end: reserveEnd, regId: regDetail.id ?? "" });
                            if (reserveEnd >= _end) {
                                testRangeListLayoutMap.delete(keysTestLayout[index]);
                                _newKey = [..._newKey] as [string, string];
                                _newKey[1] = reserveEnd;
                                __hasChange = true;
                            }

                            if (_start >= reserveStart) {
                                testRangeListLayoutMap.delete(keysTestLayout[index]);
                                _newKey = [..._newKey] as [string, string];
                                _newKey[0] = reserveStart;
                                __hasChange = true;
                            }

                            testRangeListLayoutMap.set(_newKey, listTestLayout ?? []);
                        }
                    }
                });
            }
        });
    //计算数据格子
    const __preTestRangeListLayoutMap: Map<
        [string, string],
        {
            start: string;
            end: string;
            regId: string;
            _column: number;
            _columnCnt: number;
            _columnSpan: number;
        }[]
    > = new Map();
    [...testRangeListLayoutMap.keys()].forEach((item) => {
        const __list = layoutDayEvents(testRangeListLayoutMap.get(item) ?? []);
        __preTestRangeListLayoutMap.set(item, __list);
    });

    //生成多余格子面板信息

    //生成当前时间线信息
    const _now = new Date().format("HH:mm");
    let currentTimeLineIndex = 0;
    props.rowHeaderDisplayList.findIndex((item, index) => {
        if (_now > item.timeFrom && _now <= item.timeTo) {
            currentTimeLineIndex = index;
        }
    });

    return (
        <View
            key={`${employee.reserveDate?.toDateString()}${employee.employeeId}`}
            style={[{ position: "relative", width: dataGridWidth }, { transform: [{ translateY: Sizes.dp8 }] }]}
        >
            {rowHeaderDisplayList?.map((item, index) => {
                return (
                    <AbcView
                        key={index}
                        style={[
                            !(index % 4) ? ABCStyles.topLine : {},
                            {
                                height: dataGridHeight,
                                marginHorizontal: Sizes.dp4,
                            },
                        ]}
                        onClick={() => {
                            const _registrationFormItem = JsonMapper.deserialize(RegistrationFormItem, {
                                doctorId: employee.employeeId,
                                doctorName: employee.employeeName,
                                reserveDate: employee?.reserveDate,
                                reserveStart: item.timeFrom,
                                reserveEnd: item.timeTo,
                                reserveTime: new Range(item.timeFrom, item.timeTo),
                            });
                            onHandleReg(_registrationFormItem);
                        }}
                    />
                );
            })}

            {employee.registrationList?.map((item) => {
                return (
                    <FlexibleTimeDragView
                        key={`${item.id}${dataGridWidth}`}
                        detail={item}
                        dataGridHeight={dataGridHeight}
                        dataGridWidth={dataGridWidth}
                        coverLayoutV2Map={__preTestRangeListLayoutMap}
                        onChange={(detail) => {
                            onHandleReviewRegDetail?.(detail);
                        }}
                    />
                );
            })}

            {[...coverRangeCountMap.keys()].map((item, index) => {
                const ids = coverRangeCountMap.get(item);
                const count = coverRangeCountMap.get(item)?.length;
                if (count && count >= 5) {
                    const _list = employee.registrationList?.filter((item) => ids?.includes(item.id ?? "")) ?? [];
                    const MaskView = (options: { list: RegistrationDetail[]; count: number; index: number }) => {
                        const _ref = useRef<AbcView>();
                        const { list, index } = options;
                        return (
                            <AbcView
                                ref={(ref) => {
                                    if (ref) {
                                        _ref.current = ref;
                                    }
                                }}
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    { top: 4 * (index + 1) * dataGridHeight - Sizes.dp10 },
                                    {
                                        position: "absolute",
                                        width: Sizes.dp10,
                                        right: Sizes.dp6,
                                        height: Sizes.dp10,
                                        borderRadius: Sizes.dp2,
                                    },
                                ]}
                                onClick={async () => {
                                    const layout = await UIUtils.measureInWindow(_ref.current);
                                    const contentHeight = 4 * dataGridHeight;
                                    const y = layout.y + layout.height - contentHeight;
                                    await FlexibleTimeShowMoreDialog.showMoreDialog(
                                        list,
                                        { ...layout, y: y, height: contentHeight },
                                        item[0],
                                        (result) => {
                                            ABCNavigator.pop();
                                            delayed(100).subscribe(() => {
                                                onHandleReviewRegDetail?.(result);
                                            });
                                        }
                                    );
                                }}
                            >
                                <IconFontView
                                    name={"added "}
                                    size={Sizes.dp10}
                                    style={{}}
                                    color={Colors.theme2Mask12}
                                    onClick={async () => {
                                        const layout = await UIUtils.measureInWindow(_ref.current);
                                        const contentHeight = 4 * dataGridHeight;
                                        const y = layout.y + layout.height - contentHeight;
                                        await FlexibleTimeShowMoreDialog.showMoreDialog(
                                            list,
                                            { ...layout, y: y, height: contentHeight },
                                            item[0],
                                            (result) => {
                                                ABCNavigator.pop();
                                                delayed(100).subscribe(() => {
                                                    onHandleReviewRegDetail?.(result);
                                                });
                                            }
                                        );
                                    }}
                                />
                            </AbcView>
                        );
                    };
                    return <MaskView key={index} list={_list} count={count} index={index} />;
                }
            })}

            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    { position: "absolute", left: 0, right: 0 },
                    { top: dataGridHeight * (currentTimeLineIndex + 1) },
                    { height: Sizes.dp1 },
                    // !options?.showView ? { opacity: 0 } : {},
                ]}
            >
                <View style={[{ flex: 1 }, { height: Sizes.dp1, backgroundColor: Colors.mainColorMask60 }]} />
            </View>
        </View>
    );
};
