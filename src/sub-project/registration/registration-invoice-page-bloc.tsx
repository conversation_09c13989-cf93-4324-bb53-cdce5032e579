/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { BehaviorSubject, of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { GetOutpatientRevisitStatusRsp, RegistrationAgent, RegistrationDetailReq } from "./data/registration-agent";
import {
    DEFAULT_DOCTOR_ID,
    FocusItemKeys,
    OldReserveInfo,
    PayStatusV2,
    RegistrationDesignatedDoctorInfo,
    RegistrationDesignatedTime,
    RegistrationDesignatedTimeScheduleInterval,
    RegistrationDetail,
    RegistrationDiffForRevisitedType,
    RegistrationDoctorEnableCategories,
    RegistrationFee,
    RegistrationFormItem,
    RegistrationInvoiceType,
    RegistrationPageSourceType,
    RegistrationProducts,
    RegistrationRevisitStatus,
    RegistrationStatusV2,
    VisitSourceBaseInfo,
} from "./data/bean";
import { Department, Doctor, RegistrationDataProvider } from "./data/registration";
import { showBottomSheet, showOptionsBottomSheet } from "../base-ui/dialog/bottom_sheet";
import _ from "lodash";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { Toast } from "../base-ui/dialog/toast";
import { ABCNavigator, TransitionType } from "../base-ui/views/abc-navigator";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { ClinicAgent, EmployeesMeConfig, RegAndTherapyConfig } from "../base-business/data/clinic-agent";
import { MedicalRecord, Patient, PatientSource } from "../base-business/data/beans";
import { CrmAgent } from "../patients/data/crm-agent";
import {
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeInvoiceDetailData,
    ChargePayData,
    ChargeSettlementExceptionItem,
    ChargeStatus,
    CouponPromotion,
    PatientCardPromotion,
    PatientPointsInfo,
    PayMethod,
} from "../charge/data/charge-beans";
import { ChargeUtils } from "../charge/utils/charge-utils";
import { PatientAgent } from "../base-business/data/patient-agent";
import { Pair, Range } from "../base-ui/utils/value-holder";
import { RegistrationAppointmentDoctorListPage } from "./registration-appointment-doctor-list-page";
import { OutpatientAgent } from "../outpatient/data/outpatient";
import { ChargePayPage } from "../charge/charge-pay-page";
import { AbcMap } from "../base-ui/utils/abc-map";
import { URLProtocols } from "../url-dispatcher";
import { ChargeInvoiceRefundDialog } from "../charge/charge-invoice-refund-dialog";
import { RegistrationSuccessDialog, RegistrationSuccessDialogType } from "./views/registration-success-dialog";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr, TimeUtils } from "../common-base-module/utils";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { Text, View } from "@hippy/react";
import { SelectMemberInfo } from "../charge/view/promotion-card-view";
import { TherapySignInStatus } from "./appointment/data/appointment-bean";
import {
    DataPermission,
    GetClinicBasicSetting,
    GetClinicMedicalRecordConfig,
    OnlinePropertyConfigProvider,
} from "../data/online-property-config-provder";
import { RegisterTimeDialog } from "./registration-time-dialog";
import { PatientSourceTypeSelectDialog } from "../outpatient/patient-edit-page/patient-source-type-select-dialog";
import { RegistrationUtils } from "./utils/registration-utils";
import { AbnormalTransactionList, ChargeAgent } from "../charge/data/charge-agent";
import { ChargeRefundDialog } from "../charge/view/charge-refund-dialog";
import { ChargeAbnormalDialog } from "../charge/view/charge-abnormal-dialog";
import { ABCUtils } from "../base-ui/utils/utils";
import { TherapyAppointmentAgent } from "./appointment/data/therapy-appointment-agent";
import { userCenter } from "../user-center";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { DentistrySearchProject } from "./dentistry/views/dentistry-search-project";
import {
    DentistryClinicProduct,
    DentistryConfig,
    RegistrationDailyReserveStatusRsp,
    RegistrationInfoModifyRsp,
    RegistrationLocalTimeCountRsp,
    RegistrationReserveStatus,
    RegistrationType,
    ReserveStartTime,
} from "./dentistry/data/bean";
import { DentistryRemarkInputPage } from "./dentistry/views/dentistry-remark-input-page";
import { DiscountCardDialog } from "../views/discount-card-views/discount-card-dialog";
import { DentistryAgent } from "./dentistry/data/dentistry-agent";
import { AbcCalendar } from "../base-ui/abc-app-library/calendar/calendar-static";
import { DentistryInvoicePage } from "./dentistry/dentistry-invoice-page";
import { DentistryPreDiagnosisDialog } from "./dentistry/views/dentistry-pre-diagnosis-dialog";
import { FixedSourceTime } from "./dentistry/views/fixed-source-time";
import moment from "moment";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { PatientRegAuditStatus } from "../patients/data/crm-bean";
import abcI18Next from "../language/config";
import { ExaminationsAgent } from "../data/examinations/examinations-agent";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";

class _Event extends BlocEvent {}

class _EventModifyRegistrationType extends _Event {
    type: number;

    constructor(type: number) {
        super();
        this.type = type;
    }
}

class _EventModifyRegistrationDepartment extends _Event {}

class _EventModifyRegistrationDoctor extends _Event {}

class _EventModifyRegistrationTime extends _Event {}

class _EventModifyRegistrationFee extends _Event {
    fee: number; // 修改诊费

    constructor(fee: number) {
        super();
        this.fee = fee;
    }
}

class _EventModifyMedicalRecordCard extends _Event {
    medicalRecord: MedicalRecord;

    constructor(medicalRecord: MedicalRecord) {
        super();
        this.medicalRecord = medicalRecord;
    }
}

class _EventRegistrationSignIn extends _Event {}

export class State extends BaseLoadingState {
    loading = true;
    type: RegistrationInvoiceType = RegistrationInvoiceType.create;
    detail?: RegistrationDetail;
    _detail?: RegistrationDetail;
    cloneDetail?: RegistrationDetail;

    pay = { fee: 0, memberId: "", receivable: 0 }; //本地应该收和实收结构

    //-----//
    hasChange = false;

    departments?: Array<Department>;
    _currentDepartmentDoctors: Array<Doctor> = [];
    currentSelectedDoctors?: Doctor;
    clinicRegistrationCategories?: RegistrationDoctorEnableCategories;
    doctorRegistrationCategories?: RegistrationDoctorEnableCategories;
    showStopDiagnose = false; // 展示停诊
    get currentDepartmentDoctors(): Array<Doctor> | undefined {
        const bzdys = this._currentDepartmentDoctors?.find((f) => f.doctorName === "不指定医生");
        const notCheckDoctor = JsonMapper.deserialize(Doctor, {
            isScheduled: this.isEnableNoneScheduleRegistration ? 1 : 0,
            ...bzdys,
            fee: this.pay.fee,
        });
        let list: Doctor[] = [];
        list = list.concat(
            !!bzdys ? [notCheckDoctor] : [],
            this._currentDepartmentDoctors?.filter((f) => f.doctorName !== "不指定医生")
        );
        return list;
    }

    set currentDepartmentDoctors(list: Array<Doctor> | undefined) {
        const { doctorId } = this.detail?.registrationFormItem ?? {};
        this._currentDepartmentDoctors = list ?? [];
        const isEnableNoneScheduleRegistration = this.isEnableNoneScheduleRegistration;
        if (!isEnableNoneScheduleRegistration) {
            this._currentDepartmentDoctors = (list ?? []).filter((item) => item.isScheduled);
            if (!this._currentDepartmentDoctors.find((item) => item.doctorId == doctorId)) {
                if (this.detail?.registrationFormItem) {
                    this.detail!.registrationFormItem!.doctorId = undefined;
                    this.detail!.registrationFormItem!.doctorName = undefined;
                }
            }
        }
    }

    employeeConfig?: RegAndTherapyConfig;

    //算费配置
    chargeConfig?: ChargeConfig;

    clinicFieldConfig?: GetClinicMedicalRecordConfig;

    isOpenContinueDiagnoseWithoutReg?: boolean;

    get shouldRegisteredBargain(): boolean {
        return !!this.chargeConfig?.reservationRegisteredBargainSwitch || !!userCenter.clinic?.isManager;
    }

    focusKey = "";
    showErrorHint = false;

    isEnableEditRevisitStatus?: boolean;

    abnormalList?: AbnormalTransactionList[]; //非社保异常列表

    discountTotalPrice?: number; //优惠明细价格
    localTimeCountList?: RegistrationLocalTimeCountRsp[]; //指定医生在指定科室下挂号数
    dailyReserveStatusList?: RegistrationDailyReserveStatusRsp[]; //医生指定科室日期范围内每日号源状态集合
    registrationTimeRangeList?: RegistrationDesignatedTimeScheduleInterval[]; //固定号源模式下时间段信息
    selectOrderNoTimeList?: RegistrationDesignatedTimeScheduleInterval[]; //固定模式下号源时间段列表
    registrationTimeList?: RegistrationDesignatedTimeScheduleInterval[]; //固定模式下号源时间段列表

    diagnoseCount?: number; //就诊历史个数
    //门诊挂号、理疗预约的预约设置
    registrationConfig?: DentistryConfig;

    visitDuration?: ReserveStartTime = {
        //初诊就诊时长（1、未选择项目时，根据初复诊时长来定；2、选择了项目，则以选中的项目中最长的时间为准）
        hour: 0,
        min: 0,
    };
    reVisitDuration?: ReserveStartTime = {
        //复诊就诊时长（1、未选择项目时，根据初复诊时长来定；2、选择了项目，则以选中的项目中最长的时间为准）
        hour: 0,
        min: 0,
    };
    employeesMeConfig?: EmployeesMeConfig;

    dataPermission?: DataPermission; // 数据权限

    clinicBasicSetting?: GetClinicBasicSetting;

    outpatientRevisitStatusRsp?: GetOutpatientRevisitStatusRsp;

    settlementExceptionList?: ChargeSettlementExceptionItem[]; // 结算异常列表

    // 医保退费异常
    get shebaoRefundException(): boolean {
        const abnormalList = this.settlementExceptionList?.filter((t) => t.payMode == PayMethod.payHealthCard);
        //   优先展示退费异常
        return !!abnormalList?.some((t) => t.shebaoRefundAbnormal);
    }

    /**
     * 是短期内再次就诊挂号费不同模式
     */
    get isShortRevisitsDifferent(): boolean {
        return !!this.doctorRegistrationCategories?.registrationFees?.some(
            (fee) => fee?.isDiffForRevisited == RegistrationDiffForRevisitedType.shortRevisitsDifferent
        );
    }

    /**
     * 是否X天内首诊(反之再诊)
     */
    isFirstRevisitWithinDays(options: { departmentId: string; doctorId: string }): boolean {
        const { departmentId, doctorId } = options;
        const matchingFee = this.doctorRegistrationCategories?.registrationFees?.find(
            (fee) => fee.departmentId === departmentId && fee.doctorId === doctorId
        );
        const effectiveDays = matchingFee?.revisitedFeeCustomUseRule?.effectiveDays ?? 0;

        if (!this.outpatientRevisitStatusRsp || !this.outpatientRevisitStatusRsp.lastDiagnosedTime) {
            return true;
        }

        return this.outpatientRevisitStatusRsp.isFirstRevisitWithinDays({
            reserveDate: this.detail?.registrationFormItem?.reserveDate,
            effectiveDays: effectiveDays,
        });
    }

    //能够查看患者手机号
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeePatientMobile;
    }

    //能够修改、取消挂号预约
    get canModifyRegistration(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyRegistration;
    }

    // 号源约满后提示加号
    get canRemindAdditionalOrderNo(): boolean {
        return !!this.registrationConfig?.isRemindAdditionalOrderNo;
    }

    // 算号显示+号
    get isAdditional(): boolean {
        return !!this.detail?.registrationFormItem?.isAdditional;
    }

    //挂号预约能查看就诊历史
    get canViewDiagnoseHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.medicalHistory;
    }

    get isCreate(): boolean {
        return this.type == RegistrationInvoiceType.create;
    }

    get chargeFromRegistration(): boolean {
        return this.detail?.chargeSheet?.type === 1;
    }

    get hasCharged(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }

    get isHasEditCharge(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return !(
            (registrationFormItem?.payStatusV2 === PayStatusV2.notPaid || registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid) &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.diagnosed &&
            this.chargeFromRegistration
        );
    }

    //是否部分收费
    get hasPartedPaid(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return (
            registrationFormItem?.payStatusV2 === PayStatusV2.partedPaid &&
            (registrationFormItem.statusV2 ?? 0) < RegistrationStatusV2.refunded &&
            this.chargeFromRegistration
        );
    }

    //能否修改收费相关信息（禁用：退号 || 已收费 || 已退费）
    get disabledEditCharge(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        return (
            (registrationFormItem?.payStatusV2 ?? 0) > PayStatusV2.notPaid ||
            (registrationFormItem?.statusV2 ?? 0) >= RegistrationStatusV2.refunded ||
            (this.detail?.chargeSheet?.status ?? 0) > ChargeStatus.unCharged
        );
    }

    get medicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint,
            presentHistory: detail?.presentHistory,
            pastHistory: detail?.pastHistory,
            physicalExamination: detail?.physicalExamination,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
        });
    }

    //口腔诊所预诊
    get dentistryMedicalRecord(): MedicalRecord {
        const detail = this.detail;
        return JsonMapper.deserialize(MedicalRecord, {
            chiefComplaint: detail?.chiefComplaint,
            presentHistory: detail?.presentHistory,
            pastHistory: detail?.pastHistory,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
            dentistryExaminations: detail?.dentistryExaminations,
            physicalExamination: detail?.physicalExamination,
            preDiagnosisAttachments: detail?.preDiagnosisAttachments,
        });
    }

    get disabledMR(): boolean {
        // 已退已诊不能修改患者历史
        return (this.detail?.registrationFormItem?.statusV2 ?? 0) >= RegistrationStatusV2.diagnosed;
    }

    /**
     * @description 禁用修改初复诊
     */
    get disabledEditRevisitStatus(): boolean {
        // 连锁禁用了编辑
        if (!this.isEnableEditRevisitStatus) {
            return true;
        }
        //口腔诊所、升级挂号预约诊所，已诊、回诊状态下，初/复诊可编辑
        const statusV2 = this.detail?.registrationFormItem?.statusV2;
        if (userCenter.isAllowedRegUpgrade) {
            return (statusV2 ?? 0) > RegistrationStatusV2.continueDiagnose;
        }
        return (statusV2 ?? 0) >= RegistrationStatusV2.diagnosed;
    }

    // 允许修改就诊推荐的状态 true 可以 false 不可以
    // 【待签到，待付款未就诊，待就诊，未知】四种状态下允许修改就诊推荐 【完成诊断后的状态都不能修改】
    get canModifyRecommendation(): boolean {
        const registrationFormItem = this.detail?.registrationFormItem;
        const { waitingSignIn, waitingDiagnose, waitingPay, unknown } = RegistrationStatusV2;
        return [waitingSignIn, waitingDiagnose, waitingPay, unknown].includes(registrationFormItem?.statusV2 ?? 999);
    }

    // 是否显示回诊按钮
    get canBackDirectRegistration(): boolean {
        // 状态为已诊+就诊时间是今天+开启回诊配置
        const { statusV2, reserveDate } = this.detail?.registrationFormItem ?? {};
        return (
            statusV2 == RegistrationStatusV2.diagnosed &&
            !!reserveDate &&
            TimeUtils.isToday(reserveDate!) &&
            !!this.isOpenContinueDiagnoseWithoutReg
        );
    }

    // 灵活模式
    get isFlexibleMode(): boolean {
        return !!this.registrationConfig?.isFlexibleMode;
    }

    //号源模式（固定模式）
    get isFixedMode(): boolean {
        return !!this.registrationConfig?.isFixedMode;
    }

    get isAccuratePart(): boolean {
        return !!this.registrationConfig?.isAccuratePart;
    }

    get isCustomPart(): boolean {
        return !!this.registrationConfig?.isCustomPart;
    }

    // 签到取号
    get isSignInGetOrderNo(): boolean {
        return !!this.registrationConfig?.isSignInGetOrderNo;
    }

    // 是否是老版本挂号预约的详情挂号单
    get oldVersionRegistrationForm(): boolean {
        return !userCenter.isAllowedRegUpgrade && this.type == RegistrationInvoiceType.detail;
    }

    /**
     * 是否开启多号种
     */
    get enableRegistrationCategories(): boolean {
        return !!this.clinicRegistrationCategories?.enableRegistrationCategories;
    }

    /**
     * 可使用号种列表
     */
    get doctorRegistrationCategoriesList(): RegistrationFee[] {
        let list = (this.doctorRegistrationCategories ?? this.clinicRegistrationCategories)?.registrationFees ?? [];
        if (!!this.detail?.registrationFormItem?.isReserved) {
            list = list.filter((i) => i.registrationCategory != 2);
        }
        return list;
    }

    //允许未排班挂号预约
    get isEnableNoneScheduleRegistration(): boolean {
        return !this.registrationConfig || this.registrationConfig.isEnableNoneScheduleRegistration;
    }

    get checkDoctorCurrentTimeHasSchedule(): boolean {
        if (this.isFlexibleMode) {
            const { isScheduled, scheduledRegistrationCategories } = this.currentSelectedDoctors ?? {};
            return (
                this.isEnableNoneScheduleRegistration ||
                (!!isScheduled && !!scheduledRegistrationCategories?.includes(this.detail?.registrationFormItem?.registrationCategory ?? 0))
            );
        }
        return (
            this.isEnableNoneScheduleRegistration ||
            (!!this.currentSelectedDoctors?.isScheduled &&
                !!this.registrationTimeRangeList?.find((item) => item.timeOfDay == this.detail?.registrationFormItem?.timeOfDay))
        );
    }

    //能够修改支付方式
    get canSeeModifyPayMode(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.registration?.isCanSeeModifyPayMode;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToErrorViewState extends State {
    static fromState(state: State): ScrollToErrorViewState {
        const newState = new ScrollToErrorViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class RegistrationInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<RegistrationInvoicePageBloc | undefined>(undefined);
    canSupRecord: boolean;

    static fromContext(context: RegistrationInvoicePageBloc): RegistrationInvoicePageBloc {
        return context;
    }

    private _doctorId: string | undefined;
    private _departmentId: string | undefined;
    private _oldReserveInfo: OldReserveInfo | undefined;
    private _patientInfo?: Patient;
    private _calculatePriceTrigger: Subject<boolean> = new Subject<boolean>();
    private _registrationInvoiceDetailTrigger = new BehaviorSubject<{ id: string; isInit?: boolean }>({
        id: "",
        isInit: false,
    });
    private _patientRevisitStatusTrigger: Subject<string> = new Subject<string>();
    private _getPatientBaseDetailTrigger: Subject<string> = new Subject<string>();
    private _registrationLocalTimeCountTrigger: Subject<string> = new Subject<string>();
    private _registrationDailyReserveStatusTrigger: Subject<string> = new Subject<string>();
    private _registrationDesignatedTimeTrigger: Subject<boolean> = new Subject<boolean>();
    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史
    private _setPatientRegAuditStatusSubject = new Subject<number>();

    private id?: string;
    private _source: RegistrationPageSourceType;
    private _assistantList: string[];

    private _registrationFormItem?: RegistrationFormItem;
    private defaultRegistrationTimeOptions: {
        id: number;
        title: string;
        timeOfDay: string;
        timeOfDayCount?: number;
        range: Range<string>;
    }[] = [
        {
            id: 1,
            title: "今天",
            timeOfDay: "上午",
            timeOfDayCount: undefined,
            range: new Range<string>("00:00", "12:00"),
        },
        {
            id: 2,
            title: "今天",
            timeOfDay: "下午",
            timeOfDayCount: undefined,
            range: new Range<string>("12:00", "18:00"),
        },
        {
            id: 3,
            title: "今天",
            timeOfDay: "晚上",
            timeOfDayCount: undefined,
            range: new Range<string>("18:00", "24:00"),
        },
        {
            id: 4,
            title: "指定时间",
            timeOfDay: "",
            timeOfDayCount: undefined,
            range: new Range<string>("00:00", "24:00"),
        },
    ];

    constructor(options?: {
        id?: string;
        doctorId?: string;
        departmentId?: string;
        oldReserveInfo?: OldReserveInfo;
        patient?: Patient;
        source?: RegistrationPageSourceType;
        assistantList?: string[];
        registrationFormItem?: RegistrationFormItem;
        fromKanbanEntry?: boolean;
        type?: RegistrationInvoiceType;
    }) {
        super();

        this.id = options?.id;
        this._doctorId = options?.doctorId;
        this._departmentId = options?.departmentId;
        this._oldReserveInfo = options?.oldReserveInfo;
        this._patientInfo = options?.patient;
        this._source = options?.source ?? RegistrationPageSourceType.normal;
        this._assistantList = options?.assistantList ?? [];
        this._registrationFormItem = options?.registrationFormItem;

        if (options?.type != undefined) {
            this.innerState.type = options.type;
        }

        this.canSupRecord = !!options?.registrationFormItem?.reserveStart;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;

    // 是复诊预约么
    get isAgain(): boolean {
        return this._source == RegistrationPageSourceType.again;
    }

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);

        map.set(_EventModifyRegistrationPatient, this._mapEventModifyRegistrationPatient); // 修改患者
        map.set(_EventCalculateOrderNo, this._mapEventCalculateOrderNo); // 计算当前展示号数

        map.set(_EventRefundedRegistrationFee, this._mapEventRefundedRegistrationFee); // 退还挂号费
        map.set(_EventRefundedRegistration, this._mapEventRefundedRegistration);
        map.set(_EventSaveRegistration, this._mapEventSaveRegistration); // 保存挂号
        map.set(_EventFinishRegistration, this._mapEventFinishRegistration); // 完成挂号
        map.set(_EventBackDirectRegistration, this._mapEventBackDirectRegistration); // 回诊挂号
        map.set(_EventRegistrationCharge, this._mapEventRegistrationCharge); //（保存挂号费）
        map.set(_EventUpdateMemberCard, this._mapEventUpdateMemberCard); // 更新会员卡

        map.set(_EventUpdatePromotions, this._mapEventUpdatePromotions); // 更新促销活动
        map.set(_EventModifyRegistrationVisitSource, this._mapEventModifyRegistrationVisitSource); // 修改就诊来源
        map.set(_EventModifyRegistrationVisitSourceRemark, this._mapEventModifyRegistrationVisitSourceRemark); // 修改就诊来源备注
        map.set(_EventModifyRevisitStatus, this._mapEventModifyRevisitStatus); // 修改重访状态
        map.set(_EventModifyRegistrationCategory, this._mapEventModifyRegistrationCategory); // 修改重访状态
        map.set(_EventPatientCardPromotion, this._mapEventPatientCardPromotion); //更新卡项使用情况

        map.set(_EventHandleChargeAbnormal, this._mapEventHandleChargeAbnormal);
        map.set(_EventAbnormalRefund, this._mapEventAbnormalRefund);
        map.set(_EventSearchProduct, this._mapEventSearchProduct); //选择预约项目
        map.set(_EventModifyFixedSourceDate, this._mapEventModifyFixedSourceDate); //固定模式--修改日期
        map.set(_EventModifyFixedTimeRange, this._mapEventModifyFixedTimeRange); //固定模式--预约类型时间段
        map.set(_EventModifyFixedSourceTime, this._mapEventModifyFixedSourceTime); //固定模式--修改时间
        map.set(_EventDiscountDetail, this._mapEventDiscountDetail); //优惠明细弹窗
        map.set(_EventChangeDate, this._mapEventChangeDate); //改变当前日期
        map.set(_EventUpdateRegistrationInfo, this._mapEventUpdateRegistrationInfo); //更新就诊信息
        map.set(_EventSelectTimeRange, this._mapEventSelectTimeRange); //选择预诊时间
        map.set(_EventCompletePreDiagnosis, this._mapEventCompletePreDiagnosis); // 完成预诊
        map.set(_EventUpdatePatient, this._mapEventUpdatePatient); // 完成预诊
        map.set(_EventCancelPayment, this._mapEventCancelPayment); // 取消支付（解锁）

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private resetRegistrationDoctor(): void {
        if (!this.innerState.detail?.registrationFormItem) return;
        this.innerState.detail.registrationFormItem!.doctorName = undefined;
        this.innerState.detail.registrationFormItem!.doctorId = undefined;
        this.innerState.detail.registrationFormItem!.orderNo = undefined;
        this.innerState.detail.registrationFormItem!.orderNoStr = undefined;

        this.resetRegistrationTime();
    }

    async *_mapEventModifyFixedSourceTime(): AsyncGenerator<State> {
        //此处先注释掉，因为执行先后顺序没办法保证数据及时返回，会导致数据丢失
        // if (this.innerState.isFixedMode) {
        //     //防止用户已经在预约界面，此时有人更改了配置，导致数据不匹配
        //     await this._registrationDesignatedTimeTrigger.next();
        // }
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail?.registrationFormItem;
        const timeStrList: { cont: string; type?: number; residueCount?: string }[] = []; //时间列表
        let initialIndex = 0; //选中索引
        let availableOrderNoTimeList: {
            orderNo?: number;
            timeOfDay?: string;
            orderNoTimeOfDay?: string;
            start?: string;
            end?: string;
            type?: number;
            residueCount?: number;
        }[] = [];
        this.innerState.selectOrderNoTimeList?.map((item) => {
            const availableList = (item.list ?? [])?.filter((k) => k.available == 1);
            const normalNoCount = availableList.filter((it) => !it.type).length;
            const xianNoCount = availableList.filter((it) => it.type === 1).length;
            const vipNoCount = availableList.filter((it) => it.type === 2).length;
            availableList?.forEach((t) => {
                availableOrderNoTimeList.push({
                    orderNo: this.innerState.isSignInGetOrderNo ? item.signInTimeFirstEnableUseOrderNo : t.orderNo,
                    timeOfDay: item.timeOfDay,
                    orderNoTimeOfDay: t.timeOfDay,
                    start: this.innerState.isAccuratePart ? t.start : item.start,
                    end: this.innerState.isAccuratePart ? t.end : item.end,
                    type: t.type,
                    residueCount: t.type ? (t.type == 1 ? xianNoCount : vipNoCount) : normalNoCount,
                });
            });
        });
        //只争对挂号类型：判断当前排班列表中是否存在（除了会员号与预留号）号源,如果不存在，则需要把算号添加进列表
        // const isExistOrderNo = availableOrderNoTimeList?.some((item) => !item?.type);
        // if (!registrationInfo?.isReserved && !isExistOrderNo && this.needCalcNumber()) {
        //     availableOrderNoTimeList.push({
        //         orderNo: registrationInfo?.orderNo,
        //         timeOfDay: registrationInfo?.timeOfDay,
        //         start: registrationInfo?.reserveTime?.start,
        //         end: registrationInfo?.reserveTime?.end,
        //         type: 0,
        //     });
        // } else {
        //     // 补入当前的号数
        //     const oldRegistrationInfo = this.innerState._detail?.registrationFormItem;
        //     if (!!oldRegistrationInfo) {
        //         availableOrderNoTimeList.push({
        //             orderNo: oldRegistrationInfo?.orderNo,
        //             timeOfDay: oldRegistrationInfo.timeOfDay,
        //             orderNoTimeOfDay: oldRegistrationInfo.orderNoTimeOfDay ?? oldRegistrationInfo.timeOfDay,
        //             start: oldRegistrationInfo.reserveTime?.start,
        //             end: oldRegistrationInfo.reserveTime?.end,
        //             type: oldRegistrationInfo?.type,
        //             residueCount: 0,
        //         });
        //     }
        // }
        if (this.innerState.isSignInGetOrderNo) {
            availableOrderNoTimeList = Array.from(
                new Set(availableOrderNoTimeList.map((item) => `${item.start}-${item.end}-${item.type}`))
            ).map((item) => ({ ...availableOrderNoTimeList.find((obj) => item === `${obj.start}-${obj.end}-${obj.type}`) }));
        }
        if (!_.isEmpty(availableOrderNoTimeList)) {
            initialIndex = availableOrderNoTimeList.findIndex((k) => {
                // 签到取号情况下，没有orderNo,根据时间段和type匹配
                return this.innerState.isSignInGetOrderNo
                    ? k.start == this.innerState.detail?.registrationFormItem?.reserveTime?.start &&
                          k.end == this.innerState.detail?.registrationFormItem?.reserveTime?.end &&
                          k.type == (this.innerState.detail?.registrationFormItem?.orderNoType ?? 0)
                    : k.orderNo == this.innerState.detail?.registrationFormItem?.orderNo &&
                          k.orderNoTimeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay;
            });
            availableOrderNoTimeList?.forEach((t) => {
                timeStrList.push({
                    cont: `${this.innerState.isSignInGetOrderNo ? "" : registrationInfo?.getDisplayOrderNumber(t?.orderNo)} ${
                        t?.start ?? ""
                    } ~ ${t?.end ?? ""}`,
                    type: t.type,
                    residueCount: this.innerState.isSignInGetOrderNo && t.residueCount ? ` 余${t.residueCount}` : "",
                });
            });
        } else if (this.innerState.isEnableNoneScheduleRegistration) {
            timeStrList.push({
                cont: `${this.innerState.isSignInGetOrderNo ? "" : registrationInfo?.displayOrderNumber} ${
                    registrationInfo?.reserveTime?.start ?? ""
                } ~ ${registrationInfo?.reserveTime?.end ?? ""}`,
            });
        }
        if (!timeStrList || _.isEmpty(timeStrList)) return;
        const resultIndex = await showBottomSheet<number | undefined>(
            <FixedSourceTime
                dataList={[]}
                list={timeStrList}
                initialIndex={initialIndex}
                enableLeaveForMember={
                    this.innerState.registrationConfig?.isFixedMode && this.innerState.registrationConfig?.isEnableLeaveForMember
                }
                enableLeaveForPC={this.innerState.registrationConfig?.isFixedMode && this.innerState.registrationConfig?.isEnableLeaveForPC}
            />
        );
        if (_.isUndefined(resultIndex) || resultIndex < 0) return;
        if (!_.isEmpty(availableOrderNoTimeList)) {
            const result = availableOrderNoTimeList?.[resultIndex];
            this.innerState.detail!.registrationFormItem!.orderNo = result?.orderNo;
            this.innerState.detail!.registrationFormItem!.timeOfDay = result?.timeOfDay;
            this.innerState.detail!.registrationFormItem!.orderNoTimeOfDay = result?.orderNoTimeOfDay;
            this.innerState.detail!.registrationFormItem!.reserveTime = {
                start: result?.start,
                end: result?.end,
            };
        }
        this.innerState.hasChange = true;
        this.update();
    }

    private initialRevisitStatus(): void {
        this._patientRevisitStatusTrigger.next();
    }

    //操作禁止提示
    private showOperateToastTip(): void {
        const isDisabledOperate = this.innerState.detail?.chargeSheet?.isDisabledOperate;
        const disabledOperateReason = this.innerState.detail?.chargeSheet?.disabledOperateReason;
        if (!!isDisabledOperate && !!disabledOperateReason) Toast.show(disabledOperateReason, { warning: true });
    }

    flexibleServiceDuration(): void {
        //已诊状态或者详情单下，切换初复诊，不改变挂号时间
        if (
            this.innerState.isFlexibleMode &&
            this.innerState.detail?.registrationFormItem?.statusV2 != RegistrationStatusV2.diagnosed &&
            this.innerState.detail?.registrationFormItem?.statusV2 != RegistrationStatusV2.continueDiagnose &&
            this.innerState.type == RegistrationInvoiceType.create
        ) {
            //当前选择的初始时间，在这个时间起点，更改时间间隔
            const selStartTime = this.innerState.detail?.registrationFormItem?.reserveTime?.start;
            //挂号时间只能为6-23
            let initialHourTime = new Date().getHours(),
                initialMinuteTime = new Date().getMinutes();
            if (!!selStartTime && selStartTime.indexOf(":") > -1) {
                const selHour = !!selStartTime.split(":")?.[0] ? Number(selStartTime.split(":")?.[0]) : new Date().getHours(),
                    selMinute = !!selStartTime.split(":")?.[1] ? Number(selStartTime.split(":")?.[1]) : new Date().getMinutes();
                initialHourTime = selHour ?? new Date().getHours();
                initialMinuteTime = selMinute ?? new Date().getMinutes();
            }
            const hourTime = initialHourTime < 6 ? 6 : initialHourTime > 23 ? 23 : initialHourTime;
            const minutesTime = initialHourTime < 6 || initialHourTime > 23 ? 0 : initialMinuteTime;
            const minutesList = [0, 15, 30, 45, 60];
            const num = minutesList.find((t) => t >= minutesTime);
            let timeRange, appointmentTime;
            if (this.innerState.detail?.__revisitStatusName == "初诊") {
                appointmentTime = this.innerState.visitDuration;
            } else if (this.innerState.detail?.__revisitStatusName == "复诊") {
                appointmentTime = this.innerState.reVisitDuration;
            }
            //时间只有0-24,所以还需要考虑大于24后的情况
            if (num == 60) {
                timeRange = new Range(
                    `${hourTime + 1 < 10 ? "0" + (hourTime + 1) : hourTime + 1 >= 24 ? "00" : hourTime + 1}:00`,
                    `${
                        (appointmentTime?.min ?? 0) == 60
                            ? (hourTime + 1 + (appointmentTime?.hour ?? 0) + 1 < 10
                                  ? "0" + (hourTime + 1 + (appointmentTime?.hour ?? 0) + 1)
                                  : hourTime + 1 + (appointmentTime?.hour ?? 0) + 1 >= 24
                                  ? "00"
                                  : hourTime + 1 + (appointmentTime?.hour ?? 0) + 1) + ":00"
                            : (hourTime + 1 + (appointmentTime?.hour ?? 0) < 10
                                  ? "0" + (hourTime + 1 + (appointmentTime?.hour ?? 0))
                                  : hourTime + 1 + (appointmentTime?.hour ?? 0) >= 24
                                  ? "00"
                                  : hourTime + 1 + (appointmentTime?.hour ?? 0)) +
                              ":" +
                              ((appointmentTime?.min ?? 0) < 10 ? "0" + (appointmentTime?.min ?? 0) : appointmentTime?.min ?? 0)
                    }`
                );
            } else {
                timeRange = new Range(
                    `${hourTime < 10 ? "0" + hourTime : hourTime >= 24 ? "00" : hourTime}:${num! < 10 ? "0" + num : num}`,
                    `${
                        (appointmentTime?.min ?? 0) + (num ?? 0) >= 60
                            ? (hourTime + (appointmentTime?.hour ?? 0) + 1 < 10
                                  ? "0" + (hourTime + (appointmentTime?.hour ?? 0) + 1)
                                  : hourTime + (appointmentTime?.hour ?? 0) + 1 >= 24
                                  ? "00"
                                  : hourTime + (appointmentTime?.hour ?? 0) + 1) +
                              ":" +
                              ((appointmentTime?.min ?? 0) + (num ?? 0) - 60 < 10
                                  ? "0" + ((appointmentTime?.min ?? 0) + (num ?? 0) - 60)
                                  : (appointmentTime?.min ?? 0) + (num ?? 0) - 60)
                            : (hourTime + (appointmentTime?.hour ?? 0) < 10
                                  ? "0" + (hourTime + (appointmentTime?.hour ?? 0))
                                  : hourTime + (appointmentTime?.hour ?? 0) >= 24
                                  ? "00"
                                  : hourTime + (appointmentTime?.hour ?? 0)) +
                              ":" +
                              ((appointmentTime?.min ?? 0) + (num ?? 0) < 10
                                  ? "0" + ((appointmentTime?.min ?? 0) + (num ?? 0))
                                  : (appointmentTime?.min ?? 0) + (num ?? 0))
                    }`
                );
            }
            this.innerState.detail!.registrationFormItem!.reserveTime = timeRange;
        }
    }

    //获取当前就诊时间
    private getReserveTime(): void {
        if (!!this.innerState.detail!.registrationFormItem?.timeOfDay) {
            const defaultReserve = this.defaultRegistrationTimeOptions.find(
                (item) => item.timeOfDay == this.innerState.detail!.registrationFormItem?.timeOfDay
            );
            this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve?.range;
        } else {
            const hours = new Date().getHours();
            let defaultReserve;
            if (hours >= 0 && hours < 12) {
                defaultReserve = this.defaultRegistrationTimeOptions[0];
            } else if (hours >= 12 && hours < 18) {
                defaultReserve = this.defaultRegistrationTimeOptions[1];
            } else {
                defaultReserve = this.defaultRegistrationTimeOptions[2];
            }
            this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve?.range;
        }
    }

    /**
     * 回诊
     */
    public requestBackDirectRegistration(): void {
        this.dispatch(new _EventBackDirectRegistration());
    }

    /**
     * 是否需要算号
     */
    private needCalcNumber(): boolean {
        return !this._registrationFormItem?.orderNo;
    }

    async _initVisitSource(): Promise<void> {
        const detail = this.innerState.detail;
        const params = JsonMapper.deserialize(VisitSourceBaseInfo, {
            visitSourceId: detail?.visitSourceId,
            visitSourceName: detail?.visitSourceName,
            visitSourceFrom: detail?.visitSourceFrom,
            visitSourceFromName: detail?.visitSourceFromName,
        });
        const reps = await RegistrationUtils.formatVisitSource(params);
        _.assign(this.innerState.detail, reps);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private _formatPatientAddress(patient: Patient): void {
        _.assign(patient, patient.address);
    }

    async calculateRegCategoriesFee(needRefresh = true): Promise<void> {
        const { revisitStatus } = this.innerState.detail ?? {};
        const { departmentId, doctorId = DEFAULT_DOCTOR_ID, referralFlag } = this.innerState.detail?.registrationFormItem ?? {};
        let { registrationCategory = 0 } = this.innerState.detail?.registrationFormItem ?? {};
        let _result;
        if (needRefresh) {
            _result = await RegistrationAgent.getRegistrationDoctorEnableCategories({
                departmentId,
                doctorId,
            }).catchIgnore();
        } else {
            _result = this.innerState.doctorRegistrationCategories;
        }
        if (!_result) return;
        this.innerState.doctorRegistrationCategories = _result;
        let currentCategory = this.innerState.doctorRegistrationCategoriesList?.find(
            (item) => item.registrationCategory == registrationCategory
        );
        if (!currentCategory) {
            //当前无匹配号种，重置registrationCategory为0
            this.innerState.detail!.registrationFormItem!.registrationCategory = 0;
            registrationCategory = 0;
            currentCategory = _result.registrationFees?.[0];
        }
        if (registrationCategory == 0 && !currentCategory) return;
        const { regUnitPrice, revisitedRegUnitPrice, referralRegUnitPrice, referralRevisitedRegUnitPrice } = currentCategory || {};
        let _regUnitPrice;
        // 判断是否为短期内再次就诊且挂号费不同模式
        if (this.innerState.isShortRevisitsDifferent) {
            _regUnitPrice = this.innerState.isFirstRevisitWithinDays({
                departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                doctorId: this.innerState.detail?.registrationFormItem?.doctorId ?? "",
            })
                ? regUnitPrice
                : revisitedRegUnitPrice;
        } else {
            // 否则，根据初复诊状态和是否有转诊标志来选择挂号费
            if (revisitStatus === RegistrationRevisitStatus.first) {
                _regUnitPrice = !!referralFlag ? referralRegUnitPrice : regUnitPrice;
            } else {
                _regUnitPrice = !!referralFlag ? referralRevisitedRegUnitPrice : revisitedRegUnitPrice;
            }
        }
        this.innerState.pay!.fee = _regUnitPrice ?? 0;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = _regUnitPrice;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = _regUnitPrice;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].expectedUnitPrice = _regUnitPrice;
        if (this.innerState.isShortRevisitsDifferent) {
            this._calculatePriceTrigger.next();
        }
        this.update();
    }

    // 第三种挂号模式下，计算挂号费
    private _calculateRegistrationFeeByThirdMode(): void {
        let { registrationCategory } = this.innerState.detail?.registrationFormItem ?? {};
        const { revisitStatus } = this.innerState.detail ?? {};
        const { referralFlag } = this.innerState.detail?.registrationFormItem ?? {};
        let currentCategory = this.innerState.doctorRegistrationCategoriesList?.find(
            (item) => item.registrationCategory == registrationCategory
        );
        if (!currentCategory) {
            //当前无匹配号种，重置registrationCategory为0
            this.innerState.detail!.registrationFormItem!.registrationCategory = 0;
            registrationCategory = 0;
            currentCategory = this.innerState.doctorRegistrationCategories?.registrationFees?.[0];
        }
        if (registrationCategory == 0 && !currentCategory) return;
        const { regUnitPrice, revisitedRegUnitPrice, referralRegUnitPrice, referralRevisitedRegUnitPrice } = currentCategory || {};
        let _regUnitPrice;
        // 判断是否为短期内再次就诊且挂号费不同模式
        if (this.innerState.isShortRevisitsDifferent) {
            _regUnitPrice = this.innerState.isFirstRevisitWithinDays({
                departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                doctorId: this.innerState.detail?.registrationFormItem?.doctorId ?? "",
            })
                ? regUnitPrice
                : revisitedRegUnitPrice;
        } else {
            // 否则，根据初复诊状态和是否有转诊标志来选择挂号费
            if (revisitStatus === RegistrationRevisitStatus.first) {
                _regUnitPrice = !!referralFlag ? referralRegUnitPrice : regUnitPrice;
            } else {
                _regUnitPrice = !!referralFlag ? referralRevisitedRegUnitPrice : revisitedRegUnitPrice;
            }
        }
        this.innerState.pay!.fee = _regUnitPrice ?? 0;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = _regUnitPrice;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = _regUnitPrice;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].expectedUnitPrice = _regUnitPrice;
    }

    private async *_mapEventModifyRegistrationPatient(event: _EventModifyRegistrationPatient): AsyncGenerator<State> {
        this._formatPatientAddress(event.patient);

        if (this.innerState.detail?.patient?.id && this.innerState.detail?.patient?.id == event.patient.id) {
            this.innerState.detail!.patient = event.patient;
            this.update();
            return;
        }
        if (event.patient?.id) {
            CrmAgent.getPatientById(event.patient.id)
                .toObservable()
                .subscribe(async (rsp) => {
                    const patient = rsp;
                    this.innerState.detail!.patient = patient;
                    this.innerState.detail!.chargeSheet!.patient = patient;
                    this.innerState.detail!.chargeSheet!.patientId = patient.id; // 必须有patient.id 才能正常显示优惠券信息
                    if (!!patient?.isMember) {
                        const member = await PatientAgent.getMemberInfoById(event.patient.id!).catchIgnore();
                        if (!!member) {
                            this.innerState.detail!.chargeSheet!.memberInfo = member;
                            this.innerState.detail!.chargeSheet!.memberId = member?.patientId ?? "";
                            this.innerState.pay.memberId = member?.patientId ?? "";
                        }
                    }
                    this._getHistoryListTrigger.next();
                    this.update();

                    Promise.all([
                        OutpatientAgent.getPastHistory(event.patient.id!), //获取pastHistoryInfo
                    ])
                        .toObservable()
                        .subscribe((rspAll) => {
                            this.innerState.detail!.pastHistory = rspAll[0];
                        })
                        .addToDisposableBag(this);
                    this.initialRevisitStatus();
                })
                .addToDisposableBag(this);
        } else {
            // 未搜索到patient.id时不显示
            this.innerState.detail!.patient = event.patient;
            this.innerState.detail!.chargeSheet!.patientId = undefined;
            this.innerState.detail!.chargeSheet!.memberInfo = undefined;
            this.innerState.detail!.chargeSheet!.memberId = undefined;
            this.innerState.pay.memberId = "";
            this.innerState.detail!.chargeSheet!.patient = event.patient;
            this.update();
            this.initialRevisitStatus();
        }
    }

    async *_mapEventModifyFixedSourceDate(): AsyncGenerator<State> {
        if (this.innerState.detail?.registrationFormItem?.isReserved) {
            // 预约类型
            this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
            const registrationInfo = this.innerState.detail!.registrationFormItem;
            const schedulingInfo = this.innerState.dailyReserveStatusList
                ?.filter((t) => t.status != RegistrationReserveStatus.notScheduling)
                ?.map((item, index) => {
                    return {
                        date: item.date!,
                        view: () => {
                            return (
                                <View key={index}>
                                    <Text
                                        style={[
                                            TextStyles.t10NT2.copyWith({
                                                color:
                                                    item.status == RegistrationReserveStatus.canAppointment
                                                        ? Colors.canAppointmentColor
                                                        : item.status == RegistrationReserveStatus.finishedDiagnosis
                                                        ? Colors.Y2
                                                        : Colors.T6,
                                            }),
                                        ]}
                                    >
                                        {item?.statusName ?? ""}
                                    </Text>
                                </View>
                            );
                        },
                    };
                });
            registrationInfo!.reserveDate = registrationInfo?.reserveDate ?? new Date();
            const selectDate = await AbcCalendar.show({
                date: registrationInfo?.reserveDate ?? new Date(),
                minDate: new Date(),
                maxDate: !registrationInfo?.isReserved ? registrationInfo?.reserveDate ?? new Date() : undefined,
                dateExtendView: schedulingInfo,
            });
            if (!selectDate) return;
            registrationInfo!.reserveDate = selectDate;
            await this.getClinicAllDoctorList(selectDate);
        } else {
            // 如果有排班可选指定时间
            const list = this.defaultRegistrationTimeOptions.filter((t) => t.title != "指定时间");
            const timeIndex = list?.findIndex((item) => {
                return item.timeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay; // 当日时间段
            });
            // 挂号类型
            const options = list;
            let result: number[] | undefined, resultIndex: number | undefined;
            if (!!userCenter.isAllowedRegUpgrade) {
                const registrationList: { cont: string; type?: number }[] = [];
                options?.map((item) => {
                    registrationList.push({
                        cont: item.title + item.timeOfDay,
                    });
                });
                resultIndex = await showBottomSheet<number | undefined>(
                    <FixedSourceTime dataList={[]} list={registrationList} initialIndex={timeIndex} />
                );
            } else {
                result = await showOptionsBottomSheet({
                    title: "选择挂号时间",
                    options: options?.map((item) => item.title + item.timeOfDay),
                    optionsWidgets: options?.map((item, index) => (
                        <View
                            key={index}
                            style={{
                                ...ABCStyles.rowAlignCenter,
                                height: Sizes.listItemHeight,
                                paddingHorizontal: Sizes.listHorizontalMargin,
                            }}
                        >
                            <Text
                                style={[
                                    TextStyles.t16NB,
                                    {
                                        flex: 1,
                                    },
                                ]}
                            >
                                {`${item.title} ${item.timeOfDay}`}
                            </Text>
                            {!_.isUndefined(item.timeOfDayCount) && (
                                <Text style={[TextStyles.t16NT2]}>{`(共${item.timeOfDayCount}号)`}</Text>
                            )}
                        </View>
                    )),
                    initialSelectIndexes: !_.isUndefined(timeIndex) ? new Set<number>([timeIndex]) : undefined, // 初始选择索引
                });
            }

            if (!!userCenter.isAllowedRegUpgrade) {
                if (_.isUndefined(resultIndex)) return;
            } else {
                if (!result || _.isEmpty(result)) return;
            }
            // 选择指定挂号时间
            if (result?.find((item) => item == 3) && options.find((item) => item.title == "指定时间")) {
                const { departmentId } = this.innerState.detail!.registrationFormItem!;
                // 选择指定时间需先选择医生
                if (this.innerState.detail?.registrationFormItem?.doctorId == undefined) return Toast.show("请先选择医生");
                // 所选医生（姓名 诊费
                const selectedDoctors = this.innerState.currentDepartmentDoctors?.find((item) => {
                    return item.doctorId == this.innerState.detail?.registrationFormItem?.doctorId;
                });
                // 所选医生未排班时提示
                if (selectedDoctors?.doctorName !== "不指定医生" && !this.innerState.detail?.registrationFormItem?.timeOfDayTotalCountMap) {
                    return Toast.show("该医生今天暂未排班");
                }

                const chooseSpecificTime = await RegisterTimeDialog.show(
                    selectedDoctors as RegistrationDesignatedDoctorInfo,
                    departmentId as string
                );

                if (!chooseSpecificTime) return;
                // 指定时间段
                this.innerState.detail!.registrationFormItem!.reserveTime = {
                    start: chooseSpecificTime.start,
                    end: chooseSpecificTime.end,
                };
                // 指定时间段号数
                this.innerState.detail!.registrationFormItem!.orderNo = chooseSpecificTime.orderNo;
                this.innerState.detail!.registrationFormItem!.doctorId = selectedDoctors?.doctorId;
            } else {
                this.innerState.detail!.registrationFormItem!.reserveTime = userCenter.isAllowedRegUpgrade
                    ? options[resultIndex!].range
                    : options[result![0]].range;
                this.innerState.detail!.registrationFormItem!.timeOfDay = userCenter.isAllowedRegUpgrade
                    ? options[resultIndex!].timeOfDay
                    : options[result![0]].timeOfDay;
                this.innerState.detail!.registrationFormItem!.reserveDate = new Date();
                if (this.innerState.isCustomPart) {
                    const availableOrderNoTimeList: {
                        orderNo?: number;
                        timeOfDay?: string;
                        start?: string;
                        end?: string;
                        type?: number;
                    }[] = [];
                    const timeOfDayList =
                        this.innerState.registrationTimeList?.filter(
                            (item) => item.timeOfDay == this.innerState.detail!.registrationFormItem!.timeOfDay
                        ) ?? [];
                    timeOfDayList.map((item) => {
                        item.list
                            ?.filter((k) => k.available == 1)
                            ?.forEach((t) => {
                                availableOrderNoTimeList.push({
                                    orderNo: t.orderNo,
                                    timeOfDay: item.timeOfDay,
                                    start: item.start,
                                    end: item.end,
                                    type: t.type,
                                });
                            });
                    });
                    const availableItem = availableOrderNoTimeList[0] ?? timeOfDayList[0];
                    this.innerState.detail!.registrationFormItem!.reserveTime = availableItem
                        ? new Range(availableItem.start, availableItem.end)
                        : this.innerState.detail!.registrationFormItem?.reserveTime;
                }
                this.innerState.detail!.registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    this.innerState.detail!.registrationFormItem!,
                    userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm,
                    RegistrationType.outpatientRegistration
                ).catch((error) => {
                    // 无可用号源不提示弹窗
                    if (error.msg === "无可用号源") {
                        this.innerState.showStopDiagnose = true; // 显示停诊状态
                        return JsonMapper.deserialize(RegistrationFormItem, {
                            ...this.innerState.detail!.registrationFormItem!,
                            doctorId: this.innerState.detail!.registrationFormItem?.doctorId,
                            doctorName: this.innerState.detail!.registrationFormItem?.doctorName,
                            departmentName: this.innerState.detail!.registrationFormItem?.departmentName,
                            departmentId: this.innerState.detail!.registrationFormItem?.departmentId,
                            timeOfDay: this.innerState.detail!.registrationFormItem?.timeOfDay,
                            reserveTime: undefined,
                        });
                    } else if (this.isDisposed) {
                        showQueryDialog("提示", errorToStr(error));
                    }
                    return undefined;
                });
                if (this.innerState.detail!.registrationFormItem?.reserveTime) {
                    this.innerState.showStopDiagnose = false;
                }
                list.forEach((item) => {
                    item.timeOfDayCount = this.innerState.detail!.registrationFormItem?.timeOfDayTotalCountMap?.get(item.timeOfDay);
                });
                this.innerState.detail!.registrationFormItem!.timeOfDay = userCenter.isAllowedRegUpgrade
                    ? options[resultIndex!].timeOfDay
                    : options[result![0]].timeOfDay;
            }
        }
        if (this.innerState.isFixedMode && !this.innerState.showStopDiagnose) {
            this._registrationDesignatedTimeTrigger.next();
        }
        this.innerState.hasChange = true;
        this.calculateRegCategoriesFee();
        this.update();
        // this.registerFeeCalculation();
    }

    @actionEvent(_EventModifyRegistrationType)
    private async *_mapEventModifyRegistrationType(event: _EventModifyRegistrationType): AsyncGenerator<State> {
        this.innerState.hasChange = true;
        const oldDetail = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        let newRegistrationFormItem = new RegistrationFormItem();

        this.innerState.detail!.registrationFormItem = new RegistrationFormItem();

        if (userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm) {
            newRegistrationFormItem = oldDetail;
        }
        const assignKey: (keyof Pick<RegistrationFormItem, "doctorName" | "doctorId" | "departmentName" | "departmentId" | "timeOfDay">)[] =
            ["doctorName", "doctorId", "departmentName", "departmentId", "timeOfDay"];
        assignKey.forEach((key) => {
            newRegistrationFormItem[key] = oldDetail[key];
        });
        newRegistrationFormItem.isReserved = event.type;
        //当前如果是挂号类型，日期选择器默认选中今天
        if (!newRegistrationFormItem.isReserved) {
            newRegistrationFormItem.reserveDate = new Date();
        }
        //当前如果是预约类型，日期默认为传入的日期
        if (!!newRegistrationFormItem.isReserved) {
            newRegistrationFormItem.reserveDate = this._registrationFormItem?.reserveDate ?? new Date();
        }
        this.innerState.detail!.registrationFormItem = newRegistrationFormItem;
        //口腔诊所、升级挂号预约诊所--预约类型（获取排班信息）
        if (userCenter.isAllowedRegUpgrade && newRegistrationFormItem.isReserved) {
            this._registrationDailyReserveStatusTrigger.next();
        }
        // 灵活时间模式-挂号模式-获取时间预约人数
        if (this.innerState.isFlexibleMode) {
            if (event.type == 0) this._registrationLocalTimeCountTrigger.next();
        }
        if (this.innerState.isFixedMode) {
            Object.assign(this.innerState.detail?.registrationFormItem, {
                reserveEnd: undefined,
                reserveStart: undefined,
                reserveTime: undefined,
                orderNo: undefined,
            });
            // 老版本的新增
            if (!userCenter.isAllowedRegUpgrade && !this?.id) {
                // 挂号类型需要调用算号
                if (event.type == 0) {
                    this.dispatch(new _EventCalculateOrderNo());
                } else {
                    Object.assign(this.innerState.detail?.registrationFormItem, {
                        reserveDate: undefined,
                    });
                }
            } else {
                this._registrationDesignatedTimeTrigger.next();
            }
        }
        this._calculatePriceTrigger.next();
        this.update();
    }

    private async _fixedModeCalculateOrderNo(): Promise<void> {
        //固定号源模式算号规则：
        //1.挂号时调用排班接口，有排班就不去调用算号接口
        //2.预约时调用排班接口
        const { detail, type } = this.innerState;
        const registrationFormItem = detail?.registrationFormItem ?? new RegistrationFormItem();
        const { isReserved, departmentId = "", doctorId, reserveDate, registrationCategory } = registrationFormItem;
        if (type == RegistrationInvoiceType.create && !!isReserved && this.needCalcNumber()) {
            Object.assign(registrationFormItem, {
                orderNo: undefined,
                reserveTime: undefined,
            });
        }
        const doctorScheduleIntervalRsp = await RegistrationAgent.getRegistrationDesignatedTime({
            departmentId,
            doctorId: !!doctorId ? doctorId : DEFAULT_DOCTOR_ID,
            forNormalRegistration: isReserved == 0 ? 1 : 0,
            workingDate: TimeUtils.formatDate(reserveDate ?? new Date()),
            registrationType: RegistrationType.outpatientRegistration,
        }).catchIgnore();
        if (!!doctorScheduleIntervalRsp) {
            const scheduleIntervals = doctorScheduleIntervalRsp.getScheduleIntervals(registrationCategory);
            this.innerState.registrationTimeRangeList = scheduleIntervals;
            this.innerState.registrationTimeList = scheduleIntervals;
            //筛选当前符合条件时段分组
            this.innerState.selectOrderNoTimeList = this.innerState.registrationTimeRangeList?.filter(
                (t) => t.timeOfDay == registrationFormItem?.timeOfDay
            );
            //详情编辑状态
            const isEdit = (type == RegistrationInvoiceType.detail || !!this._registrationFormItem) && !!registrationFormItem?.reserveTime;
            if (isEdit) {
                this.innerState.selectOrderNoTimeList = this.innerState.selectOrderNoTimeList?.map((t) => {
                    !!t?.list?.length &&
                        t?.list?.map((k) => {
                            if (k?.orderNo == registrationFormItem?.orderNo && !this.innerState.isSignInGetOrderNo) {
                                k.available = 1;
                            }
                        });
                    return t;
                });
            }

            //固定模式--挂号类型--列表进入---排班时间列表需要默认跳过会员跟预留号(type=1,2)
            const isRegisterType = !isReserved && this.needCalcNumber();
            const existTimeRangeList = _.cloneDeep(this.innerState.selectOrderNoTimeList)
                ?.filter((t) => !_.isEmpty(t?.list))
                ?.map((item) => {
                    item.list = item.list?.filter((k) => (isRegisterType ? k?.available == 1 && !k?.type : k?.available == 1));
                    return item;
                })
                ?.filter((t) => !_.isEmpty(t?.list));
            if (!!existTimeRangeList?.length) {
                const matchTimeRange =
                    existTimeRangeList?.find((item) => {
                        return !!item.list?.find(
                            (t) =>
                                t.orderNo == (!this.needCalcNumber() ? this._registrationFormItem?.orderNo : registrationFormItem?.orderNo)
                        );
                    }) ?? existTimeRangeList?.[0];
                //预约时间--是否按照精确时间预约(是--去list内层的时间段；否-取list外层的时间段)
                const preferredTime = matchTimeRange?.list?.find((t) =>
                    isEdit
                        ? !this.needCalcNumber()
                            ? this._registrationFormItem?.orderNo == t.orderNo
                            : registrationFormItem?.orderNo
                            ? t.orderNo == registrationFormItem?.orderNo
                            : t.restCount
                        : t.restCount
                );
                //已完成的单据在没有可匹配时段时，保持原样
                if (!this.id || !!preferredTime) {
                    this.innerState.detail!.registrationFormItem!.orderNo = preferredTime?.orderNo;
                    this.innerState.detail!.registrationFormItem!.reserveTime = {
                        start: this.innerState.isAccuratePart ? preferredTime?.start : matchTimeRange?.start,
                        end: this.innerState.isAccuratePart ? preferredTime?.end : matchTimeRange?.end,
                    };
                }
            }
        }
        // 挂号单详情单在没有排班信息的时候并且没有做任何更改的时候，不调用算号
        // 固定模式下才会调用算号，灵活模式不调用
        const initDetailState = !!this?.id && !this.innerState.hasChange;
        if (!initDetailState && !this.innerState.registrationTimeRangeList?.length) {
            //调用算号
        }
    }

    async *_mapEventChangeDate(): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail!.registrationFormItem;
        registrationInfo!.reserveDate = registrationInfo?.reserveDate ?? new Date();
        const schedulingInfo = this.innerState.dailyReserveStatusList
            ?.filter((t) => t.status != RegistrationReserveStatus.notScheduling)
            ?.map((item, index) => {
                return {
                    date: moment(item.date!).toDate(),
                    view: () => {
                        return (
                            <View key={index}>
                                <Text
                                    style={[
                                        TextStyles.t10NT2.copyWith({
                                            color:
                                                item.status == RegistrationReserveStatus.canAppointment
                                                    ? Colors.canAppointmentColor
                                                    : item.status == RegistrationReserveStatus.finishedDiagnosis
                                                    ? Colors.Y2
                                                    : Colors.T6,
                                        }),
                                    ]}
                                >
                                    {item?.statusName ?? ""}
                                </Text>
                            </View>
                        );
                    },
                };
            });
        const selectDate = await AbcCalendar.show({
            date: registrationInfo?.reserveDate ?? new Date(),
            minDate: new Date(),
            maxDate: !registrationInfo?.isReserved ? registrationInfo?.reserveDate ?? new Date() : undefined,
            dateExtendView: schedulingInfo,
        });
        if (!selectDate) return;
        registrationInfo!.reserveDate = selectDate;
        await this.getClinicAllDoctorList(selectDate);
        // 当前日期大于今天，则时间重置为08：00~08：30
        if (TimeUtils.getStartOfDate(selectDate ?? new Date()).getTime() > TimeUtils.getStartOfDate(new Date()).getTime()) {
            registrationInfo.reserveTime = {
                start: "08:00",
                end: "08:30",
            };
        } else {
            registrationInfo.reserveTime = undefined;
            this.flexibleServiceDuration();
        }
        this.calculateRegCategoriesFee(); // 修改日期后需重新计算费用
        this._registrationLocalTimeCountTrigger.next();
        this.innerState.hasChange = true;
        this.update();
    }

    @actionEvent(_EventModifyRegistrationDepartment)
    private async *_mapEventModifyRegistrationDepartment(): AsyncGenerator<State> {
        const { departments, detail } = this.innerState;
        const departmentIndex = departments?.findIndex((item) => {
            return item.departmentId == detail?.registrationFormItem?.departmentId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择科室",
            options: departments?.map((item) => item.departmentName ?? "其他"),
            initialSelectIndexes: !_.isUndefined(departmentIndex) ? new Set<number>([departmentIndex]) : undefined,
            height: pxToDp(375),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (!result || _.isEmpty(result)) return;
        this.innerState.hasChange = true;
        const currentDepartment = this.innerState.departments?.[result[0]];
        this.innerState.currentDepartmentDoctors = currentDepartment?.doctors;
        //修改科室、医生和预约时间
        const registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        registrationFormItem.departmentId = currentDepartment?.departmentId;
        registrationFormItem.departmentName = currentDepartment?.departmentName;
        registrationFormItem.doctorName = undefined;
        registrationFormItem.doctorId = undefined;
        this.innerState.currentSelectedDoctors = currentDepartment?.doctors?.find((item) => item.doctorId === DEFAULT_DOCTOR_ID);
        if (registrationFormItem.isReserved) registrationFormItem.reserveDate = undefined;
        this.innerState.detail!.registrationFormItem = registrationFormItem;
        //初始化初复诊转态
        this.initialRevisitStatus();
        this.calculateRegCategoriesFee();
        //口腔诊所、升级挂号预约诊所--预约类型（获取排班信息）
        if ((userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm) && registrationFormItem.isReserved) {
            this._registrationDailyReserveStatusTrigger.next();
        }
        // 灵活模式下，更改科室后，需要更新每个时间段预约的人数
        if (this.innerState.isFlexibleMode) {
            await this.getClinicAllDoctorList();
            this._registrationLocalTimeCountTrigger.next();
        }
        // 固定模式下，挂号需要去拉取当前选择医生的排班信息
        if (this.innerState.isFixedMode) {
            if (!registrationFormItem.isReserved) {
                this._registrationDesignatedTimeTrigger.next();
            }
        }
    }

    //修改挂号时间-属于老版本专属逻辑
    @actionEvent(_EventModifyRegistrationTime)
    private async *_mapEventModifyRegistrationTime(): AsyncGenerator<State> {
        let registrationFormItem = this.innerState.detail?.registrationFormItem;
        if (registrationFormItem?.isReserved) {
            // 预约类型
            const result = await RegistrationAppointmentDoctorListPage.show(registrationFormItem);
            if (!result || _.isEmpty(result)) return;
            this.innerState.hasChange = true;
            this.innerState.detail!.registrationFormItem = result;
            this.dispatch(new _EventModifyRegistrationFee(result?.fee ?? 0));
        } else {
            // 如果有排班可选指定时间
            const list = this.defaultRegistrationTimeOptions;
            const timeIndex = list?.findIndex((item) => {
                return item.timeOfDay == registrationFormItem?.timeOfDay; // 当日时间段
            });
            // 挂号类型
            const options = list;

            const result = await showOptionsBottomSheet({
                title: "选择挂号时间",
                options: options?.map((item) => item.title + item.timeOfDay),
                optionsWidgets: options?.map((item, index) => (
                    <View
                        key={index}
                        style={{
                            ...ABCStyles.rowAlignCenter,
                            height: Sizes.listItemHeight,
                            paddingHorizontal: Sizes.listHorizontalMargin,
                        }}
                    >
                        <Text
                            style={[
                                TextStyles.t16NB,
                                {
                                    flex: 1,
                                },
                            ]}
                        >
                            {`${item.title} ${item.timeOfDay}`}
                        </Text>
                        {!_.isUndefined(item.timeOfDayCount) && <Text style={[TextStyles.t16NT2]}>{`(共${item.timeOfDayCount}号)`}</Text>}
                    </View>
                )),
                initialSelectIndexes: !_.isUndefined(timeIndex) ? new Set<number>([timeIndex]) : undefined, // 初始选择索引
            });

            if (!result || _.isEmpty(result)) return;
            // 选择指定挂号时间
            if (result[0] == 3) {
                const { departmentId, doctorId, timeOfDayTotalCountMap } = registrationFormItem ?? {};
                // 选择指定时间需先选择医生
                if (doctorId == undefined) return Toast.show("请先选择医生");
                // 所选医生（姓名 诊费
                const selectedDoctors = this.innerState.currentDepartmentDoctors?.find((item) => {
                    return item.doctorId == doctorId;
                });
                // 所选医生未排班时提示
                if (selectedDoctors?.doctorName !== "不指定医生" && !timeOfDayTotalCountMap) {
                    return Toast.show("该医生今天暂未排班");
                }

                const chooseSpecificTime = await RegisterTimeDialog.show(
                    selectedDoctors as RegistrationDesignatedDoctorInfo,
                    departmentId as string
                );

                if (!chooseSpecificTime) return;
                // 指定时间段
                registrationFormItem!.reserveTime = {
                    start: chooseSpecificTime.start,
                    end: chooseSpecificTime.end,
                };
                // 指定时间段号数
                registrationFormItem!.orderNo = chooseSpecificTime.orderNo;
                registrationFormItem!.doctorId = selectedDoctors?.doctorId;
            } else {
                registrationFormItem!.reserveTime = options[result[0]].range;
                registrationFormItem!.reserveDate = new Date();
                registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    registrationFormItem!,
                    userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm,
                    RegistrationType.outpatientRegistration
                ).catch((error) => {
                    if (error.msg === "无可用号源") {
                        // 无可用号源不提示弹窗
                        this.innerState.showStopDiagnose = true;
                    } else if (this.isDisposed) {
                        showQueryDialog("提示", errorToStr(error));
                    }
                    return undefined;
                });
                list.forEach((item) => {
                    item.timeOfDayCount = registrationFormItem?.timeOfDayTotalCountMap?.get(item.timeOfDay);
                });
            }
            this.innerState.detail!.registrationFormItem = registrationFormItem ?? this.innerState.detail!.registrationFormItem;

            this.innerState.hasChange = true;
        }
        this.calculateRegCategoriesFee();
    }

    @actionEvent(_EventModifyRegistrationFee)
    private async *_mapEventModifyRegistrationFee(event: _EventModifyRegistrationFee): AsyncGenerator<State> {
        const fee = Number(event.fee);
        this.innerState.hasChange = true;
        this.innerState.pay!.fee = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].sourceUnitPrice = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].unitPrice = fee;
        this.innerState.detail!.chargeSheet!.chargeForms![0].chargeFormItems![0].expectedUnitPrice = fee;
        this._calculatePriceTrigger.next();
    }

    @actionEvent(_EventModifyRegistrationDoctor)
    private async *_mapEventModifyRegistrationDoctor(): AsyncGenerator<State> {
        const { currentDepartmentDoctors, detail, type } = this.innerState;
        const doctorIndex = currentDepartmentDoctors?.findIndex((item) => {
            return item.doctorId == detail?.registrationFormItem?.doctorId;
        });

        const result = await showOptionsBottomSheet({
            title: "选择医生",
            options: currentDepartmentDoctors?.map((item) => item.doctorName ?? ""),
            initialSelectIndexes: !_.isUndefined(doctorIndex) ? new Set<number>([doctorIndex]) : undefined,
            height: pxToDp(375),
            showConfirmBtn: false,
            showResetBtn: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!result) return;
        this.innerState.hasChange = true;
        const currentDoctor = this.innerState.currentDepartmentDoctors?.[result[0]];
        this.innerState.currentSelectedDoctors = currentDoctor;
        //医生和预约时间
        const registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        registrationFormItem.doctorId = currentDoctor?.doctorId;
        registrationFormItem.doctorName = !!currentDoctor?.doctorId ? currentDoctor?.doctorName : undefined;
        this.innerState.detail!.registrationFormItem = registrationFormItem;
        //初始化初复诊转态
        this.initialRevisitStatus();
        this.calculateRegCategoriesFee();
        //固定模式-挂号类别-获取各个时间段信息
        if (this.innerState.isFixedMode) {
            if (
                type == RegistrationInvoiceType.create &&
                this.innerState.detail?.registrationFormItem?.isReserved &&
                this.needCalcNumber()
            ) {
                Object.assign(registrationFormItem, {
                    reserveEnd: undefined,
                    reserveStart: undefined,
                    reserveTime: undefined,
                    orderNo: undefined,
                });
                this.innerState.detail!.registrationFormItem = registrationFormItem;
            }

            const doctorOrderNoRsp = await RegistrationAgent.getDoctorOrderNo(
                this.innerState.detail!.registrationFormItem!,
                userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm,
                RegistrationType.outpatientRegistration
            ).catch((error) => {
                // 无可用号源不提示弹窗
                if (error.msg === "无可用号源") {
                    this.innerState.showStopDiagnose = true; // 显示停诊状态
                    return JsonMapper.deserialize(RegistrationFormItem, {
                        ...this.innerState.detail!.registrationFormItem!,
                        doctorId: this.innerState.detail!.registrationFormItem?.doctorId,
                        doctorName: this.innerState.detail!.registrationFormItem?.doctorName,
                        departmentName: this.innerState.detail!.registrationFormItem?.departmentName,
                        departmentId: this.innerState.detail!.registrationFormItem?.departmentId,
                        timeOfDay: this.innerState.detail!.registrationFormItem?.timeOfDay,
                        reserveTime: undefined,
                    });
                } else if (this.isDisposed) {
                    showQueryDialog("提示", errorToStr(error));
                }
                return undefined;
            });
            if (!!doctorOrderNoRsp?.reserveTime) {
                this.innerState.showStopDiagnose = false;
                this._registrationDesignatedTimeTrigger.next();
            }
        }
        //灵活模式--获取时间预约人数
        if (this.innerState.isFlexibleMode) {
            await this.getClinicAllDoctorList();
            this._registrationLocalTimeCountTrigger.next();
        }
        //口腔诊所、升级挂号预约诊所-预约类型-获取排班信息
        if ((userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm) && registrationFormItem?.isReserved) {
            registrationFormItem.reserveDate = undefined;
            registrationFormItem.orderNo = undefined;
            this.innerState.detail!.registrationFormItem = registrationFormItem;
            this._registrationDailyReserveStatusTrigger.next();
        }

        this.update();
    }

    private async *_mapEventRefundedRegistrationFee(): AsyncGenerator<State> {
        await ChargeInvoiceRefundDialog.show(
            this.innerState.detail!.chargeSheet!,
            () => {
                ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
            },
            true
        );
    }

    private async *_mapEventRefundedRegistration(): AsyncGenerator<State> {
        const patient = this.innerState.detail?.patient;
        const registration = this.innerState.detail?.registrationFormItem;

        if (registration?.payStatusV2 === PayStatusV2.paid || registration?.payStatusV2 === PayStatusV2.partedPaid) {
            // 1. 完全收费退费，弹出退费
            await ChargeInvoiceRefundDialog.show(
                this.innerState.detail!.chargeSheet!,
                () => {
                    ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });

                    //退费成功更新一下列表
                    RegistrationAgent.changeObserver.next();
                },
                true
            );
        } else if (registration?.payStatusV2 === PayStatusV2.partedPaid) {
            // 2. 部分收费，提示退费，
        } else {
            // 3. 否则提示退号
            const result = await showQueryDialog(
                "",
                registration?.statusV2 == RegistrationStatusV2.diagnosed
                    ? `${patient?.name} 已接受就诊，确定要退号？`
                    : "退号后不可恢复，是否继续？"
            );
            if (result == DialogIndex.positive) {
                RegistrationAgent.cancelPatientRegistration(this.innerState.detail!.id!)
                    .then(() => {
                        Toast.show("退号成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((error) => {
                        Toast.show(`${errorToStr(new ABCError(error))}`, { warning: true });
                    });
            }
        }
    }

    private async *_mapEventSaveRegistration(event: _EventSaveRegistration): AsyncGenerator<State> {
        if (!this.innerState.hasChange) return;
        const detail = this.innerState.detail;
        if (!detail || !detail.id) return;
        const { chargeSheet } = detail;
        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            allergicHistory: detail.allergicHistory,
            chiefComplaint: detail.chiefComplaint,
            familyHistory: detail.familyHistory,
            pastHistory: detail.pastHistory,
            personalHistory: detail.personalHistory,
            physicalExamination: detail.physicalExamination,
            presentHistory: detail.presentHistory,
            epidemiologicalHistory: detail.epidemiologicalHistory,

            patient: detail.patient,

            registrationFormItem: detail.registrationFormItem,

            pay: this.innerState.pay,

            promotions: chargeSheet?.promotions,
            couponPromotions: chargeSheet?.couponPromotions, // 优惠券促销
            giftRulePromotions: chargeSheet?.giftRulePromotions, // 满减活动
            patientPointsInfo: chargeSheet?.patientPointsInfo, //积分抵扣
            patientCardPromotions: chargeSheet?.patientCardPromotions, // 卡项
            visitSourceId: detail.visitSourceId,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
            visitSourceRemark: detail.visitSourceRemark ?? "",
            revisitStatus: detail.revisitStatus,
        });
        if (!req.registrationFormItem?.reserveTime) return;
        const dialog = new LoadingDialog();
        dialog.show(300);
        RegistrationAgent.savePatientRegistration({ id: detail.id, detail: req })
            .then(() => {
                if (event.successCallback) {
                    event.successCallback?.();
                } else {
                    Toast.show("保存成功", { success: true }).then(() => ABCNavigator.pop({ success: true }));
                }
            })
            .catch(async (error) => {
                if (!this.isDisposed) {
                    await showQueryDialog(`保存失败`, errorToStr(new ABCError(error)));
                }
            });
        await dialog.hide();
    }

    @actionEvent(_EventModifyMedicalRecordCard)
    private async *_mapEventModifyMedicalRecordCard(event: _EventModifyMedicalRecordCard): AsyncGenerator<State> {
        this.innerState.hasChange = true;
        const { chiefComplaint, presentHistory, pastHistory, physicalExamination, epidemiologicalHistory } = event.medicalRecord;
        Object.assign(this.innerState.detail, {
            chiefComplaint,
            presentHistory,
            pastHistory,
            physicalExamination,
            epidemiologicalHistory,
        });
        this.update();
    }

    private async *_mapEventFinishRegistration(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail) return;
        const { chargeSheet } = detail;

        const doctorConfig = this.innerState.clinicFieldConfig?.getFieldConfigDetail({
            sourceKey: "registration",
            type: "create",
            field: "doctor",
        });

        ///内容完整性校验
        if (!detail.patient || !detail.patient.name) {
            //无患者
            this.innerState.focusKey = FocusItemKeys.patient;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem?.departmentId) {
            this.innerState.focusKey = FocusItemKeys.department;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!detail.registrationFormItem.reserveDate || !detail.registrationFormItem.reserveTime) {
            this.innerState.focusKey = FocusItemKeys.reserveDate;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (_.isUndefined(this.innerState.pay.fee)) {
            this.innerState.focusKey = FocusItemKeys.fee;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (
            detail.registrationFormItem.isReserved &&
            (!detail.registrationFormItem.reserveTime?.start || !detail.registrationFormItem.reserveTime?.end)
        ) {
            this.innerState.focusKey = FocusItemKeys.reserveTime;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (
            ((userCenter.isAllowedRegUpgrade &&
                detail.registrationFormItem.isReserved &&
                this.innerState.registrationConfig?.isMustReserveEmployee) ||
                !!doctorConfig?.required) &&
            !detail.registrationFormItem.doctorId
        ) {
            //预约类型下，且必须指定医生
            this.innerState.focusKey = FocusItemKeys.doctorId;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        }
        const recommendConfig = this.innerState.clinicFieldConfig?.getFieldConfigDetail({
            sourceKey: "registration",
            type: "create",
            field: "recommend",
        });
        const remarkConfig = this.innerState.clinicFieldConfig?.getFieldConfigDetail({
            sourceKey: "registration",
            type: "create",
            field: "remark",
        });

        if (!!recommendConfig?.required && !detail?.__visitSourceDisplayName) {
            this.innerState.focusKey = FocusItemKeys.fee;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        } else if (!!remarkConfig?.required && !detail.visitSourceRemark) {
            this.innerState.focusKey = FocusItemKeys.fee;
            this.innerState.showErrorHint = true;
            yield ScrollToErrorViewState.fromState(this.innerState);
            return;
        }
        ///
        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            allergicHistory: detail.allergicHistory,
            chiefComplaint: detail.chiefComplaint,
            familyHistory: detail.familyHistory,
            pastHistory: detail.pastHistory,
            personalHistory: detail.personalHistory,
            physicalExamination: detail.physicalExamination,
            presentHistory: detail.presentHistory,
            epidemiologicalHistory: detail.epidemiologicalHistory,

            patient: detail.patient,

            registrationFormItem: detail.registrationFormItem,

            pay: this.innerState.pay,

            promotions: chargeSheet?.promotions,
            couponPromotions: chargeSheet?.couponPromotions, // 优惠券促销
            giftRulePromotions: chargeSheet?.giftRulePromotions, // 满减活动
            patientPointsInfo: chargeSheet?.patientPointsInfo, //积分抵扣
            patientCardPromotions: chargeSheet?.patientCardPromotions, // 卡项
            visitSourceId: detail.visitSourceId,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
            visitSourceRemark: detail.visitSourceRemark ?? "",
            revisitStatus: detail.revisitStatus,
            registrationType: RegistrationType.outpatientRegistration,
        });
        if (this.innerState.isFixedMode && !this.innerState.isAccuratePart && !this.innerState.isSignInGetOrderNo) {
            const registrationFormItem = detail.registrationFormItem;
            this.innerState.registrationTimeList?.map((item) => {
                item.list?.map((k) => {
                    if (k.orderNo == registrationFormItem.orderNo && k.timeOfDay == registrationFormItem.orderNoTimeOfDay) {
                        req.registrationFormItem!.reserveTime!.orderNoStart = k.start;
                        req.registrationFormItem!.reserveTime!.orderNoEnd = k.end;
                    }
                });
            });
        }
        const actionText = detail.registrationFormItem.isReserved ? "预约" : "挂号";

        const dialog = new LoadingDialog();
        dialog.show(300);

        //患者沟通进入
        if (this._source == RegistrationPageSourceType.patientChat) {
            this._setPatientRegAuditStatusSubject.next();
            await dialog.hide();
            return;
        } else {
            let rsp;
            if (userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm) {
                //固定模式-挂号类型--无排班（即通过/api/v2/registrations/orderno算出来的orderNo需要置为空）的orderNo要为空
                //当前选中号数是否在排班里面,如果不在，则是计算的号，需要把orderNo清空
                const isRegisterType = !this.innerState.detail?.registrationFormItem?.isReserved && this.needCalcNumber();
                const existTimeRangeList = _.cloneDeep(this.innerState.selectOrderNoTimeList)
                    ?.filter((t) => !_.isEmpty(t?.list))
                    ?.map((item) => {
                        item.list = item.list?.filter((k) => (isRegisterType ? k?.available == 1 && !k?.type : k?.available == 1));
                        return item;
                    })
                    ?.filter((t) => !_.isEmpty(t?.list));
                if (
                    this.innerState.isSignInGetOrderNo ||
                    ((this.innerState.isFixedMode || this.innerState.oldVersionRegistrationForm) &&
                        this.innerState.detail?.registrationFormItem?.isReserved == 0 &&
                        (this._registrationFormItem?.doctorId == DEFAULT_DOCTOR_ID || !existTimeRangeList?.length))
                ) {
                    req.registrationFormItem!.orderNo = undefined;
                }
            }
            //口腔诊所、升级挂号预约升级
            if (userCenter.isAllowedRegUpgrade) {
                //新增
                if (this.innerState.isCreate) {
                    rsp = await DentistryAgent.saveRegistrationManage(req).catch((e) => new ABCError(e));
                } else {
                    //修改
                    const registrationSheetId =
                        this.innerState.detail?.registrationFormItem?.registrationSheetId ?? this.innerState.detail?.id;
                    if (!registrationSheetId) return;
                    rsp = await DentistryAgent.modifyRegistrationManage(registrationSheetId, req).catch((e) => new ABCError(e));
                }
            } else {
                //普通挂号
                if (this.innerState.isCreate) {
                    rsp = await RegistrationAgent.createRegistrationSheet(req).catch((e) => new ABCError(e));
                } else {
                    if (!detail?.id) return;
                    rsp = await RegistrationAgent.savePatientRegistration({
                        id: detail.id,
                        detail: req,
                    }).catch((e) => new ABCError(e));
                }
            }
            await dialog.hide();
            if (rsp instanceof ABCError) {
                // if (this.isDisposed && rsp.detailError.code != 401) {
                await showQueryDialog(`${actionText}失败`, errorToStr(rsp));
                // }
            } else if (rsp) {
                // 老版本挂号预约--编辑挂号单的情况
                const isOldRegistrationDetail = this.innerState.oldVersionRegistrationForm;
                if (rsp instanceof RegistrationInfoModifyRsp || isOldRegistrationDetail) {
                    dialog.show(300);
                    if ((rsp instanceof RegistrationInfoModifyRsp && rsp.isSuccess == 1) || (!!rsp && isOldRegistrationDetail)) {
                        await dialog.success("保存成功").then(() => {
                            if (this._source == RegistrationPageSourceType.normal) {
                                ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                            } else {
                                ABCNavigator.pop({ success: true });
                            }
                        });
                    } else {
                        await dialog.fail("保存失败");
                    }
                } else {
                    this.innerState.detail = rsp;
                    this.innerState.type = RegistrationInvoiceType.detail;
                    this.innerState.hasChange = false;
                    this._calculatePriceTrigger.next();
                    const result = await RegistrationSuccessDialog.show({
                        detail: this.innerState.detail,
                        isShowCategories: this.innerState.enableRegistrationCategories,
                        isShowOrderNo: !this.innerState.isFlexibleMode, //灵活模式--无诊号
                        isShowCharge: !this.isAgain,
                        isUpdateClinic: userCenter.isAllowedRegUpgrade,
                        isShowUndetermined:
                            this.innerState.isSignInGetOrderNo &&
                            this.innerState.detail?.registrationFormItem?.statusV2 == RegistrationStatusV2.waitingSignIn,
                    });
                    if (result == DialogIndex.positive) {
                        this.dispatch(new _EventRegistrationCharge());
                    } else {
                        ABCNavigator.pop({ success: true });
                    }
                }
            }
        }
    }

    private async *_mapEventRegistrationCharge(): AsyncGenerator<State> {
        // 有更改，先提交保存
        if (this.innerState.hasChange) {
            this.dispatch(
                new _EventSaveRegistration(() => {
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });
                })
            );
        }

        const detailData = this.innerState.detail?.chargeSheet;
        const payData = new ChargePayData();
        // payData.reCharge = false;
        // payData.chargeInvoiceDetailData = detailData;
        // payData.selects = new AbcMap<ChargeForm, ChargeFormItem[]>();
        //
        // payData.promotions = detailData?.promotions;
        // payData.receivableFee = detailData?.chargeSheetSummary?.needPayFee;
        // payData.totalFee = detailData?.chargeSheetSummary?.totalFee;
        // payData.adjustmentFee = detailData?.chargeSheetSummary?.adjustmentFee;
        // payData.memberId = detailData?.memberId;

        const hasPartedPaid = this.innerState.hasPartedPaid; // 是否部分收费

        ABCNavigator.navigateToPage(
            <ChargePayPage
                chargePayData={payData}
                chargeDetailData={detailData}
                hasPartedPaid={hasPartedPaid}
                successChangeCallback={() => {
                    if (this._source == RegistrationPageSourceType.normal) {
                        ABCNavigator.popUntil(URLProtocols.REGISTRATION_TAB, URLProtocols.REGISTRATION_TAB);
                    } else {
                        ABCNavigator.pop({ success: true });
                    }
                }}
                requestDetailCallback={() => {
                    this._registrationInvoiceDetailTrigger.next({ id: this.id! });
                }}
                isHideOweMethod={true}
            />
        ).then();
        this.update();
    }

    private resetRegistrationTime(): void {
        const registrationFormItem = this.innerState.detail?.registrationFormItem;
        if (!registrationFormItem) return;
        const { isReserved } = registrationFormItem;
        //固定模式--预约类型，reserveDate不用清空
        if ((this.innerState.isFixedMode || this.innerState.oldVersionRegistrationForm) && !isReserved) {
            registrationFormItem!.reserveDate = undefined;
        }
        registrationFormItem!.reserveEnd = undefined;
        registrationFormItem!.reserveStart = undefined;
        registrationFormItem!.reserveTime = undefined;
        if (isReserved == 0) {
            this.dispatch(new _EventCalculateOrderNo());
        }
    }

    async *_mapEventUpdateMemberCard(event: _EventUpdateMemberCard): AsyncGenerator<State> {
        const innerState = this.innerState;
        SelectMemberInfo.updateChargeDetail(innerState.detail!.chargeSheet!, event.member);
        this._calculatePriceTrigger.next();
        this.innerState.hasChange = true;
        yield this.innerState.clone();
    }

    async *_mapEventHandleChargeAbnormal(event: _EventHandleChargeAbnormal): AsyncGenerator<State> {
        this.innerState.abnormalList = [];
        //如果是社保异常，只做弹窗提示，操作需要到PC端
        if (event.isShebaoAbnormal) {
            ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_fail",
                title: "处理异常",
                content: "请前往电脑客户端，进入『收费』\n" + "找到当前收费单进行处理",
            });
        } else {
            //如果是非社保异常，需要根据收费单id查询异常列表
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(
                this.innerState.detail?.chargeSheet?.id ?? ""
            ).catchIgnore();
            if (_.isEmpty(this.innerState.abnormalList)) return;
            showBottomPanel(<ChargeAbnormalDialog bloc={this} />, { topMaskHeight: Sizes.dp160 });
        }
        this.update();
    }

    async *_mapEventAbnormalRefund(event: _EventAbnormalRefund): AsyncGenerator<State> {
        if (!event.abnormalMsg?.chargeSheetId && !event.abnormalMsg?.id) return;
        const refundResult = await ChargeAgent.dealAbnormalRefund(event.abnormalMsg.chargeSheetId!, event.abnormalMsg.id!).catchIgnore();
        if (refundResult?.status == 20) {
            await ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_success",
                title: refundResult?.statusName ?? "退费成功",
                content: `已收费用 (${abcI18Next.t("¥")}${ABCUtils.formatPrice(event.abnormalMsg?.amount ?? 0)})已原路退回至患者${
                    event.abnormalMsg?.payModeDisplayName
                }账户`,
            });
            //退款成功后，再次查询异常列表，如果还有，继续退费
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(refundResult?.chargeSheetId ?? "").catchIgnore();
            this._registrationInvoiceDetailTrigger.next({ id: this.id! }); // 刷新详情
            TherapyAppointmentAgent.changeObserver.next(); //刷新挂号列表
            if (_.isEmpty(this.innerState.abnormalList)) {
                ABCNavigator.pop();
            }
        } else {
            await ChargeRefundDialog.showQueryPopup({
                logo: "image_dlg_fail",
                title: "退费失败",
                content: refundResult?.statusName ?? "该收费单已有一笔收费正在进行中，请确定是否终止",
            });
            this._registrationInvoiceDetailTrigger.next({ id: this.id! }); // 刷新详情
            TherapyAppointmentAgent.changeObserver.next(); //刷新挂号列表
            ABCNavigator.pop();
        }
        this.update();
    }

    async *_mapEventSearchProduct(event: _EventSearchProduct): AsyncGenerator<State> {
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationFormItem = this.innerState.detail!.registrationFormItem;
        const updateProduct: RegistrationProducts[] = [];
        const result = await showBottomPanel<DentistryClinicProduct[]>(
            <DentistrySearchProject
                doctorId={registrationFormItem.doctorId ?? this._doctorId}
                defaultProducts={event.registrationProducts}
            />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (_.isUndefined(result)) return;
        if (!_.isEmpty(result)) {
            result?.map((item) => {
                updateProduct.push(
                    JsonMapper.deserialize(RegistrationProducts, {
                        displayName: item.displayName,
                        id: item.id,
                    })
                );
            });
            //选中项目中初诊时间最长
            const visitServiceDurationItem = _.maxBy(result, (a) => {
                return (a?.visitServiceDuration?.hour ?? 0) * 60 + (a?.visitServiceDuration?.min ?? 0);
            });
            //选中项目中复诊时间最长
            const revisitedServiceDurationItem = _.maxBy(result, (a) => {
                return (a?.revisitedServiceDuration?.hour ?? 0) * 60 + (a?.revisitedServiceDuration?.min ?? 0);
            });

            registrationFormItem!.registrationProducts = updateProduct;
            registrationFormItem!.registrationProductIds = updateProduct?.filter((t) => !!t.id)?.map((item) => item.id);
            this.innerState.visitDuration =
                visitServiceDurationItem?.visitServiceDuration ?? userCenter.dentistryConfig?.serviceDuration?.visitServiceDuration;
            this.innerState.reVisitDuration =
                revisitedServiceDurationItem?.revisitedServiceDuration ??
                userCenter.dentistryConfig?.serviceDuration?.revisitedServiceDuration;
        } else {
            if (!!registrationFormItem?.registrationProducts) {
                registrationFormItem!.registrationProducts = [];
                registrationFormItem!.registrationProductIds = [];
            }
            this.innerState.visitDuration = userCenter.dentistryConfig?.serviceDuration?.visitServiceDuration;
            this.innerState.reVisitDuration = userCenter.dentistryConfig?.serviceDuration?.revisitedServiceDuration;
        }
        this.innerState.hasChange = true;
        this.flexibleServiceDuration();
        this.update();
    }

    @actionEvent(_EventRegistrationSignIn)
    private async *_mapEventRegistrationSignIn(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail || !detail.id) return;
        const { chargeSheet, registrationFormItem } = detail;

        if (TimeUtils.formatDate(registrationFormItem?.reserveDate) != TimeUtils.formatDate(new Date())) {
            const result = await showQueryDialog(
                "",
                `患者预约时间（${TimeUtils.formatDate(registrationFormItem?.reserveDate)}）不是今天。签到后会更改预约就诊时间，是否确定`
            );
            if (result != DialogIndex.positive) {
                return;
            }
        }

        const req = JsonMapper.deserialize(RegistrationDetailReq, {
            ...detail,
            pay: this.innerState.pay,
            promotions: chargeSheet?.promotions,
            signIn: TherapySignInStatus.signed,
            visitSourceFrom: detail.visitSourceId == detail.visitSourceFrom ? "" : detail.visitSourceFrom,
        });
        if (!userCenter.isAllowedRegUpgrade && !req.registrationFormItem?.reserveTime) return;
        const callInteface = DentistryAgent.completeSignInRegistration(registrationFormItem?.registrationSheetId);

        const loading = new LoadingDialog();
        loading.show(300);
        const rsp = await callInteface.catch((error) => new ABCError(error));
        await loading.hide();
        if (rsp instanceof ABCError) {
            return await showQueryDialog(`保存失败`, errorToStr(rsp));
        }
        this.innerState.detail = rsp;
        this.innerState.type = RegistrationInvoiceType.detail;
        this.innerState.hasChange = false;
        if (this.innerState.hasCharged) {
            return Toast.show("签到成功", { success: true }).then(() => {
                ABCNavigator.pop();
            });
        }
        const result = await RegistrationSuccessDialog.show({
            detail: this.innerState.detail,
            type: RegistrationSuccessDialogType.signIn,
            isShowCharge: !this.isAgain,
        });
        if (result == DialogIndex.positive) {
            this.dispatch(new _EventRegistrationCharge());
        } else {
            ABCNavigator.pop();
        }
    }

    async *_mapEventModifyFixedTimeRange(): AsyncGenerator<State> {
        const list = this.defaultRegistrationTimeOptions.filter((t) => t.title != "指定时间");
        const timeIndex = list?.findIndex((item) => {
            return item.timeOfDay == this.innerState.detail?.registrationFormItem?.timeOfDay; // 当日时间段
        });
        const options = list;
        let result: number[] | undefined, resultIndex: number | undefined;

        if (!!userCenter.isAllowedRegUpgrade) {
            const flexibleTimeList: { cont: string; type?: number }[] = [];
            options?.map((item) => {
                flexibleTimeList.push({
                    cont: item.timeOfDay,
                });
            });
            resultIndex = await showBottomSheet<number | undefined>(
                <FixedSourceTime dataList={[]} list={flexibleTimeList} initialIndex={timeIndex} />
            );
        } else {
            result = await showOptionsBottomSheet({
                title: "选择时间",
                options: options?.map((item) => item.timeOfDay),
                optionsWidgets: options?.map((item, index) => (
                    <View
                        key={index}
                        style={{
                            ...ABCStyles.rowAlignCenter,
                            height: Sizes.listItemHeight,
                            paddingHorizontal: Sizes.listHorizontalMargin,
                        }}
                    >
                        <Text
                            style={[
                                TextStyles.t16NB,
                                {
                                    flex: 1,
                                },
                            ]}
                        >
                            {`${item.timeOfDay}`}
                        </Text>
                        {!_.isUndefined(item.timeOfDayCount) && <Text style={[TextStyles.t16NT2]}>{`(共${item.timeOfDayCount}号)`}</Text>}
                    </View>
                )),
                initialSelectIndexes: !_.isUndefined(timeIndex) ? new Set<number>([timeIndex]) : undefined, // 初始选择索引
            });
        }

        if (!!userCenter.isAllowedRegUpgrade) {
            if (_.isUndefined(resultIndex)) return;
        } else {
            if (!result || _.isEmpty(result)) return;
        }

        this.innerState.detail!.registrationFormItem!.timeOfDay = !!userCenter.isAllowedRegUpgrade
            ? options[resultIndex!].timeOfDay
            : options[result![0]].timeOfDay;
        if (this.innerState.isFixedMode || this.innerState.oldVersionRegistrationForm) {
            this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
            this.innerState.detail!.registrationFormItem!.orderNo = undefined;
            this._registrationDesignatedTimeTrigger.next();
        }
        this.innerState.hasChange = true;
        this.update();
    }

    private async *_mapEventUpdatePromotions(event: _EventUpdatePromotions): AsyncGenerator<State> {
        const chargeSheet = this.innerState.detail!.chargeSheet!;
        this.innerState.hasChange = true;
        ChargeUtils.updatePromotions(chargeSheet, event);

        this._calculatePriceTrigger.next();
    }

    private async *_mapEventModifyRegistrationVisitSource(/*event: _EventModifyRegistrationVisitSource*/): AsyncGenerator<State> {
        this.innerState.hasChange = false;
        const detail = this.innerState.detail;
        const result = await PatientSourceTypeSelectDialog.show({
            source: JsonMapper.deserialize(PatientSource, {
                id: detail?.visitSourceId,
                name: detail?.visitSourceName, // 来源方式
                sourceFrom: detail?.visitSourceFrom,
                sourceFromName: detail?.visitSourceFromName, // 患者来源-姓名
            }),
        });
        const params = JsonMapper.deserialize(VisitSourceBaseInfo, {
            visitSourceId: result?.id,
            visitSourceName: result?.name,
            visitSourceFrom: result?.sourceFrom,
            visitSourceFromName: result?.sourceFromName,
        });
        const reps = await RegistrationUtils.formatVisitSource(params);
        if (result) {
            this.innerState.detail!.visitSourceId = reps.visitSourceId;
            this.innerState.detail!.visitSourceName = reps.visitSourceName;
            this.innerState.detail!.visitSourceFrom = reps.visitSourceFrom;
            this.innerState.detail!.visitSourceFromName = reps.visitSourceFromName;
        }
        //初始化传入的值与更改后的值比较，相同则无需调接口
        const beforeVisitSource = !!this.innerState.cloneDetail?.__visitSourceDisplayName
            ? this.innerState.cloneDetail?.__visitSourceDisplayName
            : "不指定";
        const nowVisitSource = !!this.innerState.detail?.__visitSourceDisplayName
            ? this.innerState.detail?.__visitSourceDisplayName
            : "不指定";
        if (beforeVisitSource != nowVisitSource) {
            this.innerState.hasChange = true;
        }
        this.update();
    }

    private async *_mapEventModifyRegistrationVisitSourceRemark(event: _EventModifyRegistrationVisitSourceRemark): AsyncGenerator<State> {
        this.innerState.hasChange = false;
        if (!!userCenter.isAllowedRegUpgrade) {
            const result = await showBottomPanel<string>(
                <DentistryRemarkInputPage remark={event.remark} registrationType={RegistrationType.outpatientRegistration} />,
                {
                    topMaskHeight: Sizes.dp160,
                }
            );
            if (_.isUndefined(result)) return;
            this.innerState.detail!.visitSourceRemark = result;
        } else {
            this.innerState.detail!.visitSourceRemark = event.remark;
        }
        if (this.innerState.cloneDetail?.visitSourceRemark != event.remark) {
            this.innerState.hasChange = true;
        }
        this.update();
    }

    private async *_mapEventModifyRevisitStatus(/*event: _EventModifyRevisitStatus*/): AsyncGenerator<State> {
        if (this.innerState.disabledEditRevisitStatus) return;
        const initIndex = this.innerState.detail?.revisitStatus;
        let result: number[] | undefined;
        if (!!userCenter.isAllowedRegUpgrade) {
            result = await showOptionsBottomSheet({
                title: "初/复诊",
                options: ["初诊", "复诊"],
                initialSelectIndexes: initIndex ? new Set<number>([initIndex - 1]) : undefined,
                height: pxToDp(240),
                showConfirmBtn: false,
                showTopRadius: true,
                titlePosition: "center",
                titleStyle: TextStyles.t18MT1,
            });
        } else {
            result = await showOptionsBottomSheet({
                title: "选择初诊/复诊",
                options: ["初诊", "复诊"],
                initialSelectIndexes: initIndex ? new Set<number>([initIndex - 1]) : undefined,
            });
        }

        if (result && result.length) {
            this.innerState.hasChange = true;
            this.innerState.detail!.revisitStatus = result[0] + 1;

            this.calculateRegCategoriesFee();
            this.flexibleServiceDuration();
        }
    }

    private async *_mapEventModifyRegistrationCategory(/*event: _EventModifyRegistrationCategory*/): AsyncGenerator<State> {
        const registrationCategory = this.innerState.detail?.registrationFormItem?.registrationCategory ?? 0;
        const list = this.innerState.doctorRegistrationCategoriesList;

        list.sort((a, b) => (a.registrationCategory ?? 0) - (b.registrationCategory ?? 0));
        let displayList;
        if (!!list.length) {
            displayList = list.map((item) => ({
                label: item.registrationCategoryDisplay ?? "",
                value: item.registrationCategory ?? 0,
            }));
        } else {
            displayList = [{ label: "普通门诊", value: 0 }];
        }
        const initIndex = displayList.findIndex((item) => item.value == registrationCategory);
        const result = await showOptionsBottomSheet({
            title: "号种",
            options: displayList.map((item) => item.label),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            height: pxToDp(280),
            showConfirmBtn: false,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!result?.length) return;
        this.innerState.hasChange = true;
        const value = displayList[result[0]];
        this.innerState.detail!.registrationFormItem!.registrationCategory = value.value;
        if (this.innerState.isFixedMode) {
            this._registrationDesignatedTimeTrigger.next();
        }
        //计算当前挂号费用
        this.calculateRegCategoriesFee(false);

        this.update();
    }

    /**
     * 修改号种
     */
    public requestModifyRegistrationCategory(): void {
        this.dispatch(new _EventModifyRegistrationCategory());
    }

    private async *_mapEventPatientCardPromotion(event: _EventPatientCardPromotion): AsyncGenerator<State> {
        const chargeSheet = this.innerState.detail!.chargeSheet!;
        this.innerState.hasChange = true;
        chargeSheet.patientCardPromotions = event.patientCardPromotions;
        this._calculatePriceTrigger.next();
    }

    /**
     * 优惠明细弹窗
     */
    async *_mapEventDiscountDetail(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        if (!detail?.chargeSheet) return;
        const result = await showBottomPanel<number | undefined>(
            <DiscountCardDialog
                detailData={detail?.chargeSheet}
                canModify={this.innerState.isCreate || !this.innerState.disabledEditCharge}
                promotions={RegistrationUtils.filterRegistrationPromotions(detail.chargeSheet)}
                onPromotionSelectChanged={(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion) => {
                    this.requestUpdatePromotions(
                        selectPromotions,
                        selectGiftPromotions,
                        selectCouponPromotions,
                        selectMemberPointPromotion
                    );
                }}
                onPromotionMemberCardChanged={(member) => this.requestUpdateMemberCard(member)}
                onPatientCardPromotionChanged={(patientCardPromotions) => this.requestPatientCardPromotionChanged(patientCardPromotions)}
            />,
            {
                topMaskHeight: pxToDp(160),
            }
        );
        this.innerState.hasChange = true;
        if (!!result) {
            this.innerState.discountTotalPrice = result;
        }
        this.update();
    }

    private async *_mapEventCalculateOrderNo(/*event: _EventCalculateOrderNo*/): AsyncGenerator<State> {
        const registrationFormItem = this.innerState.detail?.registrationFormItem;

        //默认选中时间
        if (registrationFormItem) {
            if (!registrationFormItem.reserveTime) {
                //没有预约时间
                const hours = new Date().getHours();
                let defaultReserve: {
                    id: number;
                    title: string;
                    timeOfDay: string;
                    imeOfDayCount?: number;
                    range: Range<string>;
                };
                if (hours >= 0 && hours < 12) {
                    defaultReserve = this.defaultRegistrationTimeOptions[0];
                } else if (hours >= 12 && hours < 18) {
                    defaultReserve = this.defaultRegistrationTimeOptions[1];
                } else {
                    defaultReserve = this.defaultRegistrationTimeOptions[2];
                }
                // timeOfDay不变
                defaultReserve =
                    this.defaultRegistrationTimeOptions.find((item) => item.timeOfDay == registrationFormItem.timeOfDay) ?? defaultReserve;
                this.innerState.detail!.registrationFormItem!.timeOfDay = defaultReserve.timeOfDay;
                this.innerState.detail!.registrationFormItem!.reserveDate = new Date();
                //当前医生未排班不允许挂号不去进行算号
                const { doctorId } = registrationFormItem;
                // 所选医生（姓名 诊费
                const selectedDoctors = this.innerState.currentDepartmentDoctors?.find((item) => {
                    return item.doctorId == doctorId;
                });
                if (!this.innerState.isEnableNoneScheduleRegistration && !selectedDoctors?.isScheduled) {
                    return;
                }
                this.innerState.detail!.registrationFormItem!.reserveTime = defaultReserve.range;
                if (this.innerState.isCustomPart) {
                    const availableOrderNoTimeList: {
                        orderNo?: number;
                        timeOfDay?: string;
                        start?: string;
                        end?: string;
                        type?: number;
                    }[] = [];
                    const timeOfDayList =
                        this.innerState.registrationTimeList?.filter((item) => item.timeOfDay == defaultReserve?.timeOfDay) ?? [];
                    timeOfDayList?.map((item) => {
                        item.list
                            ?.filter((k) => k.available == 1)
                            ?.forEach((t) => {
                                availableOrderNoTimeList.push({
                                    orderNo: t.orderNo,
                                    timeOfDay: item.timeOfDay,
                                    start: item.start,
                                    end: item.end,
                                    type: t.type,
                                });
                            });
                    });
                    const availableItem = availableOrderNoTimeList[0] ?? timeOfDayList[0];
                    this.innerState.detail!.registrationFormItem!.reserveTime = availableItem
                        ? new Range(availableItem.start, availableItem.end)
                        : defaultReserve.range;
                }
                this.innerState.detail!.registrationFormItem = await RegistrationAgent.getDoctorOrderNo(
                    this.innerState.detail!.registrationFormItem!,
                    userCenter.isAllowedRegUpgrade || this.innerState.oldVersionRegistrationForm,
                    RegistrationType.outpatientRegistration
                ).catch((error) => {
                    if (error.msg === "无可用号源") {
                        // 无可用号源不提示弹窗
                        this.innerState.showStopDiagnose = true;
                    } else if (!this.isDisposed) {
                        showQueryDialog("提示", errorToStr(error));
                    }
                    return undefined;
                });
                this.defaultRegistrationTimeOptions.forEach((item) => {
                    item.timeOfDayCount = this.innerState.detail!.registrationFormItem?.timeOfDayTotalCountMap?.get(item.timeOfDay);
                });
            }
        }

        this.update();
    }

    private async _initPageConfig(): Promise<void> {
        //获取预约设置
        if (userCenter.isAllowedRegUpgrade) {
            this.innerState.registrationConfig = await DentistryAgent.queryDentistryRegistrationConfig(
                RegistrationType.outpatientRegistration,
                false
            ).catchIgnore();
        }
        // 医生科室列表
        let _reserveDate;
        if (!this.innerState.isEnableNoneScheduleRegistration) {
            _reserveDate = new Date();
        }
        await Promise.all([
            ClinicAgent.getEmployeesMeConfig().catchIgnore(), //当前员工门店设置
            OnlinePropertyConfigProvider.instance.getChargeConfig(false).catchIgnore(), //诊所收费设置
            OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore(), //是否开启回诊
            OnlinePropertyConfigProvider.instance.getChainBasicPropertyConfig().catchIgnore(),
            RegistrationAgent.getRegistrationReturnVisitFee({ doctorId: "", departmentId: "" }).catchIgnore(),
            RegistrationDataProvider.getDoctorList({ date: _reserveDate }).catchIgnore(),
            RegistrationAgent.getRegistrationDoctorEnableCategories().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicDataPermission().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicMedicalRecordConfig().catchIgnore(),
        ]).then((rsp) => {
            const [
                employeesMeConfig,
                chargeConfig,
                clinicBasicSetting,
                chainBasePropertyConfig,
                doctorFeeConfig,
                departments,
                clinicDefaultFees,
                dataPermission,
                ClinicMedicalRecordConfig,
            ] = rsp;
            this.innerState.employeesMeConfig = employeesMeConfig;
            this.innerState.employeeConfig = employeesMeConfig?.clinicInfo?.config;
            this.innerState.chargeConfig = chargeConfig; //诊所收费设置
            this.innerState.clinicFieldConfig = ClinicMedicalRecordConfig;
            //是否开启回诊
            if (!!clinicBasicSetting) {
                this.innerState.isOpenContinueDiagnoseWithoutReg =
                    clinicBasicSetting?.outpatient?.settings?.isOpenContinueDiagnoseWithoutReg == 1;
                this.innerState.clinicBasicSetting = clinicBasicSetting;
            }
            //初复诊是否可编辑
            if (!!chainBasePropertyConfig)
                this.innerState.isEnableEditRevisitStatus = !!chainBasePropertyConfig?.chainBasic?.isEnableEditRevisitStatus;
            //医生诊费
            if (!!doctorFeeConfig) this.innerState.pay.fee = doctorFeeConfig?.regUnitPrice ?? 0;
            // 医生科室列表
            this.innerState.departments = departments;

            //医生列表
            this.innerState.currentDepartmentDoctors = this.innerState.departments?.[0]?.doctors;
            this.innerState.clinicRegistrationCategories = clinicDefaultFees;
            this.innerState.dataPermission = dataPermission;
        });
    }

    /**
     * 初始选中科室医生
     * @private
     */
    private _initSelectedDepartmentDoctor(): void {
        //复诊预约只能选择当前选中医生
        if (userCenter.isAllowedRegUpgrade && this.isAgain) {
            this.innerState.departments = this.innerState.departments?.filter((department) => {
                return (
                    department.departmentId == this._departmentId ||
                    department.doctors?.some(
                        (doctor) => doctor.doctorId == this._doctorId || this._assistantList.some((asDoctor) => asDoctor == doctor.doctorId)
                    )
                );
            });

            this.innerState.departments?.map((department) => {
                department.doctors = department.doctors?.filter((doctor) => {
                    return doctor.doctorId == this._doctorId || this._assistantList.some((asDoctor) => asDoctor == doctor.doctorId);
                });
            });
        }

        this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
        const registrationFormItem = (this.innerState.detail.registrationFormItem = JsonMapper.deserialize(
            RegistrationFormItem,
            this.innerState.detail.registrationFormItem
        ));
        if (this._doctorId) {
            for (const department of this.innerState.departments ?? []) {
                if (!!this._departmentId && department.departmentId != this._departmentId) continue;
                if (!!registrationFormItem.departmentId && department.departmentId != registrationFormItem.departmentId) continue;
                const doctor = department.doctors?.find((doctor) => doctor?.doctorId == this._doctorId);
                if (!!doctor) {
                    registrationFormItem.departmentId = department.departmentId;
                    registrationFormItem.departmentName = department.departmentName;
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                    break;
                }
            }
        } else if (!!this.innerState.detail.registrationFormItem?.doctorId) {
            for (const department of this.innerState.departments ?? []) {
                if (!!this._departmentId && department.departmentId != this._departmentId) continue;
                if (!!registrationFormItem.departmentId && department.departmentId != registrationFormItem.departmentId) continue;
                const doctor = department.doctors?.find(
                    (doctor) => doctor?.doctorId == this.innerState.detail?.registrationFormItem?.doctorId
                );
                if (!!doctor) {
                    registrationFormItem.departmentId = department.departmentId;
                    registrationFormItem.departmentName = department.departmentName;
                    registrationFormItem.doctorId = doctor.doctorId;
                    registrationFormItem.doctorName = doctor.doctorName;
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                    break;
                }
            }
        } else {
            registrationFormItem.departmentId = _.first(this.innerState.departments)?.departmentId;
            registrationFormItem.departmentName = _.first(this.innerState.departments)?.departmentName;
            this.innerState.currentDepartmentDoctors = _.first(this.innerState.departments)?.doctors;
        }
        //复诊模式下需要精确选择医生
        if (userCenter.isAllowedRegUpgrade && this.isAgain) {
            const doctor = this.innerState.currentDepartmentDoctors?.find((doctor) => doctor.doctorId == this._doctorId);
            if (!!doctor) {
                registrationFormItem.doctorId = doctor.doctorId;
                registrationFormItem.doctorName = doctor.doctorName;
                this.innerState.currentSelectedDoctors = doctor;
            }
        }
        this.innerState.detail!.registrationFormItem = registrationFormItem;
    }

    async getShebaoSettlementExceptionList(): Promise<void> {
        // 如果医保结算异常，需要再拉取接口判断异常类型
        if (this.innerState.detail?.chargeSheet?.isSheBaoAbnormal && !!this.innerState.detail?.chargeSheet?.id) {
            this.innerState.settlementExceptionList = await ChargeAgent.getChargeSettlementExceptionList(
                this.innerState.detail.chargeSheet.id
            ).catchIgnore();
        }
    }
    private _initPageTrigger(): void {
        //算费
        this._calculatePriceTrigger
            .pipe(
                switchMap(() => {
                    const chargeSheet = this.innerState.detail?.chargeSheet;
                    //算费和patientId有关，所以要先填充patientId,如果是会员，在没有patientId的情况下，会导致折扣不生效
                    if (!chargeSheet) return of(undefined);
                    chargeSheet.patient = chargeSheet.patient ?? this.innerState.detail?.patient ?? new Patient();
                    chargeSheet.patientId = chargeSheet.patient?.id ?? "";
                    chargeSheet.fillKeyIds();
                    // chargeFormItems需要传入医生信息
                    if (chargeSheet.chargeForms?.[0]?.chargeFormItems?.[0]) {
                        chargeSheet.chargeForms[0].chargeFormItems[0].doctorInfo = {
                            doctorId: this.innerState.detail?.registrationFormItem?.doctorId,
                            departmentId: this.innerState.detail?.registrationFormItem?.departmentId,
                            registrationCategory: this.innerState.detail?.registrationFormItem?.registrationCategory ?? 0,
                        };
                    }
                    const cloneChargeSheet = _.clone(chargeSheet);
                    return ChargeUtils.calculatingPrice(cloneChargeSheet)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                } else {
                    ChargeUtils.syncChargeInvoiceDetail(this.innerState.detail?.chargeSheet, rsp);
                    const { chargeSheet } = this.innerState.detail!,
                        chargeForm = chargeSheet?.chargeForms?.[0];
                    this.innerState.pay = {
                        fee: chargeForm?.totalPrice ?? 0,
                        memberId: chargeSheet?.memberId ?? "",
                        receivable: chargeForm?.totalRealPayPrice ?? 0,
                    };
                }
                this.update();
            })
            .addToDisposableBag(this);

        //加载挂号单详情
        this._registrationInvoiceDetailTrigger
            .pipe(
                switchMap((params) => {
                    this.innerState.startLoading();
                    this.update();
                    if (!params?.id) return of(undefined);
                    return RegistrationAgent.getRegistrationDetail(params.id)
                        .then((rsp) => {
                            if (rsp.registrationFormItem?.doctorName === "不指定医生") {
                                rsp.registrationFormItem.doctorId = DEFAULT_DOCTOR_ID;
                            }
                            return { rsp: rsp, isInit: params?.isInit };
                        })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe(async (result) => {
                this.innerState.stopLoading();
                if (result instanceof ABCError) {
                    this.innerState.setLoadingError(result);
                } else {
                    this.innerState.stopLoading();
                    if (!result) return;
                    const { rsp, isInit } = result;
                    ///外部传入的提前签到信息
                    rsp.registrationFormItem!.oldReserveInfo = this._oldReserveInfo;
                    this.innerState.hasChange = false;
                    this.innerState.detail = rsp;
                    this.innerState._detail = _.cloneDeep(rsp);
                    this._initSelectedDepartmentDoctor();

                    this._getPatientBaseDetailTrigger.next(rsp.patient?.id);
                    if (!!isInit) this._initGetDetailAfterOperate();

                    //口腔诊所及升级挂号诊所，且选择预约时(老版本挂号预约在编辑挂号单的情况下也需要调用排班信息)
                    if (
                        (userCenter.isAllowedRegUpgrade ||
                            (this.innerState.type == RegistrationInvoiceType.detail && !userCenter.isAllowedRegUpgrade)) &&
                        this.innerState.detail?.registrationFormItem?.isReserved
                    ) {
                        this._registrationDailyReserveStatusTrigger.next();
                    }

                    const { chargeSheet } = rsp;
                    this.innerState.pay = {
                        fee: rsp.registrationFormItem?.fee ?? 0,
                        memberId: chargeSheet?.memberId ?? "",
                        receivable: chargeSheet?.chargeSheetSummary?.needPayFee ?? 0,
                    };
                    if (!!this.innerState.detail.visitSourceId) {
                        await this._initVisitSource();
                        this.innerState.cloneDetail = _.cloneDeep(this.innerState.detail);
                    }
                    //详情编辑状态下，需要查询此时还存在的号源
                    if (
                        this.innerState.type == RegistrationInvoiceType.detail &&
                        (this.innerState.isFixedMode || !userCenter.isAllowedRegUpgrade)
                    ) {
                        this._registrationDesignatedTimeTrigger.next();
                    }
                    // 如果医保结算异常，需要再拉取接口判断异常类型
                    await this.getShebaoSettlementExceptionList();
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._patientRevisitStatusTrigger
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detail,
                        doctorId = detail?.registrationFormItem?.doctorId,
                        patientId = detail?.patient?.id;
                    if (!doctorId || !patientId) {
                        return of(RegistrationRevisitStatus.first);
                    }
                    return RegistrationAgent.getOutpatientRevisitStatus({
                        doctorId,
                        patientId,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof GetOutpatientRevisitStatusRsp && !!rsp) {
                    this.innerState.detail!.revisitStatus = rsp?.revisitStatus ?? RegistrationRevisitStatus.first;
                    this.innerState.outpatientRevisitStatusRsp = rsp;
                } else {
                    this.innerState.detail!.revisitStatus = rsp;
                }
                this.calculateRegCategoriesFee();
            })
            .addToDisposableBag(this);

        //获取当前患者信息
        this._getPatientBaseDetailTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(null);
                    return CrmAgent.getPatientById(patientId).catchIgnore().toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this.innerState.detail!.patient = rsp;
                    this._getHistoryListTrigger.next();
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detail?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    this.innerState.loadError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        // 精确预约模式
        this._registrationLocalTimeCountTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.localTimeCountList = [];
                    const { registrationFormItem } = this.innerState.detail ?? {};
                    return DentistryAgent.queryLocalTimeCountByDoctorDepartment({
                        doctorId: registrationFormItem?.doctorId,
                        departmentId: registrationFormItem?.departmentId ?? "",
                        start: "06:00",
                        end: "23:00",
                        registrationType: RegistrationType.outpatientRegistration,
                        reserveDate: registrationFormItem?.reserveDate ?? new Date(),
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this.innerState.localTimeCountList = rsp;
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //口腔诊所、升级挂号预约诊所--预约类型（获取排班日期）
        this._registrationDailyReserveStatusTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.dailyReserveStatusList = [];
                    const { registrationFormItem, revisitStatus } = this.innerState.detail ?? {};
                    const currentDate = registrationFormItem?.reserveDate ?? new Date();
                    return DentistryAgent.queryDailyReserveStatusByDoctorDepartment({
                        doctorId: !!registrationFormItem?.doctorId ? registrationFormItem?.doctorId : undefined,
                        departmentId: registrationFormItem?.departmentId ?? "",
                        start: TimeUtils.getThisMonthFirstDay(currentDate),
                        end: TimeUtils.getThisMonthEndDay(currentDate),
                        registrationType: RegistrationType.outpatientRegistration,
                        isRevisited: revisitStatus ?? 0,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    //过滤掉过去时间的排班状态
                    this.innerState.dailyReserveStatusList = rsp?.filter((t) =>
                        TimeUtils.showExpiryDateTip(t.date, TimeUtils.getStartOfDate())
                    );
                    this.update();
                }
            })
            .addToDisposableBag(this);

        //固定模式下，获取号源时间段信息
        this._registrationDesignatedTimeTrigger
            .pipe(
                switchMap((needDoctorOrderNo) => {
                    this.innerState.registrationTimeRangeList = [];
                    this.innerState.selectOrderNoTimeList = [];
                    this.innerState.detail!.registrationFormItem =
                        this.innerState.detail!.registrationFormItem ?? new RegistrationFormItem();
                    if (
                        this.innerState.type == RegistrationInvoiceType.create &&
                        this.innerState.detail?.registrationFormItem?.isReserved &&
                        this.needCalcNumber()
                    ) {
                        this.innerState.detail!.registrationFormItem!.reserveTime = undefined;
                        this.innerState.detail!.registrationFormItem!.orderNo = undefined;
                    }
                    return RegistrationAgent.getRegistrationDesignatedTime({
                        departmentId: this.innerState.detail?.registrationFormItem?.departmentId ?? "",
                        doctorId: !!this.innerState.detail?.registrationFormItem?.doctorId
                            ? this.innerState.detail?.registrationFormItem?.doctorId
                            : DEFAULT_DOCTOR_ID,
                        forNormalRegistration: this.innerState.detail?.registrationFormItem?.isReserved == 0 ? 1 : 0, // 默认预约为0  挂号为1
                        workingDate: TimeUtils.formatDate(this.innerState.detail?.registrationFormItem?.reserveDate ?? new Date()),
                        registrationType: RegistrationType.outpatientRegistration,
                        registrationCategory: this.innerState.detail?.registrationFormItem?.registrationCategory,
                    })
                        .then((rsp) => ({ rsp, needDoctorOrderNo }))
                        .catchIgnore();
                })
            )
            .subscribe((_rsp) => {
                const { rsp, needDoctorOrderNo } = _rsp || {};
                if (rsp instanceof RegistrationDesignatedTime) {
                    const registrationCategory = this.innerState.detail?.registrationFormItem?.registrationCategory ?? 0;
                    this.innerState.registrationTimeRangeList = rsp?.registrationCategoryScheduleIntervals?.find(
                        (i) => i.registrationCategory == registrationCategory
                    )?.scheduleIntervals;
                    this.innerState.registrationTimeList = rsp?.registrationCategoryScheduleIntervals?.find(
                        (i) => i.registrationCategory == registrationCategory
                    )?.scheduleIntervals;
                    const registrationInfo = this.innerState.detail?.registrationFormItem;

                    this.innerState.selectOrderNoTimeList = this.innerState.registrationTimeRangeList?.filter(
                        (t) => t.timeOfDay == registrationInfo?.timeOfDay
                    );
                    // 挂号单详情单在没有做任何更改的情况下，不做任何改变
                    const initDetailState = !!this?.id && !this.innerState.hasChange;
                    if (initDetailState) return;
                    //详情编辑状态
                    const isEdit =
                        (this.innerState.type == RegistrationInvoiceType.detail || !!this._registrationFormItem) &&
                        !!registrationInfo?.reserveTime;
                    if (isEdit) {
                        this.innerState.selectOrderNoTimeList = this.innerState.selectOrderNoTimeList?.map((t) => {
                            !_.isEmpty(t?.list) &&
                                t?.list?.map((k) => {
                                    if (k?.orderNo == registrationInfo?.orderNo && !this.innerState.isSignInGetOrderNo) {
                                        k.available = 1;
                                    }
                                });
                            return t;
                        });
                        if (this.innerState.type == RegistrationInvoiceType.detail) {
                            // 详情状态下，不能根据available为1来判断，应该根据当前详情的orderNo及timeOfDay判断，只有新增才需要判断available
                            const relativeScheduling = this.innerState.selectOrderNoTimeList?.find((t) => {
                                return t.list?.find(
                                    (k) => k.timeOfDay == registrationInfo?.timeOfDay && k.orderNo == registrationInfo?.orderNo
                                );
                            });
                            if (!!relativeScheduling) {
                                const relativeSchedulingItem = relativeScheduling.list?.find(
                                    (item) => item.orderNo == registrationInfo?.orderNo && item.timeOfDay == registrationInfo?.timeOfDay
                                );
                                this.innerState.detail!.registrationFormItem!.reserveTime = {
                                    start: this.innerState.isAccuratePart ? relativeSchedulingItem?.start : relativeScheduling?.start,
                                    end: this.innerState.isAccuratePart ? relativeSchedulingItem?.end : relativeScheduling?.end,
                                };
                            }
                            return this.update();
                        }
                    }

                    //固定模式--挂号类型--列表进入---排班时间列表需要默认跳过会员跟预留号(type=1,2)
                    const isRegisterType = !this.innerState.detail?.registrationFormItem?.isReserved && this.needCalcNumber();

                    const existTimeRangeList = _.cloneDeep(this.innerState.selectOrderNoTimeList)
                        ?.filter((t) => !_.isEmpty(t?.list))
                        ?.map((item) => {
                            item.list = item.list?.filter((k) => (isRegisterType ? k?.available == 1 && !k?.type : k?.available == 1));
                            return item;
                        })
                        ?.filter((t) => !_.isEmpty(t?.list));
                    if (!_.isEmpty(existTimeRangeList)) {
                        const matchTimeRange =
                            existTimeRangeList?.find((item) => {
                                return !!item.list?.find(
                                    (t) =>
                                        t.orderNo ==
                                        (!this.needCalcNumber() ? this._registrationFormItem?.orderNo : registrationInfo?.orderNo)
                                );
                            }) ?? existTimeRangeList?.[0];
                        //预约时间--是否按照精确时间预约(是--去list内层的时间段；否-取list外层的时间段)
                        const preferredTime = matchTimeRange?.list?.find((t) =>
                            isEdit
                                ? !this.needCalcNumber()
                                    ? this._registrationFormItem?.orderNo == t.orderNo
                                    : registrationInfo?.orderNo
                                    ? t.orderNo == registrationInfo?.orderNo
                                    : t.restCount
                                : t.restCount
                        );
                        //已完成的单据在没有可匹配时段时，保持原样
                        if (!this.id || !!preferredTime) {
                            this.innerState.detail!.registrationFormItem!.orderNo = preferredTime?.orderNo;
                            this.innerState.detail!.registrationFormItem!.reserveTime = {
                                start: this.innerState.isAccuratePart ? preferredTime?.start : matchTimeRange?.start,
                                end: this.innerState.isAccuratePart ? preferredTime?.end : matchTimeRange?.end,
                            };
                        }
                    }
                    // 挂号单详情单在没有排班信息的时候并且没有做任何更改的时候，不调用算号
                    // 固定模式下才会调用算号，灵活模式不调用
                    if (
                        !this.innerState.isFlexibleMode &&
                        !initDetailState &&
                        (!userCenter.isAllowedRegUpgrade || needDoctorOrderNo || !existTimeRangeList?.length)
                    ) {
                        if (
                            this.innerState.isEnableNoneScheduleRegistration ||
                            this.innerState.registrationTimeRangeList?.find(
                                (item) => item.timeOfDay == this.innerState.detail!.registrationFormItem?.timeOfDay
                            )
                        ) {
                            this.resetRegistrationTime();
                        }
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        //微信沟通
        this._setPatientRegAuditStatusSubject
            .pipe(
                switchMap(() => {
                    const detail = this.innerState.detail,
                        registrationFormItem = detail?.registrationFormItem;
                    const req = {
                        couponPromotions: [],
                        giftRulePromotions: [],
                        patientCardPromotions: [],
                        patientPointsInfo: undefined,
                        promotions: [],
                        pay: {
                            useMemberFlag: detail?.chargeSheet?.useMemberFlag ?? 0,
                            ...this.innerState.pay,
                        },
                        registrationFormItem: {
                            departmentId: registrationFormItem?.departmentId,
                            departmentName: registrationFormItem?.departmentName,
                            doctorId: registrationFormItem?.doctorId,
                            doctorName: registrationFormItem?.doctorName,
                            isReserved: registrationFormItem?.isReserved,
                            orderNo: registrationFormItem?.orderNo,
                            registrationProductIds: registrationFormItem?.registrationProducts
                                ?.filter((t) => !!t?.id)
                                ?.map((item) => item.id),
                            reserveDate: registrationFormItem?.reserveDate,
                            reserveTime: registrationFormItem?.reserveTime,
                        },
                        revisitStatus: detail?.revisitStatus,
                        visitSourceFrom: detail?.visitSourceFrom,
                        visitSourceId: detail?.visitSourceId,
                        visitSourceRemark: detail?.visitSourceRemark,
                    };
                    return RegistrationAgent.putRegistrationPatientManageAudit(detail?.id ?? "", {
                        auditStatus: PatientRegAuditStatus.processed,
                        registrationReq: req,
                    })
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe(() => {
                ABCNavigator.pop({ success: true });
            })
            .addToDisposableBag(this);

        RegistrationAgent.changeObserver
            .subscribe((rsp) => {
                this.id = rsp?.id ?? this.id;
                this._registrationInvoiceDetailTrigger.next({ id: this.id! });
            })
            .addToDisposableBag(this);
        /**
         * 锁单socket信息（挂号目前显示收费中（支持取消）、退费中、医保退费异常）
         */
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.detail?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            if (!data.businessKey) return;
            this.innerState.detail = this.innerState.detail ?? new RegistrationDetail();
            this.innerState.detail.chargeSheet = this.innerState.detail.chargeSheet ?? new ChargeInvoiceDetailData();
            this.innerState.detail.chargeSheet.copyPatientOrderLocks = this.innerState.detail.chargeSheet.copyPatientOrderLocks ?? [];
            const patientOrderLock = this.innerState.detail.chargeSheet.copyPatientOrderLocks?.find(
                (t) => t.businessKey == PatientOrderLockType.chargeSheet
            );
            if (!!patientOrderLock) {
                Object.assign(patientOrderLock, {
                    ...data,
                });
            } else {
                this.innerState.detail.chargeSheet.copyPatientOrderLocks!.push(
                    JsonMapper.deserialize(PatientOrderLockDetail, {
                        ...data,
                    })
                );
            }
            // 如果是收费支付锁单，还需要判断当前是哪方发起的
            if (data.value?.chargeInProgress) {
                this.innerState.detail.chargeSheet.lockStatus = data.value?.businessDetail?.addedLockStatus;
                this.innerState.detail.chargeSheet.lockPayTransactionInfo = this.innerState.detail.chargeSheet.lockPayTransactionInfo ?? {};
                this.innerState.detail.chargeSheet.lockPayTransactionInfo.id = data.value?.businessDetail?.chargePayTransactionId;
            }
            this.update();
        });
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        //初始化相关依赖配置
        await this._initPageConfig();
        this._initPageTrigger();
        this._initPageData();
    }

    private _initPageData(): void {
        //初复诊占用时长
        const serviceDuration = userCenter.dentistryConfig?.serviceDuration;
        this.innerState.visitDuration = serviceDuration?.visitServiceDuration;
        this.innerState.reVisitDuration = serviceDuration?.revisitedServiceDuration;
        if (!!this.id) {
            this.innerState.type = Math.max(RegistrationInvoiceType.detail, this.innerState.type);
            this._registrationInvoiceDetailTrigger.next({ id: this.id!, isInit: true });
        } else {
            this.innerState.stopLoading();
            this.innerState.detail = new RegistrationDetail();
            this.innerState.detail.patient = this._patientInfo;
            this.innerState.detail.chargeSheet = new ChargeInvoiceDetailData();
            this.innerState.detail!.chargeSheet!.chargeForms = [
                JsonMapper.deserialize(ChargeForm, {
                    sourceFormType: 1,
                    chargeFormItems: [
                        JsonMapper.deserialize(ChargeFormItem, {
                            unit: "次",
                            name: this.innerState.clinicBasicSetting?.registrationFeeStr,
                            unitCount: 1,
                            doseCount: 1,
                            unitPrice: +(this.innerState.pay?.fee ?? 0),
                            sourceUnitPrice: +(this.innerState.pay?.fee ?? 0),
                            doctorInfo: {
                                doctorId: this.innerState.detail?.registrationFormItem?.doctorId,
                                departmentId: this.innerState.detail?.registrationFormItem?.departmentId,
                                registrationCategory: this.innerState.detail?.registrationFormItem?.registrationCategory,
                            },
                        }),
                    ],
                }),
            ];
            const registrationFormItem = JsonMapper.deserialize(RegistrationFormItem, this._registrationFormItem);
            registrationFormItem.isReserved =
                registrationFormItem.isReserved ?? (this.isAgain || this._source == RegistrationPageSourceType.patientChat ? 1 : 0);
            registrationFormItem.reserveDate = registrationFormItem.reserveDate ?? new Date();
            this.innerState.detail!.registrationFormItem = registrationFormItem;
            this.innerState.detail.revisitStatus = this.isAgain ? RegistrationRevisitStatus.again : RegistrationRevisitStatus.first;
            this._initSelectedDepartmentDoctor();
            this.calculateRegCategoriesFee();
            this._initGetDetailAfterOperate();
        }
        this.update();
    }

    // 在获取到detail数据后的操作
    private _initGetDetailAfterOperate(): void {
        //灵活模式下
        if (this.innerState.isFlexibleMode) {
            this._registrationLocalTimeCountTrigger.next();
            this.flexibleServiceDuration();
        } else {
            //固定模式下
            if (userCenter.isAllowedRegUpgrade) {
                if (this.innerState.detail?.registrationFormItem?.isReserved) {
                    this._registrationDailyReserveStatusTrigger.next();
                } else {
                    if (!this.id || this.innerState.hasChange) this.getReserveTime();
                }
                this._registrationDesignatedTimeTrigger.next();
            } else {
                // 详情不做任何更改，不调用
                const isModified = !this.id || this.innerState.hasChange;
                if (isModified) this.getReserveTime();
                if (!this.innerState.detail?.registrationFormItem?.isReserved && isModified) {
                    if (this.innerState.detail?.registrationFormItem) this.innerState.detail.registrationFormItem.reserveTime = undefined;
                    this.dispatch(new _EventCalculateOrderNo());
                }
            }
        }
        this._calculatePriceTrigger.next();
    }

    private async getClinicAllDoctorList(selectDate?: Date): Promise<void> {
        const { isEnableNoneScheduleRegistration, detail } = this.innerState;
        if (isEnableNoneScheduleRegistration) return;
        // 医生科室列表
        const { reserveDate, doctorId, departmentId } = detail?.registrationFormItem ?? {};
        this.innerState.departments = await RegistrationDataProvider.getDoctorList({ date: selectDate ?? reserveDate }).catchIgnore();
        if (this._doctorId) {
            this.innerState.departments?.forEach((department) => {
                if (!!this._departmentId && department.departmentId != this._departmentId) return;
                const doctor = department.doctors?.find((doctor) => doctor?.doctorId == this._doctorId);
                if (!!doctor) {
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                }
            });
        } else if (!!doctorId) {
            this.innerState.departments?.forEach((department) => {
                if (!!departmentId && department.departmentId != departmentId) return;
                const doctor = department.doctors?.find((doctor) => doctor?.doctorId == doctorId);
                if (!!doctor) {
                    this.innerState.currentDepartmentDoctors = department.doctors;
                    this.innerState.currentSelectedDoctors = doctor;
                }
            });
        } else {
            const currentDepartment = this.innerState.departments?.find((item) => item.departmentId == departmentId);
            this.innerState.currentDepartmentDoctors = currentDepartment?.doctors ?? _.first(this.innerState.departments)?.doctors;
        }
    }

    async *_mapEventSelectTimeRange(event: _EventSelectTimeRange): AsyncGenerator<State> {
        if (!event.range) return;
        this.innerState.detail!.registrationFormItem = this.innerState.detail?.registrationFormItem ?? new RegistrationFormItem();
        const registrationInfo = this.innerState.detail!.registrationFormItem;
        registrationInfo!.reserveTime = registrationInfo?.reserveTime ?? new Range();
        registrationInfo!.reserveTime = {
            start: TimeUtils.formatDate(event.range.start, "HH:mm"),
            end: TimeUtils.formatDate(event.range.end, "HH:mm"),
        };
        this.innerState.hasChange = true;
        this.update();
    }

    async *_mapEventUpdateRegistrationInfo(): AsyncGenerator<State> {
        const result = await ABCNavigator.navigateToPage<RegistrationDetail>(
            <DentistryInvoicePage id={this.id} doctorId={this._doctorId} />,
            {
                transitionType: TransitionType.inFromBottom,
            }
        );
        if (!result) return;
        this.innerState.detail = result;
        this.update();
    }

    /**
     * 完成预诊
     */
    async *_mapEventCompletePreDiagnosis(): AsyncGenerator<State> {
        const result = await showBottomPanel<MedicalRecord>(
            <DentistryPreDiagnosisDialog medicalRecord={this.innerState.dentistryMedicalRecord} />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );
        if (!result) return;
        this.innerState.hasChange = true;
        this.innerState.detail!.chiefComplaint = result?.chiefComplaint;
        this.innerState.detail!.presentHistory = result?.presentHistory;
        this.innerState.detail!.pastHistory = result?.pastHistory;
        this.innerState.detail!.epidemiologicalHistory = result?.epidemiologicalHistory;
        this.innerState.detail!.dentistryExaminations = result?.dentistryExaminations;
        this.innerState.detail!.physicalExamination = result?.physicalExamination;
        this.innerState.detail!.preDiagnosisAttachments = result?.preDiagnosisAttachments;
        const { detail } = this.innerState;
        const req = {
            allergicHistory: detail?.allergicHistory,
            chiefComplaint: detail?.chiefComplaint,
            dentistryExaminations: detail?.dentistryExaminations,
            epidemiologicalHistory: detail?.epidemiologicalHistory,
            familyHistory: detail?.familyHistory,
            pastHistory: detail?.pastHistory,
            personalHistory: detail?.personalHistory,
            physicalExamination: detail?.physicalExamination,
            presentHistory: detail?.presentHistory,
            preDiagnosisAttachments: detail?.preDiagnosisAttachments ?? [],
        };
        const rsp = await DentistryAgent.modifyRegistrationPreDiagnosis(this.id, req).catchIgnore();
        if (rsp?.isSuccess == 1) {
        }
        this.update();
    }

    /**
     * 修改患者信息
     */
    public requestModifyRegistrationPatient(patient: Patient): void {
        this.dispatch(new _EventModifyRegistrationPatient(patient));
    }

    /**
     * 修改挂号类型
     */
    public requestModifyRegistrationType(index: number): void {
        this.dispatch(new _EventModifyRegistrationType(index));
    }

    /**
     * 修改挂号科室
     */
    public requestModifyRegistrationDepartment(): void {
        this.dispatch(new _EventModifyRegistrationDepartment());
    }

    /**
     * 修改挂号医生
     */
    public requestModifyRegistrationDoctor(): void {
        this.dispatch(new _EventModifyRegistrationDoctor());
    }

    /**
     * 修改挂号时间
     */
    public requestModifyRegistrationTime(): void {
        this.dispatch(new _EventModifyRegistrationTime());
    }

    /**
     * 修改挂号诊费
     */
    public requestModifyRegistrationFee(fee: string): void {
        this.dispatch(new _EventModifyRegistrationFee(Number(fee)));
    }

    /**
     * 完成挂号
     */
    public requestFinishRegistration(): void {
        this.dispatch(new _EventFinishRegistration());
    }

    /**
     * 退号
     */
    public requestRefundedRegistration(): void {
        this.dispatch(new _EventRefundedRegistration());
    }

    /**
     * 退费
     */
    public requestRefundedRegistrationFee(): void {
        this.showOperateToastTip();
        const chargeSheet = this.innerState.detail?.chargeSheet;
        if (!chargeSheet?.canEditChargeSheet) return;
        this.dispatch(new _EventRefundedRegistrationFee());
    }

    /**
     * 保存挂号修改信息
     */
    public requestSaveRegistration(): void {
        this.dispatch(new _EventSaveRegistration());
    }

    /**
     * 挂号单收费
     */
    public requestRegistrationCharge(): void {
        this.dispatch(new _EventRegistrationCharge());
    }

    /**
     * 修改预诊信息
     * @param medicalRecord
     */
    public requestModifyMedicalRecord(medicalRecord: MedicalRecord): void {
        this.dispatch(new _EventModifyMedicalRecordCard(medicalRecord));
    }

    /**
     * 签到
     */
    public requestRegistrationSignIn(): void {
        this.dispatch(new _EventRegistrationSignIn());
    }

    private async *_mapEventBackDirectRegistration(): AsyncGenerator<State> {
        if (!this.innerState.detail) return;

        const { patientOrderId, id } = this.innerState.detail;
        const examinationsResult = await ExaminationsAgent.getOutpatientExaminationsListByPatientOrderId(patientOrderId!).catchIgnore();
        if (!examinationsResult?.examItems?.length) {
            const queryResult = await showQueryDialog("是否确认回诊签到", "该患者本次就诊未开具检查检验项目，是否确认回诊？");
            if (queryResult != DialogIndex.positive) return;
        }

        const loadingDialog = new LoadingDialog("正在签到");
        loadingDialog.show(500);
        const result = await DentistryAgent.putManageRegistrationStatus(id!, RegistrationStatusV2.continueDiagnose).catch(
            (e) => new ABCError(e)
        );
        if (result instanceof ABCError) {
            await loadingDialog.fail(errorSummary(result));
            return;
        }
        await loadingDialog.hide();
        RegistrationAgent.changeObserver.next();
        ABCNavigator.pop();
        // this.innerState.detail = result;
        // this.update();
    }

    private async *_mapEventUpdatePatient(event: _EventUpdatePatient): AsyncGenerator<State> {
        if (!event?.patient) return;
        this.innerState.detail!.patient = this.innerState.detail?.patient ?? new Patient();
        this.innerState.detail!.patient = event.patient;
        this.update();
    }
    async *_mapEventCancelPayment(): AsyncGenerator<State> {
        // 进行解锁
        const chargePayTransactionId = this.innerState.detail?.chargeSheet?.lockPayTransactionInfo?.id;
        if (!chargePayTransactionId) return;
        const tipsInfo = {
            confirmText: "",
            errorTitle: "",
            errorContent: "",
        };
        if (this.innerState.detail?.chargeSheet?.microclinicsOrSelfServiceMachines) {
            tipsInfo.confirmText = "您可取消本次支付，取消后患者自助支付将会失败，本单可以重新收费。";
            tipsInfo.errorTitle = "收费单内容已更新";
            tipsInfo.errorContent = "收费单内容已更新，请刷新后重试";
        } else {
            tipsInfo.confirmText = "您可取消本次支付，取消后可重新收费，本次支付不入账。";
            tipsInfo.errorTitle = "取消失败";
            tipsInfo.errorContent = "患者已完成支付，如需取消请操作退费";
        }
        const dialogIndex = await showQueryDialog(
            "支付遇到问题？",
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{tipsInfo?.confirmText}</Text>
            </View>,
            "取消本次支付",
            "暂不操作"
        );
        if (!dialogIndex) return;
        await ChargeAgent.putChargeUnlock(chargePayTransactionId)
            .then((rsp) => {
                if (rsp.code == 200) {
                    Toast.show("取消成功", { success: true });
                }
            })
            .catch((error) => {
                showConfirmDialog(tipsInfo?.errorTitle, `${error?.msg ?? tipsInfo?.errorContent}`, "知道了");
            })
            .finally(() => {
                this._registrationInvoiceDetailTrigger.next({ id: this.id!, isInit: true });
            });
    }

    /**
     * 更换会员卡信息
     * @param member
     */
    public requestUpdateMemberCard(member: SelectMemberInfo): void {
        this.dispatch(new _EventUpdateMemberCard(member));
    }

    /**
     * 更新优惠项
     */

    public requestUpdatePromotions(
        selectPromotions?: number[],
        selectGiftPromotions?: number[],
        selectCouponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ): void {
        this.dispatch(
            new _EventUpdatePromotions(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion)
        );
    }

    /**
     * 修改就诊推荐来源
     */
    public requestModifyRegistrationVisitSource(): void {
        this.dispatch(new _EventModifyRegistrationVisitSource());
    }

    /**
     * 修改就诊推荐备注
     */
    public requestModifyRegistrationVisitSourceRemark(text: string): void {
        this.dispatch(new _EventModifyRegistrationVisitSourceRemark(text));
    }

    /**
     * 切换初诊/复诊
     */
    public requestModifyRevisitStatus(): void {
        this.dispatch(new _EventModifyRevisitStatus());
    }

    /**
     * 修改使用卡项
     * @param cardPromotions
     */
    public requestPatientCardPromotionChanged(cardPromotions: PatientCardPromotion[]): void {
        this.dispatch(new _EventPatientCardPromotion(cardPromotions));
    }

    //根据异常类型，做相应的处理
    requestHandleChargeAbnormal(isShebaoAbnormal: boolean): void {
        this.dispatch(new _EventHandleChargeAbnormal(isShebaoAbnormal));
    }

    //异常退费
    requestAbnormalRefund(abnormalMsg: AbnormalTransactionList): void {
        this.dispatch(new _EventAbnormalRefund(abnormalMsg));
    }

    //预约项目
    requestSearchProject(registrationProducts?: RegistrationProducts[]): void {
        this.dispatch(new _EventSearchProduct(registrationProducts));
    }

    /**
     * 固定模式---修改日期
     */
    requestModifyFixedSourceDate(): void {
        this.dispatch(new _EventModifyFixedSourceDate());
    }

    /**
     * 固定模式--预约类型下时间段
     */
    requestModifyFixedTimeRange(): void {
        this.dispatch(new _EventModifyFixedTimeRange());
    }

    /**
     * 固定模式--修改号源时间
     */
    requestModifyFixedSourceTime(): void {
        this.dispatch(new _EventModifyFixedSourceTime());
    }

    /**
     * 优惠明细弹窗
     */
    requestDiscountDetail(): void {
        this.dispatch(new _EventDiscountDetail());
    }

    /**
     * 改变当前选中的日期
     */
    requestChangeDate(): void {
        this.dispatch(new _EventChangeDate());
    }

    /**
     * 选择预约时间范围
     * @param date
     */
    requestSelectTimeRange(range?: Range<Date>): void {
        this.dispatch(new _EventSelectTimeRange(range));
    }

    /**
     * 更新就诊相关信息
     */
    requestUpdateRegistrationInfo(): void {
        this.dispatch(new _EventUpdateRegistrationInfo());
    }

    /**
     * 完成预诊
     */
    requestCompletePreDiagnosis(): void {
        this.dispatch(new _EventCompletePreDiagnosis());
    }

    /**
     * 更新患者信息
     */
    requestUpdatePatient(patient: Patient): void {
        this.dispatch(new _EventUpdatePatient(patient));
    }
    //  取消本次支付
    requestCancelPayment(): void {
        this.dispatch(new _EventCancelPayment());
    }
}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventModifyRegistrationPatient extends _Event {
    patient: Patient;

    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}

class _EventFinishRegistration extends _Event {}

class _EventBackDirectRegistration extends _Event {}

class _EventRefundedRegistration extends _Event {}

class _EventRefundedRegistrationFee extends _Event {}

class _EventSaveRegistration extends _Event {
    successCallback?(): void;

    constructor(successCallback?: () => void) {
        super();
        this.successCallback = successCallback;
    }
}

class _EventRegistrationCharge extends _Event {}

class _EventCalculateOrderNo extends _Event {}

class _EventUpdateMemberCard extends _Event {
    member: SelectMemberInfo;

    constructor(member: SelectMemberInfo) {
        super();
        this.member = member;
    }
}

class _EventUpdatePromotions extends _Event {
    selectPromotions?: number[];
    selectGiftPromotions?: number[];
    selectCouponPromotions? = new AbcMap<CouponPromotion, Pair<boolean, number>>();
    selectMemberPointPromotion?: PatientPointsInfo;

    constructor(
        promotions?: number[],
        giftPromotions?: number[],
        couponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ) {
        super();
        this.selectPromotions = promotions;
        this.selectGiftPromotions = giftPromotions;
        this.selectCouponPromotions = couponPromotions;
        this.selectMemberPointPromotion = selectMemberPointPromotion;
    }
}

class _EventModifyRegistrationVisitSource extends _Event {}

class _EventModifyRegistrationVisitSourceRemark extends _Event {
    remark: string;

    constructor(text: string) {
        super();
        this.remark = text;
    }
}

class _EventModifyRevisitStatus extends _Event {}

class _EventModifyRegistrationCategory extends _Event {}

class _EventPatientCardPromotion extends _Event {
    patientCardPromotions: PatientCardPromotion[];

    constructor(patientCardPromotions: PatientCardPromotion[]) {
        super();
        this.patientCardPromotions = patientCardPromotions;
    }
}

class _EventHandleChargeAbnormal extends _Event {
    isShebaoAbnormal: boolean;

    constructor(isShebaoAbnormal: boolean) {
        super();
        this.isShebaoAbnormal = isShebaoAbnormal;
    }
}

class _EventAbnormalRefund extends _Event {
    abnormalMsg: AbnormalTransactionList;

    constructor(abnormalMsg: AbnormalTransactionList) {
        super();
        this.abnormalMsg = abnormalMsg;
    }
}

class _EventSearchProduct extends _Event {
    registrationProducts?: RegistrationProducts[];

    constructor(registrationProducts?: RegistrationProducts[]) {
        super();
        this.registrationProducts = registrationProducts;
    }
}

class _EventModifyFixedSourceDate extends _Event {}

class _EventModifyFixedTimeRange extends _Event {}

class _EventModifyFixedSourceTime extends _Event {}

class _EventDiscountDetail extends _Event {}

class _EventChangeDate extends _Event {}

class _EventUpdateRegistrationInfo extends _Event {}

class _EventCompletePreDiagnosis extends _Event {}

class _EventSelectTimeRange extends _Event {
    range?: Range<Date>;

    constructor(range?: Range<Date>) {
        super();
        this.range = range;
    }
}

class _EventUpdatePatient extends _Event {
    patient: Patient;

    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}
class _EventCancelPayment extends _Event {}
