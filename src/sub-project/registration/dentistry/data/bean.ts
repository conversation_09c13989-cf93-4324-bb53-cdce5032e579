import { RolesType } from "../../../user-center/user-center";
import { fromJsonToDate, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { CouponPromotion, GiftPromotion, PatientCardPromotion, PatientPointsInfo, Promotion } from "../../../charge/data/charge-beans";
import { AttachmentItem, DentistryMedicalRecordItem, Patient } from "../../../base-business/data/beans";
import { RegistrationFormItem, RegistrationsAppointmentServiceType } from "../../data/bean";
import { ShebaoCardInfo } from "../../../base-business/data/shebao/bean";

export enum RegistrationType {
    outpatientRegistration = 0, //门诊挂号
    therapyAppointment = 1, //理疗预约,
}

export enum RegistrationModeType {
    fixedMode = 0, //号源模式
    flexibleMode = 1, //灵活时间模式
}

//当前排班状态
export enum RegistrationReserveStatus {
    canAppointment = 0,
    finishedDiagnosis = 4,
    notScheduling = 5,
}
class ReserveEndTime {
    month?: number;
    day?: number;
    week?: number;
}

export class ReserveStartTime {
    hour?: number;
    min?: number;
}

//患者可预约startTime到endTime内的时间
export class ReservationTimeRange {
    @JsonProperty({ type: ReserveEndTime })
    endTime?: ReserveEndTime;
    registerStartTime?: string;
    @JsonProperty({ type: ReserveStartTime })
    startTime?: ReserveStartTime;
}

//患者不确定预约项目时
export class ServiceDuration {
    @JsonProperty({ type: ReserveStartTime })
    revisitedServiceDuration?: ReserveStartTime; //复诊占用时长
    @JsonProperty({ type: ReserveStartTime })
    visitServiceDuration?: ReserveStartTime; //初诊占用时长
}

class WechatReserveLimitCountRule {
    limitCount?: number; //限制微信预约号数数量
    @JsonProperty({ type: ReserveEndTime })
    limitCountPeriod?: ReserveEndTime;
}

class RefundRates {
    @JsonProperty({ type: ReserveStartTime })
    minAheadTime?: ReserveStartTime;
    refundRate?: number;
}
class WechatReserveRefundRule {
    @JsonProperty({ type: RefundRates })
    refundRates?: RefundRates;
    type?: number;
}

export class DentistryConfig {
    availableRoleIds?: RolesType[]; //可预约角色id集合
    chainId?: string;
    clinicId?: string;
    disableEpidemiologicalRegistration?: number; //流行病史可疑症状是否禁止挂号
    displayName?: string; //展示名称
    enableAheadCloseReserve?: number; //是否提前结束预约
    enableChooseConsultingRoom?: number; //排班时支持选择诊室; 0:不支持; 1:支持
    enableLeaveForMember?: number; //是否开启会员预留号源
    enableLeaveForPC?: number; //是否开启PC预留号源
    enableWechatAddEpidemiological?: number; //微诊所是否开启填写流行病史; 0:否；1:是;
    enableWechatBreakAppointmentPunish?: number; //是否开启微信预约爽约惩罚，0：否；1：是
    enableWechatReserveLimitCount?: number; //是否开启微信预约号数限制; 0:否; 1:是
    enableWechatReserveRefund?: number; //是否开启微信退号；0:否；1：是
    fixedOrderDisplayServiceType?: number;
    hideFeeWeClinicBeforePaid?: number; //收费前是否在微诊所隐藏挂号费。0：不隐藏；1：隐藏
    isOpen?: number; // 是否开启；0:未开启; 1:已开启;
    modeType?: RegistrationModeType; //预约模式; 0:固定号源; 1:灵活时间
    needScanSignIn?: number; //是否需要扫码签到; 0:不开启; 1:开启
    needSignIn?: number; //是否需要签到;0:不需要; 1：需要
    noneScheduleDisableRegistration?: number; //现场挂号-未排班可挂号： 0 未排班可挂号 1 未排班不可挂号（未排班禁止挂号预约，快速接诊不受影响）
    generateOrderNoTime?: number; //号数确定时机;0:预约取号; 10：签到取号
    notMustReserveEmployee?: number; //预约时可不指定人员；0：不支持；1：支持
    @JsonProperty({ type: ReservationTimeRange })
    reservationTimeRange?: ReservationTimeRange; //预约时间
    retreatNotice?: number; //固定号源退号提醒通知，0，不开启，1，开启
    @JsonProperty({ type: ServiceDuration })
    serviceDuration?: ServiceDuration; //预约时长
    serviceType?: RegistrationsAppointmentServiceType; //固定号源预约模式服务类型；0：按上午/下午/晚上; 1:按精确时间段预约; 2:按自定义时段
    showNoneScheduleDoctor?: number; //是否展示无排班的医生
    showReserveProduct?: number; //是否展示可预约项目；0:否; 1:是;
    type?: number; //预约类型; 0:门诊; 1:治疗理疗
    wechatEnableMemberCardPay?: number; //微信预约是否支持会员卡支付挂号费；0:否；1：是
    wechatReserveLimitCountRule?: WechatReserveLimitCountRule;
    wechatReserveMustPay?: number; //微信预约是否必须支付; 0:否；1：是
    wechatReserveNeedApply?: number; //微信预约是否需要申请；0:不需要，预约后直接成功; 1:需要，预约后需要诊所确认
    @JsonProperty({ type: WechatReserveRefundRule })
    wechatReserveRefundRule?: WechatReserveRefundRule;
    payMode?: number; //支付方式 0暂不收费，1必须收费
    remindAdditionalOrderNo?: number; //现场挂号-未排班不可挂号：1 号源约满后提示加号 0 号源约满后不提示加号

    // 是否需要签到
    get isNeedSignIn(): boolean {
        return this.needSignIn == 1;
    }

    // 是否签到取号
    get isSignInGetOrderNo(): boolean {
        return this.generateOrderNoTime == 10;
    }

    //是否是灵活模式
    get isFlexibleMode(): boolean {
        return this.modeType == RegistrationModeType.flexibleMode;
    }

    //是否是号源模式
    get isFixedMode(): boolean {
        return !this.modeType;
    }

    //是否开启预约项目
    get isOpenReserveProduct(): boolean {
        return this.showReserveProduct == 1;
    }

    get isDefaultPart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.defaultPart;
    }

    get isAccuratePart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.accurate;
    }

    get isCustomPart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.customPart;
    }

    //预约时，是否必须指定医生或者理疗师
    get isMustReserveEmployee(): boolean {
        return this.notMustReserveEmployee == 0;
    }

    //是否开启会员预留号
    get isEnableLeaveForMember(): boolean {
        return this.enableLeaveForMember == 1;
    }

    //是否开启现场预留号
    get isEnableLeaveForPC(): boolean {
        return this.enableLeaveForPC == 1;
    }

    // 允许未排班医生进行挂号预约
    get isEnableNoneScheduleRegistration(): boolean {
        return this.noneScheduleDisableRegistration == 0;
    }

    // 显示挂号追加号提示
    get isRemindAdditionalOrderNo(): boolean {
        return !!this.remindAdditionalOrderNo;
    }
}

class RegistrationProductSubClinic {
    clinicId?: string;
    clinicName?: string;
}

class RegistrationProductDoctor {
    doctorId?: string;
    doctorName?: string;
}

export class DentistryClinicProduct {
    attachments?: string[]; //附件介绍
    chainId?: string;
    checked?: number; //是否选中(本门店是否已安装) 0-否 1-是
    clinicId?: string;
    clinicNumber?: number; //诊所数量
    disable?: number; //是否禁用 是-1 否-0
    displayName?: string; //微诊所展示名称
    displayServiceDuration?: string; //展示服务时长
    doctorNumber?: number; //医生数量
    enableForAllClinic?: number; //是否全部诊所可约 0-否 1-是
    enableForAllEmployee?: number; //是否全部医生可约 0-否 1-是
    goodsId?: string; //	商品id
    goodsName?: string; //项目(商品名称)
    id?: string;
    introduce?: string; //项目介绍
    name?: string; //诊所可预约项目名称
    price?: number;
    @JsonProperty({ type: Array, clazz: RegistrationProductSubClinic })
    registrationProductClinics?: RegistrationProductSubClinic[]; //预约项目可约诊所列表
    @JsonProperty({ type: Array, clazz: RegistrationProductDoctor })
    registrationProductDoctors?: RegistrationProductDoctor[]; //预约项目可约医生列表
    registrationProductTypeIds?: string[]; //预约项目分类ids
    registrationProductTypeNames?: string; //预约项目分类names
    registrationType?: number; //v2_registration_config表type
    sort?: number; //排序规则
    @JsonProperty({ type: ReserveStartTime })
    serviceMaxDuration?: ReserveStartTime;
    @JsonProperty({ type: ReserveStartTime })
    serviceMinDuration?: ReserveStartTime; //理疗预约最小时长
    @JsonProperty({ type: ReserveStartTime })
    revisitedServiceDuration?: ReserveStartTime; //复诊时长
    @JsonProperty({ type: ReserveStartTime })
    visitServiceDuration?: ReserveStartTime; //初诊时长
}

export class DentistryRemarkInfo {
    chainId?: string;
    clinicId?: string;
    content?: string; //内容
    disableDelete?: number; //是否禁止删除；0：不禁止；1：禁止
    disableModify?: number; //是否禁止修改；0：不禁止；1：禁止
    id?: string;
    registrationType?: number; //预约类型
    sort?: number;
}

export interface RegistrationQuickListReq {
    offset: number;
    limit?: number;
    registrationType?: number;
    departmentId?: string;
    doctorId?: string;
    date?: Date;
    keyword?: string;
    displayStatus?: number;
    patientId?: string;
    start?: Date;
    end?: Date;
}

export interface RegistrationLocalTimeCountReq {
    departmentId?: string;
    doctorId?: string;
    start?: string;
    end?: string;
    registrationType?: number;
    reserveDate?: Date;
}

export class RegistrationLocalTimeCountRsp {
    count?: number; //数量
    @JsonProperty({ fromJson: fromJsonToDate })
    localTime?: Date; //小时分钟；HH:mm
    isStopDiagnose?: number; // 0未全部停诊 1全部停诊
}

export interface RegistrationDailyReserveStatusReq {
    departmentId?: string;
    doctorId?: string;
    start?: Date;
    end?: Date;
    registrationType?: number;
    isRevisited?: number;
}

export class RegistrationDailyReserveStatusRsp {
    @JsonProperty({ fromJson: fromJsonToDate })
    date?: Date; //日期
    dayOfWeek?: string; //周几
    status?: number; //排班状态
    statusName?: string; //排班状态展示
}

export class RegistrationPay {
    fee?: number;
    memberId?: string;
    payMode?: number;
    paySubMode?: number;
    payType?: number;
    receivable?: number;
    refundOriginalPayMode?: number;
}

export class RegistrationEntranceReq {
    allergicHistory?: string;
    chiefComplaint?: string;
    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    dentistryExaminations?: DentistryMedicalRecordItem[];
    epidemiologicalHistory?: string;
    familyHistory?: string;
    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];
    msgId?: string;
    pastHistory?: string;
    patient?: Patient;
    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];
    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;
    @JsonProperty({ type: RegistrationPay })
    pay?: RegistrationPay;
    personalHistory?: string;
    physicalExamination?: string;
    presentHistory?: string;
    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];
    @JsonProperty({ type: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem;
    registrationType?: number;
    revisitStatus?: number;
    @JsonProperty({ type: ShebaoCardInfo })
    shebaoCardInfo?: ShebaoCardInfo;
    signIn?: number;
    source?: number;
    visitSourceFrom?: string;
    visitSourceId?: string;
    visitSourceRemark?: string;
    wxUserId?: number;
}

export class RegistrationInfoModifyReq {
    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];
    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];
    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];
    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;
    @JsonProperty({ type: RegistrationPay })
    pay?: RegistrationPay;
    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];
    @JsonProperty({ type: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem;
    revisitStatus?: number;
    visitSourceFrom?: string;
    visitSourceId?: string;
    visitSourceRemark?: string;
}
export class RegistrationInfoModifyRsp {
    isSuccess?: number;
}

export class RegistrationPreDiagnosisReq {
    allergicHistory?: string;
    chiefComplaint?: string;
    // 口腔检查
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    dentistryExaminations?: DentistryMedicalRecordItem[];
    epidemiologicalHistory?: string;
    familyHistory?: string;
    pastHistory?: string;
    personalHistory?: string;
    physicalExamination?: string;
    presentHistory?: string;
    // 附件图片
    @JsonProperty({ type: Array, clazz: AttachmentItem })
    preDiagnosisAttachments?: Array<AttachmentItem>;
}
