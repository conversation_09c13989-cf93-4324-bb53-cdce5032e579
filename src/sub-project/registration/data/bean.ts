/**
 * create by deng<PERSON>e
 * desc:
 * create date 2021/1/6
 */

import {
    AttachmentItem,
    ClinicDoctorInfo,
    DentistryMedicalRecordItem,
    EpidemiologicalHistoryObj,
    GoodsInfo,
    Patient,
} from "../../base-business/data/beans";
import { fromJsonToDate, fromJsonToMap, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { ChargeInvoiceDetailData as _ChargeInvoiceDetailData, QueryExceptionType } from "../../charge/data/charge-beans";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { DataBase } from "../../views/list-data-holder";
import { Range } from "../../base-ui/utils/value-holder";
import { ScheduleIntervalsItem } from "./registration-agent";
import { TherapyRegistration } from "../appointment/data/appointment-bean";
import { AnyType } from "../../common-base-module/common-types";
import _ from "lodash";
import { RegistrationType } from "../dentistry/data/bean";
import { MedicalRecordUtils } from "../../outpatient/medical-record-page/utils/medical-record-utils";
import { Color, Colors } from "../../theme";

export const DEFAULT_DOCTOR_ID = "00000000000000000000000000000000";

export enum RegistrationViewMode {
    list, // 列表视图
    kanban = 1, // 看板视图
}

export class FocusItemKeys {
    static patient = "patient";
    static doctorId = "doctorId";
    static department = "department";
    static reserveDate = "reserveDate";
    static reserveTime = "reserveTime";
    static fee = "fee";
}

export enum RegistrationTabType {
    registration = 0,
    appointment = 1,
}

export enum RegistrationStatus {
    all, // 全部
    waitSign = 1, // 待签到
    wait = 2, // 待诊
    visited = 3, // 已诊
    expired = 4, // 过期
    refunded = 5, // 已退
    signed = 6, // 已签
    waitPay = 9, // 待收
}

export enum RegistrationStatusV2 {
    unknown = 0,
    waitingPay = 10, // 待支付
    reserved = 12, //已预约
    waitPay = 13, //待支付
    waitingSignIn = 20, // 待签到
    signed = 21, //已签
    waitingSignInContacted = 22, //已签
    waitingDiagnose = 30, // 待诊
    diagnosing = 31, // 就诊中
    diagnosed = 40, // 已诊
    continueDiagnose = 41, // 回诊
    refunded = 90, // 退号
    expired = 91, // 已过期
    canceled = 92, // 已取消
}

export enum PayStatusV2 {
    notPaid = 0,
    partedPaid = 10,
    paid = 20,
    partedRefunded = 30,
    refunded = 40,
}

export enum BusinessType {
    registration = 0, // 门诊挂号
    therapy = 1, // 理疗预约
}

// 时间表状态
export enum ScheduleSelfStatusEnum {
    NORMAL = 0, // 正常
    STOP_DIAGNOSE = 10, // 停诊
}

export enum DoctorShiftsType {
    normal, //普通号源
    forPatient, //预留号源
    forMember, //会员预留
}

export const DefaultRegistrationTimeOptions: {
    id: number;
    title: string;
    timeOfDay: string;
    timeOfDayCount?: number;
    range: Range<string>;
}[] = [
    { id: 1, title: "今天", timeOfDay: "上午", timeOfDayCount: undefined, range: new Range<string>("00:00", "12:00") },
    { id: 2, title: "今天", timeOfDay: "下午", timeOfDayCount: undefined, range: new Range<string>("12:00", "18:00") },
    { id: 3, title: "今天", timeOfDay: "晚上", timeOfDayCount: undefined, range: new Range<string>("18:00", "24:00") },
    { id: 4, title: "指定时间", timeOfDay: "", timeOfDayCount: undefined, range: new Range<string>("00:00", "24:00") },
];
export enum RegistrationInvoiceType {
    create = 1,
    detail = 2,
    dialog = 3,
}

export enum RegistrationDiffForRevisitedType {
    feesUniform = 0, // 所有患者挂号费相同
    feesDifferByVisit = 1, // 初复诊挂号费不同
    shortRevisitsDifferent = 2, // 短期内再次就诊挂号费不同
}

export interface OldReserveInfo {
    orderNo: number;
    orderNoType: number;

    reserveDate: Date;

    reserveOrderNo: number;
    reserveStart: string;
    reserveEnd: string;
    scheduleId: number;
    consultingRoomId: string;
    consultingRoomName: string;
    reason: number;
}

export class RegistrationProducts {
    displayName?: string;
    id?: string;
}

export class RegistrationFormItem {
    id?: string;
    fee?: number;
    type?: number; // type: 3，微诊所预约/挂号。2，门诊快速接诊挂号。1，微信预约/挂号（老接口）。0，pc预约/挂号
    get typeDisplayName(): string {
        switch (this.type) {
            case 0:
                return "pc预约";
            case 1:
                return "微信预约";
            case 3:
                return "微诊所预约";
            default:
                return "";
        }
    }
    orderNo?: number;
    orderNoType?: OrderNoType;
    status?: number;
    statusV2?: RegistrationStatusV2;
    statusName?: string;
    statusV2Name?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    displayCreatedByName?: string; //创建人姓名

    @JsonProperty({ fromJson: fromJsonToDate })
    diagnosingTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    currentTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    signInTime?: Date;

    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    consultingRoomId?: string;
    consultingRoomName?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    estimatedDiagnoseTime?: Date;
    isReserved?: number; // 预约=1、挂号=0
    isRevisited?: number;
    orderNoStr?: string;
    isAdditional?: number; // 0 1  （1表示+号）
    patientOrderId?: string;
    payStatusV2?: number;
    registrationSheetId?: string;
    @JsonProperty({ type: Array, clazz: RegistrationProducts })
    registrationProducts?: RegistrationProducts[]; //预约项目
    registrationProductIds?: (string | undefined)[]; //预约项目id集合
    registrationType?: RegistrationType;
    registrationFormId?: string;
    visitSourceRemark?: string;
    revisitStatus?: number;
    scheduleSelfStatus?: ScheduleSelfStatusEnum; // 停诊状态

    // 是停诊状态
    get isStopScheduleSelfStatus(): boolean {
        return this.scheduleSelfStatus == ScheduleSelfStatusEnum.STOP_DIAGNOSE;
    }

    get getRevisitStatusName(): string {
        switch (this.revisitStatus) {
            case RegistrationRevisitStatus.again:
                return "复诊";
            case RegistrationRevisitStatus.first:
                return "初诊";
            default:
                return "";
        }
    }
    // oldReserveInfo: null;
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    timeOfDay?: string;
    orderNoTimeOfDay?: string;
    reserveEnd?: string;
    reserveStart?: string;
    @JsonProperty({
        fromJson: (json) => {
            return new Range<string>(json.start, json.end, json.orderNoStart, json.orderNoEnd);
        },
    })
    reserveTime?: Range<string>;
    // 终端自定义字段（老版本挂号算号的实际时间，用于页面展示）
    // 为什么不用reserveTime展示，是因为挂号算号的时候传的是上午，下午，晚上的时段，为了方便，在算号的时候没有对这个字段进行覆盖
    _registrationReserveTime?: Range<string>;
    reserveOrderNo?: number;
    signIn?: number;

    //状态持续时间
    get statusDuration(): number {
        const diff =
            (this.currentTime ?? new Date()).getTime() -
            (
                this.diagnosingTime ??
                (this.isReserved ? this.signInTime ?? this.estimatedDiagnoseTime : this.created) ??
                new Date()
            ).getTime();
        return Math.round(diff / 1000 / 60);
    }

    // signInTime: null;

    @JsonProperty({ fromJson: fromJsonToMap })
    timeOfDayTotalCountMap?: Map<string, number>;

    oldReserveInfo?: OldReserveInfo;

    visitSourceName?: string;
    visitSourceFrom?: string;
    visitSourceFromName?: string;

    registrationCategory?: number;
    get registrationCategoryDisplay(): string {
        if (this.registrationCategory == 2) {
            return "便民门诊";
        } else if (this.registrationCategory == 1) {
            return "专家门诊";
        } else {
            return "普通门诊";
        }
    }

    get isReservedName(): string {
        let str = "";
        if (this.isReserved == 1) {
            str = "预约";
        } else str = "挂号";

        return str;
    }

    // 是额外+号
    extraNumPool(): boolean {
        // 只有挂号类型才有额外号说法，预约类型是根据排班时间段来的
        // 升级版本的挂号预约只有后置"加"图标的形式，老版本的才有前置+号形式
        const { userCenter } = require("../../user-center");
        if (this.isReserved || userCenter.isAllowedRegUpgrade) return false;
        return !_.isNil(this.isAdditional) && (this.isAdditional ?? 0) > 0;
    }

    get displayOrderNumber(): string {
        if (this.orderNo) {
            // 存在的挂号单，直接读取
            let precision = 2;
            if (this.orderNo >= 100) {
                precision = 3;
            }
            // return `${this.extraNumPool ? "+" : ""}` + StringUtils.zeroFill(this.orderNo, precision) + "号";
            return `${this.extraNumPool() ? `+${this.orderNo ?? precision}号` : `${StringUtils.zeroFill(this.orderNo, precision)}号`}`;
        }
        return "";
    }

    getDisplayOrderNumber(orderNo?: number): string {
        if (!!orderNo) {
            // 存在的挂号单，直接读取
            let precision = 2;
            if (orderNo >= 100) {
                precision = 3;
            }
            return `${this.extraNumPool() ? `+${orderNo ?? precision}号` : `${StringUtils.zeroFill(orderNo, precision)}号`}`;
        }
        return "";
    }

    get displayDoctorName(): string {
        let str = "";
        if (this.departmentName) {
            str += this.departmentName + "-";
        }
        str += this.doctorName?.length ? this.doctorName : "不指定医生";
        return str;
    }

    get displayDoctorDepartName(): string {
        let str = "";
        str += this.doctorName?.length ? this.doctorName : "不指定医生";
        if (this.departmentName) {
            str += "-" + this.departmentName;
        }

        return str;
    }

    get registrationProductName(): string {
        const productList: string[] = [];
        this.registrationProducts?.map((item) => {
            !!item?.displayName && productList.push(item.displayName);
        });
        return productList?.join("、");
    }

    _reserveTime?: Range<string>;

    referralFlag?: number; // 是否转诊 1 true 0 false
    get isReferral(): boolean {
        return (this.referralFlag ?? 0) > 0;
    }
    get isConvenient(): boolean {
        return this.registrationCategory == 2;
    }
}

export class ChargeInvoiceDetailData extends _ChargeInvoiceDetailData {
    @JsonProperty({ type: RegistrationFormItem })
    registrationDetail__?: RegistrationFormItem;
    useMemberFlag?: number;
}

export enum RegistrationRevisitStatus {
    first = 1,
    again = 2,
}

export class RegistrationReferralSource {
    patientOrderId?: string;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    referralTime?: Date;
    isReserved?: number;

    get displayDoctorDepartName(): string {
        let str = "";
        str += this.doctorName?.length ? this.doctorName : "不指定医生";
        if (this.departmentName) {
            str += "-" + this.departmentName;
        }

        return str;
    }
}

export class RegistrationDetailReserveTime {
    end?: string;
    start?: string;
}

export class RegistrationDetail extends DataBase {
    id?: string;
    registrationId?: string; // 理疗预约列表使用 ID

    allergicHistory?: string;
    chiefComplaint?: string;
    familyHistory?: string;
    pastHistory?: string;
    personalHistory?: string;
    physicalExamination?: string;
    presentHistory?: string;
    epidemiologicalHistory?: string; //流行病史
    // 口腔检查
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    dentistryExaminations?: DentistryMedicalRecordItem[];

    // shebaoCardInfo?: string;

    patientOrderId?: string;

    @JsonProperty({ type: Patient })
    patient?: Patient;

    @JsonProperty({ type: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem;

    @JsonProperty({ type: TherapyRegistration })
    therapyRegistration?: TherapyRegistration;

    @JsonProperty({ type: ChargeInvoiceDetailData })
    chargeSheet?: ChargeInvoiceDetailData;

    //【ID1002086】济华-就诊来源填写/统计查看
    visitSourceId?: string;

    visitSourceName?: string;
    visitSourceFrom?: string;
    visitSourceFromName?: string;
    visitSourceRemark?: string; // 就诊来源备注

    revisitStatus?: RegistrationRevisitStatus;
    referralSource?: RegistrationReferralSource;
    @JsonProperty({ type: Array, clazz: AttachmentItem })
    preDiagnosisAttachments?: Array<AttachmentItem>; // 预诊附件上传图片

    chargeSheetQueryExceptionType?: QueryExceptionType; //判断异常收费情况
    //社保异常判断
    get isSheBaoAbnormal(): boolean {
        return (this.chargeSheetQueryExceptionType ?? 0) == QueryExceptionType.shebaoPay ?? false;
    }

    //非社保异常判断
    get isNotSheBaoAbnormal(): boolean {
        return (this.chargeSheetQueryExceptionType ?? 0) > 1 ?? false;
    }

    get displayReferralSourceDoctorDepartName(): string {
        let str = "";
        str += this.referralSource?.doctorName ? this.referralSource.doctorName : "";
        if (this.referralSource?.departmentName) {
            str += "-" + this.referralSource?.departmentName;
        }

        return str;
    }

    get __revisitStatusName(): string {
        switch (this.revisitStatus) {
            case RegistrationRevisitStatus.again:
                return "复诊";
            case RegistrationRevisitStatus.first:
                return "初诊";
            default:
                return "";
        }
    }

    get __visitSourceDisplayName(): string {
        let str = this.visitSourceName ?? "";
        if (!!this.visitSourceFromName) {
            str += `-${this.visitSourceFromName ?? ""}`;
        }
        return str;
    }

    __visitSourceDisplay?: string;

    get __EpidemiologicalHistoryObj(): EpidemiologicalHistoryObj | undefined {
        const epidemiologicalHistory = this.epidemiologicalHistory;
        if (epidemiologicalHistory) {
            let currentObj: EpidemiologicalHistoryObj = {
                patientChecked: false,
                attendantChecked: false,
                suspiciousList: [],
                symptomList: [],
            };

            const valObj = JSON.parse(epidemiologicalHistory);
            if (Array.isArray(valObj)) {
                currentObj.symptomList = valObj;
            } else {
                currentObj = _.assign(currentObj, valObj);
            }
            return currentObj;
        }
        return undefined;
    }

    get dentistryExaminationsStr(): string | undefined {
        const strList = this.dentistryExaminations?.map((item) => {
            return !_.isEmpty(item?.toothNos) ? item?.toothNos?.join("、") : "" + !!item?.value ? " " + item?.value : "";
        });
        return strList?.join("");
    }

    //口腔--预诊信息拼接
    get dentistryPreDiagnosisInfo(): string | undefined {
        const chiefComplaint = !!this.chiefComplaint ? this.chiefComplaint : undefined;
        const presentHistory = !!this.presentHistory ? this.presentHistory : undefined;
        const pastHistory = !!this.pastHistory ? this.pastHistory : undefined;
        const epidemiologicalHistory = !!MedicalRecordUtils.getEpidemiologicalHistoryStr(this.__EpidemiologicalHistoryObj)
            ? MedicalRecordUtils.getEpidemiologicalHistoryStr(this.__EpidemiologicalHistoryObj)
            : undefined;
        const dentistryExaminations = !!this.dentistryExaminationsStr ? this.dentistryExaminationsStr : undefined;
        const historyList = [chiefComplaint, presentHistory, pastHistory, epidemiologicalHistory, dentistryExaminations]
            .filter((t) => !!t && t != " ")
            .map((t) => StringUtils.stringBr2N(t));
        return historyList.join(" ");
    }

    reserveTime?: RegistrationDetailReserveTime;
}

//
// interface ReservationTime {
//     day: number;
//     month: number;
//     week: number;
// }

export enum RegistrationsAppointmentServiceType {
    defaultPart = 0, //默认分段：上午、下午、晚上
    accurate = 1, //精确
    customPart = 2, //自定义分段
}
// 预约时间
export enum RegistrationTimeType {
    OTHER = 0, // 其他(分段、上、下午、晚上)
    ACCURATE = 1, // 精确时间预约
}

// 号数确定时机
export enum GENERATE_ORDER_NO_TIME_TYPE {
    REGISTRATION = 0, // 预约取号
    SIGN_IN = 10, // 签到取号
}

export class RegistrationsAppointmentConfig {
    serviceType?: RegistrationsAppointmentServiceType;
    enableLeaveForMember?: number;
    enableLeaveForPC?: number;

    get isDefaultPart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.defaultPart;
    }

    get isAccuratePart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.accurate;
    }

    get isCustomPart(): boolean {
        return this.serviceType == RegistrationsAppointmentServiceType.customPart;
    }
}

export class DoctorShiftsWithTime {
    doctorId?: string;
    doctorName?: string;
    registrationFee?: number;
    revisitedRegistrationFee?: number; //复诊费用
    canReserve?: number;
    dayOfWeek?: string;
    restCountToday?: number;
    departmentId?: string;
    departmentName?: string;
    // list?: ShiftsListItem[];
    scheduleIntervals?: ScheduleIntervalsItem[];

    @JsonProperty({ fromJson: fromJsonToDate })
    registerStartTime?: Date;

    @JsonProperty({ fromJson: fromJsonToDate })
    workingDate?: Date;

    get displayRestCount(): string {
        if (this.canReserve) {
            return "余" + (this.restCountToday ? this.restCountToday : 0) + "号";
        }
        return "";
    }
}

export class RegistrationVisitSource {
    @JsonProperty({
        fromJson: (json: AnyType) => {
            return json?.map((item: AnyType) => {
                return JsonMapper.deserialize(RegistrationVisitSource, item);
            });
        },
    })
    children?: RegistrationVisitSource[];
    id?: string;
    level?: number;
    name?: string;
    parentId?: string;

    get hasSubType(): boolean {
        return !!this.children?.length;
    }
}

export class RegistrationsDoctorScheduleDoctors {
    canReserve?: number;
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    isDefault?: number;
    onSchedule?: number;
    restCount?: number;
    schedulePeriods?: string[];
    type?: number;
}
export class RegistrationsDoctorScheduleRsp {
    date?: string;
    @JsonProperty({ type: Array, clazz: RegistrationsDoctorScheduleDoctors })
    doctors?: RegistrationsDoctorScheduleDoctors[];
}

/**
 * 挂号可选时间
 */

export class RegistrationDesignatedTimeItem {
    available?: number;
    end?: string;
    orderNo?: number;
    restCount?: number;
    start?: string;
    timeOfDay?: string;
    type?: number;
    scheduleSelfStatus?: ScheduleSelfStatusEnum;
}
export class RegistrationDesignatedTimeScheduleInterval {
    end?: string;
    @JsonProperty({ type: Array, clazz: RegistrationDesignatedTimeItem })
    list?: RegistrationDesignatedTimeItem[];
    start?: string;
    timeOfDay?: string;
    signInTimeFirstEnableUseOrderNo?: number;
    signInTimeFirstEnableUseOrderNoTimeOfDay?: string;
    availableCellTypes?: number[]; //当前时段可用的号源类型集合， 返回给前端使用， 0：普通号；1：现场预留号；2：会员预留号
    isStopDiagnose?: number; // 0未全部停诊 1全部停诊
}

export class RegistrationDesignatedTimeCategoryScheduleIntervals {
    registrationCategory?: number;
    @JsonProperty({ type: Array, clazz: RegistrationDesignatedTimeScheduleInterval })
    scheduleIntervals?: RegistrationDesignatedTimeScheduleInterval[];
    serviceMaxMinutes?: number; //服务时长最大分钟数，非口腔管家灵活时间使用
    serviceMinMinutes?: number; //服务时长最小分钟数，非口腔管家灵活时间使用
}
export class RegistrationDesignatedTime {
    canReserve?: number;
    dayOfWeek?: string;
    departmentId?: string;
    doctorId?: string;
    list?: RegistrationDesignatedTimeItem[];
    registerStartTime?: string;
    restCountToday?: number;
    workingDate?: string;
    /**@deprecated 弃用，默认取registrationCategoryScheduleIntervals[0].serviceMaxMinutes **/
    serviceMaxMinutes?: number; //服务时长最大分钟数，非口腔管家灵活时间使用
    /**@deprecated 弃用，默认取registrationCategoryScheduleIntervals[0].serviceMinMinutes **/
    serviceMinMinutes?: number; //服务时长最小分钟数，非口腔管家灵活时间使用
    @JsonProperty({ type: Array, clazz: RegistrationDesignatedTimeCategoryScheduleIntervals })
    registrationCategoryScheduleIntervals?: RegistrationDesignatedTimeCategoryScheduleIntervals[];
    getCategorySchedule(registrationCategory = 0): RegistrationDesignatedTimeCategoryScheduleIntervals | undefined {
        return this.registrationCategoryScheduleIntervals?.find((i) => i.registrationCategory == registrationCategory);
    }
    getScheduleIntervals(registrationCategory = 0): RegistrationDesignatedTimeScheduleInterval[] {
        return (
            this.registrationCategoryScheduleIntervals?.find((i) => i.registrationCategory == registrationCategory)?.scheduleIntervals ?? []
        );
    }
}

export class RegistrationDesignatedDoctorInfo {
    doctorId!: string;
    doctorName?: string;
    namePy?: string;
    namePyFirst?: string;
    sort?: number;
    fee?: number;
}

export class VisitSourceBaseInfo {
    visitSourceId?: string;
    visitSourceName?: string;
    visitSourceFrom?: string;
    visitSourceFromName?: string;
    relatedType?: number;
}

/**
 * 挂号获取医生诊费
 */
export class RevisitedFeeCustomUseRule {
    effectiveDays?: number; // X天内在同一医生首次就诊/再次就诊的患者，分别设置挂号费
    unavailable?: boolean;
}
export class RegistrationsDoctorFeeItemsRsp {
    chainId?: string;
    clinicId?: string;
    departmentId?: string;
    doctorId?: string;
    regCostUnitPrice?: number; // 初诊患者/X天内首次就诊患者（成本）
    regUnitPrice?: number; // 初诊患者/X天内首次就诊患者（售价）
    revisitedRegCostUnitPrice?: number; // 复诊患者/X天内再次就诊患者（成本）
    revisitedRegUnitPrice?: number; // 复诊患者/X天内再次就诊患者（售价）
    revisitedFeeCustomUseRule?: RevisitedFeeCustomUseRule;
}

export class RegistrationsPatientApplyAuditItem {
    id?: string;
    chainId?: string;
    clinicId?: string;
    registrationType?: number;
    patientOrderId?: string;
    registrationSheetId?: string;
    reserveDate?: string;
    reserveStart?: string;
    reserveEnd?: string;
    statusV2?: number;
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    patientId?: string;
    patientName?: string;
    orderNo?: number;
    isReserved?: number;
    type?: number;
    applyAuditStatus?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    wxUserPatientId?: string;

    get applyAuditStatusText(): string {
        switch (this.applyAuditStatus) {
            case 1:
                return "待处理";
            case 2:
                return "已通过";
            case 3:
                return "已拒绝";
            default:
                return "";
        }
    }
}

export class RegistrationNearestCreated {
    applyAuditStatus?: number;
    chainId?: string;
    clinicId?: string;
    created?: string;
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    id?: string;
    isReserved?: number;
    orderNo?: number;
    patientId?: string;
    patientName?: string;
    patientOrderId?: string;
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    registrationProducts?: Array<GoodsInfo>;
    registrationSheetId?: string;
    registrationType?: number;
    reserveDate?: string;
    reserveEnd?: string;
    reserveStart?: string;
    statusV2?: number;
    type?: number;
}

export enum RegistrationPageSourceType {
    normal = 0, // 普通进入，由挂号模块进入
    patientChat = 1, // 患者沟通模块进入
    again = 3, // 复诊预约进入
}

export enum OrderNoType {
    OCCUPY = 1, //现场号
    VIP = 2, // Vip号
}

export class ScheduleDetailDepartmentCell {
    timeOfDay?: string;
    orderNo?: OrderNoType;
    orderNoType?: number;
    start?: string;
    end?: string;
    @JsonProperty({ type: RegistrationDetail })
    existedRegistration?: RegistrationDetail;
    scheduleSelfStatus?: ScheduleSelfStatusEnum;
    // 是停诊状态
    get isStopScheduleSelfStatus(): boolean {
        return this.scheduleSelfStatus == ScheduleSelfStatusEnum.STOP_DIAGNOSE;
    }

    get isOccupy(): boolean {
        return this.orderNoType == OrderNoType.OCCUPY;
    }

    get isVip(): boolean {
        return this.orderNoType == OrderNoType.VIP;
    }

    get isIrreducible(): boolean {
        return !!this.existedRegistration?.registrationFormItem?.visitSourceRemark?.includes("【不可插约】");
    }
}

export class ScheduleDetailDepartment {
    departmentId?: string;
    departmentName?: string;
    timeOfDay?: string;
    start?: any;
    end?: any;
    @JsonProperty({ type: Array, clazz: ScheduleDetailDepartmentCell })
    cells?: ScheduleDetailDepartmentCell[];
    notRefundedCount?: number;
    restCount?: number;
    registrationCategory?: number;
    get registrationCategoryDisplay(): string {
        if (this.registrationCategory == 2) {
            return "便民门诊";
        } else if (this.registrationCategory == 1) {
            return "专家门诊";
        } else {
            return "普通门诊";
        }
    }

    get rangeDisplay(): string {
        let _str = "";
        if (!!this.start) {
            _str += this.start;
            _str += "~";
        }
        if (!!this.end) {
            _str += this.end;
        }
        return _str;
    }
}
export class RegistrationKanbanScheduleDetail {
    timeOfDay?: string;
    @JsonProperty({ type: Array, clazz: ScheduleDetailDepartment })
    departments?: ScheduleDetailDepartment[];
}

export class DailyEmployeeListSchedulesItem {
    beginTime?: string;
    endTime?: string;
    isStopDiagnose?: number;
    workingDate?: string;
}

export class RegistrationKanbanDailyEmployeeList {
    employeeId?: string;
    employeeName?: string;
    @JsonProperty({ type: Array, clazz: RegistrationKanbanScheduleDetail })
    scheduleDetails?: RegistrationKanbanScheduleDetail[];

    /**
     * 灵活模式使用字段
     */
    @JsonProperty({ type: Array, clazz: RegistrationDetail })
    registrationList?: RegistrationDetail[];

    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    @JsonProperty({ type: Array, clazz: DailyEmployeeListSchedulesItem })
    schedules?: DailyEmployeeListSchedulesItem[];
}

export class RegistrationKanbanDailyViewDetail {
    @JsonProperty({ fromJson: fromJsonToDate })
    reserveDate?: Date;
    weekday?: string;
    @JsonProperty({ type: Array, clazz: RegistrationKanbanDailyEmployeeList })
    dailyEmployeeList?: RegistrationKanbanDailyEmployeeList[];

    get tableHeaderY(): string[] {
        return this.dailyEmployeeList?.[0]?.scheduleDetails?.map((item) => item.timeOfDay ?? "") ?? [];
    }
}

export class RegistrationDoctorScheduleStatusList {
    canReserve?: number; //是否可预约
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    doctorNamePy?: string;
    doctorNamePyFirst?: string;
    isDefault?: number; //是否默认科室
    isDiagnosing?: number; //是否在接诊中
    notRefundedCount?: number; //未退号数量
    onSchedule?: number; //是否有排班
    restCount?: number; //剩余号源数量
    schedulePeriods?: string[]; //排班时间
    type?: number; //科室类型
    waitingDiagnoseCount?: number; //候诊数量
    get showScheduleInfo(): { statusName?: string; waitCount?: string; color?: Color } {
        let statusName = "",
            waitCount,
            color;
        if (!!this.schedulePeriods?.length) {
            if (!!this.isDiagnosing) {
                statusName = "诊中";
                color = Colors.canAppointmentColor;
            } else {
                statusName = "空闲";
                color = Colors.B1;
            }
            waitCount = `(候诊${this.waitingDiagnoseCount ?? 0}人)`;
        } else {
            statusName = "未排班";
        }
        return {
            statusName,
            waitCount,
            color,
        };
    }
}
export class RegistrationDoctorSchedule {
    date?: string;
    @JsonProperty({ type: Array, clazz: RegistrationDoctorScheduleStatusList })
    doctors?: RegistrationDoctorScheduleStatusList[];
}

export class RegistrationFee {
    chainId?: string;
    clinicId?: string;
    departmentId?: string;
    doctorId?: string;
    regUnitPrice?: number; // 天内首次就诊患者售价
    regCostUnitPrice?: number; // 天内首次就诊患者成本
    revisitedRegUnitPrice?: number; // 天内再次就诊患者售价
    revisitedRegCostUnitPrice?: number; // 天内再次就诊患者成本
    referralRegUnitPrice?: number;
    referralRegCostUnitPrice?: number;
    referralRevisitedRegUnitPrice?: number;
    referralRevisitedRegCostUnitPrice?: number;
    registrationCategory?: number;
    feeTypeId?: string;
    isUseDefault?: number; // 是否使用默认挂号费 0 否 1 是
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    feeGoodsList?: ClinicDoctorInfo[];
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    revisitedFeeGoodsList?: ClinicDoctorInfo[];
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    referralFeeGoodsList?: ClinicDoctorInfo[];
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    referralRevisitedFeeGoodsList?: ClinicDoctorInfo[];
    shebaoMatchCode?: string;
    isDiffForRevisited?: RegistrationDiffForRevisitedType; // 挂号费模式
    revisitedFeeCustomUseRule?: RevisitedFeeCustomUseRule;

    get registrationCategoryDisplay(): string {
        if (this.registrationCategory == 2) {
            return "便民门诊";
        } else if (this.registrationCategory == 1) {
            return "专家门诊";
        } else {
            return "普通门诊";
        }
    }
}

export class RegistrationDoctorEnableCategories {
    chainId?: string;
    clinicId?: string;
    departmentId?: string;
    employeeId?: string;
    enableCategories?: number[];
    @JsonProperty({ type: Array, clazz: RegistrationFee })
    registrationFees?: RegistrationFee[];

    get enableRegistrationCategories(): boolean {
        return !!this.enableCategories?.some((type) => !!type);
    }
}
