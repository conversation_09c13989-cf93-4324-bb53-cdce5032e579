/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/4
 *
 * @description
 */
import { TaskQueue } from "../common-base-module/schedulers/task-queue";
import { DisposableTracker } from "../common-base-module/cleanup/disposable";
import { LogUtils } from "../common-base-module/log";
import { Completer } from "../common-base-module/async/completer";
import _ from "lodash";
import { AppInfo } from "../base-business/config/app-info";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { errorSummary } from "../common-base-module/utils";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { ABCApiNetwork } from "../net";
import { ZipUtils } from "../archive/zip-utils";
import { AbcPlatformMix } from "../base-business/native-modules/abc-platform-mix";
import { FileDownloader } from "../base-business/file/file-downloader";
import { MD5 } from "../common-base-module/encryption/md5";
import FileUtils from "../common-base-module/file/file-utils";
import { userCenter } from "../user-center";
import { onlinePreferences } from "../base-business/preferences/online-preferences";
import { abcNetDelegate } from "../net/abc-net-delegate";
import { ABCError } from "../common-base-module/common-error";
import { Version } from "../base-ui/utils/version-utils";
import { Subject } from "rxjs";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { environment, ServerEnvType } from "../base-business/config/environment";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface PluginDownloadProgress {
    (currentInBytes: number, totalInBytes: number): void;
}

const kAssertSchema = "assets://";
export const kPluginConfFile = "conf.json";

export const kPluginAutoForceUpload = "pluginAutoForceUpload";

const kTag = "PluginUpgradeInfo.";
const kGraySuffix = "_gray";
const kPreSuffix = "_pre";

const kMainJSBuundle = "abcyun-hippy";

class PluginUpgradeInfo {
    name?: string;
    version?: string;
    url?: string;
    md5?: string;
    hostMinVersion?: string;
    hostMaxVersion?: string;
    forceUpdate?: number;
    description?: string;

    grayFlag?: number; //0非灰度，1灰度
    region?: string; //区域信息

    public isGray(): boolean {
        return this.grayFlag == 1;
    }
}

export class PluginInfo {
    name?: string;
    version?: string;
    hostMinVersion?: string;
    hostMaxVersion?: string;
    description?: string;
    rootDir?: string;

    timestamp?: string; // 生成时间
    repoTag?: string; //打包tag

    grayFlag?: number; //0非灰度，1灰度
    region?: string; //区域信息

    haveReload?: boolean; // 是否重新加载了插件

    public isGray(): boolean {
        return this.grayFlag == 1;
    }

    get isLocalPlugin(): boolean {
        return this.rootDir!.startsWith(kAssertSchema);
    }
}

class PluginManager extends DisposableTracker {
    private _workQueue = new TaskQueue();
    private _pluginUpgradeList?: PluginUpgradeInfo[]; //后台下发最新插件列表

    //内置插件列表
    private _embedPluginList: PluginInfo[] = [];

    //下载安装的插件列表
    private _downloadPluginList: PluginInfo[] = [];

    ///正在等待插件初始化结果回调
    private _pendingPreparedPlugins = new Map<String, _PreparedPluginListener[]>();

    ///是否已经加载过插件列表了
    private _hasLoadPluginList = false;

    ///安装包里自带的插件
    private _embedPlugins = [kMainJSBuundle];

    //组件加载成功抛出事件
    pluginLoadStatusObserver: Subject<boolean> = new Subject();

    init() {
        this._workQueue.schedule(() => {
            return this._loadUpgradeInfo();
        });

        this._workQueue.schedule(async () => {
            return this._loadPluginList();
        });

        //切换诊所后，可能导致灰度标记变化，插件列表版本与灰度有关
        userCenter.sClinicChangeObserver.subscribe(() => {
            this._pluginUpgradeList = undefined;
        });
    }

    async preparePlugin(
        name: string,
        params?: { onProgress?: PluginDownloadProgress; showLoading?: boolean; freshPluginList?: boolean }
    ): Promise<PluginInfo> {
        if (!!params?.freshPluginList) {
            this._pluginUpgradeList = undefined;
        }
        let autoDownload = await onlinePreferences.getBool("hippyUpdateSwitch", false).catch((/*ignore*/) => false);

        if (!!sharedPreferences.getBool(kPluginAutoForceUpload)) {
            autoDownload = true;
        }

        const { onProgress, showLoading = true } = params ?? {};
        LogUtils.d(`${kTag}PluginManager.preparePlugin name = ${name}, autoDownload = ${autoDownload}`);
        const listener = new _PreparedPluginListener();
        listener.onProgress = onProgress;

        let list = this._pendingPreparedPlugins.get(name);
        const preparing = !_.isEmpty(list);
        if (list == undefined) {
            list = [];
            this._pendingPreparedPlugins.set(name, list);
        }

        list.push(listener);

        ///已经在准备指定插件了,不用重复触发下载
        if (!preparing) {
            this._workQueue.schedule(() => {
                return this._doPreparePlugin(name, { autoDownload: autoDownload, showLoading: showLoading });
            });
        }

        return listener.completer.promise;
    }

    async loadPluginInfoFromRootDir(rootDir: string): Promise<PluginInfo | undefined> {
        if (rootDir.startsWith(kAssertSchema)) {
            const content = await AbcPlatformMix.getAssetAsString(rootDir.substr(kAssertSchema.length) + "/" + kPluginConfFile);
            return JsonMapper.deserialize(PluginInfo, JSON.parse(content));
        } else {
            const confContent = await FileUtils.readAsString(rootDir + "/" + kPluginConfFile).catch(() => null);
            if (!confContent) {
                return;
            }
            return JsonMapper.deserialize(PluginInfo, JSON.parse(confContent));
        }
    }

    async pluginRootDir(name: string): Promise<string> {
        const rootDir = await FileUtils.getPluginDir();

        const reginApi = environment.serverHostName;

        const grayFlag = this._grayFlag();
        const graySuffix = grayFlag == 1 ? kGraySuffix : grayFlag == 2 ? kPreSuffix : "";

        let pluginRootDir = `${rootDir}/${reginApi}/${name}${graySuffix}`;
        //2.9版本才开始支持此接口
        const lessThan290 = new Version(AppInfo.appVersion).compareTo(new Version("2.9.0")) < 0;

        //兼容老版本和global区域
        if (lessThan290 || environment.isGlobalEnv) {
            pluginRootDir = `${rootDir}/${name}${graySuffix}`;
        }

        const tmpDir = `${pluginRootDir}_tmp`;
        if (await FileUtils.fileExists(tmpDir)) return tmpDir;

        return pluginRootDir;
    }

    private _getPluginByName(name: string): PluginInfo | undefined {
        const embedPlugin = this._embedPluginList.find((item) => item.name === name);
        const downloadPlugin = this._downloadPluginList.find((item) => item.name === name && this._grayFlag() == item.grayFlag);

        if (embedPlugin && downloadPlugin) {
            if (this._versionToNum(embedPlugin.version!) >= this._versionToNum(downloadPlugin.version!)) return embedPlugin;
            else return downloadPlugin;
        } else return embedPlugin ? embedPlugin : downloadPlugin;
    }

    async _doPreparePlugin(name: string, params?: { autoDownload: boolean; showLoading?: boolean }): Promise<void> {
        const { autoDownload = true, showLoading = true } = params ?? {};
        LogUtils.d(`${kTag}PluginManager._doPreparePlugin 准备插件 name = ${name}, autoDownload = ${autoDownload}, pwd = ${AppInfo.pwd}`);
        await this._loadPluginList();
        await this._loadUpgradeInfo();

        const grayFlag = this._grayFlag();
        const plugin = this._getPluginByName(name);
        const upgradePlugin = this._pluginUpgradeList?.find((item) => item.name == name && item.grayFlag == grayFlag);

        LogUtils.d(`${kTag}_doPreparePlugin grayFlag = ${grayFlag} pluginInfo = ${JSON.stringify(plugin)}`);
        LogUtils.d(`${kTag}_doPreparePlugin upgradePlugin = ${JSON.stringify(upgradePlugin)}`);

        const appVersion = AppInfo.appVersion;
        let needDownload = false;

        //找到已经安装的插件
        if (plugin) {
            LogUtils.d(kTag + "_doPreparePlugin 找到已经安装插件 " + name);
            //当前app不支持插件运行，需要下载
            if (!this.versionMatch(appVersion, plugin.hostMinVersion!, plugin.hostMaxVersion!)) {
                await this.clearPlugin(name);
                needDownload = true;
            }

            //检查是否需要更新
            if (upgradePlugin && autoDownload) {
                //有更新
                if (this._versionToNum(upgradePlugin.version!) > this._versionToNum(plugin.version!)) {
                    LogUtils.d(
                        kTag +
                            `_doPreparePlugin ${name}, 有更新版本，删除老版本..., newVersion = ${upgradePlugin.version}, oldVersion = ${plugin.version}`
                    );
                    needDownload = true;
                }
            }

            ///再次检测插件目录是否存在，如果不存在还是要下载的
            const dirExists = await this.pluginDirExists(name);
            LogUtils.d(kTag + `_doPreparePlugin ${name}, dirExists ${dirExists}`);
            if (!dirExists) {
                needDownload = true;
            }
        } else {
            needDownload = true;
        }

        LogUtils.d(kTag + `_doPreparePlugin name = ${name}, needDownload = ${needDownload}`);
        if (needDownload && autoDownload) {
            if (_.isEmpty(this._pluginUpgradeList)) {
                this._notifyPreparedResult(name, "插件列表空");
                return;
            }

            if (upgradePlugin == null) {
                this._notifyPreparedResult(name, "未找到插件" + name);
                return;
            }

            const dialog = showLoading ? new LoadingDialog("") : undefined;
            dialog?.show();

            ///下载插件
            const pluginZipTmp = await this.downloadPlugin(upgradePlugin).catch((error) => new ABCError(error));
            if (pluginZipTmp instanceof ABCError || !pluginZipTmp) {
                await dialog?.hide();
                this._notifyPreparedResult(name, "插件下载失败：" + errorSummary(pluginZipTmp));
                return;
            }

            //解压插件
            const tmpUnzipPluginDir = await this.unzipPlugin(pluginZipTmp, upgradePlugin).catch((error) => new ABCError(error));
            if (!tmpUnzipPluginDir || tmpUnzipPluginDir instanceof ABCError) {
                await dialog?.hide();
                this._notifyPreparedResult(name, "插件解压失败:" + errorSummary(tmpUnzipPluginDir));
                return;
            }

            LogUtils.d(kTag + `_doPreparePlugin name = ${name}, needDownload = ${needDownload}, 插件解压目录：${tmpUnzipPluginDir}`);
            const pluginDir = await this.pluginRootDir(upgradePlugin.name!);
            const plugin = await this.loadPluginInfoFromRootDir(tmpUnzipPluginDir);

            LogUtils.d(kTag, "_doPreparePlugin loadPluginInfoFromRootDir plugin = " + JSON.stringify(plugin));
            if (!plugin) {
                this._notifyPreparedResult(name, "插件安装失败, conf.json文件读取失败");
                return;
            }

            //插件更新成功，删除老插件
            await this.clearPlugin(name);
            try {
                await FileUtils.mv(tmpUnzipPluginDir, tmpUnzipPluginDir.replace("_tmp2", "_tmp"));
            } catch (e) {
                if (environment.serverEnvType === ServerEnvType.normal) {
                    await dialog?.hide();
                } else {
                    await dialog?.fail("插件加载失败，请重启App");
                }
                return;
            }
            plugin.grayFlag = grayFlag;
            plugin.rootDir = pluginDir;
            _.remove(this._downloadPluginList, (item) => item.name == name && item.grayFlag == grayFlag);

            this._downloadPluginList.push(plugin);
            this._saveInstalledPluginInfo().then();
            await dialog?.hide();

            //重新加载
            AbcPlatformMix.reload().then();
            plugin.haveReload = true;
        } else if (!needDownload) {
            //不需要下载，但可能出现由灰度切换到非灰度诊所，但当前运行的仍然是灰度环境的js bundle,这里需要reload一下
            if (name === kMainJSBuundle) {
                LogUtils.d(`${kTag}PluginManager._doPreparePlugin 插件${name}初始化结果AppInfo.pwd = ${AppInfo.pwd}`);
                const launchGrayFlag = AppInfo.launchGrayFlag;

                LogUtils.d(
                    `${kTag}PluginManager._doPreparePlugin currentGrayBundle = ${launchGrayFlag},this._grayFlag() = ${this._grayFlag()} `
                );
                //如果启动时的灰度标记与当前标记不一致，重新加载
                if (launchGrayFlag != undefined && launchGrayFlag != this._grayFlag()) {
                    AbcPlatformMix.reload().then();
                    if (plugin) {
                        plugin.haveReload = true;
                    }
                }
            }
        }

        //插件加载成功--往外抛出插件成功事件
        this.pluginLoadStatusObserver.next(true);

        if (plugin != null) {
            this._notifyPreparedResult(name, plugin);
        }
    }

    _notifyPreparedResult(name: string, result: any) {
        LogUtils.d(`PluginManager._notifyPreparedResult 插件${name}初始化结果:${JSON.stringify(result)}`);
        const list = this._pendingPreparedPlugins.get(name);
        list?.forEach((listener) => {
            if (result instanceof PluginInfo) {
                LogUtils.d(`PluginManager._notifyPreparedResult 插件${name}初始化结果:${result.name}, ${result.rootDir}`);
                listener.completer.resolve(JsonMapper.deserialize(PluginInfo, result));
            } else {
                listener.completer.reject(result);
            }
        });

        this._pendingPreparedPlugins.delete(name);
    }

    _notifyPreparedPluginProgress(plugin: string, currentInBytes: number, totalInBytes: number) {
        const list = this._pendingPreparedPlugins.get(plugin);
        list?.forEach((listener) => {
            if (listener.onProgress != null) {
                listener.onProgress(currentInBytes, totalInBytes);
            }
        });
    }

    private _grayFlag(): number {
        //1.9.1版本才开始支持此接口
        const lessThan220 = new Version(AppInfo.appVersion).compareTo(new Version("2.2.0.0200")) < 0;
        if (lessThan220) {
            return !_.isEmpty(abcNetDelegate.grayFlag) ? 1 : 0;
        } else {
            const grayFlag = abcNetDelegate.grayFlag;
            return grayFlag?.startsWith("gray") ? 1 : grayFlag?.startsWith("pre") ? 2 : 0;
        }
    }

    async pluginDirExists(name: string): Promise<boolean> {
        const plugin = this._getPluginByName(name);

        if (DeviceUtils.isOhos()) {
            if (plugin && plugin.rootDir!.startsWith("/")) {
                return true;
            }
        } else {
            if (plugin && plugin.rootDir!.startsWith(kAssertSchema)) {
                return true;
            }
        }

        const rootDir = await this.pluginRootDir(name);

        LogUtils.d(`${kTag} pluginDirExists, rootDir = ${rootDir}`);

        return await FileUtils.fileExists(rootDir);
    }

    //下载指定插件
    async downloadPlugin(plugin: PluginUpgradeInfo): Promise<string> {
        LogUtils.d(kTag + "downloadPlugin name = " + plugin.name);
        const cacheDir = await FileUtils.getTmpDir();
        const file = `${cacheDir}/${plugin.name}.zip`;
        try {
            LogUtils.d(kTag + `downloadPlugin name = ${plugin.name}, cacheDir = ${cacheDir}`);
            if (await FileUtils.fileExists(file)) {
                const digest = await MD5.md5WithFile(file);
                LogUtils.d(kTag + `existed file path:${file} md5:${digest}， plugin md5 = ${plugin.md5}`);
                if (digest == plugin.md5) {
                    return file;
                }
            }
        } catch (e) {
            LogUtils.e(kTag + "_downloadFileAndCheckMd5 error:$e");
        }

        LogUtils.d(kTag + `downloadPlugin name = ${plugin.name}, cacheDir = ${cacheDir}`);
        LogUtils.d(kTag, `downloadPlugin name = ${plugin.name}, url = ${plugin.url}`);
        const pluginZipTmp = await FileDownloader.downloadFile(plugin.url!, {
            filePath: file,
            onProgress: (currentBytes, totalBytes) => {
                this._notifyPreparedPluginProgress(plugin.name!, currentBytes, totalBytes);
            },
        }).then((rsp) => rsp.filePath);

        if (!(await FileUtils.fileExists(pluginZipTmp))) throw "插件下载失败";

        const md5 = await MD5.md5WithFile(pluginZipTmp);
        if (md5 !== plugin.md5) {
            LogUtils.d(kTag + `md5 not match, download md5:${md5}, require md5:${plugin.md5}`);
            await FileUtils.deleteFile(pluginZipTmp);
            throw "插件md5对比失败";
        }

        return pluginZipTmp;
    }

    async unzipPlugin(zipFile: string, info: PluginUpgradeInfo): Promise<string> {
        LogUtils.d(kTag + `unzipPlugin name = ${info.name}, zipFile = ${zipFile}`);
        const pluginDir = await this.pluginRootDir(info.name!);
        const pluginTmpDir = pluginDir + "_tmp2";
        if (await FileUtils.fileExists(pluginTmpDir)) {
            await FileUtils.deleteFile(pluginTmpDir);
        }

        await ZipUtils.unzip(zipFile, pluginTmpDir);
        if (!(await FileUtils.fileExists(pluginTmpDir))) throw "解压失败";

        return pluginTmpDir;
    }

    ///清队指定插件，注：对于包里自带插件，只能将更新删除
    async clearPlugin(name: string): Promise<void> {
        LogUtils.d(kTag + `PluginManager.clearPlugin name = ${name}`);
        const rootDir = await this.pluginRootDir(name);
        LogUtils.d(kTag + `PluginManager.clearPlugin name = ${name}, rootDir = ${rootDir}`);
        await FileUtils.deleteFile(rootDir).catch(() => null);

        const grayFlag = this._grayFlag();
        _.remove(this._downloadPluginList, (item) => item.name == name && item.grayFlag == grayFlag);

        await this._saveInstalledPluginInfo();
    }

    _versionToNum(version: string): number {
        return parseInt(version.replace(/\./g, ""));
    }

    versionMatch(version: string, minVersion: string, maxVersion: string): boolean {
        LogUtils.d(kTag + "versionMatch");
        const versionNum = this._versionToNum(version);
        const minVersionNum = this._versionToNum(minVersion);
        const maxVersionNum = this._versionToNum(maxVersion);

        const ret = (minVersionNum == null || versionNum >= minVersionNum) && (maxVersionNum == null || versionNum <= maxVersionNum);
        LogUtils.d(kTag + `versionMatch version = ${version}, minVersion = ${minVersion}, maxVersion = ${maxVersion}, ret = ${ret}`);

        return ret;
    }

    ///加载内置插件信息
    async _loadEmbedPluginInfo(): Promise<void> {
        LogUtils.d(kTag + "loadEmbedPlugin");
        for (const plugin of this._embedPlugins) {
            const rootDir = "plugins/" + plugin;
            const content = await AbcPlatformMix.getAssetAsString(rootDir + "/conf.json");
            const info = JsonMapper.deserialize(PluginInfo, JSON.parse(content));
            info.rootDir = `${kAssertSchema}${rootDir}`;
            info.grayFlag = 0;
            this._embedPluginList.push(info);
        }
    }

    ///加载已安装的插件信息
    async _loadInstalledPluginInfo(): Promise<void> {
        const dbFile = await this._installedPluginDBFile();
        if (!(await FileUtils.fileExists(dbFile))) return;

        const content = await FileUtils.readAsString(await this._installedPluginDBFile());
        const contentJson = JSON.parse(content);

        this._downloadPluginList = contentJson.map((item: any) => JsonMapper.deserialize(PluginInfo, item));

        // ios每次覆盖安装doc根目录可能会发生变化，此处重新刷新插件安装的根目录
        if (this._downloadPluginList != null) {
            for (const plugin of this._downloadPluginList) {
                if (plugin.rootDir!.startsWith("/")) {
                    plugin.rootDir = await this.pluginRootDir(plugin.name!);
                }
            }
        }
    }

    async _saveInstalledPluginInfo(): Promise<void> {
        LogUtils.d(kTag + "_saveInstalledPluginInfo");
        const content = JSON.stringify(this._downloadPluginList ?? []);
        await FileUtils.writeAsString(await this._installedPluginDBFile(), content).catch((e) => LogUtils.e(e));
    }

    async _installedPluginDBFile(): Promise<string> {
        const pluginDir = await FileUtils.getPluginDir();

        return pluginDir + "/installed_plugins.db";
    }

    /// 加载插件列表（本地和下载安装的）
    async _loadPluginList() {
        if (this._hasLoadPluginList) return;
        await this._loadInstalledPluginInfo().catch((e) => LogUtils.e(e));
        await this._loadEmbedPluginInfo().catchIgnore();

        this._hasLoadPluginList = true;
    }

    ///拉取插件的配置信息
    async _loadUpgradeInfo() {
        LogUtils.d(kTag + "loadUpgradeInfo ");
        if (!(await userCenter.isLogin()) || this._pluginUpgradeList != null) return;

        this._pluginUpgradeList = await ABCApiNetwork.get<PluginUpgradeInfo[]>("plugin/latestPluginList")
            .then((rsp) => rsp.map((item) => JsonMapper.deserialize(PluginUpgradeInfo, item)))
            .catch(() => undefined);

        LogUtils.d(kTag + "loadUpgradeInfo pluginList.length = " + this._pluginUpgradeList?.length);
    }
}

class _PreparedPluginListener {
    completer = new Completer<PluginInfo>();

    onProgress?: PluginDownloadProgress;
}

const pluginManager = new PluginManager();
export { pluginManager };
