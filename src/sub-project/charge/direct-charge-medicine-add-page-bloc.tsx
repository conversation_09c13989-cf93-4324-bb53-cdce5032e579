/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { GoodsInfo, GoodsSubType, GoodsType } from "../base-business/data/beans";
import {
    DirectChargeMedicineAddInfo,
    DirectChargeMedicineAddPageCategoryItem,
    DirectChargeMedicineInvokeParams,
    DirectChargeUsage,
    NurseParams,
    ShoppingCardExpandDialog,
} from "./direct-charge-medicine-add-page";
import { interval, of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import _ from "lodash";
import { GoodsAgent } from "../data/goods/goods-agent";
import { ABCError } from "../common-base-module/common-error";
import { LogUtils } from "../common-base-module/log";
import { AbcMap } from "../base-ui/utils/abc-map";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { DirectChargeAddGoodsQrscanPage } from "./direct-charge-add-goods-qrscan-page";
import { Toast } from "../base-ui/dialog/toast";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { userCenter } from "../user-center";

class State {
    medicines: GoodsInfo[] = [];
    selectedMedicines: GoodsInfo[] = [];
    inputCounts: AbcMap<GoodsInfo, DirectChargeUsage> = new AbcMap();
    filterGoodsTypes: number[] = [];
    dosageCount = 1;

    focusGoodsInfo?: GoodsInfo;
    keyword?: string;
    searchFocus = false;

    searchError: any;
    searching = false;

    //是否允许开出0库存的药品
    allowZeroStockGoods = false;

    goodsType?: number; //DirectChargeMedicineAddPageNaviItem.type

    //推荐相关商品列表
    clinicExamTreatList?: GoodsInfo[];
    kCustomCategoryItems: { customTypeId: number; customTypeName: string }[] = [];

    //是否从执行站页面进入
    _fromNursePage?: boolean;
    //执行站可开项目type
    _openGoodsTypes?: number[];

    get isInSearchMode(): boolean {
        return !_.isEmpty(this.keyword) || this.searchFocus;
    }

    get totalSelectPrice(): number {
        let nonChineseTotalPrice = 0.0;

        let chineseTotalPrice = 0.0;

        this.inputCounts.forEach((value, key) => {
            if (value.count != undefined && !isNaN(value.count)) {
                if (key.isChineseMedicine) chineseTotalPrice += key.unitPriceWithUnit(this.inputCounts.get(key)!.unit) * value.count;
                else nonChineseTotalPrice += key.unitPriceWithUnit(this.inputCounts.get(key)!.unit) * value.count;
            }
        });

        return nonChineseTotalPrice + chineseTotalPrice * this.dosageCount;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    type: number;

    constructor(type: number) {
        super();
        this.type = type;
    }
}

class _EventUpdate extends _Event {}

class _EventUpdateGoodsType extends _Event {
    type: number;

    constructor(type: number) {
        super();
        this.type = type;
    }
}

class _EventToggleMedicineSelectState extends _Event {
    goods: GoodsInfo;

    constructor(goods: GoodsInfo) {
        super();
        this.goods = goods;
    }
}

class _EventUpdateSearchKeyWord extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}

class _EventShowShoppingCarTap extends _Event {}

class _EventUpdateSearchFocus extends _Event {
    focus: boolean;

    constructor(focus: boolean) {
        super();
        this.focus = focus;
    }
}

class _EventSaveTap extends _Event {}

class _EventGoodsItemFocusChanged extends _Event {
    goods: GoodsInfo;
    focus: boolean;

    constructor(goodsInfo: GoodsInfo, focus: boolean) {
        super();
        this.goods = goodsInfo;
        this.focus = focus;
    }
}

class _EventUpdateGoodsCount extends _Event {
    goods: GoodsInfo;
    count: number;

    constructor(goods: GoodsInfo, count: number) {
        super();
        this.goods = goods;
        this.count = count;
    }
}

class _EventUpdateDosageCount extends _Event {
    count: number;

    constructor(count: number) {
        super();
        this.count = count;
    }
}

class _EventUpdateGoodsUnit extends _Event {
    goods: GoodsInfo;
    unit: string;

    constructor(goods: GoodsInfo, unit: string) {
        super();
        this.goods = goods;
        this.unit = unit;
    }
}

class _EventAddGoodsFromQRScanTap extends _Event {}

class _EventDeleteGoodsItem extends _Event {
    goodsInfo: GoodsInfo;

    constructor(goodsInfo: GoodsInfo) {
        super();
        this.goodsInfo = goodsInfo;
    }
}

class _EventDeleteAllGoodsItem extends _Event {}

class DirectChargeMedicineAddPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<DirectChargeMedicineAddPageBloc | undefined>(undefined);

    static fromContext(context: DirectChargeMedicineAddPageBloc): DirectChargeMedicineAddPageBloc {
        return context;
    }

    constructor(type: number, params: DirectChargeMedicineInvokeParams, nurseFilterInfo?: NurseParams) {
        super();

        this._params = params;
        this.innerState._fromNursePage = nurseFilterInfo?.fromNursePage ?? false;
        this.innerState._openGoodsTypes = nurseFilterInfo?.openGoodsTypes;
        this.dispatch(new _EventInit(type));
    }

    private _keyWordPublish?: Subject<string>;

    private _innerState!: State;

    private _params: DirectChargeMedicineInvokeParams;

    get showChineseUsage(): boolean {
        return this._params.showChineseUsage;
    }

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateGoodsType, this._mapEventUpdateGoodsType);
        map.set(_EventUpdateSearchKeyWord, this._mapEventUpdateSearchKeyword);
        map.set(_EventToggleMedicineSelectState, this._mapEventToggleMedicineSelectState);
        map.set(_EventShowShoppingCarTap, this._mapEventShowShoppingCarTap);
        map.set(_EventUpdateSearchFocus, this._mapEventUpdateSearchFocus);
        map.set(_EventGoodsItemFocusChanged, this._mapEventGoodsItemFocusChanged);
        map.set(_EventUpdateGoodsCount, this._mapEventUpdateGoodsCount);
        map.set(_EventUpdateGoodsCount, this._mapEventUpdateGoodsCount);
        map.set(_EventUpdateGoodsUnit, this._mapEventUpdateGoodsUnit);
        map.set(_EventSaveTap, this._mapEventSaveTap);
        map.set(_EventAddGoodsFromQRScanTap, this._mapEventAddGoodsFromQRScanTap);
        map.set(_EventDeleteGoodsItem, this._mapEventDeleteGoodsItem);
        map.set(_EventUpdateDosageCount, this._mapEventUpdateDosageCount);
        map.set(_EventDeleteAllGoodsItem, this._mapEventDeleteAllGoodsItem);

        return map;
    }

    private async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        if (this._params?.medicineInfo) {
            const { inputCounts, dosageCount, goods } = this._params.medicineInfo;
            this.innerState.inputCounts = inputCounts ?? new AbcMap();
            this.innerState.dosageCount = dosageCount ?? 1;
            this.innerState.selectedMedicines = goods ?? [];
            this.innerState.filterGoodsTypes = this._params.goodsTypes ?? [];
        }

        this.innerState.goodsType = event.type;

        //库存开关设置disableNoStockGoods为1时，不允许开出，0、2允许开出
        const disableNoStockGoods = userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 1 ?? true;
        this.innerState.allowZeroStockGoods = !disableNoStockGoods;

        this._keyWordPublish = new Subject();
        this.addDisposable(this._keyWordPublish);

        this._keyWordPublish
            .pipe(
                debounce((/*ignored*/) => {
                    if (_.isEmpty(this.innerState.keyword)) return of(0);
                    return interval(300);
                }),
                // debounceTime(300),
                switchMap((/*ignored*/) => {
                    const innerState = this.innerState;
                    innerState.searching = true;
                    innerState.searchError = undefined;
                    innerState.medicines = [];
                    this.update();

                    const { keyword } = innerState;
                    //当前处于搜索模式
                    if (innerState.isInSearchMode) {
                        if (!_.isEmpty(keyword)) {
                            if (this.innerState._fromNursePage) {
                                return GoodsAgent.searchStockGoods({
                                    keyword: keyword!,
                                    jsonType: innerState.filterGoodsTypes.map((t) => ({ type: t })),
                                }).toObservable();
                            }
                            if (innerState.filterGoodsTypes.length == 1) {
                                const type = innerState.filterGoodsTypes[0];
                                return GoodsAgent.searchStockGoods({
                                    keyword: keyword!,
                                    jsonType: [{ type }],
                                }).toObservable();
                            }

                            return GoodsAgent.searchAllGoods(keyword!, innerState.filterGoodsTypes)
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        } else return of([]);
                    }

                    //避免执行站初始进入，就调用一次
                    if (!_.isEmpty(innerState.filterGoodsTypes) && !this.innerState._fromNursePage) {
                        if (innerState.filterGoodsTypes.length == 1) {
                            const type = innerState.filterGoodsTypes[0];
                            return GoodsAgent.searchStockGoods({
                                keyword: "",
                                jsonType: [{ type }],
                            })
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        }
                        return GoodsAgent.searchAllGoods("", innerState.filterGoodsTypes);
                    }

                    switch (this.innerState.goodsType!) {
                        case DirectChargeMedicineAddPageCategoryItem.naviIdWestern:
                            return GoodsAgent.searchAllGoods(
                                "",
                                [GoodsType.medicine],
                                [GoodsSubType.medicineWestern, GoodsSubType.medicineChinesePatent]
                            ).toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.naviIdChinese:
                            return GoodsAgent.searchChineseMedicine("")
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.naviIdTreatment:
                            return GoodsAgent.searchTreatments({})
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.naviIdExamination:
                            return GoodsAgent.searchExamination({})
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.naviIdGoods:
                            return GoodsAgent.searchAllGoods("", [GoodsType.goods])
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial:
                            return GoodsAgent.searchAllGoods("", [GoodsType.material], [GoodsSubType.materialMedical])
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.nurseProduct:
                            return GoodsAgent.searchAllGoods("", [GoodsType.nurseProduct])
                                .catch((error) => new ABCError(error))
                                .toObservable();

                        case DirectChargeMedicineAddPageCategoryItem.naviIdPackage:
                            return GoodsAgent.searchPackage("")
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.otherGoods:
                            return GoodsAgent.searchAllGoods("", [GoodsType.otherGoods49])
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        case DirectChargeMedicineAddPageCategoryItem.glassesGoods:
                            return GoodsAgent.searchAllGoods("", [GoodsType.glasses])
                                .catch((error) => new ABCError(error))
                                .toObservable();
                        default:
                            let arr = [];
                            arr =
                                this.innerState.clinicExamTreatList?.filter((item) => item.customTypeId == this.innerState.goodsType) ?? [];
                            return of(arr);
                    }
                })
            )
            .subscribe((data) => {
                this.innerState.searching = false;
                if (data instanceof ABCError) {
                    this.innerState.searchError = data.detailError;
                    this.update();
                    return;
                }

                data = data.filter((item) => item.inStock && item.isSell == 1);
                this.innerState.medicines = data;
                this.update();
            })
            .addToDisposableBag(this);

        if (this._params.autoSearch) this._keyWordPublish.next("");

        GoodsAgent.getClinicExamTreatList(userCenter.clinic?.clinicId ?? "").then((rsp) => {
            this.innerState.clinicExamTreatList =
                rsp.filter((item) => item.type == GoodsType.treatment && item.subType == GoodsSubType.treatment) ?? [];
            this.innerState.clinicExamTreatList.forEach((item) => {
                if (item.customTypeId && item.customTypeName) {
                    const existItem = this.innerState.kCustomCategoryItems.find((it) => it.customTypeId === item.customTypeId);
                    if (!existItem) {
                        this.innerState.kCustomCategoryItems.push({
                            customTypeId: item.customTypeId,
                            customTypeName: item.customTypeName,
                        });
                    }
                }
            });
            this.update();
        });

        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    private async *_mapEventUpdateSearchKeyword(event: _EventUpdateSearchKeyWord): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (innerState.keyword == event.keyword) return;
        innerState.keyword = event.keyword;
        this._keyWordPublish?.next(event.keyword);
    }

    private async *_mapEventUpdateGoodsType(event: _EventUpdateGoodsType): AsyncGenerator<State> {
        this.innerState.goodsType = event.type;
        this._keyWordPublish?.next(this.innerState.keyword);

        yield this.innerState.clone();
    }

    private async *_mapEventToggleMedicineSelectState(event: _EventToggleMedicineSelectState): AsyncGenerator<State> {
        const { selectedMedicines, inputCounts } = this.innerState;
        if (inputCounts.has(event.goods)) {
            await this._deleteGoodsItemWithQuery(event.goods);
        } else {
            selectedMedicines.push(event.goods);
            inputCounts.set(event.goods, new DirectChargeUsage(_.last(event.goods.sellUnits)!, 1));

            //搜索出来的套餐不套餐
            if (event.goods.isPackage) {
                GoodsAgent.searchGoodsByIds({ goodsIds: [event.goods.id ?? ""] }).then((rsp) => {
                    const newGoodsInfo = _.first(rsp);
                    if (inputCounts.has(event.goods) && newGoodsInfo) {
                        Object.assign(event.goods, newGoodsInfo, { keyId: event.goods.keyId, _scrollKey: event.goods._scrollKey });
                        this.update();
                    }
                });
            }
        }

        yield this.innerState.clone();
    }

    private async *_mapEventShowShoppingCarTap(/*ignored: _EventShowShoppingCarTap*/): AsyncGenerator<State> {
        ShoppingCardExpandDialog.show(this).then((value) => {
            if (value) {
                if (value == ShoppingCardExpandDialog.dialogDismissFromSave) {
                    this.dispatch(new _EventSaveTap());
                }
            }
        });
    }

    private async *_mapEventUpdateSearchFocus(event: _EventUpdateSearchFocus): AsyncGenerator<State> {
        const innerState = this.innerState;
        //如果当前搜索关键字为空，强制设置searchFocus为false，解决搜索后清空字段，列表显示无数据
        innerState.searchFocus = _.isEmpty(innerState.keyword) ? false : event.focus;
        yield innerState.clone();

        if (!_.isEmpty(innerState.keyword)) return; //解决ios上点击确定按钮时重新触一次同keyword搜索

        if (!this._params.autoSearch) return;

        this._keyWordPublish?.next(innerState.keyword!);
    }

    private async *_mapEventGoodsItemFocusChanged(event: _EventGoodsItemFocusChanged): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (event.focus && event.goods == innerState.focusGoodsInfo) return;

        if (event.focus) {
            innerState.focusGoodsInfo = event.goods;
        } else if (innerState.focusGoodsInfo == event.goods) {
            innerState.focusGoodsInfo = undefined;
        }

        this._removeZeroCountMedicine();
        yield innerState.clone();
    }

    private _removeZeroCountMedicine(): void {
        const innerState = this.innerState;
        innerState.inputCounts.forEach((value, key, map) => {
            let count = value.count;
            if (key.isChineseMedicine) count *= innerState.dosageCount;
            if (count <= 0) {
                map.delete(key);
            }
        });
    }

    private async *_mapEventUpdateGoodsCount(event: _EventUpdateGoodsCount): AsyncGenerator<State> {
        this.innerState.inputCounts.get(event.goods)!.count = event.count;
        yield this.innerState.clone();
    }

    private async *_mapEventUpdateGoodsUnit(event: _EventUpdateGoodsUnit): AsyncGenerator<State> {
        LogUtils.d("_mapEventUpdateGoodsUnit event");
        const usage = this.innerState.inputCounts.get(event.goods);
        usage!.unit = event.unit;
        yield this.innerState.clone();
    }

    private async *_mapEventSaveTap(/*ignored: _EventSaveTap*/): AsyncGenerator<State> {
        const canSave = (await this._params.onPreSave?.(this.innerState.selectedMedicines ?? [])) ?? true;
        if (!canSave) return;

        const info = new DirectChargeMedicineAddInfo();
        info.goods = this.innerState.selectedMedicines;
        info.dosageCount = this.innerState.dosageCount;
        info.inputCounts = this.innerState.inputCounts;
        ABCNavigator.pop(info);
    }

    private async *_mapEventAddGoodsFromQRScanTap(/*ignored: _EventAddGoodsFromQRScanTap*/): AsyncGenerator<State> {
        const result: DirectChargeMedicineAddInfo | undefined = await ABCNavigator.navigateToPage(<DirectChargeAddGoodsQrscanPage />);
        if (_.isEmpty(result?.goods)) return;
        ABCNavigator.pop(result);
        /*下面注释内容：添加多种药品到购物车形式*/
        // const innerState = this.innerState;
        // for (const goods of result!.goods) {
        //     //扫码添加的药品已经在购物车中，直接更新信息
        //     if (this.innerState.selectedMedicines.find((item) => goods.compareKey() == item.compareKey())) {
        //         const newUsage = result!.inputCounts.get(goods);
        //         if (newUsage != null) innerState.inputCounts.set(goods, newUsage);
        //     } else {
        //         innerState.inputCounts.set(goods, result!.inputCounts.get(goods)!);
        //         innerState.selectedMedicines.push(goods);
        //     }
        // }
        //
        // yield this.innerState.clone();
    }

    private async *_mapEventDeleteGoodsItem(event: _EventDeleteGoodsItem): AsyncGenerator<State> {
        await this._deleteGoodsItemWithQuery(event.goodsInfo);
        yield this.innerState.clone();
    }

    private async *_mapEventDeleteAllGoodsItem(/*event: _EventDeleteAllGoodsItem*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const canDelete = (await this._params.onDeleteItems?.(innerState.selectedMedicines ?? [])) ?? true;
        if (!canDelete) return;
        innerState.focusGoodsInfo = undefined;
        innerState.selectedMedicines = [];
        innerState.inputCounts.clear();
        this.update();
    }

    private async *_mapEventUpdateDosageCount(event: _EventUpdateDosageCount): AsyncGenerator<State> {
        this.innerState.dosageCount = event.count;
        yield this.innerState.clone();
    }

    private async _deleteGoodsItemWithQuery(goodsInfo: GoodsInfo): Promise<void> {
        const _innerState = this.innerState;
        const canDelete = (await this._params.onDeleteItems?.([goodsInfo])) ?? true;
        if (!canDelete) {
            return;
        }
        if (goodsInfo == _innerState.focusGoodsInfo) {
            _innerState.focusGoodsInfo = undefined;
        }

        _.remove(_innerState.selectedMedicines, (item) => goodsInfo.compareKey() == item.compareKey());
        _innerState.inputCounts.delete(goodsInfo);
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    public requestUpdateSearchText(newValue: string): void {
        this.dispatch(new _EventUpdateSearchKeyWord(newValue));
    }

    public requestToggleMedicineSelectState(goods: GoodsInfo): void {
        this.dispatch(new _EventToggleMedicineSelectState(goods));
    }

    public requestUpdateGoodsType(id: number): void {
        this.dispatch(new _EventUpdateGoodsType(id));
    }

    public requestRetrySearch(): void {
        this._keyWordPublish?.next(this.innerState.keyword!);
    }

    public requestShowShoppingCar(): void {
        this.dispatch(new _EventShowShoppingCarTap());
    }

    public requestUpdateSearchFocus(focus: boolean): void {
        this.dispatch(new _EventUpdateSearchFocus(focus));
    }

    public requestSave(): void {
        this.dispatch(new _EventSaveTap());
    }

    public requestGoodsItemFocusChanged(goodsInfo: GoodsInfo, focus: boolean): void {
        this.dispatch(new _EventGoodsItemFocusChanged(goodsInfo, focus));
    }

    public requestUpdateGoodsCount(goods: GoodsInfo, value: number): void {
        this.dispatch(new _EventUpdateGoodsCount(goods, value));
    }

    public requestUpdateDosageCount(count: number): void {
        this.dispatch(new _EventUpdateDosageCount(count));
    }

    public requestUpdateGoodsUnit(goods: GoodsInfo, unit: string): void {
        this.dispatch(new _EventUpdateGoodsUnit(goods, unit));
    }

    public requestAddGoodsFromQRScan(): void {
        this.dispatch(new _EventAddGoodsFromQRScanTap());
    }

    public validateForSave(): boolean {
        for (const entry of this.innerState.inputCounts) {
            const goods = entry[0];
            const count = entry[1];
            if (count.count == undefined) {
                Toast.show(`${goods.displayName ?? ""}数量错误`, { warning: true });
                return false;
            }
        }

        return true;
    }

    public requestDeleteGoodsItem(goodsInfo: GoodsInfo): void {
        AbcTextInput.focusInput?.blur();
        this.dispatch(new _EventDeleteGoodsItem(goodsInfo));
    }

    public requestDeleteAll(): void {
        AbcTextInput.focusInput?.blur();
        this.dispatch(new _EventDeleteAllGoodsItem());
    }
}

export { DirectChargeMedicineAddPageBloc, State };
