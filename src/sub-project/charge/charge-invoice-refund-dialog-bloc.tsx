/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { ChargeConfig, ChargeForm, ChargeFormItem, ChargeInvoiceDetailData, ChargeRefundData, ChargeStatus } from "./data/charge-beans";
import { AbcMap } from "../base-ui/utils/abc-map";
import { AbcSet } from "../base-ui/utils/abc-set";
import { ChargeAgent, ChargeCalculatePayType, ChargeCalculateRspData } from "./data/charge-agent";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { LogUtils } from "../common-base-module/log";
import { Toast } from "../base-ui/dialog/toast";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { switchMap } from "rxjs/operators";
import _ from "lodash";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ChargeRefundPayPage } from "./charge-refund-pay-page";
import { of, Subject } from "rxjs";
import { ABCError } from "../common-base-module/common-error";
import { ChargeItemModifyUtils } from "./utils/charge-item-modify-utils";
import { GetClinicBasicSetting, OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { ChargeRefundAuditDialog } from "./view/charge-refund-audit-dialog/charge-refund-audit-dialog";
import { userCenter } from "../user-center";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";

class State {
    detailData!: ChargeInvoiceDetailData;
    selectedChargeItems = new AbcSet<ChargeFormItem>();
    countInputItems = new AbcMap<ChargeFormItem, number>();
    adjustmentFeeCheck = true;

    calculateRspData?: ChargeCalculateRspData;
    calculateRefundFailed: any;
    calculatingRefund = false;

    realRefundFee?: number = 0; //输入的实际退费全额，此值需要小于总可退费金（选中项全额+欠退金）
    realRefundFocus = false;

    chargeConfig?: ChargeConfig;
    shebaoAllRefundSetting?: number; // 收费-医保支付项目 0:不是 1:是

    clinicBasicSetting?: GetClinicBasicSetting;

    get refundAdjustmentFee(): number {
        const adjustmentFee = this.detailData?.chargeSheetSummary?.netAdjustmentFee ?? 0.0;

        if (this.adjustmentFeeCheck && adjustmentFee > 0.0) return adjustmentFee;

        return 0.0;
    }

    isChargeItemSelected(item: ChargeFormItem): boolean {
        return this.selectedChargeItems.has(item);
    }

    get owedRefundFee(): number | undefined {
        return this.detailData!.chargeSheetSummary?.owedRefundFee;
    }

    get totalRefundFee(): number {
        return this.calculateRspData!.needRefundFee! + this.owedRefundFee!;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    //中药处方中当前选中的项目
    get selectedChinesePrescriptionItems(): AbcMap<ChargeForm, ChargeFormItem[]> {
        const map = new AbcMap<ChargeForm, ChargeFormItem[]>();
        const detailData = this.detailData;
        detailData?.chargeForms
            ?.filter((t) => t.isChinesePrescription)
            ?.forEach((form) => {
                const formItems: ChargeFormItem[] = [];
                form.chargeFormItems
                    ?.filter((t) => t.canRefund)
                    ?.forEach((formItem) => {
                        if (this.isChargeItemSelected(formItem) ?? false) formItems.push(formItem);
                    });
                if (formItems.length > 0) map.set(form, formItems);
            });

        return map;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventUpdateRealRefundFocus extends _Event {
    constructor(focus: boolean) {
        super();
        this.focus = focus;
    }

    focus: boolean;
}

class _EventUpdateRealRefundFee extends _Event {
    constructor(fee: number) {
        super();
        this.fee = fee;
    }

    fee: number;
}

class _EventToggleAdjustmentFeeCheck extends _Event {}

class _EventUpdateItemInputCount extends _Event {
    constructor(item: ChargeFormItem, count: number, isBtn?: boolean) {
        super();
        this.item = item;
        this.count = count;
        this.isBtn = isBtn;
    }

    item: ChargeFormItem;
    count: number;
    isBtn?: boolean;
}

class _EventToggleItemSelect extends _Event {
    constructor(item: ChargeFormItem, chargeForm?: ChargeForm) {
        super();
        this.item = item;
        this.chargeForm = chargeForm;
    }

    item: ChargeFormItem;
    chargeForm?: ChargeForm;
}

class _EventSubmit extends _Event {
    callback?(): void;

    constructor(callback?: () => void) {
        super();
        this.callback = callback;
    }
}

class _EventUpdateCalculatePrice extends _Event {
    calculating?: boolean;
    error?: any;
    rsp?: ChargeCalculateRspData;

    constructor(options: { calculating?: boolean; error?: any; rsp?: ChargeCalculateRspData }) {
        super();

        const { calculating, error, rsp } = options;
        this.calculating = calculating;
        this.error = error;
        this.rsp = rsp;
    }
}

class _EventToggleChargeFormSelect extends _Event {
    chargeForm: ChargeForm;

    constructor(chargeForm: ChargeForm) {
        super();
        this.chargeForm = chargeForm;
    }
}

class _EventModifySelectAllChineseStatus extends _Event {
    chargeForm: ChargeForm;
    status: boolean;
    constructor(chargeForm: ChargeForm, status: boolean) {
        super();
        this.chargeForm = chargeForm;
        this.status = status;
    }
}

class _EventModifyAllDoseCount extends _Event {
    chargeForm: ChargeForm;
    value: number;
    constructor(chargeForm: ChargeForm, value: number) {
        super();
        this.chargeForm = chargeForm;
        this.value = value;
    }
}

class _EventCompareExecuteNumber extends _Event {
    formItem?: ChargeFormItem;
    constructor(formItem?: ChargeFormItem) {
        super();
        this.formItem = formItem;
    }
}

class ChargeInvoiceRefundDialogBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ChargeInvoiceRefundDialogBloc | undefined>(undefined);

    static fromContext(context: ChargeInvoiceRefundDialogBloc): ChargeInvoiceRefundDialogBloc {
        return context;
    }

    detailData: ChargeInvoiceDetailData;
    _chargeCalculateTrigger = new Subject<number>();

    constructor(detailData: ChargeInvoiceDetailData) {
        super();
        this.detailData = _.cloneDeep(detailData);
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);

        map.set(_EventToggleItemSelect, this._mapEventToggleItemSelect);
        map.set(_EventUpdateItemInputCount, this._mapEventUpdateItemCount);
        map.set(_EventToggleAdjustmentFeeCheck, this._mapEventToggleAdjustmentFeeCheck);
        map.set(_EventUpdateRealRefundFee, this._mapEventUpdateRealRefundFee);
        map.set(_EventUpdateCalculatePrice, this._mapEventUpdateCalculatePrice);
        map.set(_EventUpdateRealRefundFocus, this._mapEventUpdateRealRefundFocus);
        map.set(_EventSubmit, this._mapEventSubmit);
        map.set(_EventToggleChargeFormSelect, this._mapEventToggleChargeFormSelect);
        map.set(_EventModifySelectAllChineseStatus, this._mapEventModifySelectAllChineseStatus);
        map.set(_EventModifyAllDoseCount, this._mapEventModifyAllDoseCount);

        return map;
    }

    async *_mapEventModifySelectAllChineseStatus(event: _EventModifySelectAllChineseStatus): AsyncGenerator<State> {
        //全选当前中药处方
        if (event.status) {
            event.chargeForm?.chargeFormItems
                ?.filter((t) => t.canRefund)
                ?.forEach((item) => {
                    this._selectChargeItem(item);
                });
        } else {
            //    取消全选
            event.chargeForm?.chargeFormItems
                ?.filter((t) => t.canRefund)
                ?.forEach((item) => {
                    this._deselectChargeItem(item);
                    // 只有在可编辑剂数时，才需要回显最初的剂数
                    if (event.chargeForm?.isCanRefundDoseCount == 1) {
                        item.canRefundDoseCount = event.chargeForm.currentCanRefundDoseCount;
                        item.doseCount = event.chargeForm.currentCanRefundDoseCount;
                    }
                });
        }
        //取消后再全选，需要显示为默认的剂数
        event.chargeForm.usageInfo!.doseCount__ = event.chargeForm.currentCanRefundDoseCount;
        this.update();

        this._chargeCalculateTrigger.next(0);
    }

    async *_mapEventModifyAllDoseCount(event: _EventModifyAllDoseCount): AsyncGenerator<State> {
        event.chargeForm.usageInfo!.doseCount__ = event.value;
        //将全选，此时的剂数，遍历赋值给每一项
        event.chargeForm?.chargeFormItems?.forEach((item) => {
            item.canRefundDoseCount = event.value;
            item.doseCount = event.value; // 算费接口根据doseCount来计算
        });
        this.update();

        this._chargeCalculateTrigger.next(0);
    }

    async *_mapEventInit(/*ignored: _EventInit*/): AsyncGenerator<State> {
        const _innerState = this.innerState;
        _innerState.detailData = this.detailData;
        _innerState.chargeConfig = await OnlinePropertyConfigProvider.instance.getChargeConfig(false).catchIgnore();
        _innerState.shebaoAllRefundSetting = await OnlinePropertyConfigProvider.instance
            .getClinicChargeShebaoAllRefundSetting()
            .catchIgnore();

        //初始化时，需要给中药处方中的usageInfo字段doseCount__赋值
        _innerState.detailData.chargeForms
            ?.filter((t) => t.isChinesePrescription)
            ?.forEach((item) => {
                item.usageInfo!.doseCount__ = item.currentCanRefundDoseCount;
                item?.chargeFormItems?.forEach((subItem) => {
                    subItem.doseCount = subItem.canRefundDoseCount ?? 0;
                });
            });

        function guessRefundUnitCount(formItem: ChargeFormItem) {
            return Math.min(formItem.canRefundUnitCount!, Math.max(0, formItem.unitCount! - (formItem.executedUnitCount ?? 0)));
        }

        if (_innerState.detailData.chargeForms) {
            for (const form of _innerState.detailData.chargeForms) {
                // 空中药房没有退费前不能退单
                if (form.isAirPharmacy && form.isCanBeRefund !== 1) continue;
                // 若设置为审核/调配后不可退费,当前处方已审核/调配，则不可退费，需关闭审核/调配后才行
                if (_innerState.chargeConfig?.openAuditOrCompound && form?.hasFinished) continue;

                for (const formItem of form.chargeFormItems ?? []) {
                    if (!formItem.canRefund) continue;
                    if (formItem.isPackage) {
                        if (!_.isEmpty(formItem.composeChildren)) {
                            for (const subFormItem of formItem.composeChildren!) {
                                if (!subFormItem.canRefund) continue;

                                const guessRefundCount = guessRefundUnitCount(subFormItem);
                                if (guessRefundCount > 0) this._selectChargeItem(subFormItem);

                                _innerState.countInputItems.set(subFormItem, guessRefundCount);
                            }
                        }
                    } else {
                        let executedDeductedCount = 0; // 卡项折扣当前可抵扣的值
                        //收费项目---退的数据，默认等于最大能退数, 需要减去营销卡项抵扣的可抵扣的数量
                        if (formItem?.canRefundDeductCount) {
                            formItem.canRefundUnitCount = (formItem.canRefundUnitCount ?? 0) - formItem?.canRefundDeductCount;
                        }
                        // 优先执行的抵扣项目
                        if (formItem.canRefundDeductCount && formItem?.deductedTotalCount) {
                            //已执行的数量 大于 总抵扣数量
                            if ((formItem?.executedUnitCount ?? 0) >= formItem?.deductedTotalCount) {
                                formItem.executedUnitCount = (formItem.executedUnitCount ?? 0) - formItem.deductedTotalCount;
                                // 执行数量最大只展示到 可退数量
                                formItem.executedUnitCount = Math.min(formItem.executedUnitCount ?? 0, formItem.canRefundUnitCount ?? 0);
                                executedDeductedCount = Math.min(formItem.deductedTotalCount, formItem.canRefundDeductCount);
                            } else {
                                executedDeductedCount = Math.min(formItem.executedUnitCount ?? 0, formItem.canRefundDeductCount);
                                formItem.executedUnitCount = 0;
                            }
                        }

                        const guessRefundCount = guessRefundUnitCount(formItem);
                        if (guessRefundCount > 0) this._selectChargeItem(formItem);

                        _innerState.countInputItems.set(formItem, guessRefundCount);

                        if (formItem.isHasDeductItem) {
                            const newFormItem = JsonMapper.deserialize(ChargeFormItem, {
                                ...formItem,
                                id: !!formItem.id ? `${formItem.id}_isHasDeductItem` : formItem.id,
                                keyId: !!formItem.keyId ? `${formItem.keyId}_isHasDeductItem` : formItem.keyId,
                            });
                            // 根据当前item中可退的折扣数量，判断是否选中

                            if (executedDeductedCount) {
                                executedDeductedCount = Math.max((formItem?.canRefundDeductCount ?? 0) - executedDeductedCount, 0);
                            } else {
                                executedDeductedCount = formItem?.canRefundDeductCount ?? 0;
                            }
                            if (executedDeductedCount > 0) this._selectChargeItem(newFormItem);
                            _innerState.countInputItems.set(newFormItem, executedDeductedCount);
                        }
                    }
                }
            }
        }

        yield _innerState.clone();

        this.addDisposable(this._chargeCalculateTrigger);
        this._chargeCalculateTrigger
            .pipe(
                switchMap(() => {
                    this.dispatch(new _EventUpdateCalculatePrice({ calculating: true }));
                    if (_innerState.detailData.status == ChargeStatus.partCharged) {
                        const rspData = new ChargeCalculateRspData();
                        rspData.needRefundFee = _innerState.detailData!.chargeSheetSummary?.receivedFee;
                        return of(rspData);
                    }

                    const adjustmentFee = _innerState.refundAdjustmentFee;
                    return ChargeAgent.postChargeInvoiceCalculate({
                        payMode: ChargeCalculatePayType.refund,
                        chargeSheetId: _innerState.detailData.id,
                        selects: this._collectSelectItems(),
                        promotions: _innerState.detailData.promotions,
                        giftPromotions: _innerState.detailData.giftRulePromotions,
                        memberId: _innerState.detailData.memberId,
                        patientId: _innerState.detailData.patientId,
                        refundItemInputCount: _innerState.countInputItems,
                        expectedAdjustmentFee: adjustmentFee,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((data) => {
                if (data instanceof ABCError) {
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            error: data.detailError,
                        })
                    );
                } else this.dispatch(new _EventUpdateCalculatePrice({ calculating: false, rsp: data }));
            })
            .addToDisposableBag(this);

        this._chargeCalculateTrigger.next(0);

        this.innerState.clinicBasicSetting = await OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore();
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState.clone();
    }

    private async *_mapEventToggleItemSelect(event: _EventToggleItemSelect): AsyncGenerator<State> {
        if (this._innerState.isChargeItemSelected(event.item)) {
            this._deselectChargeItem(event.item);
        } else {
            this._selectChargeItem(event.item);
        }

        //在最后退药的时候，会根据doseCount__这个字段是否存在，来判断当前中药处方是否全选，存在即全选
        if (event.chargeForm && event.chargeForm.isChinesePrescription) {
            const { selectedChinesePrescriptionItems } = this.innerState;
            if (selectedChinesePrescriptionItems.get(event.chargeForm)?.length != event.chargeForm.__ChinesePrescriptionDispensedUnit) {
                event.chargeForm.usageInfo!.doseCount__ = undefined;
                //当取消全选时，每个药品的剂数，需要默认到原先的总剂数
                event.chargeForm?.chargeFormItems?.forEach((subItem) => {
                    subItem.canRefundDoseCount = event.chargeForm?.currentCanRefundDoseCount;
                    subItem.doseCount = event.chargeForm?.currentCanRefundDoseCount;
                });
            }
        }

        //这是一个补刀逻辑，用于解决当实退输入框处于焦点状态时，重新计费后，输入框内容不更新的问题
        //通过取消息它的焦点，使新算的值能够更新（因为默认情况下编辑框处于焦点状态时，忽略了动态设置的值参考 ABCTextField.didUpdateWidget)
        if (this._innerState.realRefundFocus) AbcTextInput.focusInput?.blur();

        yield this._innerState.clone();

        this._chargeCalculateTrigger.next(0);
    }

    private async *_mapEventUpdateItemCount(event: _EventUpdateItemInputCount): AsyncGenerator<State> {
        const _innerState = this.innerState;
        _innerState.countInputItems.set(event.item, event.count);

        //这是一个补刀逻辑，用于解决当实退输入框处于焦点状态时，重新计费后，输入框内容不更新的问题
        //通过取消息它的焦点，使新算的值能够更新（因为默认情况下编辑框处于焦点状态时，忽略了动态设置的值参考 ABCTextField.didUpdateWidget)
        if (_innerState.realRefundFocus) AbcTextInput.focusInput?.blur();
        if (!!event?.isBtn) {
            //检查退费项目是否大于剩余可执行项
            const countInputItemsCopy = this.innerState.countInputItems.clone();
            if (ChargeItemModifyUtils._isNeedCheckForModify(event.item)) {
                const refundCount = countInputItemsCopy.get(event.item);
                const unExecutedUnitCount = Math.max(event.item.unitCount! - event.item.executedUnitCount!, 0); //未执行次数
                //退费次数大于未执行次数
                if ((refundCount ?? 0) > unExecutedUnitCount) {
                    if (unExecutedUnitCount == 0) {
                        await Toast.show(`无可退数量，请先撤销执行后退费`, { warning: true });
                    } else {
                        await Toast.show(`退费数量不能大于可退数量(${unExecutedUnitCount}次)`, { warning: true });
                    }
                }
            }
        }

        yield _innerState.clone();
        this._chargeCalculateTrigger.next(0);
    }

    private async *_mapEventToggleAdjustmentFeeCheck(/*ignored: _EventToggleAdjustmentFeeCheck*/): AsyncGenerator<State> {
        const _innerState = this.innerState;
        _innerState.adjustmentFeeCheck = !_innerState.adjustmentFeeCheck;
        yield _innerState.clone();

        this._chargeCalculateTrigger.next(0);
    }

    private async *_mapEventUpdateRealRefundFee(event: _EventUpdateRealRefundFee): AsyncGenerator<State> {
        const _innerState = this.innerState;
        _innerState.realRefundFee = isNaN(event.fee) ? undefined : event.fee;
        yield _innerState.clone();
    }

    private async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice): AsyncGenerator<State> {
        LogUtils.d("_mapEventUpdateCalculatePrice");
        const _innerState = this.innerState;
        if (event.rsp) {
            _innerState.calculateRspData = event.rsp;
            _innerState.realRefundFee = _innerState.totalRefundFee;
        }

        _innerState.calculateRefundFailed = event.error;
        _innerState.calculatingRefund = event.calculating ?? false;

        yield _innerState.clone();
    }

    private async *_mapEventUpdateRealRefundFocus(event: _EventUpdateRealRefundFocus): AsyncGenerator<State> {
        const _innerState = this.innerState;
        _innerState.realRefundFocus = event.focus;
        yield _innerState.clone();
    }

    private async *_mapEventSubmit(event: _EventSubmit): AsyncGenerator<State> {
        const _innerState = this.innerState;
        // 检查选中的项目中是否有医保结算药品
        const selectedItemsMap = this._collectSelectItems();
        if (!selectedItemsMap || selectedItemsMap.size === 0) {
            return;
        }
        const selectedItems = Array.from(selectedItemsMap.values()).flat();
        if (selectedItems.length === 0) {
            return;
        }

        const hasHealthCardItem = selectedItems.some((item) => {
            return item.isMarkedByHealthCardPay;
        });
        // 满足医保结算药品的条件
        const list = selectedItems.filter((item) => item.isMarkedByHealthCardPay).map((item) => item.name);
        if (hasHealthCardItem) {
            await showConfirmDialog(
                "App无法医保退费",
                `${list.join("、")}等${list.length}项医保结算药品，需在电脑端进行医保退费。`,
                "我知道了"
            );
            return;
        }
        // 检查是否开启退费审核开关
        // 如果开启了，则需要审核人进行审核，但是如果当前登录者是审核人则退费时不需要审核
        const employeeId = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        let accessToken = undefined;
        if (
            _innerState.chargeConfig?.openRefundCheck &&
            !_innerState.chargeConfig.refundCheckEmployees?.find((item) => item.id == employeeId)
        ) {
            if (!!_innerState.detailData?.id) {
                const result: string = await ChargeRefundAuditDialog.show({ chargeSheetId: _innerState.detailData?.id });
                if (!result) return;
                accessToken = result;
            }
        }
        if (_innerState.calculateRefundFailed != undefined) {
            await Toast.show("费用计算失败", { warning: true });
            return;
        }

        if (_innerState.calculatingRefund) {
            await Toast.show("正在计算费用", { warning: true });
            return;
        }

        if (_innerState.realRefundFee === undefined) {
            await Toast.show("请输入实退金额", { warning: true });
            return;
        }

        if (_innerState.realRefundFee < 0) {
            await Toast.show("实退金额不能少于0", { warning: true });
            return;
        }

        if (_innerState.realRefundFee > _innerState.totalRefundFee) {
            //      ABCToast.show('实退金额不能大于项目金额+欠退金额(${_innerState.totalRefundFee})', context, warning: true);
            await Toast.show("实退金额不能大于项目金额+欠退金额", { warning: true });
            return;
        }

        const selects = this._collectSelectItems();
        for (const enables of selects) {
            const formItems = enables[1];
            for (const formItem of formItems) {
                let count = 0;
                // const count = formItem.unitCount ?? 0.0;
                //套餐需要区分开
                if (formItem.isPackage) {
                    if (!_.isEmpty(formItem.composeChildren)) {
                        for (const subItem of formItem.composeChildren!) {
                            count = _innerState.countInputItems.get(subItem) ?? 0;
                            if (count <= 0) {
                                await Toast.show(`${formItem.displayName}无可退数量，请先撤销执行后退费`, {
                                    warning: true,
                                });
                                return;
                            }
                        }
                    }
                } else {
                    count = _innerState.countInputItems.get(formItem) ?? 0;
                }
                if (count <= 0) {
                    await Toast.show(`${formItem.displayName}无可退数量，请先撤销执行后退费`, {
                        warning: true,
                    });
                    return;
                }
            }
        }

        //检查退费项目是否大于剩余可执行项
        const countInputItemsCopy = this.innerState.countInputItems.clone();
        countInputItemsCopy.forEach((value, key, map) => {
            if (!this.innerState.selectedChargeItems.has(key)) {
                map.delete(key);
            }
        });

        if (!(await ChargeItemModifyUtils.checkRefundUnitCount(countInputItemsCopy, true))) return;

        const payData = new ChargeRefundData();

        payData.chargeInvoiceDetailData = _innerState.detailData;
        payData.needRefundFee = _innerState.calculateRspData!.needRefundFee;
        payData.owedRefundFee = _innerState.detailData!.chargeSheetSummary?.owedRefundFee;
        payData.selects = selects;
        payData.clinicBargain = false;
        payData.refundItemInputCount = _innerState.countInputItems;
        payData.refundAdjustmentFee = _innerState.refundAdjustmentFee;
        payData.realRefundFee = _innerState.realRefundFee;

        ABCNavigator.navigateToPage(
            <ChargeRefundPayPage chargePayData={payData} successCallBack={event.callback} accessToken={accessToken} />,
            {
                replace: true,
            }
        ).then();
        // ABCNavigator.navigateToWithPage(context, ChargeRefundPayPage(chargePayData: payData), replace: true);
    }

    async *_mapEventToggleChargeFormSelect(event: _EventToggleChargeFormSelect): AsyncGenerator<State> {
        const formItems = event.chargeForm.chargeFormItems;
        const firstItem = _.first(formItems);
        const select = firstItem ? this.innerState.isChargeItemSelected(firstItem) : false;
        formItems?.forEach((item) => {
            if (select) this._deselectChargeItem(item);
            else this._selectChargeItem(item);
        });

        //这是一个补刀逻辑，用于解决当实退输入框处于焦点状态时，重新计费后，输入框内容不更新的问题
        //通过取消息它的焦点，使新算的值能够更新（因为默认情况下编辑框处于焦点状态时，忽略了动态设置的值参考 ABCTextField.didUpdateWidget)
        if (this._innerState.realRefundFocus) AbcTextInput.focusInput?.blur();

        yield this._innerState.clone();

        this._chargeCalculateTrigger.next(0);
    }

    @actionEvent(_EventCompareExecuteNumber)
    async *_mapEventCompareExecuteNumber(event: _EventCompareExecuteNumber): AsyncGenerator<State> {
        if (!event?.formItem) return;
        //检查退费项目是否大于剩余可执行项
        const countInputItemsCopy = this.innerState.countInputItems.clone();
        if (!ChargeItemModifyUtils._isNeedCheckForModify(event.formItem)) return;
        const refundCount = countInputItemsCopy.get(event.formItem);
        const unExecutedUnitCount = Math.max(event.formItem.unitCount! - event.formItem.executedUnitCount!, 0); //未执行次数
        //退费次数大于未执行次数
        if ((refundCount ?? 0) > unExecutedUnitCount) {
            if (unExecutedUnitCount == 0) {
                await Toast.show(`无可退数量，请先撤销执行后退费`, { warning: true });
            } else {
                await Toast.show(`退费数量不能大于可退数量(${unExecutedUnitCount}次)`, { warning: true });
            }
        }
    }

    _collectSelectItems(): AbcMap<ChargeForm, Array<ChargeFormItem>> {
        const _innerState = this.innerState;
        const copyDetail = JsonMapper.deserialize(ChargeInvoiceDetailData, _innerState.detailData);

        const selectItems = new AbcMap<ChargeForm, Array<ChargeFormItem>>();
        for (const form of copyDetail.chargeForms!) {
            const formItems: ChargeFormItem[] = [];
            for (const formItem of form.chargeFormItems!) {
                const subFormItems: ChargeFormItem[] = [];
                if (formItem.isPackage) {
                    if (!_.isEmpty(formItem.composeChildren)) {
                        for (const subFormItem of formItem.composeChildren!) {
                            if (_innerState.isChargeItemSelected(subFormItem)) {
                                subFormItem.unitCount = _innerState.countInputItems.get(subFormItem);
                                subFormItems.push(subFormItem);
                            }
                            if (subFormItem.isHasDeductItem) {
                                const newSubFormItem = JsonMapper.deserialize(ChargeFormItem, {
                                    ...subFormItem,
                                    id: !!subFormItem.id ? `${subFormItem.id}_isHasDeductItem` : subFormItem.id,
                                    keyId: !!subFormItem.keyId ? `${subFormItem.keyId}_isHasDeductItem` : subFormItem.keyId,
                                });
                                if (_innerState.countInputItems.get(newSubFormItem)) {
                                    newSubFormItem.deductTotalCount = _innerState.countInputItems.get(newSubFormItem);
                                    subFormItems.push(newSubFormItem);
                                }
                            }
                        }

                        //有子项被选中
                        if (!_.isEmpty(subFormItems)) {
                            formItem.composeChildren = subFormItems;
                            formItems.push(formItem);
                        }
                    }
                } else {
                    if (_innerState.isChargeItemSelected(formItem)) {
                        formItem.unitCount = _innerState.countInputItems.get(formItem);
                        formItems.push(formItem);
                    }

                    if (formItem.isHasDeductItem) {
                        const newFormItem = JsonMapper.deserialize(ChargeFormItem, {
                            ...formItem,
                            id: !!formItem.id ? `${formItem.id}_isHasDeductItem` : formItem.id,
                            keyId: !!formItem.keyId ? `${formItem.keyId}_isHasDeductItem` : formItem.keyId,
                        });

                        if (_innerState.isChargeItemSelected(newFormItem)) {
                            newFormItem.deductTotalCount = _innerState.countInputItems.get(newFormItem);
                            formItems.push(newFormItem);
                        }
                    }
                }
            }

            if (!_.isEmpty(formItems)) {
                selectItems.set(form, formItems);
            }
        }
        return selectItems;
    }

    private _selectChargeItem(item: ChargeFormItem): void {
        LogUtils.i("_selectChargeItem");
        this._innerState.selectedChargeItems.add(item);
    }

    private _deselectChargeItem(item: ChargeFormItem): void {
        this._innerState.selectedChargeItems.delete(item);
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    //点击退费
    public requestSubmit(callback?: () => void): void {
        this.dispatch(new _EventSubmit(callback));
    }

    //点击一项收费项，进行切换选择状态
    public requestToggleItemSelect(item: ChargeFormItem, chargeForm?: ChargeForm): void {
        this.dispatch(new _EventToggleItemSelect(item, chargeForm));
    }

    //更新输入的退费数量
    public requestUpdateItemInputCount(item: ChargeFormItem, value: number, isBtn?: boolean): void {
        this.dispatch(new _EventUpdateItemInputCount(item, value, isBtn));
    }

    //更新实退金额
    public requestUpdateRealRefundFee(feePrice: number): void {
        this.dispatch(new _EventUpdateRealRefundFee(feePrice));
    }

    //请求重新计算费用
    public requestReCalculatePrice(): void {
        this.dispatch(new _EventUpdateCalculatePrice({ calculating: false }));
        this._chargeCalculateTrigger.next(0);
    }

    //更新实退输入框当前的焦点状态
    public requestUpdateRealRefundFocus(focus: boolean): void {
        this.dispatch(new _EventUpdateRealRefundFocus(focus));
    }

    public requestToggleChargeFormSelect(chargeForm: ChargeForm): void {
        this.dispatch(new _EventToggleChargeFormSelect(chargeForm));
    }

    // 更改中药处方全选的状态
    modifySelectAllChineseStatus(chargeForm: ChargeForm, status: boolean): void {
        this.dispatch(new _EventModifySelectAllChineseStatus(chargeForm, status));
    }

    // 更改中药处方中总剂数
    modifyAllDoseCount(chargeForm: ChargeForm, value: number): void {
        this.dispatch(new _EventModifyAllDoseCount(chargeForm, value));
    }

    //比对执行次数与退费次数（退费次数不能高于未执行次数）
    requestCompareExecuteNumber(formItem?: ChargeFormItem): void {
        this.dispatch(new _EventCompareExecuteNumber(formItem));
    }
}

export { ChargeInvoiceRefundDialogBloc, State };
