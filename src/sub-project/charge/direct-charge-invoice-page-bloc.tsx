/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-06-9
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import {
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeFormStatus,
    ChargeInvoiceDetailData,
    ChargePayData,
    ChargeSettlementExceptionItem,
    ChargeSheetSummary,
    ChargeSourceFormType,
    ChargeStatus,
    PayMethod,
} from "./data/charge-beans";
import { ClinicDoctorInfo, DeliveryInfo, GoodsInfo, Patient, WxBindStatus } from "../base-business/data/beans";
import { OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import _, { isNil } from "lodash";
import { DirectChargeDraftManager } from "./data/direct-charge-draft-manager";
import { userCenter } from "../user-center";
import { LogUtils } from "../common-base-module/log";
import { ChargeUtils } from "./utils/charge-utils";
import { of, Subject } from "rxjs";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { debounceTime, switchMap } from "rxjs/operators";
import {
    AbnormalTransactionList,
    ChargeAgent,
    ChargeCalculateRspData,
    ChargeEventDirectCharge,
    ChargeEventPushOrder,
    ChargeEventRenewpaid,
    ChargeEventRepaid,
    ChargeEventSafeDraft,
    PayStatus,
} from "./data/charge-agent";
import { ABCError } from "../common-base-module/common-error";
import { ClinicAgent, ClinicAirPharmacyConfig, ClinicVirtualPharmacyConfig, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { AbcMap } from "../base-ui/utils/abc-map";
import { SingleBargainDialog } from "./single-bargain-dialog";
import { Toast } from "../base-ui/dialog/toast";
import { ChargePayPage } from "./charge-pay-page";
import { errorToStr } from "../common-base-module/utils";
import { DialogBuilder, DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { URLProtocols } from "../url-dispatcher";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { PatientAgent } from "../base-business/data/patient-agent";
import { ChargeInvoicePage } from "./charge-invoice-page";
import { ChargeInvoiceDetailDataChangedState, ChargeInvoiceEditViewBloc } from "./charge-invoice-edit-view-bloc";
import { ChargeRefundDialog } from "./view/charge-refund-dialog";
import { ChargeAbnormalDialog } from "./view/charge-abnormal-dialog";
import { ABCUtils } from "../base-ui/utils/utils";
import { CrmAgent } from "../patients/data/crm-agent";
import { PatientFamilyWxStatus } from "../patients/data/crm-bean";
import { directChargePushTrigger } from "./direct-charge-invoice-page-bean";
import { ProductAddExecutorDialog } from "../outpatient/product-add-page/views/product-add-executor-dialog";
import { OutpatientAgent } from "../outpatient/data/outpatient";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import abcI18Next from "../language/config";
import { MedicineScopeId } from "./data/charge-bean-air-pharmacy";
import { ConsultantList, ModuleRolesId } from "../base-business/data/clinic-data";
import { Text, View } from "@hippy/react";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventPatientUpdate extends _Event {
    patient: Patient;

    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}

class _EventClearPatient extends _Event {}

class _EventRequestBack extends _Event {}

class _EventSingleBargainTap extends _Event {}

class _EventChargeBtnTap extends _Event {}

class _EventDeleteDraft extends _Event {}

class _EventSend extends _Event {}

class _EventSaveDraft extends _Event {}

class _EventSaveChargeInvoice extends _Event {}

class _EventReloadData extends _Event {}

class _EventCloseChargeSheet extends _Event {}

class _EventHandleChargeAbnormal extends _Event {
    isShebaoAbnormal: boolean;
    constructor(isShebaoAbnormal: boolean) {
        super();
        this.isShebaoAbnormal = isShebaoAbnormal;
    }
}

class _EventAbnormalRefund extends _Event {
    abnormalMsg: AbnormalTransactionList;
    constructor(abnormalMsg: AbnormalTransactionList) {
        super();
        this.abnormalMsg = abnormalMsg;
    }
}

class _EventModifyProductEmployee extends _Event {}
class _EventModifyConsultant extends _Event {
    index?: number;
    constructor(index?: number) {
        super();
        this.index = index;
    }
}

class _EventSelectItemSeller extends _Event {
    item?: ClinicDoctorInfo;
    constructor(item?: ClinicDoctorInfo) {
        super();
        this.item = item;
    }
}

class _EventCancelPayment extends _Event {}
class State {
    detailData?: ChargeInvoiceDetailData;
    calculating = false;
    calculateFailed: any;

    showErrorHint = false; //显示错误提示
    showSellerRequiredHint = false; //开单人必填提示

    chargeConfig?: ChargeConfig;

    //诊所空中药房配置
    airPharmacyConfig?: ClinicAirPharmacyConfig;
    virtualPharmacyConfig?: ClinicVirtualPharmacyConfig;

    loadingDetailData = false;
    loadingDetailDataError: any;

    newLocalDraft = false;
    canPushChargeOrder = false;
    patientWxPushConfig?: PatientFamilyWxStatus; //当前患者是否可以微信推送等配置

    editBloc?: ChargeInvoiceEditViewBloc;

    _hasChanged = false;

    //销售员是否必填
    chargeRequiredSeller = false;

    abnormalList?: AbnormalTransactionList[]; //非社保异常列表

    employeesMeConfig?: EmployeesMeConfig;

    diagnoseCount?: number; //就诊历史
    consultantList?: ConsultantList[]; //咨询师
    allClinicDoctorInfo: ClinicDoctorInfo[] = [];
    matchClinicDoctorInfo: ClinicDoctorInfo[] = [];

    settlementExceptionList?: ChargeSettlementExceptionItem[]; // 结算异常列表

    // 医保退费异常
    get shebaoRefundException(): boolean {
        const abnormalList = this.settlementExceptionList?.filter((t) => t.payMode == PayMethod.payHealthCard);
        //   优先展示退费异常
        return !!abnormalList?.some((t) => t.shebaoRefundAbnormal);
    }

    get hasChanged(): boolean {
        return this.editBloc?.currentState.hasChanged || this._hasChanged;
    }

    //是否有内容
    get hasContent(): boolean {
        return this.detailData?.hasContent() ?? false;
    }

    /**
     * @description 收费员查看患者就诊历史
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeePatientHistory;
    }

    /**
     * @description 收费员查看患者手机号
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientMobile(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeePatientMobile;
    }

    /**
     * 口腔诊所
     */
    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    /**
     * 当前添加的项目
     */
    get treatmentChargeForm(): ChargeFormItem[] {
        return [
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.examination)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.package)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.westernPrescription)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.infusionPrescription)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.chinesePrescription)?.chargeFormItems ?? []),
        ];
    }

    /**
     * 项目医生相关信息
     */
    get doctorDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item?.doctorName && str.add(item?.doctorName);
            if (item?.doctorId == "") {
                str.add("不指定");
            }
        }
        return [...str.values()].join("、");
    }

    /**
     * 项目护士相关信息
     */
    get nurseDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item?.nurseName && str.add(item.nurseName);
            if (item?.nurseId == "") {
                str.add("不指定");
            }
        }
        return [...str.values()].join("、");
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class DirectChargeInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<DirectChargeInvoicePageBloc | undefined>(undefined);
    localDraftId?: string;
    networkDraftChargeId?: string;
    detailData?: ChargeInvoiceDetailData;

    editBloc: ChargeInvoiceEditViewBloc;

    _calculatePriceTrigger = new Subject<number>(); //用于触发算费请求

    private _getHistoryListTrigger = new Subject<string>(); // 就诊历史

    private _patientInfoTrigger = new Subject<string>(); //患者信息

    static fromContext(context: DirectChargeInvoicePageBloc): DirectChargeInvoicePageBloc {
        return context;
    }

    constructor(params: { localDraftId?: string; detailData?: ChargeInvoiceDetailData; networkDraftChargeId?: string }) {
        super();

        this.localDraftId = params.localDraftId;
        this.networkDraftChargeId = params.networkDraftChargeId;
        this.detailData = params.detailData;

        this.editBloc = new ChargeInvoiceEditViewBloc();
        this.addDisposable(this.editBloc);

        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventPatientUpdate, this._mapEventPatientUpdate);
        map.set(_EventRequestBack, this._mapEventRequestBack);
        map.set(_EventSingleBargainTap, this._mapEventSingleBargainTap);
        map.set(_EventChargeBtnTap, this._mapEventChargeBtnTap);
        map.set(_EventDeleteDraft, this._mapEventDeleteDraft);
        map.set(_EventSend, this._mapEventSend);
        map.set(_EventSaveDraft, this._mapEventSaveDraft);
        map.set(_EventSaveChargeInvoice, this._mapEventSaveChargeInvoice);
        map.set(_EventReloadData, this._mapEventReloadData);
        map.set(_EventCloseChargeSheet, this._mapEventCloseChargeSheet);
        map.set(_EventHandleChargeAbnormal, this._mapEventHandleChargeAbnormal);
        map.set(_EventAbnormalRefund, this._mapEventAbnormalRefund);
        return map;
    }

    private _initPageConfig(): void {
        Promise.all([
            OnlinePropertyConfigProvider.instance.getChargeConfig().catch((/*ignored*/) => undefined),
            ClinicAgent.getAirPharmacyConfig().catch((/*ignore*/) => undefined),
            OnlinePropertyConfigProvider.instance.getChargeRequiredSeller().catch((/*ignore*/) => false),
            ClinicAgent.getVirtualPharmacyConfig().catchIgnore(),
            ClinicAgent.getEmployeesMeConfig().catchIgnore(),
            ClinicAgent.updateEmployeesConsultant([ModuleRolesId.ROLE_CONSULTANT_ID]).catchIgnore(), //获取咨询师列表
            ClinicAgent.getClinicAllDoctorsRegfee(0, 0, 1, 1).catchIgnore(), // 医助全部医生 (不含儿保医生)
            ChargeAgent.getProcessRules(false).catchIgnore(), //强制刷新一次药品加工配置,后面加工费编辑可能会用到
        ])
            .toObservable()
            .subscribe((rsp) => {
                const [
                    chargeConfig,
                    airPharmacyConfig,
                    chargeRequiredSeller,
                    virtualPharmacyConfig,
                    employeesMeConfig,
                    consultantList,
                    allDoctors,
                ] = rsp;
                this.innerState.chargeConfig = chargeConfig;
                this.innerState.airPharmacyConfig = airPharmacyConfig;
                this.innerState.chargeRequiredSeller = chargeRequiredSeller ?? false;
                this.innerState.virtualPharmacyConfig = virtualPharmacyConfig;
                this.innerState.employeesMeConfig = employeesMeConfig;
                this.innerState.consultantList = consultantList;
                const doctorHimself = allDoctors?.filter((item) => item.doctorId == userCenter.employee?.id);
                // 医生去重
                const duplicateRemoval: ClinicDoctorInfo[] =
                    allDoctors?.filter((item) => !doctorHimself?.find((itt) => itt.doctorId == item.doctorId)) ?? [];
                this.innerState.allClinicDoctorInfo = doctorHimself?.concat(duplicateRemoval ?? []) ?? [];
                this._collectMatchData();
                this.update();
            })
            .addToDisposableBag(this);
    }
    private _initPageTrigger(): void {
        const innerState = this.innerState;
        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loadingDetailData = false;
                    this.innerState.loadingDetailDataError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);

        // 患者信息
        this._patientInfoTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(0);
                    return CrmAgent.getPatientById(patientId)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((patient) => {
                if (patient instanceof Patient) {
                    this.innerState.detailData!.patient = _.merge(this.innerState.detailData!.patient, patient);
                }
                this.update();
            })
            .addToDisposableBag(this);

        LogUtils.d("_mapEventInit UserCenter = " + userCenter.constructor.name);

        this.editBloc.state
            .subscribe((state) => {
                if (state instanceof ChargeInvoiceDetailDataChangedState) {
                    //药品信息变化，清除整单议价
                    if (state.detailData?.chargeSheetSummary != undefined) {
                        state.detailData!.chargeSheetSummary!.expectedTotalPrice__ = undefined;
                    }
                    if (state.copyField) {
                        //如果是零售收费
                        this.innerState.detailData = state.detailData;
                    }
                    this._calculatePriceTrigger.next(0);
                } else {
                    this.update();
                }
            })
            .addToDisposableBag(this);

        directChargePushTrigger
            .subscribe(() => {
                this._getPatientWxStatus(false);
            })
            .addToDisposableBag(this);

        this._calculatePriceTrigger
            .pipe(
                debounceTime(500),
                switchMap(() => {
                    this.innerState.calculating = true;
                    this.innerState.calculateFailed = undefined;
                    this.update();

                    return this._calculatingPrice()
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.calculating = false;
                if (rsp instanceof ABCError) {
                    this.innerState.calculateFailed = rsp.detailError;
                } else {
                    ChargeUtils.syncChargeInvoiceDetail(innerState.detailData, rsp);
                }

                this.update();
            })
            .addToDisposableBag(this);
        LogUtils.d("_mapEventInit event = " + this.innerState.detailData);

        ChargeAgent.chargeStatusPublisher.subscribe(async (event) => {
            if (!innerState.detailData) return;
            if (event instanceof ChargeEventDirectCharge) {
                if (event.draftId == innerState.detailData!.id && event.payRsp.payStatus == PayStatus.success) {
                    //收费成功，删除草稿
                    await this._removeDraftChargeInvoice();
                    innerState.detailData.id = event.payRsp.id;
                    innerState.detailData.status = event.payRsp.status;
                }
            } else if (event instanceof ChargeEventPushOrder) {
                await this._removeDraftChargeInvoice(); ////挂单成功后，清除掉本地草稿
            } else if (event instanceof ChargeEventSafeDraft) {
                if (!_.isEmpty(event.data.localDraftId) && event.data.localDraftId == innerState.detailData.localDraftId) {
                    await this._removeDraftChargeInvoice(); ////挂单成功后，清除掉本地草稿
                }
            } else if (event instanceof ChargeEventRenewpaid) {
                const rsp = event.rsp;
                if (rsp.id == innerState.detailData.id) {
                    innerState.detailData.status = rsp.status;
                }
            } else if (event instanceof ChargeEventRepaid) {
                if (innerState.detailData.id == event.rsp.id && event.rsp.payStatus == PayStatus.success) {
                    innerState.detailData.status = event.rsp.status;
                    //收费成功，删除草稿
                    await this._removeDraftChargeInvoice();
                }
            }
        });

        // 锁单socket接口（收费模块只显示门诊医嘱编辑处方、收费支付锁单、退费锁单）
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.detailData?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            this.innerState.detailData = this.innerState.detailData ?? new ChargeInvoiceDetailData();
            this.innerState.detailData.copyPatientOrderLocks = this.innerState.detailData.copyPatientOrderLocks ?? [];
            const patientOrderLock = this.innerState.detailData.copyPatientOrderLocks?.find((t) => t.businessKey == data.businessKey);
            // 门诊编辑处方锁
            if (!!patientOrderLock) {
                Object.assign(patientOrderLock, {
                    ...data,
                });
            } else {
                this.innerState.detailData.copyPatientOrderLocks!.push(
                    JsonMapper.deserialize(PatientOrderLockDetail, {
                        ...data,
                    })
                );
            }
            // 如果是收费支付锁单，还需要判断当前是哪方发起的
            if (data.businessKey == PatientOrderLockType.chargeSheet) {
                this.innerState.detailData.lockStatus = data.value?.businessDetail?.addedLockStatus;
                this.innerState.detailData.lockPayTransactionInfo = this.innerState.detailData.lockPayTransactionInfo ?? {};
                this.innerState.detailData.lockPayTransactionInfo.id = data.value?.businessDetail?.chargePayTransactionId;
            }
            this.update();
        });
    }
    private async _initPageData(): Promise<void> {
        const innerState = this.innerState;
        innerState.editBloc = this.editBloc;
        LogUtils.d("_mapEventInit this.localDraftId = " + this.localDraftId);
        if (!_.isEmpty(this.localDraftId)) {
            try {
                innerState.detailData = await DirectChargeDraftManager.instance.loadDraftChargeDetailData(this.localDraftId!);
            } catch (e) {
                innerState.detailData = DirectChargeDraftManager.createDraft();
                innerState.detailData.localDraftId = this.localDraftId!;
            }
        } else if (!_.isEmpty(this.networkDraftChargeId)) {
            await this._loadNetworkDraft().then();
        } else if (this.detailData) {
            innerState.detailData = JsonMapper.deserialize(ChargeInvoiceDetailData, this.detailData);
            innerState.detailData.status = ChargeStatus.unCharged;

            innerState.detailData.chargeForms?.forEach((form) => {
                form.status = ChargeFormStatus.unCharged;
                form.chargeFormItems?.forEach((item) => {
                    item.status = ChargeFormItemStatus.unCharged;
                    item.localAdd = true;
                });
            });
        } else {
            this.innerState.detailData = DirectChargeDraftManager.createDraft();
            this.innerState.newLocalDraft = true;
        }

        this._refreshAirPharmacy();

        if (innerState.detailData) this.editBloc.requestUpdateDetailData(innerState.detailData);
        if (!!innerState.detailData?.patient?.id) {
            this._patientInfoTrigger.next(innerState.detailData?.patient?.id);
            this._getHistoryListTrigger.next(innerState.detailData?.patient?.id);
        }
        //获取当前患者推送的相关条件
        this._getPatientWxStatus();
        // 获取医保结算异常具体类型
        await this.getShebaoSettlementExceptionList();

        this._calculatePriceTrigger.next(0);
    }
    async *_mapEventInit(/*ignored: _EventInit*/): AsyncGenerator<State> {
        this._initPageConfig();
        this._initPageTrigger();
        await this._initPageData();
        yield this.innerState.clone();
    }

    async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        yield event.state ?? this.innerState.clone();
    }

    async *_mapEventPatientUpdate(event: _EventPatientUpdate): AsyncGenerator<State> {
        const _innerState = this.innerState;
        const patientIdChanged = this.innerState.detailData?.patient?.id != event.patient.id;
        this.innerState.detailData!.patient = event.patient;
        this.innerState.detailData!.patientId = event?.patient?.id;
        yield this.innerState.clone();

        if (patientIdChanged) {
            if (!_.isEmpty(event.patient.id)) {
                if (!!event.patient?.isMember) {
                    const memberInfo = await PatientAgent.getMemberInfoById(event.patient.id!).catchIgnore();
                    _innerState.detailData!.memberId = memberInfo?.patientId;
                    _innerState.detailData!.memberInfo = memberInfo;
                }

                const patientId = event.patient.id;
                if (this.editBloc.currentState.dispensingConfig?.toHome == 1) {
                    ChargeAgent.getDeliverList({ patientId: event.patient.id! })
                        .toObservable()
                        .subscribe((lists) => {
                            if (
                                patientId != _innerState.detailData!.patient!.id ||
                                !_.isEmpty(_innerState.detailData!.deliveryInfo?.displayAddress("/", true))
                            )
                                return;

                            _.remove(lists, (item) => item.isAvailable === 0);
                            if (lists?.length == 1) {
                                _innerState.detailData!.deliveryInfo = _.first(lists);
                                this.update();
                            }
                        })
                        .addToDisposableBag(this);
                }
                this._patientInfoTrigger.next(patientId);
                this._getHistoryListTrigger.next(patientId);
            } else {
                _innerState.detailData!.memberId = undefined;
                _innerState.detailData!.memberInfo = undefined;
                _innerState.detailData!.deliveryInfo = new DeliveryInfo();
                ChargeUtils.setDeliveryInfo(_innerState.detailData!, false, undefined);
            }

            this._calculatePriceTrigger.next(0);
            this._getPatientWxStatus();

            if (_innerState.detailData?.chargeSheetSummary) _innerState.detailData.chargeSheetSummary.expectedTotalPrice__ = undefined;
            _innerState._hasChanged = true;
            yield _innerState.clone();
        }
    }

    @actionEvent(_EventClearPatient)
    async *_mapEventClearPatient(/*event:_EventClearPatient*/): AsyncGenerator<State> {
        if (this.innerState.detailData) {
            this.innerState.detailData.patient = new Patient();
            this.innerState.diagnoseCount = 0;
            this.update();
        }
    }

    async *_mapEventRequestBack(/*ignored: _EventRequestBack*/): AsyncGenerator<State> {
        await this._saveLocalDraft();
        this.editBloc.innerState.copyField = false;
        ABCNavigator.pop();
    }

    async *_mapEventSingleBargainTap(/*ignored: _EventSingleBargainTap*/): AsyncGenerator<State> {
        const chargeData = this.innerState.detailData!;
        const allowViewMedicinePrice = !!this.innerState.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeeGoodsCostPrice;
        const result = await showBottomPanel<
            | {
                  chargeSheet: ChargeInvoiceDetailData;
                  itemUnitPriceChanged: boolean; //修改了单项单价
                  itemTotalPriceChanged: boolean; //修改了单项总价
                  totalPriceChanged: boolean; //总价
              }
            | undefined
        >(
            <SingleBargainDialog
                detailData={chargeData}
                editable={chargeData.canEditChargeSheet}
                allowViewMedicinePrice={allowViewMedicinePrice}
            />
        );
        if (result != undefined) {
            this.innerState.detailData = result.chargeSheet;
            this.editBloc.requestUpdateDetailData(this.innerState.detailData);
            if (result.itemTotalPriceChanged || result.itemUnitPriceChanged)
                this.innerState.detailData!.markPatientPointsInfoChecked(false);

            this.innerState._hasChanged = true;
            yield this.innerState.clone();

            this._calculatePriceTrigger.next(0);
        }
    }

    async *_mapEventChargeBtnTap(/*ignored: _EventChargeBtnTap*/): AsyncGenerator<State> {
        //清除掉所有数量为0的项
        const innerState = this.innerState;

        if (innerState.chargeRequiredSeller && !innerState.detailData?.sellerId) {
            this.innerState.showSellerRequiredHint = true;
            yield this.innerState.clone();
            return;
        }

        if (!(await this.editBloc.validate())) {
            this.innerState.showErrorHint = true;
            const chargeForms = this.editBloc.innerState.detailData?.chargeForms;
            chargeForms?.forEach((item) => {
                if (item.isAirPharmacy || item.isVirtualPharmacy) {
                    const medicinesItem = item.chargeFormItems?.filter((sub) => !sub.isDelivery && !sub.isDecoction && !sub.isIngredient);
                    if (_.isEmpty(medicinesItem)) {
                        Toast.show(`${item.isAirPharmacy ? "空中药房" : "代煎代配"}没有添加药品`, { warning: true });
                    } else {
                        if (
                            item?.isAirPharmacy &&
                            item.vendor__?.doseCountLimit == 1 &&
                            item.vendor__?.chooseMedicalStateId == MedicineScopeId.keLi
                        ) {
                            if ((item.doseCount ?? 0) % 2 != 0) {
                                Toast.show(`${item.vendor__?.pharmacyName ?? item.vendor__?.vendorName ?? ""}需剂数为双数才可调价`, {
                                    warning: true,
                                });
                            }
                        } else {
                            Toast.show("请完善必填项", { warning: true });
                        }
                    }
                }
            });

            yield this.innerState.clone();
            return;
        }
        if (innerState.calculating) {
            await Toast.show("正在计算费用，稍后再试", { warning: true });
            return;
        }

        //上次算费失败了，再算一次
        const ret = await this._calculatingPriceSync();
        if (!ret) return;

        const payData = new ChargePayData();
        payData.directCharge = true;
        payData.chargeInvoiceDetailData = innerState.detailData;

        const selects = new AbcMap<ChargeForm, ChargeFormItem[]>();
        for (const chargeForm of innerState.detailData!.chargeForms!) {
            if (chargeForm.chargeFormItems != null) {
                selects.set(chargeForm, chargeForm.chargeFormItems);
            }
        }

        payData.reCharge = innerState.detailData!.reCharge__ ?? false;
        payData.selects = selects;
        payData.receivableFee = innerState.detailData!.chargeSheetSummary?.receivableFee;
        payData.promotions = innerState.detailData!.promotions;
        payData.totalFee = innerState.detailData!.chargeSheetSummary?.totalFee;

        payData.memberId = innerState.detailData!.memberId;
        await ABCNavigator.navigateToPage(<ChargePayPage chargePayData={payData} />);

        //直接收费的单子重新收费
        if (innerState.detailData!.reCharge__ ?? false) {
            if (innerState.detailData!.status == ChargeStatus.partCharged) {
                //单子由unCharged变为非待收状态，说明收费成功了，需要回退到之前收费界面
                ABCNavigator.pop();
            }
        } else if (innerState.detailData!.status == ChargeStatus.partCharged) {
            ABCNavigator.navigateToPage(<ChargeInvoicePage chargeId={innerState.detailData!.id!} />, { replace: true }).then();
        }
    }

    async *_mapEventDeleteDraft(/*ignored: _EventDeleteDraft*/): AsyncGenerator<State> {
        const _innerState = this.innerState;
        if (!_innerState.detailData) return;
        if (_innerState.detailData.isLocalDraft) {
            const select = await showQueryDialog("确认删除本收费单？", "删除后不能恢复");
            if (select != DialogIndex.positive) return;
            await this._removeDraftChargeInvoice();

            await Toast.show("删除成功", { success: true });
            await delayed(1000).toPromise();

            ABCNavigator.pop();
            return;
        }
        //只支持删除挂单
        if (!_innerState.detailData.isNetworkDraft) return;

        const dialog = new DialogBuilder();
        dialog.negative = "取消";
        dialog.positive = "删除";
        dialog.title = "是否确定删除本收费单？";
        dialog.content = "删除后不能恢复。";

        const select = await dialog.show();
        if (select != DialogIndex.positive) return;
        await this._removeDraftChargeInvoice();

        const loadingDialog = new LoadingDialog("正在删除");
        loadingDialog.show();

        let ret = false;
        let errorStr: any;
        try {
            ret = await ChargeAgent.deleteDraft(_innerState.detailData.id!);
        } catch (e) {
            errorStr = errorToStr(e);
            LogUtils.e(`deleteDraft failed for ${errorStr}`);
        }

        if (ret) {
            await loadingDialog.success("删除成功");

            ABCNavigator.pop();
        } else {
            await loadingDialog.fail(`删除失败${errorStr}`);
        }
    }

    async *_mapEventSend(/*ignored: _EventSend*/): AsyncGenerator<State> {
        const _innerState = this.innerState;
        if (!(await this.editBloc.validate())) return;

        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData!)) return;

        // 判断当前患者关注与绑定微信状态信息
        const wxStatus = _innerState.patientWxPushConfig?.list?.[0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND ?? false;
        const chargeSheetId = await ChargeUtils.pushChargeOrder(_innerState.detailData!, wxStatus);
        if (!_.isEmpty(chargeSheetId)) {
            _innerState.detailData!.id = chargeSheetId;
            ABCNavigator.pop();
        }
    }

    async *_mapEventSaveDraft(/*ignored: _EventSaveDraft*/): AsyncGenerator<State> {
        const dialog = new DialogBuilder();
        dialog.negative = "取消";
        dialog.positive = "确认";
        dialog.content = "挂单后，该单据会共享为所有收费员可见。";
        dialog.title = "确认挂单？";

        const select = await dialog.show();
        if (select != DialogIndex.positive) return;

        //上次算费失败了，再算一次
        const ret = await this._calculatingPriceSync();
        if (!ret) return;

        const loadingDialog = new LoadingDialog("正在保存");
        loadingDialog.show();

        let chargeSheetId;
        let errorStr;
        try {
            chargeSheetId = await ChargeAgent.saveDraft(this.innerState.detailData!);
        } catch (e) {
            errorStr = errorToStr(e);
            LogUtils.e(`safeDraft failed for ${errorStr}`);
        }

        if (!_.isEmpty(chargeSheetId)) {
            this.innerState.detailData!.id = chargeSheetId;
            await loadingDialog.success("挂单成功");

            ABCNavigator.popUntil(URLProtocols.CHARGE_TAB, URLProtocols.CHARGE_TAB).then();
        } else {
            await loadingDialog.fail(`挂单失败:${errorStr}`);
        }
    }

    async *_mapEventSaveChargeInvoice(/*ignored: _EventSaveChargeInvoice*/): AsyncGenerator<State> {
        const _innerState = this.innerState;
        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData!)) return;

        if (!_.isEmpty(_innerState.detailData!.id)) {
            const loadingDialog = new LoadingDialog("正在保存");
            loadingDialog.show();
            try {
                await ChargeAgent.saveChargeInvoice(_innerState.detailData!);
            } catch (e) {
                await loadingDialog.fail(`保存失败：${errorToStr(e)}`);
                return;
            }
            await loadingDialog.success("保存成功");
            ABCNavigator.popUntil(URLProtocols.CHARGE_TAB, URLProtocols.CHARGE_TAB);
            return;
        }
    }

    private async *_mapEventReloadData(/*event: _EventReloadData*/): AsyncGenerator<State> {
        this._loadNetworkDraft().then();
    }

    private async *_mapEventCloseChargeSheet(/*event: _EventCloseChargeSheet*/): AsyncGenerator<State> {
        await ChargeUtils.closeChargeSheetWithUIAsk(this.innerState.detailData!.id!, this.detailData?.isNetworkDraft ?? false);
    }

    async _calculatingPrice(): Promise<ChargeCalculateRspData | undefined> {
        const _innerState = this.initialState();
        if (_.isEmpty(_innerState.detailData?.chargeForms)) {
            //没有收费项时，删除之前的算费结果
            if (_innerState.detailData) _innerState.detailData.chargeSheetSummary = new ChargeSheetSummary();
            return;
        }

        return ChargeUtils.calculatingPrice(_innerState.detailData!);
    }

    async _calculatingPriceSync(): Promise<boolean> {
        if (this._innerState?.calculateFailed == undefined) return true;

        try {
            const rsp = await this._calculatingPrice();
            if (rsp) {
                ChargeUtils.syncChargeInvoiceDetail(this.detailData, rsp);
            }

            return true;
        } catch (e) {
            this.innerState.calculateFailed = e;
            await Toast.show(`计算费用失败：${errorToStr(e)}`, { warning: true });
        }

        return false;
    }

    async *_mapEventHandleChargeAbnormal(event: _EventHandleChargeAbnormal): AsyncGenerator<State> {
        this.innerState.abnormalList = [];
        //如果是社保异常，只做弹窗提示，操作需要到PC端
        if (event.isShebaoAbnormal) {
            ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_fail",
                title: "处理异常",
                content: "请前往电脑客户端，进入『收费』\n" + "找到当前收费单进行处理",
            });
        } else {
            //如果是非社保异常，需要根据收费单id查询异常列表
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(this.innerState.detailData?.id ?? "").catchIgnore();
            if (_.isEmpty(this.innerState.abnormalList)) return;
            showBottomPanel(<ChargeAbnormalDialog bloc={this} />, { topMaskHeight: Sizes.dp160 });
        }
        this.update();
    }

    async *_mapEventAbnormalRefund(event: _EventAbnormalRefund): AsyncGenerator<State> {
        if (!event.abnormalMsg?.chargeSheetId && !event.abnormalMsg?.id) return;
        const refundResult = await ChargeAgent.dealAbnormalRefund(event.abnormalMsg.chargeSheetId!, event.abnormalMsg.id!).catchIgnore();
        if (refundResult?.status == 20) {
            await ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_success",
                title: refundResult?.statusName ?? "退费成功",
                content: `已收费用 (${abcI18Next.t("¥")}${ABCUtils.formatPrice(event.abnormalMsg?.amount ?? 0)})已原路退回至患者${
                    event.abnormalMsg?.payModeDisplayName
                }账户`,
            });
            //退款成功后，再次查询异常列表，如果还有，继续退费
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(refundResult?.chargeSheetId ?? "").catchIgnore();
            this._loadNetworkDraft().then(); // 刷新详情
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeDraft(this.innerState.detailData ?? new ChargeInvoiceDetailData())); // 刷新列表
            if (_.isEmpty(this.innerState.abnormalList)) {
                ABCNavigator.pop();
            }
        } else {
            await ChargeRefundDialog.showQueryPopup({
                logo: "image_dlg_fail",
                title: "退费失败",
                content: refundResult?.statusName ?? "该收费单已有一笔收费正在进行中，请确定是否终止",
            });
            this._loadNetworkDraft().then(); // 刷新详情
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeDraft(this.innerState.detailData ?? new ChargeInvoiceDetailData())); // 刷新列表
            ABCNavigator.pop();
        }
    }

    update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }

    requestUpdatePatient(patient: Patient): void {
        this.dispatch(new _EventPatientUpdate(patient));
    }

    /**
     * 清空患者信息
     */
    requestClearPatient(): void {
        this.dispatch(new _EventClearPatient());
    }

    requestBack(): void {
        this.dispatch(new _EventRequestBack());
    }

    private async _saveLocalDraft() {
        const _innerState = this.innerState;

        if (_innerState.detailData?.isLocalDraft) {
            //新建的草稿且没有输入内容，不用保存
            if (_innerState.newLocalDraft && !_innerState.hasContent) {
                return;
            }

            await DirectChargeDraftManager.instance.saveDraft(_innerState.detailData);
        }

        //如果是网络挂单
        if (_innerState.detailData!.isNetworkDraft && !_.isEmpty(_innerState.detailData!.localDraftId)) {
            await DirectChargeDraftManager.instance.saveDraft(_innerState.detailData!);
        }
    }

    async _removeDraftChargeInvoice(): Promise<void> {
        const detailData = this.innerState.detailData!;
        if (!_.isEmpty(detailData.localDraftId)) {
            await DirectChargeDraftManager.instance.removeDraft(detailData.localDraftId!);
            detailData.localDraftId = undefined;
        }
    }

    private async _loadNetworkDraft() {
        if (!_.isEmpty(this.networkDraftChargeId)) {
            const innerState = this.innerState;
            //挂单
            innerState.loadingDetailData = true;
            innerState.loadingDetailDataError = null;
            this.update();
            try {
                innerState.detailData = await ChargeAgent.getChargeInvoiceDetail(this.networkDraftChargeId!);
                innerState.detailData.fixAirPharmacyPrescriptionInfo();
                this._refreshAirPharmacy();
            } catch (e) {
                innerState.loadingDetailDataError = e;
            } finally {
                innerState.loadingDetailData = false;
            }
            this.editBloc.requestUpdateDetailData(innerState.detailData!);
            this._calculatePriceTrigger.next(0);
        }
    }

    private _refreshAirPharmacy(): void {
        const detailData = this.innerState.detailData;
        if (!detailData) return;

        ChargeUtils.refreshAirPharmacy(detailData?.chargeForms)
            .toObservable()
            .subscribe((update) => {
                if (update.deliveryUpdate || update.priceChanged) {
                    if (detailData?.chargeSheetSummary) {
                        detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
                        this._calculatePriceTrigger.next(0);
                    } else {
                        this.update();
                    }
                }
            })
            .addToDisposableBag(this);
    }

    /**
     * 获取推送相关条件
     */
    private _getPatientWxStatus(isRefreshPatient = true): void {
        const innerState = this.innerState;
        const { patientWxPushConfig } = innerState;
        if (!isRefreshPatient && !!patientWxPushConfig) {
            this.innerState.canPushChargeOrder =
                patientWxPushConfig && !_.isEmpty(patientWxPushConfig.list)
                    ? (patientWxPushConfig?.list![0].isPush == 1 ||
                          (patientWxPushConfig?.list![0].isPush != 1 &&
                              patientWxPushConfig?.list![0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND)) &&
                      !_.isEmpty(this.innerState.detailData?.chargeForms)
                    : false;
            return;
        }
        CrmAgent.postPatientFamilyWxStatus(this.innerState.detailData?.patient?.chainId ?? "", [
            this.innerState.detailData?.patient?.id ?? "",
        ])
            .catch((e) => new ABCError(e))
            .toObservable()
            .subscribe((rsp) => {
                if (!rsp) {
                } else if (rsp instanceof ABCError) {
                } else {
                    this.innerState.patientWxPushConfig = rsp;
                    this.innerState.canPushChargeOrder =
                        rsp && !_.isEmpty(rsp.list)
                            ? (rsp?.list![0].isPush == 1 ||
                                  (rsp?.list![0].isPush != 1 && rsp?.list![0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND)) &&
                              !_.isEmpty(this.innerState.detailData?.chargeForms)
                            : false;
                }
            })
            .addToDisposableBag(this);
    }

    async getShebaoSettlementExceptionList(): Promise<void> {
        // 如果医保结算异常，需要再拉取接口判断异常类型
        if (this.innerState.detailData?.isSheBaoAbnormal && !!this.innerState.detailData?.id) {
            this.innerState.settlementExceptionList = await ChargeAgent.getChargeSettlementExceptionList(
                this.innerState.detailData.id
            ).catchIgnore();
        }
    }

    requestModifyProductEmployee(): void {
        this.dispatch(new _EventModifyProductEmployee());
    }

    requestSingleBargain(): void {
        this.dispatch(new _EventSingleBargainTap());
    }

    requestCharge(): void {
        this.dispatch(new _EventChargeBtnTap());
    }

    requestDeleteDraft(): void {
        this.dispatch(new _EventDeleteDraft());
    }

    requestSend(): void {
        this.dispatch(new _EventSend());
    }

    requestSaveDraft(): void {
        this.dispatch(new _EventSaveDraft());
    }

    requestSaveChargeInvoice(): void {
        this.dispatch(new _EventSaveChargeInvoice());
    }

    public requestReloadData(): void {
        this.dispatch(new _EventReloadData());
    }

    /**
     * 关闭收费单
     */
    public requestCloseChargeSheet(): void {
        this.dispatch(new _EventCloseChargeSheet());
    }

    //根据异常类型，做相应的处理
    requestHandleChargeAbnormal(isShebaoAbnormal: boolean): void {
        this.dispatch(new _EventHandleChargeAbnormal(isShebaoAbnormal));
    }

    //异常退费
    requestAbnormalRefund(abnormalMsg: AbnormalTransactionList): void {
        this.dispatch(new _EventAbnormalRefund(abnormalMsg));
    }

    // 更新咨询师
    requestModifyConsultant(index?: number): void {
        this.dispatch(new _EventModifyConsultant(index));
    }
    // 当前选中的开单人
    requestSelectItemSeller(item?: ClinicDoctorInfo): void {
        this.dispatch(new _EventSelectItemSeller(item));
    }
    //  取消本次支付
    requestCancelPayment(): void {
        this.dispatch(new _EventCancelPayment());
    }

    @actionEvent(_EventModifyProductEmployee)
    private async *_mapEventModifyProductEmployee(): AsyncGenerator<State> {
        const goodsInfoList: GoodsInfo[] = [];
        const doctorList: AbcMap<string, { id?: string; name?: string; departmentId?: string }> = new AbcMap<
                string,
                { id?: string; name?: string; departmentId?: string }
            >(),
            nurseList: AbcMap<string, { id?: string; name?: string }> = new AbcMap<string, { id?: string; name?: string }>();

        this.innerState.treatmentChargeForm.map((item) => {
            goodsInfoList.push(item.goodsInfo);
            nurseList.set(item.goodsInfo.scrollKey, { id: item?.nurseId, name: item?.nurseName });
            doctorList.set(item.goodsInfo.scrollKey, {
                id: item?.doctorId,
                name: item?.doctorName,
                departmentId: item?.departmentId,
            });
        });

        const rsp = await ProductAddExecutorDialog.show({ goodsInfoList: goodsInfoList, doctorList, nurseList });

        if (!!rsp) {
            const currentDoctorList = rsp.doctorList;
            const currentNurseList = rsp.nurseList;

            const setDoctorInfo = (list?: ChargeFormItem[]) => {
                list?.forEach((chargeItem) => {
                    this.innerState.treatmentChargeForm.forEach((item) => {
                        if (chargeItem.goodsInfo.scrollKey != item.goodsInfo.scrollKey) return;
                        if (currentDoctorList.has(item.goodsInfo.scrollKey)) {
                            const currentDoctor = currentDoctorList.get(item.goodsInfo.scrollKey);
                            chargeItem!.doctorId = currentDoctor?.id;
                            chargeItem!.doctorName = currentDoctor?.name;
                            chargeItem!.departmentId = currentDoctor?.departmentId;
                            chargeItem!.departmentName = currentDoctor?.departmentName;
                        }
                        if (currentNurseList.has(item.goodsInfo.scrollKey)) {
                            const currentNurse = currentNurseList.get(item.goodsInfo.scrollKey);
                            chargeItem!.nurseId = currentNurse?.id;
                            chargeItem!.nurseName = currentNurse?.name;
                        }
                    });
                });
            };
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.examination)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.package)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.westernPrescription)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.infusionPrescription)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.chinesePrescription)?.chargeFormItems);

            this.update();
        }
    }

    @actionEvent(_EventModifyConsultant)
    async *_mapEventModifyConsultant(event: _EventModifyConsultant): AsyncGenerator<State> {
        const index = event.index;
        if (isNil(index)) return;
        this.innerState.detailData = this.innerState.detailData ?? new ChargeInvoiceDetailData();
        this.innerState.detailData.consultantId = this.innerState.consultantList?.[index]?.employeeId;
        this.innerState.detailData.consultantName = this.innerState.consultantList?.[index]?.employeeName;
        this.update();
    }
    @actionEvent(_EventSelectItemSeller)
    async *_mapEventSelectItemSeller(event: _EventSelectItemSeller): AsyncGenerator<State> {
        const data = event?.item,
            innerState = this.innerState;
        if (!data?.doctorId) {
            innerState.detailData!.sellerId = undefined;
            innerState.detailData!.sellerName = undefined;
            innerState.detailData!.sellerDepartmentId = undefined;
            innerState.detailData!.sellerDepartmentName = undefined;
        } else {
            innerState.detailData!.sellerId = data.doctorId;
            innerState.detailData!.sellerName = data.doctorName;

            innerState.detailData!.sellerDepartmentId = data.departmentId;
            innerState.detailData!.sellerDepartmentName = data.departmentName;
            innerState.showSellerRequiredHint = false;
        }
        this.update();
    }
    _collectMatchData(searchText?: string): void {
        const keyword = (searchText ?? "").toLowerCase();
        if (keyword.length === 0) {
            this.innerState.matchClinicDoctorInfo = this.innerState.allClinicDoctorInfo;
        }

        this.innerState.matchClinicDoctorInfo = this.innerState.allClinicDoctorInfo
            .filter(
                (item) =>
                    item.doctorName!.toLowerCase().indexOf(keyword) >= 0 ||
                    item.doctorNamePyFirst!.toLowerCase().indexOf(keyword) >= 0 ||
                    item.doctorNamePy!.toLowerCase().indexOf(keyword) >= 0
            )
            .sort((item) => (item.doctorId == userCenter.employee?.id ? -1 : 1)); // 当前医生置顶显示
        this.innerState.matchClinicDoctorInfo.splice(
            0,
            0,
            JsonMapper.deserialize(ClinicDoctorInfo, {
                doctorName: "不指定",
                departmentName: "",
                doctorNamePyFirst: "",
                doctorNamePy: "",
                doctorId: "",
                departmentId: "",
            })
        );
    }

    @actionEvent(_EventCancelPayment)
    async *_mapEventCancelPayment(): AsyncGenerator<State> {
        // 进行解锁
        const chargePayTransactionId = this.innerState.detailData?.lockPayTransactionInfo?.id;
        if (!chargePayTransactionId) return;
        const tipsInfo = {
            confirmText: "",
            errorTitle: "",
            errorContent: "",
        };
        if (this.innerState.detailData?.microclinicsOrSelfServiceMachines) {
            tipsInfo.confirmText = "您可取消本次支付，取消后患者自助支付将会失败，本单可以重新收费。";
            tipsInfo.errorTitle = "收费单内容已更新";
            tipsInfo.errorContent = "收费单内容已更新，请刷新后重试";
        } else {
            tipsInfo.confirmText = "您可取消本次支付，取消后可重新收费，本次支付不入账。";
            tipsInfo.errorTitle = "取消失败";
            tipsInfo.errorContent = "患者已完成支付，如需取消请操作退费";
        }
        const dialogIndex = await showQueryDialog(
            "支付遇到问题？",
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{tipsInfo?.confirmText}</Text>
            </View>,
            "取消本次支付",
            "暂不操作"
        );
        if (!dialogIndex) return;
        await ChargeAgent.putChargeUnlock(chargePayTransactionId)
            .then((rsp) => {
                if (rsp.code == 200) {
                    Toast.show("取消成功", { success: true });
                }
            })
            .catch((error) => {
                showConfirmDialog(tipsInfo?.errorTitle, `${error?.msg ?? tipsInfo?.errorContent}`, "知道了");
            })
            .finally(() => {
                this._loadNetworkDraft().then();
            });
    }
}

export { DirectChargeInvoicePageBloc, State };
