/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { ChargeForm, ChargeFormItem, ChargeInvoiceDetailData, MedicineProcessType, ProcessInfo, ProcessRule } from "./data/charge-beans";
import { ChargeAgent } from "./data/charge-agent";
import _ from "lodash";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import { ChargeUtils } from "./utils/charge-utils";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { errorSummary, TimeUtils, UUIDGen } from "../common-base-module/utils";
import { ValidatorUtils } from "../base-ui/utils/validator-utils";
import { LogUtils } from "../common-base-module/log";
import { ignore } from "../common-base-module/global";
import { ChineseMedicineSpecType, GoodsType, UsageInfo } from "../base-business/data/beans";
import { Toast } from "../base-ui/dialog/toast";
import { ChineseMedicineUsageInfo, ChineseUsageItemInfo } from "../outpatient/data/chinese-medicine-config";
import ChineseMedicine from "../../assets/medicine_usage/chinese-medicine-config";
import { MedicineScopeId } from "./data/charge-bean-air-pharmacy";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { userCenter } from "../user-center";
import { AbcCalendar } from "../base-ui/abc-app-library/calendar/calendar-static";
import { storeLastWithdrawalTime } from "../data/memory-operation";

export const LAST_PROCESS_BAGS = "LAST_PROCESS_BAGS"; // 加工费默认上次袋数标识

class State {
    chargeData!: ChargeInvoiceDetailData;
    processRules?: ProcessRule[];
    //goods配置相关信息--此处关注用法导致库房变更
    pharmacyInfoConfig?: InventoryClinicConfig;

    initting = false;
    initError: any;
    calculating = false;
    calculateError: any;
    mutable = true; //是否可以修改

    processBagUnitCountHint = false; //1剂煎药袋数为空的提示
    totalProcessCountHint = false; //共煎药袋数为空的提示

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    chargeData: ChargeInvoiceDetailData;
    mutable: boolean;

    constructor(chargeData: ChargeInvoiceDetailData, mutable: boolean) {
        super();
        this.chargeData = chargeData;
        this.mutable = mutable;
    }
}

class _EventUpdate extends _Event {}

class _EventUpdateProcessType extends _Event {
    chargeForm?: ChargeForm;
    type?: number;

    constructor(chargeForm: ChargeForm | undefined, type: number) {
        super();
        this.chargeForm = chargeForm;
        this.type = type;
    }
}

class _EventUpdateProcessSubType extends _Event {
    chargeForm?: ChargeForm;
    subType: number;

    constructor(chargeForm: ChargeForm | undefined, subType: number) {
        super();
        this.chargeForm = chargeForm;
        this.subType = subType;
    }
}

class _EventUpdateBagUnitCount extends _Event {
    chargeForm?: ChargeForm;
    count: number;

    constructor(chargeForm: ChargeForm | undefined, count: number) {
        super();

        this.chargeForm = chargeForm;
        this.count = count;
    }
}

class _EventUpdateTotalProcessCount extends _Event {
    chargeForm?: ChargeForm;
    count: number;

    constructor(chargeForm: ChargeForm | undefined, count: number) {
        super();

        this.chargeForm = chargeForm;
        this.count = count;
    }
}

class _EventUpdateMobile extends _Event {
    mobile: string;

    constructor(mobile: string) {
        super();
        this.mobile = mobile;
    }
}

class _EventUpdateComment extends _Event {
    chargeForm: ChargeForm | undefined;
    remark?: string;
    constructor(chargeForm: ChargeForm | undefined, remark?: string) {
        super();
        this.chargeForm = chargeForm;
        this.remark = remark;
    }
}

class _EventSave extends _Event {}

class _EventUpdateChargeFormCheckStatus extends _Event {
    chargeForm?: ChargeForm;
    check: boolean;

    constructor(chargeForm?: ChargeForm, check = false) {
        super();
        this.chargeForm = chargeForm;
        this.check = check;
    }
}
class _EventModifyDrugWithdrawalDate extends _Event {
    chargeForm?: ChargeForm;
    date?: Date;
    constructor(chargeForm?: ChargeForm, date?: Date) {
        super();
        this.chargeForm = chargeForm;
        this.date = date;
    }
}
class _EventModifyDrugWithdrawalTime extends _Event {
    chargeForm?: ChargeForm;
    hour?: string;
    minutes?: string;
    constructor(chargeForm?: ChargeForm, hour?: string, minutes?: string) {
        super();
        this.chargeForm = chargeForm;
        this.hour = hour;
        this.minutes = minutes;
    }
}
class MedicineProcessCostPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<MedicineProcessCostPageBloc | undefined>(undefined);

    static fromContext(context: MedicineProcessCostPageBloc): MedicineProcessCostPageBloc {
        return context;
    }

    constructor(chargeData: ChargeInvoiceDetailData, mutable: boolean) {
        super();

        this.dispatch(new _EventInit(chargeData, mutable));
    }

    private _innerState!: State;

    private _calculateTrigger?: Subject<number>;
    private _bagUnitCount?: number;
    private _bagTotalCount?: number;

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateProcessType, this._mapEventUpdateProcessType);
        map.set(_EventUpdateProcessSubType, this._mapEventUpdateProcessSubType);
        map.set(_EventUpdateBagUnitCount, this._mapEventUpdateBagUnitCount);
        map.set(_EventUpdateMobile, this._mapEventUpdateMobile);
        map.set(_EventSave, this._mapEventSave);
        map.set(_EventUpdateChargeFormCheckStatus, this._mapUpdateChargeFormCheckStatus);

        return map;
    }

    async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        const chargeData = JsonMapper.deserialize(ChargeInvoiceDetailData, event.chargeData)!;
        this.innerState.chargeData = chargeData;
        this.innerState.mutable = event.mutable;
        const chineseMedicine = chargeData.chargeForms?.find((form) => form?.isChinesePrescription);
        if (chineseMedicine) {
            this._bagUnitCount = (await this.computedTisanesSack(chineseMedicine))?.processBagUnitCount;
            this._bagTotalCount = (await this.computedTisanesSack(chineseMedicine))?.totalProcessCount;
            this.update();
        }
        this.innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore();
        this._getProcessRules();
        this.innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore();
        if (event.mutable) {
            this._calculateTrigger = new Subject<number>();
            this._calculateTrigger
                .pipe(
                    switchMap(() => {
                        this.innerState.calculating = true;
                        this.innerState.calculateError = undefined;
                        this.update();
                        chargeData.chargeForms?.forEach((form) => {
                            if (form.processInfo && !form.processInfo.keyId) form.processInfo.keyId = UUIDGen.generate();
                        });
                        return ChargeAgent.calculateProcess(chargeData)
                            .catch((error) => new ABCError(error))
                            .toObservable();
                    })
                )
                .subscribe((rsp) => {
                    this.innerState.calculating = false;
                    if (rsp instanceof ABCError) {
                        this.innerState.calculateError = rsp.detailError;
                    } else {
                        ChargeUtils.syncChargeDataWithProcessCalculate(chargeData, rsp, this.innerState.mutable);
                    }
                    this.update();
                })
                .addToDisposableBag(this);
        }
        this._initProcessInfoTakeTime();
        yield this.innerState.clone();
    }

    /**
     * 初始话取药时间是否可操作
     */
    _initProcessInfoTakeTime(): void {
        this.innerState.chargeData.chargeForms?.forEach((form) => {
            if (form.isDecoction) {
                form.processInfo = form.processInfo ?? new ProcessInfo();
                form.processInfo.isNeedAssignmentTime = true;
            }
        });
    }

    async computedTisanesSack(form: ChargeForm): Promise<{ processBagUnitCount?: number; totalProcessCount?: number }> {
        const usageInfo = form?.usageInfo;
        const pharmacyType = form.pharmacyType;
        const freqName = usageInfo?.freq,
            dailyName = usageInfo?.dailyDosage,
            usageLevelName = usageInfo?.usageLevel,
            medicineStateScopeId = form.medicineStateScopeId,
            usageScopeId = form.usageScopeId,
            count = form.doseCount__ ?? _.first(form?.chargeFormItems)?.doseCount;
        let usageLevelList: ChineseUsageItemInfo[] = [];
        const z =
            !!freqName && ChineseMedicine.freq.map((t) => t.name).includes(freqName) && freqName.indexOf("1日") > -1
                ? Number(freqName.substr(2, 1))
                : 0;
        const selDailyDosage = ChineseMedicine.dailyDosage.find((t) => t.name == dailyName);
        const x = !!selDailyDosage ? selDailyDosage?.daysCount : 0;
        const y = !!selDailyDosage ? selDailyDosage?.dosageCount : 0;
        //本地药房饮片--加工通过process
        //空中药房饮片（代煎）--加工通过chinesePrescriptionUsage用法

        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        let w = 0;
        if (value && value.usageLevel) {
            usageLevelList = value.usageLevel;
            w = usageLevelList?.find((t) => t.name == usageLevelName)?.value ?? 0;
        }
        const keliSingleBags = ((z * x) / y) * w;
        const processInfo = new ProcessInfo();
        form.usageInfo = JsonMapper.deserialize(UsageInfo, form?.usageInfo);
        const specification = form.chargeFormItems?.[0]?.goodsInfo.cMSpec == ChineseMedicineSpecType.fullNames()[1] ? 2 : 1;

        const result = await ChargeAgent.processBagCountCalculate({
            dailyDosage: dailyName,
            doseCount: count,
            freq: freqName,
            pharmacyType: pharmacyType,
            type: specification,
            usageLevel: usageLevelName,
        }).catchIgnore();
        if (!result) return {};

        if (!pharmacyType) {
            processInfo.processBagUnitCount = result?.bagUnitCount;
            processInfo.totalProcessCount = result?.bagTotalCount;
        } else {
            if (medicineStateScopeId == MedicineScopeId.daiJian) {
                form.usageInfo!.processBagUnitCountDecimal = result?.bagUnitCount;
                form.usageInfo!.totalProcessCount = result?.bagTotalCount;
            }
            if (medicineStateScopeId == MedicineScopeId.keLi) {
                //袋数只能为1，2，3，4，6；如果计算为5，向下取整成4，超出6，则填6
                form.usageInfo!.processBagUnitCountDecimal = !!y
                    ? Math.ceil(keliSingleBags) > 4 && Math.ceil(keliSingleBags) < 6
                        ? 4
                        : Math.ceil(keliSingleBags) > 6
                        ? 6
                        : Math.ceil(keliSingleBags)
                    : undefined;
            }
        }

        return {
            processBagUnitCount: processInfo?.processBagUnitCount,
            totalProcessCount: processInfo?.totalProcessCount,
        };
    }

    //拉取加工规则
    private _getProcessRules() {
        const chargeData = this.innerState.chargeData;
        this.innerState.initting = true;
        ChargeAgent.getProcessRules()
            .catch((error) => new ABCError(error))
            .toObservable()
            .subscribe((rules) => {
                this.innerState.initting = false;
                if (rules instanceof ABCError) {
                    this.innerState.initError = rules.detailError;
                } else {
                    this.innerState.processRules = rules;
                    const firstRule = _.first(rules);
                    LogUtils.d("_getProcessRules = firstRule = " + firstRule);
                    if (firstRule) {
                        const type = firstRule.type;
                        const subType = _.first(firstRule.children)?.subType;
                        const chineseForms = chargeData.chargeForms?.filter((form) => form.isChinesePrescription);
                        if (chineseForms?.length) {
                            chineseForms.forEach((form) => {
                                const info = chargeData.getProcessInfo(form.id);
                                ignore(info);
                            });
                        } else {
                            //对于没有中药处方的单子，触发创建一个空的加工费
                            const info = chargeData.getProcessInfo(undefined);
                            ignore(info);
                        }

                        chargeData.chargeForms?.forEach((form) => {
                            if (form.isDecoction) {
                                let processInfo = form.processInfo;
                                if (processInfo === undefined || processInfo.type === undefined) {
                                    form.processInfo = processInfo = {
                                        chargeFormId: form.chargeSheetId,
                                        type: type,
                                        subType: subType,
                                        processBagUnitCount: this._bagUnitCount,
                                        // type == MedicineProcessType.decoction ? sharedPreferences.getInt(LAST_PROCESS_BAGS) : undefined, //默认3袋
                                        checked: true,
                                        totalProcessCount: this._bagTotalCount,
                                        processRemark: undefined,
                                    };
                                }

                                processInfo.keyId = processInfo.keyId ?? UUIDGen.generate();

                                if (processInfo.type == 0 && processInfo.subType == 0) {
                                    processInfo.type = type;
                                    processInfo.subType = subType;
                                }
                                LogUtils.d("_getProcessRules222 = firstRule = " + JSON.stringify(processInfo));
                            }
                        });

                        this._calculateTrigger?.next(0);
                    }
                }

                this.update();
            });
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    private async *_mapEventUpdateProcessType(event: _EventUpdateProcessType): AsyncGenerator<State> {
        const chargeData = this.innerState.chargeData;
        const processInfo = chargeData.getProcessInfo(event.chargeForm?.id)!;
        //只修改type,但目标值与当前值相同,返回
        if (processInfo.type === event.type) return;

        const rule = this.innerState.processRules?.find((item) => item.type === event.type);
        processInfo.type = event.type;
        processInfo.subType = _.first(rule?.children)?.subType;
        ChargeUtils.updateChargeChineseFormPharmacyWithProcess(chargeData, this.innerState.pharmacyInfoConfig);
        this._clearDecoctionAdjustmentFee();
        this._calculateTrigger?.next(0);
        yield this.innerState.clone();
    }

    private async *_mapEventUpdateProcessSubType(event: _EventUpdateProcessSubType): AsyncGenerator<State> {
        const chargeData = this.innerState.chargeData;
        const processInfo: ProcessInfo = chargeData.getProcessInfo(event.chargeForm?.id)!;
        //只修改type,但目标值与当前值相同,返回
        if (processInfo.subType == event.subType) return;
        processInfo.subType = event.subType;
        ChargeUtils.updateChargeChineseFormPharmacyWithProcess(chargeData, this.innerState.pharmacyInfoConfig);
        this._clearDecoctionAdjustmentFee();
        this._calculateTrigger?.next(0);
    }

    private async *_mapEventUpdateBagUnitCount(event: _EventUpdateBagUnitCount): AsyncGenerator<State> {
        this.innerState.processBagUnitCountHint = false;
        const chargeData = this.innerState.chargeData;
        const processInfo = chargeData.getProcessInfo(event.chargeForm?.id);
        processInfo!.processBagUnitCount = event.count;
        const count = event.chargeForm?.doseCount__ ?? _.first(event?.chargeForm?.chargeFormItems)?.doseCount;
        if (!!count && !!event?.count) processInfo!.totalProcessCount = !!count && event.count ? Math.ceil(count * event.count) : undefined;
        this._clearDecoctionAdjustmentFee();
        yield this.innerState.clone();
        if (!!event?.count) this._calculateTrigger?.next(0);
    }

    @actionEvent(_EventUpdateTotalProcessCount)
    async *_mapEventUpdateTotalProcessCount(event: _EventUpdateTotalProcessCount): AsyncGenerator<State> {
        this.innerState.totalProcessCountHint = false;
        const chargeData = this.innerState.chargeData;
        const processInfo = chargeData.getProcessInfo(event.chargeForm?.id);
        processInfo!.totalProcessCount = event.count;
        const count = event.chargeForm?.doseCount__ ?? _.first(event?.chargeForm?.chargeFormItems)?.doseCount,
            totalProcessCount = event.count;
        if (!!count && !!event?.count)
            processInfo!.processBagUnitCount = !!count
                ? /[\.]/.test((totalProcessCount / count).toString())
                    ? Number((totalProcessCount / count).toFixed(1))
                    : totalProcessCount / count
                : undefined;
        this._clearDecoctionAdjustmentFee();
        yield this.innerState.clone();
        if (!!event?.count) this._calculateTrigger?.next(0);
    }

    @actionEvent(_EventUpdateComment)
    async *_mapEventUpdateComment(event: _EventUpdateComment): AsyncGenerator<State> {
        const chargeData = this.innerState.chargeData;
        const processInfo = chargeData.getProcessInfo(event.chargeForm?.id);
        processInfo!.processRemark = event?.remark;
        this.update();
    }

    private async *_mapEventUpdateMobile(event: _EventUpdateMobile): AsyncGenerator<State> {
        this.innerState.chargeData.contactMobile = event.mobile;
    }

    private async *_mapEventSave(/*ignored: _EventSave*/): AsyncGenerator<State> {
        if (this.innerState.calculateError) {
            Toast.show(`算费错误：${errorSummary(this.innerState.calculateError)}`).then();
            this._calculateTrigger?.next(0);
            return;
        }

        if (!ValidatorUtils.validatePhoneNumber(this.innerState.chargeData.contactMobile, true)) {
            return;
        }

        _.remove(this.innerState.chargeData.chargeForms ?? [], (form) => form.isDecoction && !(form.processInfo!.checked ?? true));
        for (const form of this.innerState.chargeData.chargeForms ?? []) {
            if (form.isDecoction) {
                // 1剂X袋，默认3袋，如果修改，记住每个用户上次填写的数字(加工费默认上次的袋数【九味堂】)
                // sharedPreferences.setInt(LAST_PROCESS_BAGS, form.processInfo?.processBagUnitCount ?? ChargeConsts.processBagUnitCount);
                const formItem = form.getFormItem(GoodsType.decoctionFee);
                if (formItem && form.processInfo) {
                    //诊所必须有加工方式而且加工类型为煎药类型才可以对加工袋数进行校验
                    if (
                        !!this.innerState.processRules?.length &&
                        form.processInfo?.type == MedicineProcessType.decoction &&
                        (!form.processInfo?.processBagUnitCount || !form.processInfo?.totalProcessCount)
                    ) {
                        if (!form.processInfo?.processBagUnitCount) this.innerState.processBagUnitCountHint = true;
                        if (!form.processInfo?.totalProcessCount) this.innerState.totalProcessCountHint = true;
                        Toast.show("不能为空").then();
                        yield this.innerState.clone();
                        return;
                    }
                    formItem.unitCount = 1;
                    formItem.unitPrice = form.processInfo.processFee ?? 0;
                    // 记忆上一次取药时间
                    if (!!form.processInfo?.takeMedicationTime) {
                        storeLastWithdrawalTime(form.processInfo?.takeMedicationTime);
                    }
                }
            }
        }

        ABCNavigator.pop(this.innerState.chargeData);
    }

    private async *_mapUpdateChargeFormCheckStatus(event: _EventUpdateChargeFormCheckStatus): AsyncGenerator<State> {
        const chargeData = this.innerState.chargeData;
        const info = chargeData.getProcessInfo(event.chargeForm?.id);
        info!.checked = event.check;
        this._clearDecoctionAdjustmentFee();
        this._calculateTrigger?.next(0);
        yield this.innerState.clone();
    }
    @actionEvent(_EventModifyDrugWithdrawalDate)
    private async *_mapEventModifyDrugWithdrawalDate(event: _EventModifyDrugWithdrawalDate): AsyncGenerator<State> {
        const selectDate = await AbcCalendar.show({
            date: TimeUtils.formatTimeForDateParse(event?.date),
            minDate: new Date(),
            maxDate: undefined,
        });
        if (!selectDate) return;
        const chargeData = this.innerState.chargeData;
        const info = chargeData.getProcessInfo(event.chargeForm?.id) ?? new ProcessInfo();
        if (!info?.takeMedicationTime) {
            info!.takeMedicationTime = selectDate;
            info.isNeedAssignmentTime = false;
        } else {
            const takeMedicationTime = TimeUtils.formatTimeForDateParse(info!.takeMedicationTime);
            takeMedicationTime?.setFullYear(selectDate.getFullYear());
            takeMedicationTime?.setMonth(selectDate.getMonth());
            takeMedicationTime?.setDate(selectDate.getDate());
            info.takeMedicationTime = takeMedicationTime;
        }
        this.update();
    }
    @actionEvent(_EventModifyDrugWithdrawalTime)
    private async *_mapEventModifyDrugWithdrawalTime(event: _EventModifyDrugWithdrawalTime): AsyncGenerator<State> {
        const chargeData = this.innerState.chargeData;
        const info = chargeData.getProcessInfo(event.chargeForm?.id) ?? new ProcessInfo();
        if (!info?.takeMedicationTime) {
            info!.takeMedicationTime = new Date();
            info!.takeMedicationTime?.setHours(Number(event.hour ?? "00"));
            info!.takeMedicationTime?.setMinutes(Number(event.minutes ?? "00"));
        } else {
            const takeMedicationTime = TimeUtils.formatTimeForDateParse(info!.takeMedicationTime);
            takeMedicationTime?.setHours(Number(event.hour ?? "00"));
            takeMedicationTime?.setMinutes(Number(event.minutes ?? "00"));
            info.takeMedicationTime = takeMedicationTime;
        }
        info.isNeedAssignmentTime = true;

        this.update();
    }

    //清除加工费，解决配置不同的加工时，费用不会更新的问题
    private _clearDecoctionAdjustmentFee(chargeForm?: ChargeForm) {
        let formItem: ChargeFormItem | undefined = undefined;
        if (chargeForm) {
            formItem = this.innerState.chargeData.chargeForms
                ?.find((form) => form.processInfo?.chargeFormId === chargeForm.id)
                ?.getFormItem(GoodsType.decoctionFee);
        }

        if (formItem) {
            formItem.expectedTotalPrice = undefined;
        }
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    /**
     * 更新处方的加工类型
     * @param chargeForm 对于直接收费，未添加中药处方情况下，chargeForm可以为空
     * @param type
     */
    public requestUpdateProcessType(chargeForm: ChargeForm | undefined, type: number): void {
        this.dispatch(new _EventUpdateProcessType(chargeForm, type));
    }

    /**
     * 更新处方的加工子类型
     * @param chargeForm 对于直接收费，未添加中药处方情况下，chargeForm可以为空
     * @param subType
     */
    public requestUpdateProcessSubType(chargeForm: ChargeForm | undefined, subType: number): void {
        this.dispatch(new _EventUpdateProcessSubType(chargeForm, subType));
    }

    //更新每剂袋数
    public requestUpdateBagUnitCount(chargeForm: ChargeForm | undefined, count: number): void {
        this.dispatch(new _EventUpdateBagUnitCount(chargeForm, count));
    }

    //更新共煎袋数
    requestUpdateTotalProcessCount(chargeForm: ChargeForm | undefined, count: number): void {
        this.dispatch(new _EventUpdateTotalProcessCount(chargeForm, count));
    }

    //更新联系电话
    public requestUpdateMobile(mobile: string): void {
        this.dispatch(new _EventUpdateMobile(mobile));
    }

    //点击保存
    public requestSave(): void {
        this.dispatch(new _EventSave());
    }

    /**
     * 更新处方的加工与否选项
     * @param chargeForm 对于直接收费，可能未还没有添加中药处方时，chargeForm为空
     * @param check
     */
    public requestUpdateChargeFormProcessCheckStatus(chargeForm?: ChargeForm, check = false): void {
        this.dispatch(new _EventUpdateChargeFormCheckStatus(chargeForm, check));
    }

    //更新加工备注信息
    requestUpdateComment(chargeForm: ChargeForm | undefined, remark?: string): void {
        this.dispatch(new _EventUpdateComment(chargeForm, remark));
    }

    /**
     * 取药日期
     */
    requestModifyDrugWithdrawalDate(chargeForm?: ChargeForm, date?: Date): void {
        this.dispatch(new _EventModifyDrugWithdrawalDate(chargeForm, date));
    }
    /**
     * 取药时间
     */
    requestModifyDrugWithdrawalTime(chargeForm?: ChargeForm, hour?: string, minutes?: string): void {
        this.dispatch(new _EventModifyDrugWithdrawalTime(chargeForm, hour, minutes));
    }
}

export { MedicineProcessCostPageBloc, State };
