/**
 * create by dengjie
 * desc:
 * create date 2020/5/6
 */
import PathUtils from "../base-business/file/path-utils";
import { LogUtils } from "../common-base-module/log";
import FileUtils from "../common-base-module/file/file-utils";

export class ChargeConsts {
    static async getDraftDir(): Promise<string | null> {
        const dir = await PathUtils.getChargeInvoiceDir();
        const ret = await FileUtils.createDir(`${dir}/draft`);

        if (!ret) {
            LogUtils.d("failed to create outpatient draft dir");
            return null;
        }

        return dir;
    }

    static async getDraftFile(draftId: string): Promise<string> {
        const draftDir = await this.getDraftDir();

        return `${draftDir}/${draftId}.dat`;
    }

    static defaultSex = "男";

    //蕉药加工，默认3袋
    static processBagUnitCount = 3;
}
