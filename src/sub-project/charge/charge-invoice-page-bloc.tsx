/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { BehaviorSubject, of, Subject } from "rxjs";
import { userCenter } from "../user-center";
import {
    AirPharmacyMedicineState,
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeFormStatus,
    ChargeInvoiceData,
    ChargeInvoiceDetailData,
    ChargeInvoiceType,
    ChargeOweSheets,
    ChargePayData,
    ChargeRefundData,
    ChargeRegistrationInfo,
    ChargeSettlementExceptionItem,
    ChargeSourceFormType,
    ChargeStatus,
    ChineseAirPharmacyBagsParam,
    CouponPromotion,
    PatientCardPromotion,
    PatientPointsInfo,
    PayMethod,
    ProcessInfo,
    ServiceDeductCollection,
} from "./data/charge-beans";
import { AbcMap } from "../base-ui/utils/abc-map";
import { ClinicConfig } from "../user-center/user-center";
import {
    ClinicDispensingConfig,
    DataPermission,
    GetClinicBasicSetting,
    OnlinePropertyConfigProvider,
} from "../data/online-property-config-provder";
import { debounceTime, switchMap } from "rxjs/operators";
import {
    AbnormalTransactionList,
    ChargeAgent,
    ChargeCalculateRspData,
    ChargeEventPaid,
    ChargeEventPaidBack,
    ChargeEventRefund,
    ChargeEventRenewpaid,
    ChargeEventRepaid,
    ChargeEventSafeDraft,
    ChargeOweSinglePaid,
} from "./data/charge-agent";
import { ChargeUtils } from "./utils/charge-utils";
import { ABCError } from "../common-base-module/common-error";
import {
    ChineseMedicineSpecType,
    DeliveryInfo,
    FormItemBatchInfos,
    GoodsInfo,
    GoodsType,
    GoodsTypeId,
    MedicalRecord,
    Patient,
    UsageInfo,
    WxBindStatus,
} from "../base-business/data/beans";
import _, { isNil } from "lodash";
import { keyboardListener } from "../common-base-module/utils/keyboard-listener";
import { LogUtils } from "../common-base-module/log";
import { Department, Doctor } from "../registration/data/registration";
import { Pair } from "../base-ui/utils/value-holder";
import { SelectMemberInfo } from "./view/promotion-card-view";
import { Toast } from "../base-ui/dialog/toast";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ChargePayPage } from "./charge-pay-page";
import { DirectChargeInvoicePage } from "./direct-charge-invoice-page";
import { DirectChargeMedicineAddInfo, DirectChargeMedicineInvokeParams } from "./direct-charge-medicine-add-page";
import { SingleBargainDialog } from "./single-bargain-dialog";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { errorToStr } from "../common-base-module/utils";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { ChargeRefundPayPage } from "./charge-refund-pay-page";
import { AirPharmacyPrescriptionFormViewKeyIds } from "./view/air-pharmacy-prescription-form";
import { ChargeInvoiceDetailDataChangedState, ChargeInvoiceEditViewBloc } from "./charge-invoice-edit-view-bloc";
import { ChargeItemModifyUtils } from "./utils/charge-item-modify-utils";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { StockDisableCheckDialog } from "./view/stock-disable-check-dialog";
import { GoodsUtils } from "../base-business/utils/utils";
import { ChargeRefundDialog } from "./view/charge-refund-dialog";
import { ChargeAbnormalDialog } from "./view/charge-abnormal-dialog";
import { ABCUtils } from "../base-ui/utils/utils";
import { ChargeInvoiceRefundNewDialog } from "./charge-invoice-refund-dialog-new";
import { CrmAgent } from "../patients/data/crm-agent";
import { PatientFamilyWxStatus } from "../patients/data/crm-bean";
import { HospitalRegisterDetail, HospitalRegisterDetailStatus } from "../base-business/data/hospital-register/bean";
import { ChargeRepaymentDialog } from "./view/charge-repayment-dialog";
import { URLProtocols } from "../url-dispatcher";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { MedicalState, MedicineScopeId, PharmacyType } from "./data/charge-bean-air-pharmacy";
import { ChinesePrescriptionUsage } from "../outpatient/data/medicine-add-bean";
import { ChineseMedicineUsageInfo, ChineseUsageItemInfo } from "../outpatient/data/chinese-medicine-config";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { ChineseAirPharmacyBagsDialog } from "../outpatient/medicine-add-page/views/chinese-air-pharmacy-bags-dialog";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { AbcDialog } from "../base-ui/abc-app-library";
import ChineseMedicine from "../../assets/medicine_usage/chinese-medicine-config";
import { ProductAddExecutorDialog } from "../outpatient/product-add-page/views/product-add-executor-dialog";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { MedicineUsagePicker } from "../base-ui/picker/medicine-usage-picker";
import { OutpatientAgent } from "../outpatient/data/outpatient";
import { DiscountCardDialog } from "../views/discount-card-views/discount-card-dialog";
import { ChargeAddMedicinePage } from "./view/charge-add-medicine-page";
import { MedicineSearchHintView } from "./medicine-search-hint-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { AirPharmacyStepsView } from "./view/air-pharmacy-steps-view";
import { StepStatusListItem } from "../base-ui/abc-steps/abc-steps";
import abcI18Next from "../language/config";
import { ClinicAgent, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { ConsultantList, ModuleRolesId } from "../base-business/data/clinic-data";
import { StringUtils } from "../base-ui/utils/string-utils";
import { MedicineBatchInfoList } from "../outpatient/data/outpatient-beans";
import { MedicineBatchInfoDialog } from "../outpatient/medicine-add-page/views/medicine-batch-info-dialog";
import { ShebaoAgent } from "../base-business/mix-agent/shebao-agent";
import { InvoiceShebaoAgent } from "./data/invoice-shebao-agent";
import { showStateLogoConfirmDialog } from "./view/state-logo-dialog-builder";
import { AbcWithStateLogTitle } from "../base-ui/abc-with-state-log-title/abc-with-state-log-title";
import { View, Text } from "@hippy/react";

class State {
    detailData!: ChargeInvoiceDetailData;
    loadingDetailError: any;
    loadingDetailData = false;

    calculatingCharge = false; // 正在计算费用
    calculateChargeError: any; // 计算费用失败

    clinicConfig?: ClinicConfig;
    chargeConfig?: ChargeConfig;
    canPushChargeOrder = false;

    doctorMutable = false;
    departmentMutable = false;

    focusMedicineItem?: ChargeFormItem; //编辑过程中当前选中项

    hasChanged = false;
    showErrorHint = false;

    focusErrorViewKey?: string;
    patientWxPushConfig?: PatientFamilyWxStatus; //当前患者是否可以微信推送等配置

    abnormalList?: AbnormalTransactionList[]; //非社保异常列表

    hospitalDetail?: HospitalRegisterDetail; //长护详情

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;

    dispensingConfig?: ClinicDispensingConfig;
    //当前是否开启代加工
    get isOpenProcess(): boolean {
        return !!this.dispensingConfig?.isDecoction;
    }
    get isOpenTakeMedicine(): boolean {
        return !!this.dispensingConfig?.isOpenTakeMedicationTime;
    }

    diagnoseCount?: number; //就诊历史

    employeesMeConfig?: EmployeesMeConfig;

    consultantList?: ConsultantList[]; //咨询师

    dataPermission?: DataPermission; // 数据权限

    clinicBasicSetting?: GetClinicBasicSetting;

    isOpenElecSetlCertUpload?: boolean; // 是否开启医保电子结算凭证
    isEnabledRefund?: boolean; // 是否可以退费(0--否，1--是)

    settlementExceptionList?: ChargeSettlementExceptionItem[]; // 结算异常列表

    // 医保退费异常
    get shebaoRefundException(): boolean {
        const abnormalList = this.settlementExceptionList?.filter((t) => t.payMode == PayMethod.payHealthCard);
        //   优先展示退费异常
        return !!abnormalList?.some((t) => t.shebaoRefundAbnormal);
    }

    get selectedChargeItems(): AbcMap<ChargeForm, ChargeFormItem[]> {
        const map = new AbcMap<ChargeForm, ChargeFormItem[]>();
        const chargeData = this.detailData;
        chargeData.chargeForms?.forEach((form) => {
            const formItems: ChargeFormItem[] = [];
            form.chargeFormItems?.forEach((formItem) => {
                if (formItem.checked ?? true) formItems.push(formItem);
            });
            if (formItems.length > 0) map.set(form, formItems);
        });

        return map;
    }

    get canEdit(): boolean {
        const { canEditChargeSheet, status } = this.detailData;
        return canEditChargeSheet && (status == ChargeStatus.unCharged || status == ChargeStatus.draft);
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    isChargeItemSelected(chargeForm: ChargeForm, item: ChargeFormItem): boolean {
        return item.checked ?? true;
    }

    //判断长护是否欠费
    get canHospitalDischarge(): boolean {
        if (!this.hospitalDetail) return false;
        const { status } = this.hospitalDetail;
        return status == HospitalRegisterDetailStatus.discharge;
    }

    /**
     * 口腔诊所
     */
    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    /**
     * 当前添加的项目
     */
    get treatmentChargeForm(): ChargeFormItem[] {
        return [
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.examination)?.chargeFormItems ?? []),
            ...(this.detailData.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems ?? []),
            ...(this.detailData.getChargeForm(ChargeSourceFormType.package)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.westernPrescription)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.infusionPrescription)?.chargeFormItems ?? []),
            ...(this.detailData?.getChargeForm(ChargeSourceFormType.chinesePrescription)?.chargeFormItems ?? []),
        ];
    }

    /**
     * 项目医生相关信息
     */
    get doctorDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item?.doctorName && str.add(item?.doctorName);
        }
        return [...str.values()].join("、");
    }

    /**
     * 项目护士相关信息
     */
    get nurseDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item?.nurseName && str.add(item.nurseName);
        }
        return [...str.values()].join("、");
    }

    /**
     * @description 收费员查看患者就诊历史
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeePatientHistory;
    }
    /**
     * @description 收费员查看患者手机号
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientMobile(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeePatientMobile;
    }
    /**
     * @description 收费员修改支付方式
     * @return true 能;
     * @return false 不能;
     */
    get isCanSeeModifyPayMode(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeeModifyPayMode;
    }
}

export class ScrollToErrorViewState extends State {
    static fromState(state: State): ScrollToErrorViewState {
        const newState = new ScrollToErrorViewState();
        Object.assign(newState, state);
        return newState;
    }
}

class ChargeInvoiceMedicineAddState extends State {
    constructor() {
        super();
    }

    static fromState(state: State) {
        const newState = new ChargeInvoiceMedicineAddState();
        Object.assign(newState, state);
        return newState;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventUpdateCalculatePrice extends _Event {
    calculating: boolean;
    rspData?: ChargeCalculateRspData;
    error?: any;

    constructor(options: { calculating: boolean; rspData?: ChargeCalculateRspData; error?: any }) {
        super();
        const { calculating, rspData, error } = options;
        this.calculating = calculating;
        this.rspData = rspData;
        this.error = error;
    }
}

class _EventToggleChargeItem extends _Event {
    constructor(chargeForm: ChargeForm, chargeFormItem: ChargeFormItem) {
        super();
        this.chargeForm = chargeForm;
        this.chargeFormItem = chargeFormItem;
    }

    chargeForm: ChargeForm;
    chargeFormItem: ChargeFormItem;
}

class _EventUpdateDoctor extends _Event {
    formItem: ChargeFormItem;
    department: Department;
    doctor: Doctor;

    constructor(formItem: ChargeFormItem, department: Department, doctor: Doctor) {
        super();
        this.formItem = formItem;
        this.department = department;
        this.doctor = doctor;
    }
}

class _EventToggleDecoctionDelivery extends _Event {
    constructor(decoction?: boolean, delivery?: boolean) {
        super();
        this.decoction = decoction;
        this.delivery = delivery;
    }

    decoction?: boolean;
    delivery?: boolean;
}

class _EventUpdateDecoctionInfo extends _Event {
    contactMobile?: string;
    decoctionForms?: ChargeForm[];

    constructor(contactMobile?: string, decoctionForms?: ChargeForm[]) {
        super();
        this.contactMobile = contactMobile;
        this.decoctionForms = decoctionForms;
    }
}

class _EventSavePendingOrder extends _Event {}

class _EventSendChargeInvoice extends _Event {}

class _EventUpdateDeliveryInfo extends _Event {
    constructor(deliveryInfo?: DeliveryInfo, chargeForm?: ChargeForm) {
        super();
        this.deliveryInfo = deliveryInfo;
        this.chargeForm = chargeForm;
    }

    deliveryInfo?: DeliveryInfo;
    chargeForm?: ChargeForm;
}

class _EventSave extends _Event {}

class _EventChangeDosageCount extends _Event {
    chargeForm?: ChargeForm;
    dosageCount: number;

    constructor(chargeForm: ChargeForm | undefined, dosageCount: number) {
        super();
        this.chargeForm = chargeForm;
        this.dosageCount = dosageCount;
    }
}

//修改药品
class _EventMedicineCountChange extends _Event {
    constructor(formItem: ChargeFormItem, unitCount?: number, unit?: string) {
        super();
        this.formItem = formItem;
        this.unitCount = unitCount;
        this.unit = unit;
    }

    formItem: ChargeFormItem;
    unitCount?: number;
    unit?: string;
}

class _EventDeleteMedicine extends _Event {
    constructor(chargeFormItem: ChargeFormItem) {
        super();
        this.chargeFormItem = chargeFormItem;
    }

    chargeFormItem: ChargeFormItem;
}

class _EventChangeFocusMedicineItem extends _Event {
    constructor(formItem: ChargeFormItem, focus: boolean) {
        super();
        this.formItem = formItem;
        this.focus = focus;
    }

    formItem: ChargeFormItem;
    focus: boolean;
}

class _EventSingleBargainTap extends _Event {}

class _EventUpdateChargeItemAmount extends _Event {
    formItem: ChargeFormItem;
    count?: number;
    unit?: string;

    constructor(formItem: ChargeFormItem, count?: number, unit?: string) {
        super();
        this.formItem = formItem;
        this.count = count;
        this.unit = unit;
    }
}

class _EventAddNewChargeItem extends _Event {}

class _EventReChargeTap extends _Event {}

class _EventRefundTap extends _Event {}

class _EventChargeTap extends _Event {
    isCharge: boolean;
    constructor(isCharge: boolean) {
        super();
        this.isCharge = isCharge;
    }
}

class _EventModifyConsultant extends _Event {
    index?: number;
    constructor(index?: number) {
        super();
        this.index = index;
    }
}

class _EventUpdatePromotions extends _Event {
    selectPromotions?: number[];
    selectGiftPromotions?: number[];
    selectCouponPromotions? = new AbcMap<CouponPromotion, Pair<boolean, number>>();
    selectMemberPointPromotion?: PatientPointsInfo;

    constructor(
        promotions?: number[],
        giftPromotions?: number[],
        couponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ) {
        super();
        this.selectPromotions = promotions;
        this.selectGiftPromotions = giftPromotions;
        this.selectCouponPromotions = couponPromotions;
        this.selectMemberPointPromotion = selectMemberPointPromotion;
    }
}

class _EventCancelAdjustmentFee extends _Event {}

class _EventUpdateMemberCard extends _Event {
    member: SelectMemberInfo;

    constructor(member: SelectMemberInfo) {
        super();
        this.member = member;
    }
}

class _EventUpdateComment extends _Event {
    chargeForm: ChargeForm;
    comment: string;

    constructor(chargeForm: ChargeForm, comment: string) {
        super();
        this.chargeForm = chargeForm;
        this.comment = comment;
    }
}

class _EventChangeChiefComplaint extends _Event {
    chargeForm: ChargeForm;
    chiefComplaint: string;

    constructor(chargeForm: ChargeForm, chiefComplaint: string) {
        super();
        this.chargeForm = chargeForm;
        this.chiefComplaint = chiefComplaint;
    }
}

class _EventChangeDiagnosis extends _Event {
    chargeForm: ChargeForm;
    diagnosis: string;

    constructor(chargeForm: ChargeForm, diagnosis: string) {
        super();
        this.chargeForm = chargeForm;
        this.diagnosis = diagnosis;
    }
}

class _EventChangeChargeDoctor extends _Event {
    doctor?: Doctor;
    department?: Department;

    constructor(doctor?: Doctor, department?: Department) {
        super();
        this.doctor = doctor;
        this.department = department;
    }
}

class _EventCloseChargeSheet extends _Event {}

class _EventReOpenToCharge extends _Event {}

class _EventModifyDecoctionInfo extends _Event {
    chargeForm: ChargeForm;
    decoctionInfo: ChargeFormItem;

    constructor(chargeForm: ChargeForm, decoctionInfo: ChargeFormItem) {
        super();
        this.chargeForm = chargeForm;
        this.decoctionInfo = decoctionInfo;
    }
}

class _EventPatientCardPromotion extends _Event {
    patientCardPromotions: PatientCardPromotion[];
    constructor(patientCardPromotions: PatientCardPromotion[]) {
        super();
        this.patientCardPromotions = patientCardPromotions;
    }
}

class _EventUpdateServiceDeductCollect extends _Event {
    serviceDeductPromotion?: ServiceDeductCollection;
    constructor(serviceDeductPromotion?: ServiceDeductCollection) {
        super();
        this.serviceDeductPromotion = serviceDeductPromotion;
    }
}

class _EventHandleChargeAbnormal extends _Event {
    isShebaoAbnormal: boolean;
    constructor(isShebaoAbnormal: boolean) {
        super();
        this.isShebaoAbnormal = isShebaoAbnormal;
    }
}

class _EventAbnormalRefund extends _Event {
    abnormalMsg: AbnormalTransactionList;
    constructor(abnormalMsg: AbnormalTransactionList) {
        super();
        this.abnormalMsg = abnormalMsg;
    }
}

class _EventHospitalDetail extends _Event {
    detail?: HospitalRegisterDetail;
    constructor(detail?: HospitalRegisterDetail) {
        super();
        this.detail = detail;
    }
}

class _EventRePayment extends _Event {}

class _EventChangeMedicineState extends _Event {
    chargeForm: ChargeForm;
    medicineStateId: MedicineScopeId;
    medicineStateName: string;
    medicineStateList?: AirPharmacyMedicineState[];

    constructor(
        chargeForm: ChargeForm,
        medicineStateId: MedicineScopeId,
        medicineStateName: string,
        medicineStateList?: AirPharmacyMedicineState[]
    ) {
        super();
        this.chargeForm = chargeForm;
        this.medicineStateId = medicineStateId;
        this.medicineStateName = medicineStateName;
        this.medicineStateList = medicineStateList;
    }
}

class _EventModifyProductEmployee extends _Event {}

class _EventChangePharmacyType extends _Event {
    chargeForm: ChargeForm;
    formItem?: ChargeFormItem;
    constructor(chargeForm: ChargeForm, formItem?: ChargeFormItem) {
        super();
        this.chargeForm = chargeForm;
        this.formItem = formItem;
    }
}

class _EventUpdateRemark extends _Event {
    sourceFormType?: ChargeSourceFormType;
    formItem?: ChargeFormItem;
    constructor(sourceFormType?: ChargeSourceFormType, formItem?: ChargeFormItem) {
        super();
        this.sourceFormType = sourceFormType;
        this.formItem = formItem;
    }
}
class _EventDeleteRemark extends _Event {
    sourceFormType?: ChargeSourceFormType;
    formItem?: ChargeFormItem;
    constructor(sourceFormType?: ChargeSourceFormType, formItem?: ChargeFormItem) {
        super();
        this.sourceFormType = sourceFormType;
        this.formItem = formItem;
    }
}

class _EventShowDiscountSummaryDetail extends _Event {}
class _EventBack extends _Event {}

class _EventCheckExpressInfo extends _Event {
    formId: string;
    vendorId: string;
    companyInfo?: string;
    constructor(formId: string, vendorId: string, companyInfo?: string) {
        super();
        this.formId = formId;
        this.vendorId = vendorId;
        this.companyInfo = companyInfo;
    }
}
class _EventChangeDiscount extends _Event {
    formItem: ChargeFormItem;
    discount: number;
    constructor(formItem: ChargeFormItem, discount: number) {
        super();
        this.formItem = formItem;
        this.discount = discount;
    }
}

class _EventChangeTotalPrice extends _Event {
    formItem: ChargeFormItem;
    totalPrice: number;
    constructor(formItem: ChargeFormItem, totalPrice: number) {
        super();
        this.formItem = formItem;
        this.totalPrice = totalPrice;
    }
}
class _EventCheckGoodsBatchInfo extends _Event {
    chargeForm: ChargeForm | ChargeForm[];
    constructor(chargeForm: ChargeForm | ChargeForm[]) {
        super();
        this.chargeForm = chargeForm;
    }
}
class _EventCancelPayment extends _Event {}
class ChargeInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ChargeInvoicePageBloc | undefined>(undefined);
    public editViewBloc?: ChargeInvoiceEditViewBloc;

    static fromContext(context: ChargeInvoicePageBloc): ChargeInvoicePageBloc {
        return context;
    }

    chargeId: string;
    enableDraft: boolean;

    _triggerCalculatePay = new Subject<number>();
    _triggerLoadDetailData = new Subject<number>();
    _triggerPatientCard = new Subject<number>();
    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史
    private _getAirPharmacyLogisticsTrace = new BehaviorSubject<{ formId: string; vendorId: string }>({ formId: "", vendorId: "" }); //空中药房处方对应物流信息
    private _patientInfoTrigger = new Subject<string>(); //患者信息

    constructor(chargeId: string, enableDraft = true) {
        super();

        this.chargeId = chargeId;
        this.enableDraft = enableDraft;

        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();

        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateCalculatePrice, this._mapEventUpdateCalculatePrice);
        map.set(_EventToggleChargeItem, this._mapEventToggleChargeItem);
        map.set(_EventUpdateMemberCard, this._mapEventUpdateMemberCard);
        map.set(_EventUpdatePromotions, this._mapEventUpdatePromotions);
        map.set(_EventCancelAdjustmentFee, this._mapEventCancelAdjustmentFee);
        map.set(_EventChargeTap, this._mapEventChargeTap);
        map.set(_EventRefundTap, this._mapEventRefundTap);
        map.set(_EventReChargeTap, this._mapEventReChargeTap);
        map.set(_EventAddNewChargeItem, this._mapEventAddNewChargeItem);
        map.set(_EventUpdateChargeItemAmount, this._mapEventUpdateChargeItemAmount);
        map.set(_EventSingleBargainTap, this._mapEventSingleBargainTap);
        map.set(_EventUpdateDoctor, this._mapEventUpdateDoctor);
        map.set(_EventChangeFocusMedicineItem, this._mapEventChangeFocusMedicineItem);
        map.set(_EventDeleteMedicine, this._mapEventDeleteMedicine);
        map.set(_EventMedicineCountChange, this._mapEventMedicineCountChange);
        map.set(_EventChangeDosageCount, this._mapEventChangeDosageCount);
        map.set(_EventUpdateDeliveryInfo, this._mapEventUpdateDeliveryInfo);
        map.set(_EventSave, this._mapEventSave);
        map.set(_EventSendChargeInvoice, this._mapEventSendChargeInvoice);
        map.set(_EventSavePendingOrder, this._mapEventSavePendingOrder);
        map.set(_EventToggleDecoctionDelivery, this._mapEventToggleDecoctionDelivery);
        map.set(_EventUpdateDecoctionInfo, this._mapEventUpdateDecoctionInfo);
        map.set(_EventUpdateComment, this._mapEventUpdateComment);
        map.set(_EventChangeChiefComplaint, this._mapEventChangeChiefComplaint);
        map.set(_EventChangeDiagnosis, this._mapEventChangeDiagnosis);
        map.set(_EventChangeChargeDoctor, this._mapEventChangeDoctor);
        map.set(_EventCloseChargeSheet, this._mapEventCloseChargeSheet);
        map.set(_EventReOpenToCharge, this._mapEventReOpenToCharge);
        map.set(_EventModifyDecoctionInfo, this._mapEventModifyDecoctionInfo);
        map.set(_EventPatientCardPromotion, this._mapEventPatientCardPromotion);
        map.set(_EventHandleChargeAbnormal, this._mapEventHandleChargeAbnormal);
        map.set(_EventAbnormalRefund, this._mapEventAbnormalRefund);

        return map;
    }

    private async _initPageConfig(): Promise<void> {
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
        this.innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore();
        //读取当前加工配置
        this.innerState.dispensingConfig = await OnlinePropertyConfigProvider.instance.getClinicDispensingConfig().catchIgnore();
        this.innerState.clinicConfig = await userCenter.getClinicConfig().catchIgnore();
        this.innerState.chargeConfig = await OnlinePropertyConfigProvider.instance.getChargeConfig().catchIgnore();
        ChargeAgent.getProcessRules(false).then(); //强制刷新一次药品加工配置,后面加工费编辑可能会用到
        GoodsUtils.initGoodsCustomUnitIfNeed().catch((/*error*/) => false); //初始化拉取自goods的自定义单位，在这里不使用，在界面上显示自定义理疗单位时要使用
        //获取咨询师列表
        this.innerState.consultantList = await ClinicAgent.updateEmployeesConsultant([ModuleRolesId.ROLE_CONSULTANT_ID]).catchIgnore();
        this.innerState.dataPermission = await OnlinePropertyConfigProvider.instance.getClinicDataPermission().catchIgnore();
        this.innerState.clinicBasicSetting = await OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore();
        const shebaoConfig = await ShebaoAgent.getClinicShebaoConfig().catchIgnore();
        this.innerState.isOpenElecSetlCertUpload = shebaoConfig?.basicInfo?.openElecSetlCertUpload;
    }
    private _initPageTrigger(): void {
        this._triggerCalculatePay
            .pipe(
                debounceTime(300),
                switchMap(() => {
                    this.dispatch(new _EventUpdateCalculatePrice({ calculating: true }));

                    return ChargeUtils.calculatingPrice(this.innerState.detailData)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError)
                    this.dispatch(
                        new _EventUpdateCalculatePrice({
                            calculating: false,
                            error: rsp.detailError,
                        })
                    );
                else this.dispatch(new _EventUpdateCalculatePrice({ calculating: false, rspData: rsp }));
            })
            .addToDisposableBag(this);

        ChargeAgent.chargeStatusPublisher
            .subscribe((event) => {
                let chargeId = "";
                if (event instanceof ChargeEventPaid) {
                    chargeId = event.rsp.id!;
                } else if (event instanceof ChargeEventRepaid) {
                    chargeId = event.rsp.id!;
                } else if (event instanceof ChargeEventRefund) {
                    chargeId = event.rsp.id!;
                } else if (event instanceof ChargeEventPaidBack) {
                    chargeId = event.rsp.id!;
                } else if (event instanceof ChargeEventRenewpaid) {
                    chargeId = event.rsp.id!;
                } else if (event instanceof ChargeOweSinglePaid) {
                    chargeId = event.rsp.id!;
                }

                if (chargeId == this.innerState.detailData.id) {
                    this._triggerLoadDetailData.next(0);
                }
            })
            .addToDisposableBag(this);

        this._getAirPharmacyLogisticsTrace
            .subscribe(async ({ formId, vendorId }) => {
                const rsp = await ChargeAgent.getAirPharmacyLogisticsTrace(formId, vendorId);
                if (rsp instanceof ABCError) return;
                this.innerState.detailData?.chargeForms?.forEach((item) => {
                    if (!item.isAirPharmacy) return;
                    if (item?.id == formId) {
                        item.deliveryInfo = item.deliveryInfo ?? new DeliveryInfo();
                        item.deliveryInfo!.__logisticTraceList = rsp;
                    }
                });
                this.update();
            })
            .addToDisposableBag(this);

        this._triggerLoadDetailData
            .pipe(
                debounceTime(100),
                switchMap(() => {
                    this._updateLoadDetailDataStatus({ loading: true });
                    return ChargeAgent.getChargeInvoiceDetail(this.chargeId)
                        .then(async (chargeRsp) => {
                            //有可能出现单子里只有药态id,没有对应的name,这里需要获取修改下
                            await Promise.all([
                                ChargeUtils.refreshAirPharmacy(chargeRsp.chargeForms).then((rsp) => {
                                    if (rsp.priceChanged || rsp.deliveryUpdate) {
                                        const chargeSheetSummary = chargeRsp.chargeSheetSummary;
                                        if (chargeSheetSummary) chargeSheetSummary.expectedTotalPrice__ = undefined;
                                    }
                                }),
                                ChargeUtils.fixAirPharmacyMedicalState(chargeRsp.chargeForms),
                            ]).catchIgnore();

                            return chargeRsp;
                        })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(async (rsp) => {
                if (rsp instanceof ABCError) {
                    this._updateLoadDetailDataStatus({
                        loading: false,
                        error: rsp.detailError,
                    });
                } else {
                    this._updateLoadDetailDataStatus({ loading: false, detailData: rsp });
                    // this._triggerPatientCard.next(0);
                    if (!!this.innerState.detailData?.patient?.id) {
                        this._patientInfoTrigger.next(this.innerState.detailData?.patient?.id);
                        this._getHistoryListTrigger.next(0);
                    }
                    // 在已开票的状态下，才需要去查询电子凭证上报状态，来决定当前收费单是否可退费
                    if (this.innerState.detailData.isInvoiceHasBeenIssued && !!this.innerState.detailData?.id) {
                        const isEnabledRefund = await InvoiceShebaoAgent.getInvoiceCheckByBusinessId({
                            businessId: this.innerState.detailData?.id,
                            businessScene: !!this.innerState.detailData?.hospitalSheetId ? "hospital_charge" : "charge",
                        }).catchIgnore();
                        this.innerState.isEnabledRefund = !!isEnabledRefund;
                    }
                    this.innerState.patientWxPushConfig = await CrmAgent.postPatientFamilyWxStatus(
                        this.innerState.detailData.patient?.chainId ?? "",
                        [this.innerState.detailData.patient?.id ?? ""]
                    ).catch((/*error*/) => undefined); //判断当前患者是否可以推送
                    const result = this.innerState.patientWxPushConfig;
                    // isPush，这个为1就能推，不会管微信绑定和关注状态；要是这个isPush不为1，就再去判断这个患者是否绑定且关注微信，达到条件就可以推送
                    if (result && !_.isEmpty(result.list)) {
                        this.innerState.canPushChargeOrder =
                            (result.list![0].isPush == 1 ||
                                (result.list![0].isPush != 1 && result.list![0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND)) ??
                            false;
                    }
                    //查询对应空中药房处方的物流信息
                    const airPharmacyChargeForm = this.innerState.detailData.chargeForms?.filter((t) => t.isAirPharmacy);
                    if (!!airPharmacyChargeForm?.length) {
                        airPharmacyChargeForm.forEach((item) => {
                            //有物流号的时候，才调用完整的物流信息接口
                            if (!!item?.deliveryInfo?.deliveryNo && !!item?.id && !!item?.vendorId) {
                                this._getAirPharmacyLogisticsTrace.next({ formId: item?.id, vendorId: item?.vendorId });
                            }
                        });
                    }
                    // 如果医保结算异常，需要再拉取接口判断异常类型
                    await this.getShebaoSettlementExceptionList();
                    this.update();
                }
            })
            .addToDisposableBag(this);

        this._triggerPatientCard
            .pipe(
                debounceTime(300),
                switchMap(() => {
                    return ChargeAgent.getChargePatientCard(this.chargeId ?? "")
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    return;
                } else {
                    if (!_.isEmpty(rsp)) {
                        this.innerState.detailData.canPaidPatientCards = rsp;
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        keyboardListener
            .subscribe((visible) => {
                if (visible.visible || !this.innerState.detailData) return;
                if (this._removeAllZeroCountMedicine()) this.update();
            })
            .addToDisposableBag(this);

        // 锁单socket接口（收费模块只显示门诊医嘱编辑处方、收费支付锁单、退费锁单）
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.detailData?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            this.innerState.detailData.copyPatientOrderLocks = this.innerState.detailData.copyPatientOrderLocks ?? [];
            const patientOrderLock = this.innerState.detailData.copyPatientOrderLocks?.find(
                (t) => t.businessKey == PatientOrderLockType.chargeSheet
            );
            if (!!patientOrderLock) {
                Object.assign(patientOrderLock, {
                    ...data,
                });
            } else {
                this.innerState.detailData.copyPatientOrderLocks!.push(
                    JsonMapper.deserialize(PatientOrderLockDetail, {
                        ...data,
                    })
                );
            }
            // 如果是收费支付锁单，还需要判断当前是哪方发起的
            if (data.value?.chargeInProgress) {
                this.innerState.detailData.lockStatus = data.value?.businessDetail?.addedLockStatus;
                this.innerState.detailData.lockPayTransactionInfo = this.innerState.detailData.lockPayTransactionInfo ?? {};
                this.innerState.detailData.lockPayTransactionInfo.id = data.value?.businessDetail?.chargePayTransactionId;
            }
            this.update();
        });
        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detailData?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loadingDetailData = false;
                    this.innerState.loadingDetailError = error;
                    this.update();
                }
            )
            .addToDisposableBag(this);
        // 患者信息
        this._patientInfoTrigger
            .pipe(
                switchMap((patientId) => {
                    if (!patientId) return of(0);
                    return CrmAgent.getPatientById(patientId)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((patient) => {
                if (patient instanceof Patient) {
                    this.innerState.detailData!.patient = _.merge(this.innerState.detailData!.patient, patient);
                }
            })
            .addToDisposableBag(this);
    }
    private async *_mapEventInit(/*ignored: _EventInit*/) {
        await this._initPageConfig();
        this._initPageTrigger();
        this._triggerLoadDetailData.next(0);
    }

    async getShebaoSettlementExceptionList(): Promise<void> {
        // 如果医保结算异常，需要再拉取接口判断异常类型
        if (this.innerState.detailData?.isSheBaoAbnormal && !!this.innerState.detailData?.id) {
            this.innerState.settlementExceptionList = await ChargeAgent.getChargeSettlementExceptionList(
                this.innerState.detailData.id
            ).catchIgnore();
        }
    }
    private async *_mapEventUpdateCalculatePrice(event: _EventUpdateCalculatePrice) {
        this.innerState.calculateChargeError = event.error;
        this.innerState.calculatingCharge = event.calculating;
        if (event.rspData != null) {
            ChargeUtils.syncChargeInvoiceDetail(this.innerState.detailData, event.rspData);
        }

        yield this.innerState.clone();
    }

    private async *_mapEventToggleChargeItem(event: _EventToggleChargeItem) {
        await this._toggleChargeItem(event.chargeForm, event.chargeFormItem);

        yield this._medicineChanged();
    }

    private async *_mapEventUpdateMemberCard(event: _EventUpdateMemberCard) {
        const innerState = this.innerState;
        SelectMemberInfo.updateChargeDetail(innerState.detailData, event.member);

        yield this._setHasChanged();
    }

    private async *_mapEventUpdatePromotions(event: _EventUpdatePromotions) {
        LogUtils.i("_mapEventUpdatePromotions");

        const _innerState = this.innerState;
        ChargeUtils.updatePromotions(_innerState.detailData!, event);

        yield this._setHasChanged();
    }

    private async *_mapEventCancelAdjustmentFee(/*event: _EventCancelAdjustmentFee*/) {
        const _innerState = this.innerState;
        _innerState.detailData.chargeSheetSummary!.expectedTotalPrice__ = undefined;
        _innerState.detailData.chargeSheetSummary!.draftAdjustmentFee = 0;
        yield this._setHasChanged();
    }

    private async _checkAirPharmacy(): Promise<boolean> {
        const chargeData = this.innerState.detailData;
        for (const chargeForm of chargeData?.chargeForms ?? []) {
            if (chargeForm.isAirPharmacy || chargeForm.isVirtualPharmacy) {
                if (_.find(chargeForm.chargeFormItems, (item) => item.isChineseMedicine) == undefined) {
                    await Toast.show("空中药房没有添加药品", { warning: true });
                    return false;
                }

                for (const formItem of chargeForm.chargeFormItems ?? []) {
                    if (formItem.unitCount == undefined) {
                        this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.medicineContentView);
                        return false;
                    }
                }
                const deliveryFeeItem = chargeForm.getFormItem(ChargeSourceFormType.delivery)!;
                const deliveryInfo = JsonMapper.deserialize(DeliveryInfo, deliveryFeeItem?.productInfo);
                // 空中药房，需要校验快递地址的完整性(没有药时，不用校验地址)
                if (chargeForm.isAirPharmacy) {
                    const isExistCheckedGoods = !!chargeForm.chargeFormItems?.find(
                        (item) => item?.goodsInfo?.isChineseMedicine && item.checked
                    );
                    if (isExistCheckedGoods) {
                        if (_.isEmpty(deliveryInfo?.displayAddress("/", true))) {
                            Toast.show("收货地址不能为空", { warning: true });
                            this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.delivery);
                            return false;
                        }

                        if (_.isEmpty(deliveryInfo?.deliveryMobile)) {
                            Toast.show("收货人电话号码不能为空", { warning: true });
                            this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.delivery);
                            return false;
                        }

                        if (!StringUtils.validateMobile(deliveryInfo?.deliveryMobile ?? "", deliveryInfo?.deliveryCountryCode)) {
                            Toast.show("收货人电话号码格式错误", { warning: true });
                            this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.delivery);
                            return false;
                        }

                        if (_.isEmpty(deliveryInfo?.deliveryCompany?.id)) {
                            Toast.show("请选择快递公司", { warning: true });
                            this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.delivery);
                            return false;
                        }
                    }
                }

                if ((chargeForm.doseCount ?? 0) < ChargeUtils.calcMinDosageCount(chargeForm)) {
                    this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.dosageCount);
                    return false;
                }

                const processRule = chargeForm.processRule;
                const totalWeight = ChargeUtils.computePrescriptionDosageWeight(chargeForm);
                if (totalWeight < (processRule?.minimum ?? 0)) {
                    this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.dosageCount);
                    return false;
                }

                if (chargeForm.isAirPharmacy && _.isEmpty(chargeData?.chiefComplaint)) {
                    this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.chiefComplaint);
                    return false;
                }
                if (_.isEmpty(chargeData?.diagnosis)) {
                    this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.diagnosis);
                    return false;
                }

                if (_.isEmpty(chargeData?.doctorName)) {
                    this._setFocusErrorView(chargeForm.compareKey() + AirPharmacyPrescriptionFormViewKeyIds.doctor);
                    return false;
                }
            }
        }
        return true;
    }

    private async *_mapEventChargeTap(event: _EventChargeTap) {
        const _innerState = this.innerState;
        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData!)) {
            _innerState.showErrorHint = true;
            yield this.innerState.clone();
            return;
        }

        if (_innerState.calculateChargeError) {
            await Toast.show("费用计算失败");
            return;
        }

        if (_innerState.calculatingCharge ?? false) {
            await Toast.show("正在计算费用");
            return;
        }

        const detailData = _innerState.detailData;
        //如果有挂号费，但没有指定医生，提示选择
        //检测挂号费收费时是否选择了医生
        const registrationForm = detailData.chargeForms?.find((item) => item.isRegistration);
        if (registrationForm && !_.isEmpty(registrationForm.chargeFormItems)) {
            const registrationFormItem = _.first(registrationForm.chargeFormItems)!;
            //挂号费未收
            if (registrationFormItem.status == ChargeFormItemStatus.unCharged) {
                const info = registrationFormItem.productInfo as ChargeRegistrationInfo;
                if (_.isEmpty(info.doctorId)) {
                    await Toast.show(`${_innerState.clinicBasicSetting?.registrationFeeStr}未选择医生`);
                    return;
                }
            }
        }

        if (!(await this._checkAirPharmacy())) return;
        // 是否开启整单收退费开关
        const isOpenSingleChargeRefund = _innerState.chargeConfig?.openWholeSheetOperateEnable;
        if (!(await StockDisableCheckDialog.checkIfCanCharge(detailData, isOpenSingleChargeRefund))) return;

        //检查收费单是否包含收费项
        let totalChargeFormCount = 0;
        detailData.chargeForms?.forEach((form) => {
            totalChargeFormCount += form.chargeFormItems?.length ?? 0;
        });
        if (totalChargeFormCount == 0) {
            await Toast.show("请添加收费项", { warning: true });
            return;
        }

        //检查药敏是否能收费
        // if (detailData.disabledPayReason?.length) {
        //     await showConfirmDialog("", detailData.disabledPayReason);
        //     return;
        // }

        const payData = new ChargePayData();
        payData.reCharge = detailData.reCharge__ ?? false;
        payData.chargeInvoiceDetailData = detailData;
        payData.selects = _innerState.selectedChargeItems;

        payData.promotions = detailData.promotions;
        payData.receivableFee = detailData.chargeSheetSummary?.needPayFee;
        payData.totalFee = detailData.chargeSheetSummary?.totalFee;
        payData.adjustmentFee = _innerState.detailData.chargeSheetSummary?.adjustmentFee;
        LogUtils.i(`onChargeBtnTap = ${_innerState.clinicConfig}`);
        payData.memberId = _innerState.detailData.memberId;
        ABCNavigator.navigateToPage(<ChargePayPage chargePayData={payData} hasPartedPaid={!event.isCharge} />).then();
    }

    private async *_mapEventRefundTap(/*ignored: _EventRefundTap*/) {
        // 开启了医保电子结算凭证的诊所，是否可以退费
        if (this.innerState.isOpenElecSetlCertUpload && !this.innerState.isEnabledRefund) {
            await showStateLogoConfirmDialog({
                title: ``,
                content: (
                    <AbcWithStateLogTitle
                        logo={"image_dlg_fail"}
                        title={"无法退费"}
                        content={"收费单对应电子发票已上报至医保中心，请前往ABC客户端退费！"}
                    />
                ),
                positive: "知道了",
                contentPadding: Sizes.paddingLTRB(Sizes.dp24),
            });
            return;
        }
        const innerState = this.innerState;
        const detailData = innerState.detailData!;
        const status = detailData.status;
        // 退费时查询收费单发药/执行等详情数据信息
        // 如果有错误，则提示错误信息，不进行下一步
        let isExistError = false;
        if (!!detailData.id) {
            await ChargeAgent.queryChargeRefundDetail({
                chargeSheetId: detailData.id,
                dispensingQueryCheck: 2,
            }).catch(async (error) => {
                await showConfirmDialog("提示", errorToStr(error), "知道了");
                isExistError = true;
            });
        }
        if (isExistError) return;

        if (status == ChargeStatus.partCharged) {
            const chargeSheetSummary = detailData.chargeSheetSummary;
            const payData = new ChargeRefundData();
            payData.chargeInvoiceDetailData = innerState.detailData;
            payData.needRefundFee = chargeSheetSummary!.receivedFee! + chargeSheetSummary!.refundFee!;
            payData.owedRefundFee = chargeSheetSummary!.owedRefundFee;
            payData.selects = innerState.selectedChargeItems;
            payData.clinicBargain = false;

            payData.refundAdjustmentFee = 0;
            payData.realRefundFee = payData.needRefundFee;

            ABCNavigator.navigateToPage(<ChargeRefundPayPage chargePayData={payData} />).then();
            return;
        }

        //在进入到退费单之前，检测是否有可退项
        let hasCanRefundItems = false;
        let hasChargedItems = false;
        for (const form of innerState.detailData.chargeForms!) {
            for (const formItem of form.chargeFormItems!) {
                if (formItem.canRefund) {
                    hasCanRefundItems = true;
                    break;
                }

                if (formItem.status != ChargeFormItemStatus.chargeBack && !hasChargedItems) {
                    hasChargedItems = true;
                }
            }
        }

        if (hasCanRefundItems || !hasChargedItems || detailData.chargeSheetSummary!.owedRefundFee! > 0) {
            await ChargeInvoiceRefundNewDialog.show({
                detailData: this.innerState.detailData,
                enableWholeChargeRefund: this.innerState.chargeConfig?.openWholeSheetOperateEnable,
            });
        } else if (hasChargedItems) {
            //有收费项，但不能退，需要先退药
            await showConfirmDialog("已发出的药品，需要在药房完成退药后才能退费", "");
        }
    }

    private async *_mapEventReChargeTap(/*ignored: _EventReChargeTap*/) {
        const select = await showQueryDialog("", "收费单已全部退费。是否确定打开该收费单重新收费");
        if (select !== DialogIndex.positive) return;
        const innerState = this.innerState;
        if (innerState.detailData.type == ChargeInvoiceType.onlineConsultation) {
            await Toast.show("咨询收费单不能重收");
            return;
        }

        const detail = await ChargeAgent.putChargeRefundBillRenew(innerState.detailData.id ?? "").catchIgnore();
        if (!detail) return;

        innerState.detailData = detail;

        innerState.detailData.chargeSheetSummary!.adjustmentFee = 0.0;
        const type = innerState.detailData.type;
        if (type == ChargeInvoiceType.retail || type == ChargeInvoiceType.therapy) {
            ABCNavigator.navigateToPage(<DirectChargeInvoicePage detailData={innerState.detailData} />).then();
            return;
        }

        innerState.detailData.status = ChargeStatus.unCharged;
        //点击重新收费时，清除议价
        for (const form of innerState.detailData.chargeForms!) {
            form.status = ChargeFormStatus.unCharged;
            for (const item of form.chargeFormItems!) {
                item.status = ChargeFormItemStatus.unCharged;
            }
        }

        this._updateLoadDetailDataStatus({
            loading: false,
            detailData: this._innerState.detailData,
        });
    }

    private async *_mapEventAddNewChargeItem(/*ignored: _EventAddNewChargeItem*/) {
        let params: DirectChargeMedicineInvokeParams;
        const _innerState = this.innerState;
        const outpatientWaiting = _innerState.detailData.outpatientStatus == ChargeInvoiceData.outpatientStatusWaiting;

        //特殊处理，对于待诊的单子，只允许添加治疗理疗，商品项
        if (outpatientWaiting) {
            params = new DirectChargeMedicineInvokeParams();
            params.autoFocusSearch = false;
            params.showCategorySearchNavi = true;
            params.medicineInfo = new DirectChargeMedicineAddInfo();
        } else {
            params = new DirectChargeMedicineInvokeParams();
            params.autoFocusSearch = true;
            params.goodsTypes = [
                GoodsType.material,
                GoodsType.examination,
                GoodsType.treatment,
                GoodsType.goods,
                GoodsType.package,
                GoodsType.otherGoods49,
            ];
            params.showCategorySearchNavi = false;
            params.searchHintContent = <MedicineSearchHintView />;
            params.medicineInfo = new DirectChargeMedicineAddInfo();
            params.autoSearch = false;
        }

        params.medicineInfo = ChargeUtils.toDirectChargeMedicineAddInfo(_innerState.detailData);

        // const medicines = await DirectChargeMedicineAddUtils.addMedicine(params);
        const medicines = await showBottomPanel<DirectChargeMedicineAddInfo | undefined>(<ChargeAddMedicinePage params={params} />, {
            topMaskHeight: Sizes.dp160,
        });

        if (medicines != null && !_.isEmpty(medicines.goods)) {
            //删除本地添加的项目
            ChargeUtils.removeAllLocalAddItems(_innerState.detailData);

            LogUtils.i(`_mapEventAddNewChargeItem new medicines count = ${medicines.goods.length}`);
            ChargeUtils.addMedicinesToChargeInvoice({
                detailData: _innerState.detailData,
                medicines,
            });
            this._selectAllLocalAddItems();
            yield this._medicineChanged();
            yield ChargeInvoiceMedicineAddState.fromState(_innerState);
        }
    }

    private async *_mapEventUpdateChargeItemAmount(event: _EventUpdateChargeItemAmount) {
        if (event.count != undefined) event.formItem.unitCount = event.count;
        if (event.unit != undefined) {
            event.formItem.setUnit(event.unit);
        }

        yield this._medicineChanged();
    }

    private async *_mapEventSingleBargainTap(/*ignored: _EventSingleBargainTap*/) {
        const innerState = this.innerState;
        const { canEditChargeSheet } = innerState.detailData;
        const allowViewMedicinePrice = !!innerState.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeeGoodsCostPrice;
        const result = await showBottomPanel<
            | {
                  chargeSheet: ChargeInvoiceDetailData;
                  itemUnitPriceChanged: boolean; //修改了单项单价
                  itemTotalPriceChanged: boolean; //修改了单项总价
                  totalPriceChanged: boolean; //总价
              }
            | undefined
        >(
            <SingleBargainDialog
                detailData={innerState.detailData}
                editable={canEditChargeSheet}
                allowViewMedicinePrice={allowViewMedicinePrice}
            />,
            {
                topMaskHeight: Sizes.dp160,
            }
        );

        if (result != undefined && canEditChargeSheet) {
            // 不能直接赋值，因为收费单中存在有反选的项，而整单议价中只展示了选中的项，需要比对chargeForm中id相同的项以及子项chargeFormItems中id相同的项
            const originalForms = innerState.detailData.chargeForms ?? [];
            const bargainedForms = result.chargeSheet.chargeForms ?? [];

            // 遍历原始表单，更新议价后的数据
            originalForms.forEach((originalForm) => {
                // 找到对应的议价后的表单
                const matchedForm = bargainedForms.find((form) => form.id === originalForm.id);
                if (!matchedForm) return;

                // 遍历原始表单项，更新议价后的数据
                originalForm.chargeFormItems?.forEach((originalItem) => {
                    // 找到对应的议价后的表单项
                    const matchedItem = matchedForm.chargeFormItems?.find((item) => item.id === originalItem.id);
                    if (!matchedItem) return;

                    // 更新议价后的数据（保留原有项的其他属性）
                    Object.assign(originalItem, matchedItem);
                });
            });

            // 更新收费单数据，但保留原有的未议价项
            innerState.detailData = JsonMapper.deserialize(ChargeInvoiceDetailData, {
                ...result.chargeSheet,
                chargeForms: originalForms,
            });
            if (result.itemUnitPriceChanged || result.itemTotalPriceChanged) yield this._medicineChanged(false);
            else yield this._setHasChanged(false);
        }
    }

    private async *_mapEventUpdateDoctor(event: _EventUpdateDoctor) {
        const info = event.formItem.productInfo as ChargeRegistrationInfo;
        info.doctorName = event.doctor.doctorName;
        info.doctorId = event.doctor.doctorId;
        event.formItem.sourceUnitPrice = event.doctor.fee;
        event.formItem.registrationLocalModified = true;
        LogUtils.d(`_mapEventUpdateDoctor, unitPrice = ${event.formItem.unitPrice}`);
        yield this._setHasChanged();
    }

    private async *_mapEventChangeFocusMedicineItem(event: _EventChangeFocusMedicineItem) {
        LogUtils.i("_mapEventChangeFocusMedicineItem");
        const _innerState = this.innerState;
        if (event.focus && event.formItem == _innerState.focusMedicineItem) return;
        if (event.focus) {
            _innerState.focusMedicineItem = event.formItem;
        } else if (_innerState.focusMedicineItem == event.formItem) {
            _innerState.focusMedicineItem = undefined;
        }

        this._removeAllZeroCountMedicine();
        yield _innerState.clone();
    }

    private async *_mapEventDeleteMedicine(event: _EventDeleteMedicine) {
        const innerState = this.innerState;
        if (event.chargeFormItem == innerState.focusMedicineItem) innerState.focusMedicineItem = undefined;

        const chargeForm = innerState.detailData.chargeForms?.find((form) =>
            form.chargeFormItems?.find((item) => event.chargeFormItem.compareKey() == item.compareKey())
        );

        _.remove(chargeForm!.chargeFormItems!, (item) => event.chargeFormItem.compareKey() == item.compareKey());
        //如果某个form里收费项全部删除了，则同时删除该form项
        if (_.isEmpty(chargeForm?.chargeFormItems)) {
            _.remove(innerState.detailData!.chargeForms!, (item) => chargeForm!.compareKey() == item.compareKey());
        }

        yield this._medicineChanged();
    }

    private async *_mapEventMedicineCountChange(event: _EventMedicineCountChange) {
        yield this._doChangeMedicineCount(event.formItem, event.unitCount, event.unit);
    }

    /**
     * 计算中药饮片加工袋数
     * 用法用量：x日y剂，1日z次
     * 每剂煎药袋数 =(z * x) / y  若计算结果为小数，保留小数，但总袋数向上取整
     */
    async computedTisanesSack(form: ChargeForm): Promise<void> {
        const usageInfo = form?.usageInfo;
        const pharmacyType = form.pharmacyType;
        const freqName = usageInfo?.freq,
            dailyName = usageInfo?.dailyDosage,
            usageLevelName = usageInfo?.usageLevel,
            medicineStateScopeId = form.medicineStateScopeId,
            usageScopeId = form.usageScopeId,
            count = form.leftDoseCount ?? form.refundDoseCount;
        let usageLevelList: ChineseUsageItemInfo[] = [];
        const z =
            !!freqName && ChineseMedicine.freq.map((t) => t.name).includes(freqName) && freqName.indexOf("1日") > -1
                ? Number(freqName.substr(2, 1))
                : 0;
        const selDailyDosage = ChineseMedicine.dailyDosage.find((t) => t.name == dailyName);
        const x = !!selDailyDosage ? selDailyDosage?.daysCount : 0;
        const y = !!selDailyDosage ? selDailyDosage?.dosageCount : 0;
        //本地药房饮片--加工通过process
        //空中药房饮片（代煎）--加工通过chinesePrescriptionUsage用法

        // 空中药房不同的 制法会对应不同的服用量
        const UsageInfoMap = ChineseMedicineUsageInfo.GetChineseUsageParam;
        const value = UsageInfoMap(medicineStateScopeId) || UsageInfoMap(usageScopeId);
        let w = 0;
        if (value && value.usageLevel) {
            usageLevelList = value.usageLevel;
            w = usageLevelList?.find((t) => t.name == usageLevelName)?.value ?? 0;
        }
        const keliSingleBags = ((z * x) / y) * w;
        const processInfo = new ProcessInfo();
        form.usageInfo = JsonMapper.deserialize(UsageInfo, form?.usageInfo);

        const specification = form.chargeFormItems?.[0]?.goodsInfo.cMSpec == ChineseMedicineSpecType.fullNames()[1] ? 2 : 1;
        const result = await ChargeAgent.processBagCountCalculate({
            dailyDosage: dailyName,
            doseCount: count,
            freq: freqName,
            pharmacyType: pharmacyType,
            type: specification,
            usageLevel: usageLevelName,
        }).catchIgnore();
        if (!result) return;

        if (!pharmacyType) {
            processInfo.processBagUnitCount = result?.bagUnitCount;
            processInfo.totalProcessCount = result?.bagTotalCount;
        } else {
            if (medicineStateScopeId == MedicineScopeId.daiJian) {
                form.usageInfo!.processBagUnitCountDecimal = result?.bagUnitCount;
                form.usageInfo!.totalProcessCount = result?.bagTotalCount;
            }
            if (medicineStateScopeId == MedicineScopeId.keLi) {
                //袋数只能为1，2，3，4，6；如果计算为5，向下取整成4，超出6，则填6
                form.usageInfo!.processBagUnitCountDecimal = !!y
                    ? Math.ceil(keliSingleBags) > 4 && Math.ceil(keliSingleBags) < 6
                        ? 4
                        : Math.ceil(keliSingleBags) > 6
                        ? 6
                        : Math.ceil(keliSingleBags)
                    : undefined;
            }
        }
        //给本地药房加工袋数进行赋值
        const detailData = this.innerState.detailData;
        detailData?.chargeForms?.forEach((form) => {
            if (form.isDecoction) {
                if (pharmacyType != PharmacyType.air && pharmacyType != PharmacyType.virtual) {
                    form.processInfo = JsonMapper.deserialize(ProcessInfo, form?.processInfo);
                    form.processInfo.totalProcessCount = processInfo?.totalProcessCount;
                    form.processInfo.processBagUnitCount = processInfo?.processBagUnitCount;
                } else if (pharmacyType == PharmacyType.air) {
                    form.usageInfo = JsonMapper.deserialize(UsageInfo, form?.usageInfo);
                    form.usageInfo!.processBagUnitCountDecimal = processInfo?.processBagUnitCount;
                    form.usageInfo!.totalProcessCount = processInfo?.totalProcessCount;
                }
            }
        });
        this.update();
    }

    private async *_mapEventChangeDosageCount(event: _EventChangeDosageCount) {
        const innerState = this.innerState;
        const chineseChargeForm = event.chargeForm ?? innerState.detailData?.chargeForms?.find((form) => form.isChinesePrescription);
        event.chargeForm && (event.chargeForm!.leftDoseCount = event.dosageCount);
        for (const item of chineseChargeForm!.chargeFormItems!) {
            if (item.isDecoction || item.isDelivery || item.isIngredient) {
                item.doseCount = 1;
                continue;
            }
            item.expectedDoseCount = event.dosageCount;
        }
        if (event.chargeForm) this.computedTisanesSack(event.chargeForm);
        yield this._medicineChanged();
    }

    private async *_mapEventUpdateDeliveryInfo(event: _EventUpdateDeliveryInfo) {
        const _innerState = this.innerState;
        if (event.chargeForm) {
            ChargeUtils.setDeliveryInfoWithChargeForm(event.chargeForm, true, event.deliveryInfo);
            _innerState.hasChanged = true;
        } else {
            ChargeUtils.setDeliveryInfo(_innerState.detailData, _innerState.detailData.deliveryType__ == 1, event.deliveryInfo);
            if (_innerState.detailData.status != ChargeStatus.unCharged) {
                //已收费单子，允许修改快递地址
                const loadingDialog = new LoadingDialog("正在保存");
                loadingDialog.show();
                try {
                    await ChargeAgent.saveChargeInvoice(_innerState.detailData!);
                    await loadingDialog.success("保存成功");
                } catch (e) {
                    await loadingDialog.fail(`保存失败：${errorToStr(e)}`);
                }
            } else {
                _innerState.hasChanged = true;
                yield _innerState.clone();
            }
        }

        //已收费的单子也可以修改快递信息，但不应该触发费用计算了
        const status = _innerState.detailData.status ?? ChargeStatus.unCharged;
        const uncharged = status == ChargeStatus.unCharged || status == ChargeStatus.draft;
        if (uncharged) {
            this._autoFillDeliveryInfo(event.deliveryInfo);
            this._setHasChanged();
        }
    }

    private async *_mapEventSave(/*ignored: _EventSave*/) {
        const _innerState = this.innerState;
        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData)) return;

        const loadingDialog = new LoadingDialog("正在保存");
        loadingDialog.show();
        try {
            // 过滤未选中的收费项，同时过滤掉没有选中项的表单
            const detailData = _innerState.detailData;
            detailData.chargeForms = detailData.chargeForms?.filter((form) => {
                form.chargeFormItems = form.chargeFormItems?.filter((item) => item.checked);
                return !!form?.chargeFormItems?.length;
            });
            await ChargeAgent.saveChargeInvoice(_innerState.detailData!);
        } catch (e) {
            await loadingDialog.fail(`保存失败：${errorToStr(e)}`);
            return;
        }

        await loadingDialog.success("保存成功");
        this.requestReloadData();
    }

    private async *_mapEventSendChargeInvoice(/*ignored: _EventSendChargeInvoice*/) {
        const _innerState = this.innerState;
        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData)) return;

        if (!(await this._checkAirPharmacy())) return;

        if (
            !(await StockDisableCheckDialog.checkIfCanCharge(_innerState.detailData, _innerState.chargeConfig?.openWholeSheetOperateEnable))
        )
            return;

        // 判断当前患者关注与绑定微信状态信息
        const wxStatus = _innerState.patientWxPushConfig?.list?.[0].wxStatus == WxBindStatus.SUBSCRIBE_AND_BIND ?? false;
        const chargeSheetId = await ChargeUtils.pushChargeOrder(_innerState.detailData, wxStatus);
        LogUtils.i(`_mapEventSendChargeInvoice = ${chargeSheetId}`);
        if (!_.isEmpty(chargeSheetId)) {
            ABCNavigator.pop();
        }
    }

    private async *_mapEventSavePendingOrder(/*ignored: _EventSavePendingOrder*/) {
        const _innerState = this.innerState;
        if (!ChargeUtils.checkDeliveryAndDecoctionForCharge(_innerState.detailData)) return;

        const select = await showQueryDialog("确认挂单？", "挂单后，该单据会共享为所有收费员可见。");

        if (select != DialogIndex.positive) return;

        const loadingDialog = new LoadingDialog("正在保存");
        loadingDialog.show();

        let chargeSheetId: string | undefined;
        let errorStr;
        try {
            // 过滤未选中的收费项，同时过滤掉没有选中项的表单
            const detailData = _innerState.detailData;
            detailData.chargeForms = detailData.chargeForms?.filter((form) => {
                form.chargeFormItems = form.chargeFormItems?.filter((item) => item.checked);
                return !!form?.chargeFormItems?.length;
            });
            chargeSheetId = await ChargeAgent.saveDraft(detailData);
        } catch (e) {
            errorStr = errorToStr(e);
            LogUtils.e(`safeDraft failed for ${errorStr}`);
        }

        if (!_.isEmpty(chargeSheetId)) {
            _innerState.detailData.id = chargeSheetId;
            await loadingDialog.success("挂单成功");
            ABCNavigator.pop();
        } else {
            await loadingDialog.fail(`挂单失败:${errorStr}`);
        }
    }

    private async *_mapEventToggleDecoctionDelivery(event: _EventToggleDecoctionDelivery) {
        const chargeData = this.innerState.detailData;
        let hasChanged = false;
        if (event.decoction !== undefined) {
            hasChanged = await ChargeUtils.setDecoctionSwitch(chargeData, event.decoction, this.innerState.isOpenTakeMedicine);
            // 更新库房剩余库存
            if (hasChanged) {
                ChargeUtils.updateChargeChineseFormPharmacyWithProcess(chargeData, this.innerState.pharmacyInfoConfig);
            }
        }

        if (event.delivery !== undefined) {
            if (await ChargeUtils.setDeliverySwitch(chargeData, event.delivery)) {
                hasChanged = true;
            }
        }

        yield this.innerState.clone();
        if (!hasChanged) return;

        if (event.delivery) {
            this._autoFillDeliveryInfo(chargeData.deliveryInfoFromChargeForm);
        }
        this.innerState.hasChanged = hasChanged;
        this._triggerCalculatePay.next(0);
    }

    private async *_mapEventUpdateDecoctionInfo(event: _EventUpdateDecoctionInfo) {
        ChargeUtils.setDecoctionInfo(this.innerState.detailData!, event.contactMobile, event.decoctionForms);
        this.innerState.detailData!.markPatientPointsInfoChecked(false);
        yield this._setHasChanged();
    }

    private async *_mapEventUpdateComment(event: _EventUpdateComment) {
        const innerState = this.innerState;
        event.chargeForm.medicalRecord = event.chargeForm.medicalRecord ?? new MedicalRecord();
        event.chargeForm.medicalRecord!.comment = event.comment;
        yield innerState.clone();
    }

    private async *_mapEventChangeChiefComplaint(event: _EventChangeChiefComplaint): AsyncGenerator<State> {
        const medicalRecord = (event.chargeForm.medicalRecord = event.chargeForm.medicalRecord ?? {});
        medicalRecord.chiefComplaint = event.chiefComplaint;
        yield this.innerState.clone();
    }

    private async *_mapEventChangeDiagnosis(event: _EventChangeDiagnosis): AsyncGenerator<State> {
        const medicalRecord = (event.chargeForm.medicalRecord = event.chargeForm.medicalRecord ?? {});
        medicalRecord.diagnosis = event.diagnosis;
        yield this.innerState.clone();
    }

    private async *_mapEventChangeDoctor(event: _EventChangeChargeDoctor): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        detailData.doctorId = event.doctor?.doctorId;
        detailData.doctorName = event.doctor?.doctorName;
        detailData.departmentId = event.department?.departmentId;
        detailData.departmentName = event.department?.departmentName;

        yield this.innerState.clone();
    }

    private async *_mapEventCloseChargeSheet(/*event:_EventCloseChargeSheet*/): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        await ChargeUtils.closeChargeSheetWithUIAsk(detailData.id!, detailData.isNetworkDraft);
    }

    private async *_mapEventReOpenToCharge(/*event:_EventReOpenToCharge*/): AsyncGenerator<State> {
        const select = await showQueryDialog("", "收费单已关闭。是否确定打开该收费单重新收费");
        if (select !== DialogIndex.positive) return;
        const detailData = await ChargeAgent.putChargeClosedBillOpen(this.innerState.detailData.id!).catchIgnore();
        if (!detailData) return;
        const innerState = this.innerState;
        innerState.detailData = detailData;
        innerState.detailData.status = ChargeStatus.unCharged;
        const type = innerState.detailData.type;
        if (type == ChargeInvoiceType.retail || type == ChargeInvoiceType.therapy) {
            const cloneData = JsonMapper.deserialize(ChargeInvoiceDetailData, innerState.detailData!);
            cloneData.reChargeFromClosedChargeSheet__ = true;
            cloneData.isClosed = 0;
            ABCNavigator.navigateToPage(<DirectChargeInvoicePage detailData={cloneData} />).then();
            return;
        }

        this.innerState.detailData.reChargeFromClosedChargeSheet__ = true;
        this.innerState.detailData.isClosed = 0;
        this._updateLoadDetailDataStatus({
            loading: false,
            detailData: this._innerState.detailData,
        });
    }

    async *_mapEventModifyDecoctionInfo(event: _EventModifyDecoctionInfo): AsyncGenerator<State> {
        const decoctionInfo = event.chargeForm.chargeFormItems?.find((item) => item.productType == event.decoctionInfo.productType);
        if (decoctionInfo) {
            decoctionInfo.totalPrice = event.decoctionInfo.totalPrice;
            this.innerState.hasChanged = true;
            yield this._setHasChanged();
        }
    }

    private async *_mapEventUpdate(event: _EventUpdate) {
        yield event.state ?? this.innerState.clone();
    }

    private async *_mapEventPatientCardPromotion(event: _EventPatientCardPromotion): AsyncGenerator<State> {
        this.innerState.detailData.patientCardPromotions = event.patientCardPromotions;
        this._triggerCalculatePay.next(0);
    }

    @actionEvent(_EventUpdateServiceDeductCollect)
    async *_mapEventUpdateServiceDeductCollect(event: _EventUpdateServiceDeductCollect): AsyncGenerator<State> {
        this.innerState.detailData.patientCardPromotions = event?.serviceDeductPromotion?.patientCardPromotions;
        this.innerState.detailData.patientPointDeductProductPromotions = event?.serviceDeductPromotion?.patientPointDeductProductPromotions;
        this._triggerCalculatePay.next(0);
    }

    async *_mapEventHandleChargeAbnormal(event: _EventHandleChargeAbnormal): AsyncGenerator<State> {
        this.innerState.abnormalList = [];
        //如果是社保异常，只做弹窗提示，操作需要到PC端
        if (event.isShebaoAbnormal) {
            ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_fail",
                title: "处理异常",
                content: "请前往电脑客户端，进入『收费』\n" + "找到当前收费单进行处理",
            });
        } else {
            //如果是非社保异常，需要根据收费单id查询异常列表
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(this.innerState.detailData?.id ?? "").catchIgnore();
            if (_.isEmpty(this.innerState.abnormalList)) return;
            showBottomPanel(<ChargeAbnormalDialog bloc={this} />, { topMaskHeight: Sizes.dp160 });
        }
        this.update();
    }

    async *_mapEventAbnormalRefund(event: _EventAbnormalRefund): AsyncGenerator<State> {
        if (!event.abnormalMsg?.chargeSheetId && !event.abnormalMsg?.id) return;
        const refundResult = await ChargeAgent.dealAbnormalRefund(event.abnormalMsg.chargeSheetId!, event.abnormalMsg.id!).catchIgnore();
        if (refundResult?.status == 20) {
            await ChargeRefundDialog.showConfirmPopup({
                logo: "image_dlg_success",
                title: refundResult?.statusName ?? "退费成功",
                content: `已收费用 (${abcI18Next.t("¥")}${ABCUtils.formatPrice(event.abnormalMsg.amount ?? 0)})已原路退回至患者${
                    event.abnormalMsg.payModeDisplayName
                }账户`,
            });
            //退款成功后，再次查询异常列表，如果还有，继续退费
            this.innerState.abnormalList = await ChargeAgent.getListAbnormaltransaction(refundResult.chargeSheetId ?? "").catchIgnore();
            this.chargeId = !!refundResult.chargeSheetId ? refundResult.chargeSheetId : this.chargeId;
            this._triggerLoadDetailData.next(0); // 刷新详情
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeDraft(this.innerState.detailData)); // 刷新列表
            if (_.isEmpty(this.innerState.abnormalList)) {
                ABCNavigator.pop();
            }
        } else {
            await ChargeRefundDialog.showQueryPopup({
                logo: "image_dlg_fail",
                title: "退费失败",
                content: refundResult?.statusName ?? "该收费单已有一笔收费正在进行中，请确定是否终止",
            });
            this.chargeId = !!refundResult?.chargeSheetId ? refundResult.chargeSheetId : this.chargeId;
            this._triggerLoadDetailData.next(0);
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventRefund(this.innerState.detailData));
            ABCNavigator.pop();
        }
        this.update();
    }

    @actionEvent(_EventHospitalDetail)
    async *_mapEventHospitalDetail(event: _EventHospitalDetail): AsyncGenerator<State> {
        this.innerState.hospitalDetail = event?.detail;
        this.update();
    }

    @actionEvent(_EventRePayment)
    private async *_mapEventRePayment(): AsyncGenerator<State> {
        //获取当前还款的单据
        const { detailData } = this.innerState;
        const patientId = detailData?.patientId ?? detailData.patient?.id ?? "";
        if (!patientId) return;
        //长护单中不允许App进行还款
        if (detailData.isHospitalizationSheet) {
            return Toast.show("长护单据请前往客户端进行还款", { warning: true });
        }
        const result = await ChargeAgent.getOweStayPaidList(patientId).catchIgnore();
        const oweList = result?.chargeOweSheets;
        if (!oweList) return;
        let repaymentList: ChargeOweSheets[] = [];
        // 默认选中当前收费单欠费项
        oweList.map((item) => (item.checked__ = item.chargeSheetId == this.chargeId));
        let isSingleOweCharge = true; //是否为单个欠费还款单（决定还款金额是否可以更改）
        //1、单个欠费单还款，支持多次还款；
        //2、多个收费单批量还款时，只支持选中的收费单全部一次还清，不支持医保收费方式(当前收费单欠费的项默认选中，且不可编辑，当只选择了一项时，还款走单个欠费逻辑)
        if (oweList.length > 1) {
            const result: ChargeOweSheets[] = await showBottomPanel(
                <ChargeRepaymentDialog oweList={oweList} chargeSheetId={this.chargeId} />,
                { topMaskHeight: Sizes.dp160 }
            );
            if (_.isEmpty(result)) {
                return;
            } //没有勾选还款项
            repaymentList = result;
        } else {
            repaymentList = oweList;
        }
        isSingleOweCharge = repaymentList.length == 1;
        let receivableFee = 0;
        //欠费列表应收的金额
        repaymentList.forEach((item) => {
            receivableFee += item.needPay ?? 0;
        });

        const payData = new ChargePayData();
        payData.directCharge = true;
        payData.chargeInvoiceDetailData = detailData;

        const selects = new AbcMap<ChargeForm, ChargeFormItem[]>();
        for (const chargeForm of detailData!.chargeForms!) {
            if (chargeForm.chargeFormItems != null) {
                selects.set(chargeForm, chargeForm.chargeFormItems);
            }
        }

        payData.reCharge = detailData!.reCharge__ ?? false;
        payData.selects = selects;
        payData.receivableFee = receivableFee;
        payData.promotions = detailData!.promotions;

        payData.memberId = detailData!.memberId;
        await ABCNavigator.navigateToPage(
            <ChargePayPage
                chargePayData={payData}
                isRepayment={true}
                repaymentList={repaymentList}
                isSingleOweCharge={isSingleOweCharge}
                successChangeCallback={() => {
                    ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeDraft(detailData)); // 刷新列表
                    ABCNavigator.popUntil(URLProtocols.CHARGE_TAB, URLProtocols.CHARGE_TAB).then();
                }}
            />
        );
    }

    @actionEvent(_EventChangeMedicineState)
    async *_mapEventChangeMedicineState(event: _EventChangeMedicineState): AsyncGenerator<State> {
        const usageList = event.medicineStateList as MedicalState[];
        const options = usageList?.map((item) => item.medicalStatName);
        const initIndex = usageList!.findIndex((i) => i.medicalStatId == event.medicineStateId);
        const dosageCount =
            event.chargeForm?.chargeFormItems?.find((item) => item.isChineseMedicine)?.doseCount ?? event.chargeForm?.doseCount__;
        const usage = event.chargeForm?.usageInfo;
        const prescriptionUsage = JsonMapper.deserialize(ChinesePrescriptionUsage, {
            specificationType: ChineseMedicineSpecType.typeFromName(usage?.specification ?? ""),
            count: dosageCount,
            freq: JsonMapper.deserialize(ChineseUsageItemInfo, { name: usage?.freq }),
            usage: JsonMapper.deserialize(ChineseUsageItemInfo, { name: usage?.usage }),
            dailyDosage: JsonMapper.deserialize(ChineseUsageItemInfo, { name: usage?.dailyDosage }),
            usageDays: JsonMapper.deserialize(ChineseUsageItemInfo, { name: usage?.usageDays }),
            usageLevel: JsonMapper.deserialize(ChineseUsageItemInfo, { name: usage?.usageLevel }),
            medicineStateScopeId: event.medicineStateId,
            usageScopeId: event.chargeForm.usageScopeId,
        });
        if (
            event.medicineStateId == MedicineScopeId.keLi ||
            event.medicineStateId == MedicineScopeId.daiJian ||
            event.medicineStateId == MedicineScopeId.zhiJian
        ) {
            const info = await showBottomPanel<ChineseAirPharmacyBagsParam>(
                <ChineseAirPharmacyBagsDialog
                    options={options}
                    initIndex={initIndex}
                    chinesePrescriptionUsage={prescriptionUsage}
                    medicalStateList={usageList}
                    canChangeMedicineState={false}
                />,
                {
                    topMaskHeight: pxToDp(312),
                }
            );
            if (_.isUndefined(info)) return;
            event.chargeForm.medicineStateScopeId = usageList![info.selectIndex!].medicalStatId;
            event.chargeForm.usageInfo!.processBagUnitCountDecimal = info.processBagUnitCount;
            event.chargeForm.usageInfo!.totalProcessCount = info.totalProcessCount;
            event.chargeForm.usageInfo!.processRemark = info.processRemark;
        } else {
            const result = await AbcDialog.showOptionsBottomSheet({
                title: "药态",
                options: options,
                initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            });
            if (!result || !result.length) return;
            event.chargeForm.medicineStateScopeId = usageList![result[0]].medicalStatId;
            event.chargeForm.medicineStateScopeName = usageList![result[0]].medicalStatName;
        }

        event.chargeForm.fixAirPharmacyPrescriptionInfo();
        this.innerState.hasChanged = true;
        yield this._medicineChanged();
    }

    @actionEvent(_EventChangePharmacyType)
    async *_mapEventChangePharmacyType(event: _EventChangePharmacyType): AsyncGenerator<State> {
        const { pharmacyInfoConfig, detailData } = this.innerState;
        if (!pharmacyInfoConfig?.filterNormalPharmacyList?.length) return;
        let initGoodsTypeId = undefined,
            pharmacyNo: number | undefined;
        //处方类型
        //药房号：中药处方的药房取form上的，其他处方的取item上的
        pharmacyNo = event?.formItem?.pharmacyNo;
        if (event.chargeForm.isChinesePrescription) {
            initGoodsTypeId =
                event.chargeForm?.specification == ChineseMedicineSpecType.fullNames()[1]
                    ? GoodsTypeId.medicineChineseGranule
                    : GoodsTypeId.medicineChinesePiece;
            pharmacyNo = event.chargeForm?.pharmacyNo;
        } else if (event.chargeForm.isInfusionPrescription || event.chargeForm.isWesternPrescription) {
            initGoodsTypeId = GoodsTypeId.medicineWest;
        } else if (event.chargeForm.isGoods) {
            initGoodsTypeId = GoodsTypeId.goods;
        } else if (event.chargeForm.isMaterial) {
            initGoodsTypeId = GoodsTypeId.material;
        }
        const filterExistPharmacy = pharmacyInfoConfig?.filterNormalPharmacyList?.find((pharmacy) => pharmacy.no == pharmacyNo);
        const defaultPharmacy = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: detailData?.departmentId,
            goodsInfo: {
                typeId: event?.formItem?.goodsInfo?.typeId ?? initGoodsTypeId,
                customTypeId: event?.formItem?.goodsInfo?.customTypeId,
            },
        });
        const selectPharmacyInfo = !!filterExistPharmacy ? filterExistPharmacy : defaultPharmacy;
        const initIndex = pharmacyInfoConfig?.filterNormalPharmacyList?.findIndex((t) => t.no == selectPharmacyInfo?.no);

        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "药品来源",
            options: pharmacyInfoConfig?.filterNormalPharmacyList.map(
                (item) => `${item.name}${!event.chargeForm.isChinesePrescription ? (defaultPharmacy?.no == item.no ? "(默认)" : "") : ""}`
            ),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 2,
            height: pxToDp(375),
        });
        if (!selectIndex || !selectIndex.length) return;
        const result = pharmacyInfoConfig?.filterNormalPharmacyList[selectIndex[0]];
        if (event.chargeForm.isChinesePrescription) {
            event.chargeForm.handleChangePharmacy(result);
        } else {
            //其他处方，只修改当前药品的药房信息
            event.formItem!.pharmacyNo = result?.no;
            event.formItem!.pharmacyType = result?.type;
            event.formItem?.resetStockByPharmacyNo();
        }
        this.update();
    }

    @actionEvent(_EventUpdateRemark)
    async *_mapEventUpdateRemark(event: _EventUpdateRemark): AsyncGenerator<State> {
        const type = event.sourceFormType;
        let isRemarkField = true;
        if (type == ChargeSourceFormType.infusionPrescription || type == ChargeSourceFormType.westernPrescription) {
            isRemarkField = false;
        }
        const _value = await MedicineUsagePicker.showCustomUsageDialog({
            title: "备注",
            value: isRemarkField ? event.formItem?.remark : event.formItem?.specialRequirement,
            enableDefaultToolBar: false,
            maxLength: 200,
        });
        if (_.isUndefined(_value)) return;
        if (isRemarkField) {
            event.formItem!.remark = _value?.name;
        } else {
            event.formItem!.specialRequirement = _value?.name;
        }
        this.update();
    }

    @actionEvent(_EventDeleteRemark)
    async *_mapEventDeleteRemark(event: _EventDeleteRemark): AsyncGenerator<State> {
        const type = event.sourceFormType;
        let isRemarkField = true;
        if (type == ChargeSourceFormType.infusionPrescription || type == ChargeSourceFormType.westernPrescription) {
            isRemarkField = false;
        }
        if (isRemarkField) {
            event.formItem!.remark = undefined;
        } else {
            event.formItem!.specialRequirement = undefined;
        }
        this.update();
    }

    @actionEvent(_EventModifyConsultant)
    async *_mapEventModifyConsultant(event: _EventModifyConsultant): AsyncGenerator<State> {
        const index = event.index;
        if (isNil(index)) return;
        this.innerState.detailData.consultantId = this.innerState.consultantList?.[index]?.employeeId;
        this.innerState.detailData.consultantName = this.innerState.consultantList?.[index]?.employeeName;
        this.update();
    }

    private _setFocusErrorView(focusErrorViewKey: string): void {
        this.innerState.showErrorHint = true;
        this.innerState.focusErrorViewKey = focusErrorViewKey;
        this.update(ScrollToErrorViewState.fromState(this.innerState));
    }

    private _doChangeMedicineCount(formItem: ChargeFormItem, unitCount?: number, unit?: string): State {
        if (unitCount != unit) {
            formItem.unitCount = unitCount;
            if (formItem.expectedTotalPrice != null) {
                formItem.expectedTotalPrice = undefined;
                formItem.expectedUnitPrice = formItem.unitPrice;
            }
        }

        if (unit != undefined) {
            const info = formItem.productInfo as GoodsInfo;
            formItem.unit = unit;
            if (unit == info.packageUnit) {
                formItem.unitPrice = info.packagePrice;
                formItem.useDismounting = 0;
            } else {
                formItem.unitPrice = info.piecePrice;
                formItem.useDismounting = 1;
            }
        }

        return this._medicineChanged();
    }

    //选中所有本地添加的项目
    private _selectAllLocalAddItems() {
        if (this.innerState.detailData.chargeForms == undefined) return;
        for (const chargeForm of this.innerState.detailData.chargeForms) {
            if (_.isEmpty(chargeForm.chargeFormItems)) continue;

            for (const formItem of chargeForm.chargeFormItems!) {
                if (formItem.localAdd ?? false) {
                    this._selectChargeItem(chargeForm, formItem);
                }
            }
        }
    }

    _removeAllZeroCountMedicine(): boolean {
        LogUtils.i("_removeAllZeroCountMedicine");
        const innerState = this.innerState;
        const chargeForms = innerState.detailData.chargeForms;
        if (!chargeForms || chargeForms.length === 0) return false;
        let hasRemovedItem = false;
        for (const form of chargeForms) {
            //空中药房暂时不删除为0的项
            if (form.isAirPharmacy) continue;
            const length = form.chargeFormItems?.length;
            form.chargeFormItems = form.chargeFormItems?.filter((item) => item.unitCount! * item.doseCount! > 0);

            hasRemovedItem = hasRemovedItem || length != form.chargeFormItems?.length;
        }

        const length = chargeForms.length;
        for (let i = length - 1; i >= 0; --i) {
            const form = chargeForms[i];
            if (!_.isEmpty(form.chargeFormItems)) continue;
            chargeForms.splice(i, 1);
        }

        const hasChanged = hasRemovedItem || length != chargeForms.length;
        if (hasChanged) {
            this._medicineChanged();
        }

        return hasChanged;
    }

    private _updateLoadDetailDataStatus(options: { loading?: boolean; error?: any; detailData?: ChargeInvoiceDetailData }) {
        const { loading, error, detailData } = options;
        const _innerState = this.innerState;
        _innerState.loadingDetailData = loading ?? false;
        _innerState.loadingDetailError = error;

        if (detailData) {
            if (detailData.outpatientStatus == ChargeInvoiceData.outpatientStatusWaiting) {
                if (!this.editViewBloc) {
                    this.editViewBloc = new ChargeInvoiceEditViewBloc();
                    this.addDisposable(this.editViewBloc);
                    this.editViewBloc.requestUpdateDetailData(detailData);
                    this.editViewBloc.state.subscribe((state) => {
                        if (state instanceof ChargeInvoiceDetailDataChangedState) {
                            this._triggerCalculatePay.next(0);
                        } else {
                            this.update();
                        }
                    });
                }
            }
            _innerState.detailData = detailData;
            _innerState.detailData.deliveryInfo = _innerState.detailData.deliveryInfo ?? detailData.deliveryInfoFromChargeForm;
            _innerState.hasChanged = false;

            // 检查是否开启了整单收退费开关，如果开启则自动选择所有项目
            if (_innerState.chargeConfig?.openWholeSheetOperateEnable) {
                // 自动选择所有项目
                _innerState.detailData.chargeForms?.forEach((form) => {
                    form.chargeFormItems?.forEach((item) => {
                        item.checked = true;
                    });
                });
            }
            const registrationForm = detailData.chargeForms?.find((item) => item.isRegistration);
            if (registrationForm && !_.isEmpty(registrationForm.chargeFormItems)) {
                const registrationFormItem = _.first(registrationForm.chargeFormItems);
                if (
                    registrationFormItem!.productInfo instanceof ChargeRegistrationInfo &&
                    _innerState.detailData.status == ChargeStatus.unCharged
                ) {
                    const production = registrationFormItem!.productInfo;
                    _innerState.departmentMutable = _.isEmpty(production.departmentId) && _.isEmpty(production.doctorId);
                    _innerState.doctorMutable = _.isEmpty(production.doctorId);
                }
            }

            if (_innerState.detailData) {
                if (detailData.chargeForms) {
                    for (const form of detailData.chargeForms) {
                        if (form.chargeFormItems) {
                            for (const formItem of form.chargeFormItems) {
                                //              if (!formItem.canReCharge && detailData.reCharge__) continue;
                                //库存足时，可以进行收费
                                // if (formItem.stockInfo().stockEnough) this._selectChargeItem(form, formItem);
                                // else this._deselectChargeItem(form, formItem);
                                this._selectChargeItem(form, formItem);
                                // 在收费单一次都没有收过费的情况下，收费子项状态为已退单，则默认不选中
                                if (detailData.status == ChargeStatus.unCharged) {
                                    if (formItem.status == ChargeFormItemStatus.chargeBack) {
                                        formItem.checked = false;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (_innerState.detailData.status == ChargeStatus.unCharged) this._triggerCalculatePay.next(0);
        }

        this.update();
    }

    private _deselectChargeItem(ignore: ChargeForm, item: ChargeFormItem) {
        item.checked = false;
    }

    private _selectChargeItem(ignore: ChargeForm, item: ChargeFormItem) {
        item.checked = true;
    }

    private async _toggleChargeItem(chargeForm: ChargeForm, chargeFormItem: ChargeFormItem): Promise<void> {
        if (this.innerState.isChargeItemSelected(chargeForm, chargeFormItem)) {
            if (!(await ChargeItemModifyUtils.checkDeleteChargeItems([chargeFormItem], undefined, undefined, "是否确认退单"))) return;
            this._deselectChargeItem(chargeForm, chargeFormItem);
        } else {
            this._selectChargeItem(chargeForm, chargeFormItem);
        }
    }

    private _setHasChanged(clearAdjustmentFee = true): State {
        const innerState = this.innerState;
        innerState.hasChanged = true;

        if (clearAdjustmentFee && innerState.detailData.chargeSheetSummary) {
            innerState.detailData.chargeSheetSummary.expectedTotalPrice__ = undefined;
        }

        this._triggerCalculatePay.next(0); //有新项加入重新计算费用
        return innerState.clone();
    }

    private _medicineChanged(clearAdjustmentFee = true): State {
        this.innerState.detailData.markPatientPointsInfoChecked(false);
        return this._setHasChanged(clearAdjustmentFee);
    }

    //操作禁止提示
    private showOperateToastTip(): void {
        const { isDisabledOperate, disabledOperateReason } = this.innerState.detailData;
        if (!!isDisabledOperate && !!disabledOperateReason) Toast.show(disabledOperateReason, { warning: true });
    }

    requestModifyProductEmployee(): void {
        this.dispatch(new _EventModifyProductEmployee());
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }

    //点击一收费项，反转状态
    public requestToggleChargeItem(chargeForm: ChargeForm, item: ChargeFormItem): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit || item.isSelfProvided) return;
        this.dispatch(new _EventToggleChargeItem(chargeForm, item));
    }

    //点击重试
    public requestReloadData(): void {
        this._triggerLoadDetailData.next(0);
    }

    public requestCalculatePrice(): void {
        this._triggerCalculatePay.next(0);
    }

    //更换会员卡
    public requestUpdateMemberCard(member: SelectMemberInfo): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventUpdateMemberCard(member));
    }

    //更新优惠项
    public requestUpdatePromotions(
        selectPromotions?: number[],
        selectGiftPromotions?: number[],
        selectCouponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>,
        selectMemberPointPromotion?: PatientPointsInfo
    ): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(
            new _EventUpdatePromotions(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion)
        );
    }

    public requestCancelAdjustmentFee(): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventCancelAdjustmentFee());
    }

    //点击收费按钮
    public requestCharge(isCharge: boolean): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventChargeTap(isCharge));
    }

    //请求退费
    public requestRefund(): void {
        this.showOperateToastTip();
        // if (!this.judgeHasNeedRefund()) {
        //     showQueryDialog("", "已发出的药品，需要在药房完成退药后才能退费");
        //     return;
        // }
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventRefundTap());
    }

    // [判断是否有欠退金额/可退药/议价费用]
    public judgeHasNeedRefund(): boolean {
        const { detailData } = this.innerState;
        const { chargeSheetSummary, chargeForms } = detailData;
        // 判断是否有欠退金额
        if (chargeSheetSummary?.owedRefundFee !== 0) {
            return true;
        }
        // 判断是否有议价（加价）未退
        if ((chargeSheetSummary?.netAdjustmentFee ?? 0) > 0) {
            return true;
        }
        // 当全部为已退单时，可退费
        let outAll = false;
        // 判断是否有可退药
        let hasDrug = false;
        chargeForms?.forEach((form) => {
            form?.chargeFormItems?.forEach((item) => {
                if ((item.status === 1 || item.status === 4) && (item?.canRefundUnitCount ?? 0) > 0) {
                    hasDrug = true;
                }
                if (item.status !== 3 && outAll === false) {
                    outAll = true;
                }
            });
        });
        if (outAll) {
            return hasDrug;
        }
        return true;
    }

    //点击重新收费
    public requestReCharge(): void {
        this.showOperateToastTip();
        // if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventReChargeTap());
    }

    //添加新收费项
    public requestAddNewChargeItem(): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventAddNewChargeItem());
    }

    //单项议价
    public requestSingleBargain(): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventSingleBargainTap());
    }

    //修改收费项的数量和单位
    public requestUpdateChargeItemAmount(item: ChargeFormItem, count?: number, unit?: string): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventUpdateChargeItemAmount(item, count, unit));
    }

    //修改医生
    public requestUpdateDoctor(formItem: ChargeFormItem, department: Department, doctor: Doctor): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventUpdateDoctor(formItem, department, doctor));
    }

    public requestChangeFocusMedicineItem(chargeFormItem: ChargeFormItem, focus: boolean): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventChangeFocusMedicineItem(chargeFormItem, focus));
    }

    //修改中药剂数
    public requestChangeDosageCount(chargeForm: ChargeForm | undefined, dosageCount: number): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventChangeDosageCount(chargeForm, dosageCount));
    }

    public requestSave(): void {
        this.showOperateToastTip();
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventSave());
    }

    public requestUpdateDeliveryInfo(deliveryInfo?: DeliveryInfo, chargeForm?: ChargeForm): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventUpdateDeliveryInfo(deliveryInfo, chargeForm));
    }

    public requestSendChargeInvoice(): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventSendChargeInvoice());
    }

    public requestSavePendingOrder(): void {
        this.dispatch(new _EventSavePendingOrder());
    }

    public requestToggleDecoctionDelivery(decoction?: boolean, delivery?: boolean): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventToggleDecoctionDelivery(decoction, delivery));
    }

    public requestUpdateDecoctionInfo(contactMobile?: string, decoctionForms?: ChargeForm[]): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventUpdateDecoctionInfo(contactMobile, decoctionForms));
    }

    //更新form单的备注信息
    public requestUpdateComment(chargeForm: ChargeForm, value: string): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventUpdateComment(chargeForm, value));
    }

    private _autoFillDeliveryInfo(deliveryInfo?: DeliveryInfo) {
        ChargeUtils.autoFillAirPharmacyDeliveryInfo(this.innerState.detailData.chargeForms, deliveryInfo, true).then((needUpdate) => {
            if (needUpdate) this._triggerCalculatePay.next(0);
        });
    }

    public requestChangeChiefComplaint(chargeForm: ChargeForm, chiefComplaint: string): void {
        this.dispatch(new _EventChangeChiefComplaint(chargeForm, chiefComplaint));
    }

    public requestChangeDiagnosis(chargeForm: ChargeForm, diagnosis: string): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventChangeDiagnosis(chargeForm, diagnosis));
    }

    public requestChangeDoctor(doctor?: Doctor, department?: Department): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventChangeChargeDoctor(doctor, department));
    }

    public requestCloseChargeSheet(): void {
        this.dispatch(new _EventCloseChargeSheet());
    }

    //重新打开收费
    public requestReOpenToCharge(): void {
        this.showOperateToastTip();
        this.dispatch(new _EventReOpenToCharge());
    }

    public requestModifyDecoctionInfo(chargeForm: ChargeForm, decoctionInfo: ChargeFormItem): void {
        this.showOperateToastTip();
        if (!this.innerState.detailData.canEditChargeSheet) return;
        this.dispatch(new _EventModifyDecoctionInfo(chargeForm, decoctionInfo));
    }

    public requestPatientCardPromotionChanged(cardPromotions: PatientCardPromotion[]): void {
        this.dispatch(new _EventPatientCardPromotion(cardPromotions));
    }

    requestUpdateServiceDeductCollect(serviceDeductPromotion?: ServiceDeductCollection): void {
        this.dispatch(new _EventUpdateServiceDeductCollect(serviceDeductPromotion));
    }

    //根据异常类型，做相应的处理
    requestHandleChargeAbnormal(isShebaoAbnormal: boolean): void {
        this.dispatch(new _EventHandleChargeAbnormal(isShebaoAbnormal));
    }

    //异常退费
    requestAbnormalRefund(abnormalMsg: AbnormalTransactionList): void {
        this.dispatch(new _EventAbnormalRefund(abnormalMsg));
    }

    //获取长护详情
    handleHospitalDetail(detail?: HospitalRegisterDetail): void {
        this.dispatch(new _EventHospitalDetail(detail));
    }

    //还款
    requestRePayment(): void {
        this.dispatch(new _EventRePayment());
    }

    //更新空中药房药态
    requestChangeMedicineState(
        chargeForm: ChargeForm,
        medicineStateId: MedicineScopeId,
        medicineStateName: string,
        medicineStateList?: AirPharmacyMedicineState[]
    ): void {
        this.dispatch(new _EventChangeMedicineState(chargeForm, medicineStateId, medicineStateName, medicineStateList));
    }

    //删除当前项
    requestDeleteItem(chargeFormItem: ChargeFormItem): void {
        this.dispatch(new _EventDeleteMedicine(chargeFormItem));
    }
    // 更新咨询师
    requestModifyConsultant(index?: number): void {
        this.dispatch(new _EventModifyConsultant(index));
    }

    @actionEvent(_EventModifyProductEmployee)
    private async *_mapEventModifyProductEmployee(): AsyncGenerator<State> {
        const goodsInfoList: GoodsInfo[] = [];
        const doctorList: AbcMap<string, { id?: string; name?: string; departmentId?: string }> = new AbcMap<
                string,
                { id?: string; name?: string; departmentId?: string }
            >(),
            nurseList: AbcMap<string, { id?: string; name?: string }> = new AbcMap<string, { id?: string; name?: string }>();

        this.innerState.treatmentChargeForm.map((item) => {
            goodsInfoList.push(item.goodsInfo);
            nurseList.set(item.goodsInfo.scrollKey, { id: item?.nurseId, name: item?.nurseName });
            doctorList.set(item.goodsInfo.scrollKey, {
                id: item?.doctorId,
                name: item?.doctorName,
                departmentId: item?.departmentId,
            });
        });

        const rsp = await ProductAddExecutorDialog.show({ goodsInfoList: goodsInfoList, doctorList, nurseList });

        if (!!rsp) {
            const currentDoctorList = rsp.doctorList;
            const currentNurseList = rsp.nurseList;
            const setDoctorInfo = (list?: ChargeFormItem[]) => {
                list?.forEach((chargeItem) => {
                    this.innerState.treatmentChargeForm.forEach((item) => {
                        if (chargeItem.goodsInfo.scrollKey != item.goodsInfo.scrollKey) return;
                        if (currentDoctorList.has(item.goodsInfo.scrollKey)) {
                            const currentDoctor = currentDoctorList.get(item.goodsInfo.scrollKey);
                            chargeItem!.doctorId = currentDoctor?.id;
                            chargeItem!.doctorName = currentDoctor?.name;
                            chargeItem!.departmentId = currentDoctor?.departmentId;
                            chargeItem!.departmentName = currentDoctor?.departmentName;
                        }
                        if (currentNurseList.has(item.goodsInfo.scrollKey)) {
                            const currentNurse = currentNurseList.get(item.goodsInfo.scrollKey);
                            chargeItem!.nurseId = currentNurse?.id;
                            chargeItem!.nurseName = currentNurse?.name;
                        }
                    });
                });
            };
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.examination)?.chargeFormItems);
            setDoctorInfo(this.innerState.detailData?.getChargeForm(ChargeSourceFormType.package)?.chargeFormItems);

            this.update();
        }
    }

    @actionEvent(_EventShowDiscountSummaryDetail)
    async *_mapEventShowDiscountSummaryDetail(/*event: _EventShowDiscountSummaryDetail*/): AsyncGenerator<State> {
        const { detailData } = this.innerState,
            { status, canEditChargeSheet } = detailData,
            editable = canEditChargeSheet && status == ChargeStatus.unCharged;
        await showBottomPanel<number | undefined>(
            <DiscountCardDialog
                mode={"charge"}
                detailData={detailData}
                canModify={editable}
                promotions={ChargeUtils.filterRegistrationPromotions(detailData)}
                onPromotionSelectChanged={(selectPromotions, selectGiftPromotions, selectCouponPromotions, selectMemberPointPromotion) => {
                    this.requestUpdatePromotions(
                        selectPromotions,
                        selectGiftPromotions,
                        selectCouponPromotions,
                        selectMemberPointPromotion
                    );
                }}
                onPromotionMemberCardChanged={(member) => {
                    this.requestUpdateMemberCard(member);
                }}
                onPatientCardPromotionChanged={(patientCardPromotions) => {
                    this.requestPatientCardPromotionChanged(patientCardPromotions);
                }}
            />,
            {
                topMaskHeight: pxToDp(312),
            }
        );
        this.update();
    }

    @actionEvent(_EventBack)
    async *_mapEventBack(): AsyncGenerator<State> {
        if (this.innerState.hasChanged) {
            const select = await showQueryDialog("确认退出编辑吗？", "你的修改内容还未保存，确定要离开 ？");
            if (select != DialogIndex.positive) return;
            ABCNavigator.pop();
        } else {
            ABCNavigator.pop();
        }
    }

    @actionEvent(_EventCheckExpressInfo)
    async *_mapEventCheckExpressInfo(event: _EventCheckExpressInfo): AsyncGenerator<State> {
        if (!event?.formId) return;
        const result = await ChargeAgent.getAirPharmacyLogisticsTrace(event.formId, event.vendorId).catchIgnore();
        if ((result?.traceList?.length ?? 0) == 0) return;
        const logisticTranceData: StepStatusListItem[] = [];
        //更新门诊单页面的物流信息，以防数据不同步
        const airPharmacyForm = this.innerState.detailData?.chargeForms?.filter((t) => t?.isAirPharmacy);
        airPharmacyForm?.map((item) => {
            if (item?.id == event.formId) item.deliveryInfo!.__logisticTraceList = result;
            return item;
        });
        yield this.innerState.clone();
        result?.traceList?.map((item, index) => {
            logisticTranceData.push({
                id: index,
                title: item?.context ?? "",
                complete: index == 0,
                date: item?.ftime ?? "",
            });
        });
        await AirPharmacyStepsView.show({
            data: [
                {
                    stepList: logisticTranceData,
                },
            ],
            subTitle: event?.companyInfo ?? "",
        });
        this.update();
    }

    @actionEvent(_EventChangeDiscount)
    async *_mapEventChangeDiscount(event: _EventChangeDiscount): AsyncGenerator<State> {
        event.formItem.expectedTotalPrice = undefined;
        event.formItem.expectedUnitPrice = undefined;
        event.formItem.totalPriceRatio = undefined;
        event.formItem.expectedTotalPriceRatio = (event.discount ?? 0) / 100;
        event.formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        if (this.innerState.detailData?.chargeSheetSummary) this.innerState.detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
        this._triggerCalculatePay.next(0);
        this.update();
    }

    @actionEvent(_EventChangeTotalPrice)
    async *_mapEventChangeTotalPrice(event: _EventChangeTotalPrice): AsyncGenerator<State> {
        event.formItem.expectedUnitPrice = undefined;
        event.formItem.totalPriceRatio = undefined;
        event.formItem.expectedTotalPriceRatio = undefined;
        event.formItem.totalPrice = undefined;
        event.formItem.expectedTotalPrice = event.totalPrice;
        event.formItem.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
        if (this.innerState.detailData?.chargeSheetSummary) this.innerState.detailData!.chargeSheetSummary.expectedTotalPrice__ = undefined;
        this._triggerCalculatePay.next(0);
        this.update();
    }
    handleBatchInfo(chargeForm: ChargeForm): MedicineBatchInfoList[] {
        const goodsBatchList: MedicineBatchInfoList[] = [];
        chargeForm.chargeFormItems?.forEach((item) => {
            const batchList: FormItemBatchInfos[] = [];
            item.chargeFormItemBatchInfos?.forEach((batch) => {
                batchList.push(
                    JsonMapper.deserialize(FormItemBatchInfos, {
                        batchNo: batch?.batchNo,
                        expiryDate: batch?.expiryDate,
                        unitCount: batch.unitCount,
                        totalPrice: batch.totalPrice,
                        batchId: batch.batchId,
                    })
                );
            });
            goodsBatchList.push({
                displayName: item.displayName,
                manufacturer: item?.manufacturer,
                unit: item.displayUnit,
                count: item?.unitCount,
                totalPrice: item.totalPrice,
                specifications: (item.productInfo as GoodsInfo).packageSpec,
                goodsBatchInfoList: Array.from(batchList),
            });
        });
        return goodsBatchList;
    }

    @actionEvent(_EventCheckGoodsBatchInfo)
    async *_mapEventCheckGoodsBatchInfo(event: _EventCheckGoodsBatchInfo): AsyncGenerator<State> {
        const { chargeForm } = event;
        if (!chargeForm) return;
        let goodsBatchList: MedicineBatchInfoList[] = [];
        if (chargeForm instanceof Array) {
            chargeForm?.forEach((form) => {
                goodsBatchList = this.handleBatchInfo(form);
            });
        } else {
            goodsBatchList = this.handleBatchInfo(chargeForm);
        }

        if (!goodsBatchList?.length) return;
        await showBottomPanel(<MedicineBatchInfoDialog batchList={goodsBatchList} />, {
            topMaskHeight: pxToDp(160),
        });
    }

    @actionEvent(_EventCancelPayment)
    async *_mapEventCancelPayment(): AsyncGenerator<State> {
        // 进行解锁
        const chargePayTransactionId = this.innerState.detailData?.lockPayTransactionInfo?.id;
        if (!chargePayTransactionId) return;
        const tipsInfo = {
            confirmText: "",
            errorTitle: "",
            errorContent: "",
        };
        if (this.innerState.detailData?.microclinicsOrSelfServiceMachines) {
            tipsInfo.confirmText = "您可取消本次支付，取消后患者自助支付将会失败，本单可以重新收费。";
            tipsInfo.errorTitle = "收费单内容已更新";
            tipsInfo.errorContent = "收费单内容已更新，请刷新后重试";
        } else {
            tipsInfo.confirmText = "您可取消本次支付，取消后可重新收费，本次支付不入账。";
            tipsInfo.errorTitle = "取消失败";
            tipsInfo.errorContent = "患者已完成支付，如需取消请操作退费";
        }
        const dialogIndex = await showQueryDialog(
            "支付遇到问题？",
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{tipsInfo?.confirmText}</Text>
            </View>,
            "取消本次支付",
            "暂不操作"
        );
        if (!dialogIndex) return;
        await ChargeAgent.putChargeUnlock(chargePayTransactionId)
            .then((rsp) => {
                if (rsp.code == 200) {
                    Toast.show("取消成功", { success: true });
                }
            })
            .catch((error) => {
                showConfirmDialog(tipsInfo?.errorTitle, `${error?.msg ?? tipsInfo?.errorContent}`, "知道了");
            })
            .finally(() => {
                this._triggerLoadDetailData.next(0);
            });
    }

    // 修改药房
    requestChangePharmacyType(chargeForm: ChargeForm, formItem?: ChargeFormItem): void {
        this.dispatch(new _EventChangePharmacyType(chargeForm, formItem));
    }

    //更新备注
    requestChangeRemark(sourceFormType?: ChargeSourceFormType, formItem?: ChargeFormItem): void {
        this.dispatch(new _EventUpdateRemark(sourceFormType, formItem));
    }

    //取消备注
    requestDeleteRemark(sourceFormType?: ChargeSourceFormType, formItem?: ChargeFormItem): void {
        this.dispatch(new _EventDeleteRemark(sourceFormType, formItem));
    }

    //查看当前优惠详情
    requestShowDiscountSummaryDetail(): void {
        this.dispatch(new _EventShowDiscountSummaryDetail());
    }

    requestBack(): void {
        this.dispatch(new _EventBack());
    }

    //查看空中药房物流信息
    requestCheckExpressInfo(formId: string, vendorId: string, companyInfo?: string): void {
        this.dispatch(new _EventCheckExpressInfo(formId, vendorId, companyInfo));
    }

    // 单项折扣议价
    requestChangeDiscount(formItem: ChargeFormItem, discount: number): void {
        this.dispatch(new _EventChangeDiscount(formItem, discount));
    }
    // 单项议总价
    requestChangeTotalPrice(formItem: ChargeFormItem, totalPrice: number): void {
        this.dispatch(new _EventChangeTotalPrice(formItem, totalPrice));
    }
    // 查看药品批次信息
    requestCheckGoodsBatchInfo(chargeForm: ChargeForm | ChargeForm[]): void {
        this.dispatch(new _EventCheckGoodsBatchInfo(chargeForm));
    }
    //  取消本次支付
    requestCancelPayment(): void {
        this.dispatch(new _EventCancelPayment());
    }
}

export { ChargeInvoicePageBloc, State };
