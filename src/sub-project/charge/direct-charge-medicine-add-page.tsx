/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/6/9
 *
 * @description 直接收费
 *
 */
import { DividerLine, IconFontView, SizedBox, Spacer, UniqueKey } from "../base-ui";
import React from "react";
import { DirectChargeMedicineAddPageBloc } from "./direct-charge-medicine-add-page-bloc";
import { GoodsInfo, GoodsType } from "../base-business/data/beans";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { AppSearchBar } from "../base-ui/app-bar";
import { ABCStyles, ABCStyleSheet, Color, Colors, Sizes, TextStyles } from "../theme";
import { Animation, ListView, ScrollView, Style, Text, View } from "@hippy/react";
import { BaseBlocComponent, BaseComponent } from "../base-ui/base-component";
import { ABCNetworkPageContentStatus, BaseBlocPage, NetworkView } from "../base-ui/base-page";
import _ from "lodash";
import { LogUtils } from "../common-base-module/log";
import { ABCUtils } from "../base-ui/utils/utils";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { AbcButton } from "../base-ui/views/abc-button";
import { UIUtils } from "../base-ui/utils";
import { Point } from "../base-ui/ui-events";
import { ValueHolder } from "../base-ui/utils/value-holder";
import { delayed } from "../common-base-module/rxjs-ext/rxjs-ext";
import { BottomSheetCloseButton, showBottomSheet } from "../base-ui/dialog/bottom_sheet";
import { MedicineSellCountInputButton, MedicineSellUnitSelectButton } from "./view/charge-views";
import { Const } from "../base-ui/utils/consts";
import { CloseBtn } from "../base-ui/views/close-btn";
import { CustomInput } from "../base-ui/input/custom-input";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { GroupDivider } from "../base-ui/divider-line";
import { AbcMap } from "../base-ui/utils/abc-map";
import { ChargeFormItem, ChargeFormItemStatus } from "./data/charge-beans";
import { errorToStr, UUIDGen } from "../common-base-module/utils";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { StockNotEnoughTextView } from "../base-ui/views/stock-not-enough-text-view";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { AbcView } from "../base-ui/views/abc-view";
import { userCenter } from "../user-center";
import abcI18Next from "../language/config";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";

export class DirectChargeUsage {
    unit: string;
    count: number;

    constructor(unit: string, count: number) {
        this.unit = unit;
        this.count = count;
    }
}

export class DirectChargeMedicineAddInfo {
    goods: GoodsInfo[] = [];
    inputCounts: AbcMap<GoodsInfo, DirectChargeUsage> = new AbcMap();
    dosageCount?: number;

    toChargeFormItem(goodsInfo: GoodsInfo, chargeFormItem?: ChargeFormItem): ChargeFormItem {
        const usage = this.inputCounts.get(goodsInfo);

        const formItem = chargeFormItem ?? new ChargeFormItem();

        formItem.keyId = UUIDGen.generate();
        formItem.name = goodsInfo.displayName;
        formItem.unitCount = usage!.count ?? 1;
        formItem.eqCoefficient = goodsInfo.eqCoefficient;
        formItem.unit = usage!.unit;
        formItem.unitPrice = goodsInfo.unitPriceWithUnit(usage!.unit);
        formItem.productId = goodsInfo.id;
        formItem.productType = goodsInfo.type;
        formItem.productSubType = goodsInfo.subType;
        formItem.localAdd = true; //本地添加标志
        formItem.status = ChargeFormItemStatus.unCharged;
        formItem.doseCount = goodsInfo.isChineseMedicine ? this.dosageCount : 1;
        formItem.useDismounting = goodsInfo.useDismounting(usage!.unit) ? 1 : 0;
        formItem.productInfo = goodsInfo;

        formItem.composeChildren = goodsInfo.composeChildren?.map((item) => {
            return JsonMapper.deserialize(ChargeFormItem, {
                productInfo: item,
            });
        });

        //添加formItem 的 PharmacyInfo
        formItem.pharmacyNo = goodsInfo.pharmacyNo;
        formItem.pharmacyType = goodsInfo.pharmacyType;
        formItem.pharmacyName = goodsInfo.pharmacyName;

        return formItem;
    }
}

export class DirectChargeMedicineInvokeParams {
    medicineInfo?: DirectChargeMedicineAddInfo; //原始药品信息
    goodsTypes: number[] = []; //GoodsType
    autoFocusSearch = false;

    showCategorySearchNavi = true; //是否显示分类搜索栏

    searchHintContent?: JSX.Element; //开始搜索，搜索key为空时的背景提示界面

    autoSearch = true;

    showChineseUsage = true;

    /**
     * 删除指定项时回调，return true表示允许删除，false表示阻止删除
     * @param goods
     */
    onDeleteItems?(goods: GoodsInfo[]): Promise<boolean>;

    /**
     * 点击保存时回调 return true表示允许保存,false表示禁止保存
     */
    onPreSave?(goods: GoodsInfo[]): Promise<boolean>;
}

//执行站可开项目配置
export class NurseParams {
    openGoodsTypes?: number[];
    fromNursePage?: boolean; //是否从执行站页面进入
}

export class DirectChargeMedicineAddPageCategoryItem {
    static naviIdWestern = 1; // 中西成药
    static naviIdChinese = 2; // 中药
    static naviIdTreatment = 3; // 治疗项目
    static naviIdExamination = 4; // 检查项目
    static naviIdGoods = 5; // 商品
    static naviIdMedicalMaterial = 6; // 医用物资
    static naviIdPackage = 7; // 套餐
    static otherGoods = 19; // 其他
    static nurseProduct = 21; // 护理项目

    static glassesGoods = 24;

    isDentistry: boolean;
    isHospital: boolean;

    id: number;
    title: string;

    constructor(id: number, title: string, isDentistry?: boolean, isHospital = false) {
        this.id = id;
        this.title = title;
        this.isDentistry = isDentistry ?? false;
        this.isHospital = isHospital;
    }
}

interface DirectChargeMedicineAddPageProps {
    params: DirectChargeMedicineInvokeParams;
    nurseFilterInfo?: NurseParams; //是否从执行站页面进入
}

export class DirectChargeMedicineAddUtils {
    static addMedicine(
        params: DirectChargeMedicineInvokeParams,
        nurseFilterInfo?: NurseParams
    ): Promise<DirectChargeMedicineAddInfo | undefined> {
        return ABCNavigator.navigateToPage(<DirectChargeMedicineAddPage params={params} nurseFilterInfo={nurseFilterInfo} />);
    }
}

const kCategoryItems = [
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdWestern, "中西成药"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdChinese, "中药"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdTreatment, "治疗理疗"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdExamination, "检查检验", true),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdPackage, "套餐", true),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdGoods, "商品"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.glassesGoods, "眼镜商品"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial, "医用物资"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.otherGoods, "其他", true),
];

const styles = ABCStyleSheet.create({
    categoryItemContainer: {
        height: Sizes.listItemHeight,
        paddingLeft: Sizes.listHorizontalMargin,
        justifyContent: "center",
    },
    listItemContainer: {
        flexDirection: "row",
        height: Sizes.listItemHeight,
        justifyContent: "space-between",
        alignItems: "center",
    },
    listItemUnitText: { ...TextStyles.t14NT4, marginLeft: 8 },
    goodsNameText: { flexShrink: 1, ...TextStyles.t16MT1 },
    listItemPackage: { flexDirection: "row", flex: 1, ...ABCStyles.rowAlignCenter },
});

export class DirectChargeMedicineAddPage extends BaseBlocPage<DirectChargeMedicineAddPageProps, DirectChargeMedicineAddPageBloc> {
    _currentCategory: DirectChargeMedicineAddPageCategoryItem;
    private _shoppingCarIconRef: View | null = null;
    private animationBall?: JSX.Element;
    private _contentView: View | null = null;
    private _appBar: AppSearchBar | null = null;

    constructor(props: DirectChargeMedicineAddPageProps) {
        super(props);
        //是否存在药品（中西成药、中药）
        const isExistMedicine = props?.nurseFilterInfo?.openGoodsTypes?.includes(1);

        //是否存在商品、耗材（商品、医用物资）
        const isExistGoods = props?.nurseFilterInfo?.openGoodsTypes?.includes(2);

        const isOphthalmologyClinic = !!userCenter.clinic?.isOphthalmologyClinic;
        const copyKCategoryItems = [...kCategoryItems];
        _.remove(
            copyKCategoryItems,
            (item) =>
                (!isExistMedicine &&
                    (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdWestern ||
                        item.id == DirectChargeMedicineAddPageCategoryItem.naviIdChinese)) ||
                (!isExistGoods &&
                    (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdGoods ||
                        item.id == DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial)) ||
                (!isOphthalmologyClinic && item.id == DirectChargeMedicineAddPageCategoryItem.glassesGoods)
        );

        this._currentCategory =
            props.nurseFilterInfo?.fromNursePage && !userCenter.clinic?.isDentistryClinic
                ? copyKCategoryItems[0]
                : kCategoryItems[userCenter.clinic?.isDentistryClinic ? 3 : 0];
        this.bloc = new DirectChargeMedicineAddPageBloc(this._currentCategory.id, this.props.params, this.props?.nurseFilterInfo);

        this.addDisposable(this.bloc);
    }

    getAppBar(): JSX.Element {
        return (
            <AppSearchBar
                ref={(ref) => (this._appBar = ref)}
                placeholder={"输入项目全称或拼音首字母"}
                rightPart={this.getRightAppBarIcons()}
                onBackClick={this.onBackClick.bind(this)}
                autoFocus={this.props.params.autoFocusSearch}
                onChangeText={(value) => this.bloc.requestUpdateSearchText(value)}
                onFocus={() => this._onSearchFocusChanged(true)}
                onBlur={() => this._onSearchFocusChanged(false)}
            />
        );
    }

    onBackClick(): void {
        if (this.bloc.currentState.isInSearchMode) {
            AbcTextInput.focusInput?.blur();
            this._appBar?.setSearchText("");
            return;
        }

        ABCNavigator.pop();
    }

    getBottomSafeAreaColor(): Color {
        return Colors.white;
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <IconFontView
                name={"scan"}
                key={"RightAppBarIcons"}
                size={Sizes.dp20}
                color={Colors.black}
                onClick={() => this.bloc.requestAddGoodsFromQRScan()}
            />,
        ];
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { showCategorySearchNavi } = this.props.params;
        return (
            <View style={{ flex: 1 }} ref={(ref) => (this._contentView = ref)} collapsable={false}>
                <View style={{ flexDirection: "row", flex: 1 }}>
                    {!state.isInSearchMode && showCategorySearchNavi && (
                        <_CategoryPicker
                            selectCategory={this._currentCategory.id}
                            onChanged={(newCategory) => this._onCategoryChanged(newCategory)}
                        />
                    )}
                    <_GoodsSearchDisplayView
                        onClickItem={(goods, ref) => this._onClickItem(goods, ref)}
                        hintContent={this.props.params.searchHintContent}
                    />
                </View>

                <_ShoppingCarBar
                    count={state.selectedMedicines.length ?? 0}
                    price={state.totalSelectPrice}
                    onShoppingCarIconRef={(ref) => (this._shoppingCarIconRef = ref)}
                    onSaveTap={() => this.bloc.requestSave()}
                    showDeleteAll={false}
                    onShoppingCarTap={() => this.bloc.requestShowShoppingCar()}
                />

                {this.animationBall}
            </View>
        );
    }

    private _onCategoryChanged(category: DirectChargeMedicineAddPageCategoryItem) {
        this._currentCategory = category;
        this.bloc.requestUpdateGoodsType(category.id);
    }

    private async _onClickItem(goods: GoodsInfo, ref: View | AbcView | null) {
        if (!goods.hasStock && !this.bloc.currentState.allowZeroStockGoods) return;

        const contains = this.bloc.currentState.inputCounts.has(goods);
        if (!contains && this._shoppingCarIconRef && ref && this._contentView) {
            const contentViewLayout = await UIUtils.measureInWindow(this._contentView as any).catch((err) => {
                LogUtils.d("_onClickItem error = " + errorToStr(err));
                throw err;
            });

            const startLayout = await UIUtils.measureInWindow(ref as any).catch((err) => {
                LogUtils.d("_onClickItem error = " + errorToStr(err));
                throw err;
            });
            const endLayout = await UIUtils.measureInWindow(this._shoppingCarIconRef as any);
            LogUtils.d("_onClickItem startLayout = " + JSON.stringify(startLayout) + ", endLayout = " + endLayout);

            this.animationBall = (
                <ShoppingCartGuideAnimationBall
                    key={"ShoppingCartGuideAnimationBall"}
                    startPos={{
                        x: startLayout.width + startLayout.x - Sizes.dp30,
                        y: startLayout.y - contentViewLayout.y + startLayout.height / 2,
                    }}
                    endPos={{
                        x: endLayout.x - endLayout.width / 2,
                        y: endLayout.y - endLayout.height / 2,
                    }}
                    onAnimationEnd={() => {
                        this.animationBall = undefined;
                        this.setState({});
                    }}
                />
            );

            this.setState({});
        }

        this.bloc.requestToggleMedicineSelectState(goods);
    }

    private _onSearchFocusChanged(focus: boolean) {
        this.bloc.requestUpdateSearchFocus(focus);
    }
}

export interface _CategoryPickerProps {
    selectCategory: number;
    onChanged: (newCategory: DirectChargeMedicineAddPageCategoryItem) => void;
}

export class _CategoryPicker<T extends _CategoryPickerProps = _CategoryPickerProps> extends BaseComponent<T> {
    static contextType = DirectChargeMedicineAddPageBloc.Context;

    render(): JSX.Element {
        const { selectCategory } = this.props;
        const state = DirectChargeMedicineAddPageBloc.fromContext(this.context).currentState;
        let __kCategoryItems = [...kCategoryItems];
        if (userCenter.clinic?.isDentistryClinic) {
            __kCategoryItems.splice(
                4,
                0,
                ...state.kCustomCategoryItems.map(
                    (item) => new DirectChargeMedicineAddPageCategoryItem(item.customTypeId, item.customTypeName, true)
                )
            );
            __kCategoryItems = __kCategoryItems.filter((item) => item.isDentistry);
        }

        if (state._fromNursePage) {
            //是否存在药品（中西成药、中药）
            const isExistMedicine = state._openGoodsTypes?.includes(1);

            //是否存在商品、耗材（商品、检查检验、医用物资）
            const isExistGoods = state._openGoodsTypes?.includes(2);

            const isOphthalmologyClinic = !!userCenter.clinic?.isOphthalmologyClinic;
            _.remove(
                __kCategoryItems,
                (item) =>
                    (!isExistMedicine &&
                        (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdWestern ||
                            item.id == DirectChargeMedicineAddPageCategoryItem.naviIdChinese)) ||
                    (!isExistGoods &&
                        (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdGoods ||
                            item.id == DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial)) ||
                    (!isOphthalmologyClinic && item.id == DirectChargeMedicineAddPageCategoryItem.glassesGoods)
            );
        }

        return (
            <View style={{ backgroundColor: Colors.D2, width: Sizes.dp80 }}>
                {__kCategoryItems.map((item) => {
                    const select = selectCategory === item.id;
                    return (
                        <View
                            key={item.id.toString()}
                            style={{
                                ...styles.categoryItemContainer,
                                backgroundColor: select ? Colors.white : undefined,
                            }}
                            onClick={() => this.props.onChanged(item)}
                        >
                            <Text style={select ? TextStyles.t14NT1 : TextStyles.t14NT4}>{item.title}</Text>
                        </View>
                    );
                })}
            </View>
        );
    }
}

interface _GoodsSearchDisplayViewProps {
    onClickItem?: (goods: GoodsInfo, ref: View | AbcView | null /*View*/) => void;
    hintContent?: JSX.Element;
    allowZeroStockGoods?: boolean;
}

export class _GoodsSearchDisplayView extends NetworkView<_GoodsSearchDisplayViewProps> {
    static contextType = DirectChargeMedicineAddPageBloc.Context;

    constructor(props: _GoodsSearchDisplayViewProps) {
        super(props);
    }

    public componentDidMount(): void {
        DirectChargeMedicineAddPageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.searching) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.searchError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (_.isEmpty(state.medicines)) {
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.searchError);
            })
            .addToDisposableBag(this);
    }

    public renderContent(): JSX.Element {
        const state = DirectChargeMedicineAddPageBloc.fromContext(this.context).currentState;

        const { medicines, inputCounts } = state;

        LogUtils.d("renderContent count = " + medicines.length);
        const length = medicines.length;
        return (
            <ListView
                accessibilityLabel={"abc-list-view"}
                style={{ flex: 1 }}
                renderRow={(data, ignored, index) => this._renderRow(data, index!, length, inputCounts.has(data))}
                dataSource={medicines}
                getRowKey={(index) => index.toString()}
                numberOfRows={length}
                scrollEventThrottle={300}
            />
        );
    }

    public emptyContent(): JSX.Element {
        const state = DirectChargeMedicineAddPageBloc.fromContext(this.context).currentState;
        if (_.isEmpty(state.medicines) && _.isEmpty(state.keyword)) {
            let content = this.props.hintContent;
            if (content) {
                content = (
                    <View
                        style={{
                            flex: 1,
                            alignItems: "stretch",
                            justifyContent: "stretch",
                        }}
                    >
                        {content}
                    </View>
                );
            }
            if (content) return content;
        }

        if (_.isEmpty(state.keyword)) return <View />;
        return super.emptyContent();
    }

    public reloadData(): void {
        DirectChargeMedicineAddPageBloc.fromContext(this.context).requestRetrySearch();
    }

    protected _renderRow(data: GoodsInfo, index: number, totalCount: number, select: boolean): JSX.Element {
        const valueHolder = new ValueHolder<View | null>();
        return (
            <View
                collapsable={false}
                ref={(ref) => (valueHolder.value = ref)}
                style={{
                    backgroundColor: Colors.white,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                }}
                onClick={() => this.props.onClickItem?.(data, valueHolder.value!)}
            >
                <_ListItem goodsInfo={data} select={select} />
                {index != totalCount - 1 && <DividerLine />}
            </View>
        );
    }
}

interface _ListItemProps {
    goodsInfo: GoodsInfo;
    select: boolean;
    allowZeroStockGoods?: boolean; //是否允许开0的药品
}

export class _ListItem extends React.Component<_ListItemProps> {
    render(): JSX.Element {
        const { goodsInfo } = this.props;
        if (
            goodsInfo.isChineseMedicine ||
            goodsInfo.isWesternMedicine ||
            goodsInfo.isChineseWesternMedicine ||
            goodsInfo.isGoods ||
            goodsInfo.isGlasses ||
            goodsInfo.isMedicalMaterial
        ) {
            return this._renderNormalMedicine();
        } else if (
            goodsInfo.type == GoodsType.treatment ||
            goodsInfo.type == GoodsType.examination ||
            goodsInfo.isNurseFee ||
            goodsInfo.isOtherFee
        ) {
            return this._renderTreatmentItem();
        } else if (goodsInfo.isPackage) {
            return this._renderTreatmentPackageItem();
        } else {
            return <View />;
        }
    }

    _renderNormalMedicine(): JSX.Element {
        const { goodsInfo, select } = this.props;
        let specification = goodsInfo.packageSpec;
        if (_.isEmpty(specification)) specification = `${goodsInfo.cMSpec} ${goodsInfo.pieceUnit}`;

        const stock = goodsInfo.displayStockInfo();
        const hasStock = goodsInfo.hasStock || userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 2;

        return (
            <View style={[styles.listItemContainer, { height: Sizes.dp72 }]}>
                <View style={{ flex: 1 }}>
                    <View style={{ flexDirection: "row", marginBottom: Sizes.dp8 }}>
                        <View style={{ flexDirection: "row", flex: 1, alignItems: "center" }}>
                            <Text style={styles.goodsNameText} numberOfLines={1}>
                                {goodsInfo.displayName ?? ""}
                            </Text>
                            {goodsInfo.type == GoodsType.treatment ||
                                (goodsInfo.type == GoodsType.examination && <Text style={styles.listItemUnitText}>{goodsInfo.unit}</Text>)}
                        </View>

                        <SizedBox width={Sizes.dp5} />
                        <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })]}>
                            {ABCUtils.formatPrice(goodsInfo.unitPricePreferPackage)}
                        </Text>
                    </View>

                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                        <Text style={[TextStyles.t14NT4, { paddingRight: Sizes.dp10 }]}>{specification}</Text>
                        <Text
                            style={[
                                TextStyles.t14NT4,
                                {
                                    paddingRight: Sizes.dp8,
                                    flex: 1,
                                },
                            ]}
                            numberOfLines={1}
                        >
                            {goodsInfo.manufacturer ?? ""}
                        </Text>
                        <Text style={hasStock ? TextStyles.t14NT4 : TextStyles.t14NY2}>{`${stock}`}</Text>
                    </View>
                </View>

                <IconFontView
                    style={{ paddingLeft: Sizes.dp5 }}
                    name={select ? "Chosen" : "plus_circle"}
                    size={Sizes.dp16}
                    color={select ? Colors.mainColor : Colors.black}
                />
            </View>
        );
    }

    protected _renderTreatmentItem(): JSX.Element {
        const { goodsInfo, select } = this.props;
        return (
            <View style={styles.listItemContainer}>
                <View style={{ flexDirection: "row", flex: 1, alignItems: "center" }}>
                    <Text style={styles.goodsNameText} numberOfLines={1}>
                        {goodsInfo.displayName ?? ""}
                    </Text>
                    <Text style={styles.listItemUnitText}>{goodsInfo.unit}</Text>
                </View>

                <SizedBox width={Sizes.dp5} />
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })]}>{ABCUtils.formatPrice(goodsInfo.unitPrice)}</Text>

                <IconFontView
                    style={{ paddingLeft: Sizes.dp5 }}
                    name={select ? "Chosen" : "plus_circle"}
                    size={Sizes.dp16}
                    color={select ? Colors.mainColor : Colors.black}
                />
            </View>
        );
    }

    protected _renderTreatmentPackageItem(): JSX.Element {
        const { goodsInfo, select } = this.props;
        return (
            <View style={styles.listItemContainer}>
                <View style={styles.listItemPackage}>
                    <Text style={styles.goodsNameText} numberOfLines={1}>
                        {goodsInfo.displayName ?? ""}
                    </Text>
                    <Text style={styles.listItemUnitText}>【套餐】</Text>
                </View>

                <SizedBox width={Sizes.dp5} />
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })]}>{ABCUtils.formatPrice(goodsInfo.unitPrice)}</Text>
                <IconFontView
                    style={{ paddingLeft: Sizes.dp5 }}
                    name={select ? "Chosen" : "plus_circle"}
                    size={Sizes.dp16}
                    color={select ? Colors.mainColor : Colors.black}
                />
            </View>
        );
    }
}

interface _ShoppingCartBarProps {
    count: number;
    price: number;
    showDeleteAll?: boolean; //true
    onShoppingCarTap?: () => void;

    onClearAllClick?(): void;

    onSaveTap?(): void;

    onShoppingCarIconRef?: (ref: View | null) => void;
}

class _ShoppingCarBar extends BaseComponent<_ShoppingCartBarProps> {
    constructor(props: _ShoppingCartBarProps) {
        super(props);
    }

    render() {
        const { count, price, showDeleteAll = true } = this.props;
        return (
            <AbcView
                // @ts-ignore
                style={{
                    ...ABCStyles.topLine,
                    // shadowColor: Colors.black,
                    // shadowOpacity: 0.1,
                    // shadowOffset: { width: 0, height: -Sizes.dp2 },
                    height: Sizes.dp58,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.white,
                    flexDirection: "row",
                    alignItems: "center",
                }}
                onClick={() => this.props.onShoppingCarTap?.()}
            >
                <View
                    style={{ width: Sizes.dp35, height: Sizes.dp35, justifyContent: "center" }}
                    ref={(ref) => this.props.onShoppingCarIconRef?.(ref)}
                    collapsable={false}
                >
                    <AssetImageView name={"shoppingcar"} style={{ width: Sizes.dp24, height: Sizes.dp24 }} />
                    <View
                        style={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            backgroundColor: Colors.red,
                            paddingHorizontal: Sizes.dp4,
                            borderRadius: 7.5,
                        }}
                    >
                        <Text style={TextStyles.t13NT9.copyWith({ color: Colors.white })}>{count}</Text>
                    </View>
                </View>

                <Text style={[TextStyles.t14MY2, { paddingHorizontal: Sizes.dp12 }]}>{`${abcI18Next.t("¥")}${ABCUtils.formatPrice(
                    price
                )}`}</Text>

                {showDeleteAll && (
                    <Text style={TextStyles.t14NT1} onClick={() => this.props.onClearAllClick?.()}>
                        全部清空
                    </Text>
                )}
                <Spacer />
                <AbcButton text={"保存"} onClick={() => this.props.onSaveTap?.()} />
            </AbcView>
        );
    }
}

interface ShoppingCartGuideAnimationBallProps {
    startPos: Point;
    endPos: Point;

    onAnimationEnd?: () => void;
}

const _ballStyle: Style = {
    position: "absolute",
    width: Sizes.dp15,
    height: Sizes.dp15,
    borderRadius: Sizes.dp15 / 2,
    backgroundColor: Colors.mainColor,
};

class ShoppingCartGuideAnimationBall extends BaseComponent<ShoppingCartGuideAnimationBallProps> {
    private xAnimation?: Animation;
    private yAnimation?: Animation;

    constructor(props: ShoppingCartGuideAnimationBallProps) {
        super(props);
    }

    componentWillMount() {
        const { startPos, endPos } = this.props;

        const duration = 300;
        this.xAnimation = new Animation({
            startValue: startPos.x,
            toValue: endPos.x,
            duration: duration,
            delay: 0,
            mode: "timing",
            timingFunction: "linear",
        });

        this.yAnimation = new Animation({
            startValue: startPos.y,
            toValue: endPos.y,
            duration: duration,
            delay: 0,
            mode: "timing", // 动画模式
            timingFunction: "ease-in",
        });

        this.xAnimation.start();
        this.yAnimation.start();

        delayed(duration)
            .subscribe(() => this.props.onAnimationEnd?.())
            .addToDisposableBag(this);
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        this.yAnimation?.destory();
        this.xAnimation?.destory();
    }

    render() {
        return (
            <View
                style={[
                    _ballStyle,
                    {
                        transform: [
                            {
                                translateX: this.xAnimation,
                                translateY: this.yAnimation,
                            },
                        ],
                    },
                ]}
            />
        );
    }
}

interface ShoppingCardExpandDialogProps {
    bloc: DirectChargeMedicineAddPageBloc;
}

const countViewWidth = Sizes.dp48;
const unitViewWidth = Sizes.dp48;
const countUnitMargin = Sizes.dp8;
const closeViewWidth = Sizes.dp32;

export class ShoppingCardExpandDialog extends BaseBlocComponent<ShoppingCardExpandDialogProps, DirectChargeMedicineAddPageBloc> {
    static dialogDismissFromSave = 1;
    static dialogDismissFromCancel = 3;

    static show(bloc: DirectChargeMedicineAddPageBloc): Promise<number | undefined> {
        return showBottomSheet(<ShoppingCardExpandDialog bloc={bloc} />, {
            dismissWhenTouchOutside: false,
        });
    }

    constructor(props: ShoppingCardExpandDialogProps) {
        super(props);
        this.bloc = this.props.bloc;
    }

    protected takeBlocOwnership(): boolean {
        return false;
    }

    public renderContent(): JSX.Element {
        const { bloc } = this.props;
        const state = bloc.currentState;
        const selectGoods = bloc.currentState.selectedMedicines;
        const views: JSX.Element[] = [];
        const western = this._renderWesternGroup();
        if (western) {
            views.push(western);
        }

        const chinese = this._renderChineseMedicineGroup();
        if (chinese) {
            if (views.length > 0) {
                views.push(<GroupDivider key={UniqueKey()} />);
            }

            views.push(chinese);
        }

        const treatment = this._renderTreatmentGroup();
        if (treatment) {
            if (views.length > 0) views.push(<GroupDivider key={UniqueKey()} />);

            views.push(treatment);
        }
        return (
            <View style={{ flex: 1 }}>
                <View style={{ height: Const.bottomSheetTopMargin }} onClick={() => ABCNavigator.pop()} />
                <View style={{ flex: 1, backgroundColor: Colors.window_bg }}>
                    {this._renderTopBar()}
                    {this._renderHeader()}
                    <ScrollView showsVerticalScrollIndicator={false}>{views}</ScrollView>

                    <_ShoppingCarBar
                        count={selectGoods.length ?? 0}
                        price={state.totalSelectPrice}
                        onSaveTap={() => {
                            if (bloc.validateForSave()) ABCNavigator.pop(ShoppingCardExpandDialog.dialogDismissFromSave);
                        }}
                        onClearAllClick={this._onClearAllClick.bind(this)}
                        onShoppingCarTap={() => ABCNavigator.pop(ShoppingCardExpandDialog.dialogDismissFromCancel)}
                    />
                </View>
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </View>
        );
    }

    private _renderTopBar() {
        return (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    paddingLeft: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.white,
                    height: Sizes.listItemHeight,
                    ...ABCStyles.bottomLine,
                }}
            >
                <Text style={TextStyles.t16MT3}>已选</Text>
                <Spacer />
                <BottomSheetCloseButton onTap={() => ABCNavigator.pop(ShoppingCardExpandDialog.dialogDismissFromCancel)} />
            </View>
        );
    }

    private _renderHeader() {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.listHorizontalPadding,
                    {
                        height: Sizes.dp32,
                        backgroundColor: Colors.D2,
                    },
                ]}
            >
                <Text style={[TextStyles.t14NT2, { flex: 1 }]}>名称</Text>
                <Text style={[TextStyles.t14NT2, { width: countViewWidth, textAlign: "center" }]}>数量</Text>
                <Text
                    style={[
                        TextStyles.t14NT2,
                        {
                            marginLeft: countUnitMargin,
                            marginRight: closeViewWidth,
                            width: unitViewWidth,
                            textAlign: "center",
                        },
                    ]}
                >
                    单位
                </Text>
            </View>
        );
    }

    private _renderWesternGroup() {
        const goods = this.bloc.currentState.selectedMedicines.filter(
            (item) => item.isChineseWesternMedicine || item.isWesternMedicine || item.isGoods || item.isMedicalMaterial
        );
        if (_.isEmpty(goods)) return;

        const length = goods.length;
        return (
            <View key={"_renderWesternGroup"}>
                {goods.map((item, index) => {
                    return (
                        <_MedicineEditItemView key={item.compareKey() + index.toString()} goods={item} bottomLine={index != length - 1} />
                    );
                })}
            </View>
        );
    }

    private _renderChineseMedicineGroup() {
        const { dosageCount, selectedMedicines } = this.bloc.currentState;
        const { showChineseUsage } = this.bloc;
        const goods = selectedMedicines.filter((item) => item.isChineseMedicine);
        if (_.isEmpty(goods)) return;

        // const length = goods.length;
        return (
            <View key={"chinese"}>
                {goods.map((item, index) => {
                    return <_MedicineEditItemView key={item.compareKey() + index.toString()} goods={item} bottomLine={true} />;
                })}

                {showChineseUsage && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            ABCStyles.listHorizontalPadding,
                            {
                                height: Sizes.listItemHeight,
                                backgroundColor: Colors.contentBgColor,
                            },
                        ]}
                    >
                        <Spacer />
                        <CustomInput
                            style={{ width: countViewWidth }}
                            key={"unitCount"}
                            type={"input"}
                            placeholder={""}
                            value={dosageCount}
                            formatter={PrecisionLimitFormatter(0)}
                            onChange={(newValue) => {
                                const value = parseFloat(newValue);
                                if (value >= 0) this.bloc.requestUpdateDosageCount(value);
                            }}
                        />

                        <SizedBox width={countUnitMargin} />
                        <View
                            style={{
                                height: Sizes.dp28,
                                width: unitViewWidth,
                                borderRadius: Sizes.dp14,
                                borderWidth: 1,
                                borderColor: Colors.P1,
                                justifyContent: "center",
                                alignItems: "center",
                            }}
                        >
                            <Text style={[TextStyles.subTitleStyle, {}]} numberOfLines={1}>
                                剂
                            </Text>
                        </View>

                        <SizedBox width={closeViewWidth} />
                    </View>
                )}
            </View>
        );
    }

    private _renderTreatmentGroup() {
        const goods = this.bloc.currentState.selectedMedicines.filter(
            (item) => item.type == GoodsType.treatment || item.type == GoodsType.examination || item.isPackage
        );
        if (_.isEmpty(goods)) return;

        const length = goods.length;
        return (
            <View key={"treatment"}>
                {goods.map((item, index) => {
                    return <_MedicineEditItemView key={item.id + index.toString()} goods={item} bottomLine={index != length - 1} />;
                })}
            </View>
        );
    }

    private _onClearAllClick() {
        this.bloc.requestDeleteAll();
    }
}

interface _MedicineEditItemViewProps {
    goods: GoodsInfo;
    bottomLine: boolean;
}

class _MedicineEditItemView extends BaseComponent<_MedicineEditItemViewProps> {
    static contextType = DirectChargeMedicineAddPageBloc.Context;

    constructor(props: _MedicineEditItemViewProps) {
        super(props);
    }

    render() {
        const state = DirectChargeMedicineAddPageBloc.fromContext(this.context).currentState;
        const { inputCounts } = state;
        const { goods, bottomLine } = this.props;
        const usage = inputCounts.get(goods);
        const specification = goods.packageSpec;
        const height = specification.length == 0 ? Sizes.dp48 : Sizes.dp72;
        let input: MedicineSellCountInputButton | null = null;
        return (
            <View
                style={[
                    {
                        backgroundColor: Colors.contentBgColor,
                        paddingLeft: Sizes.listHorizontalMargin,
                    },
                ]}
            >
                <View
                    style={[bottomLine ? ABCStyles.bottomLine : {}, { height: height, justifyContent: "center" }]}
                    onClick={() => {
                        DirectChargeMedicineAddPageBloc.fromContext(this.context).requestGoodsItemFocusChanged(this.props.goods, true);
                        input?.focus();
                    }}
                >
                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                        <StockNotEnoughTextView
                            text={goods.displayName}
                            style={{ flex: 1 }}
                            textStyle={{ ...TextStyles.t16MB }}
                            goodsStock={goods.stockInfo(usage?.unit, usage?.count, 1)}
                        />
                        <MedicineSellCountInputButton
                            ref={(ref) => (input = ref)}
                            goodsInfo={goods}
                            unit={usage?.unit ?? ""}
                            count={usage?.count ?? 0}
                            floatPrecisionLength={goods.isChineseMedicine || goods.isGoods ? Const.chineseMedicineSellPrecision : 0}
                            dosageCount={goods.isChineseMedicine ? state.dosageCount : 1}
                            width={countViewWidth}
                            onChanged={(value) =>
                                DirectChargeMedicineAddPageBloc.fromContext(this.context).requestUpdateGoodsCount(goods, value)
                            }
                        />
                        <SizedBox width={countUnitMargin} />

                        <SizedBox width={unitViewWidth}>
                            <MedicineSellUnitSelectButton
                                unit={usage?.unit ?? ""}
                                units={goods.sellUnits}
                                onUnitSelected={(unit) =>
                                    DirectChargeMedicineAddPageBloc.fromContext(this.context).requestUpdateGoodsUnit(goods, unit)
                                }
                            />
                        </SizedBox>

                        <SizedBox width={unitViewWidth}>
                            {state.focusGoodsInfo == goods && (
                                <CloseBtn
                                    style={{
                                        paddingVertical: Sizes.dp3,
                                        paddingRight: Sizes.dp16,
                                        paddingLeft: Sizes.dp10,
                                    }}
                                    onClick={() => DirectChargeMedicineAddPageBloc.fromContext(this.context).requestDeleteGoodsItem(goods)}
                                />
                            )}
                        </SizedBox>
                    </View>

                    {specification.length > 0 && <SizedBox height={Sizes.dp8} />}
                    {specification.length > 0 && (
                        <View>
                            <Text style={TextStyles.t16NT4} numberOfLines={1}>
                                {specification}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        );
    }
}
