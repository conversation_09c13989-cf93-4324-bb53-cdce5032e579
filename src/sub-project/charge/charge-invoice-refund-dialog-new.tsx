/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/6/20
 *
 * @description
 *
 */
import { BaseBlocComponent, BaseComponent } from "../base-ui/base-component";
import { ScrollView, StyleSheet, Text, View } from "@hippy/react";
import React from "react";
import { ChargeForm, ChargeFormItem, ChargeFormItemStatus, ChargeInvoiceDetailData } from "./data/charge-beans";
import { ChargeInvoiceRefundDialogBloc } from "./charge-invoice-refund-dialog-bloc";
import { AbcCheckbox, AbcIndeterminateNewCheckbox, IndeterminateCheckboxStatus } from "../base-ui/views/abc-checkbox";
import { DividerLine, IconFontView, SizedBox, Spacer, ToolBar, ToolBarButtonStyle1 } from "../base-ui";
import { ABCStyles, AbcViewProps, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import { CardHeader } from "../views/card-header";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import _ from "lodash";
import { NewStyleNumberStepperInputView } from "../base-ui/views/number-stepper-input-view";
import { Const } from "../base-ui/utils/consts";
import { ABCUtils } from "../base-ui/utils/utils";
import { GoodsInfo } from "../base-business/data/beans";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { ignore } from "../common-base-module/global";
import { ExecuteChargeStatusViewText } from "./view/charge-views";
import { TextStyle } from "../theme/text-styles";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { ChargeItemModifyUtils } from "./utils/charge-item-modify-utils";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { BottomSheetHelper } from "../base-ui/abc-app-library";
import abcI18Next from "../language/config";
import { AbcView } from "../base-ui/views/abc-view";
import { Toast } from "../base-ui/dialog/toast";
import { GetClinicBasicSetting } from "../data/online-property-config-provder";

interface ChargeInvoiceRefundDialogProps {
    chargeInvoiceDetailData: ChargeInvoiceDetailData;
    hideHint?: boolean;
    enableWholeChargeRefund?: boolean; // 是否开启整单收退费
    successCallBack?(): void;
}

// @ts-ignore
const styles = StyleSheet.create({
    itemStyle: {
        minHeight: Sizes.listItemHeight,
        paddingHorizontal: Sizes.listHorizontalMargin,
        paddingVertical: Sizes.dp9,
    },
});

export class ChargeInvoiceRefundNewDialog extends BaseBlocComponent<ChargeInvoiceRefundDialogProps, ChargeInvoiceRefundDialogBloc> {
    static show(options: {
        detailData: ChargeInvoiceDetailData;
        callback?: () => void;
        hideHint?: boolean;
        enableWholeChargeRefund?: boolean;
    }): Promise<void> {
        const { detailData, callback, hideHint, enableWholeChargeRefund } = options || {};
        return showBottomPanel(
            <ChargeInvoiceRefundNewDialog
                chargeInvoiceDetailData={detailData}
                successCallBack={callback}
                hideHint={hideHint}
                enableWholeChargeRefund={enableWholeChargeRefund}
            />,
            { topMaskHeight: Sizes.dp160 }
        );
    }

    bloc: ChargeInvoiceRefundDialogBloc;

    // 是否开启整单收退费
    private get isWholeChargeRefundEnabled(): boolean {
        return !!this.props.enableWholeChargeRefund;
    }

    constructor(props: ChargeInvoiceRefundDialogProps) {
        super(props);

        this.bloc = new ChargeInvoiceRefundDialogBloc(this.props.chargeInvoiceDetailData);

        this.addDisposable(this.bloc);
    }

    renderContent(): JSX.Element {
        if (this.bloc.currentState.detailData === undefined) return <View />;

        return (
            <View style={{ flex: 1 }}>
                <View style={{ flex: 1, backgroundColor: Colors.white }}>
                    {BottomSheetHelper.createTitleBar("选择退费项目")}
                    <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                        {!this.props.hideHint && this._renderRefundHintTips()}
                        {this._renderChargeForm()}
                        {this._renderPrescriptionChargeForm()}
                        {this._renderAdjustmentFeeWidget()}
                        {this._renderTotalChargeWidget()}
                    </ScrollView>

                    {this._renderCalculateFailedBar()}
                    {this._renderBottomBar()}
                </View>
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </View>
        );
    }

    //构建收费表
    private _renderChargeForm(): JSX.Element | undefined {
        const items: JSX.Element[] = [];
        const { detailData, clinicBasicSetting } = this.bloc.currentState;
        const countInputItemsCopy = this.bloc.currentState.countInputItems;

        const cloneDetail = _.cloneDeep(detailData);
        cloneDetail.chargeForms?.map((chargeForm) => {
            chargeForm.chargeFormItems?.forEach((formItem) => {
                //判断formItem中isHasDeductItem是否存在，true--存在卡项抵扣，则需要复制一份当前的值，但是需要更改一下id、keyId，避免两者关联,
                if (!!formItem?.isHasDeductItem && !formItem.isSelfProvided) {
                    chargeForm.chargeFormItems?.push(
                        JsonMapper.deserialize(ChargeFormItem, {
                            ...formItem,
                            id: !!formItem.id ? `${formItem.id}_isHasDeductItem` : formItem.id,
                            keyId: !!formItem.keyId ? `${formItem.keyId}_isHasDeductItem` : formItem.keyId,
                            isShowDeductTag: true, // 便于展示卡项抵扣标识
                        })
                    );
                }
            });
        });

        cloneDetail.chargeForms?.forEach((chargeForm, formIndex) => {
            chargeForm.chargeFormItems?.forEach((formItem, index) => {
                const key = formIndex.toString() + index.toString();

                if (
                    formItem.canRefund &&
                    (chargeForm.isConsultation ||
                        chargeForm.isDecoction ||
                        chargeForm.isDelivery ||
                        chargeForm.isTreatment ||
                        chargeForm.isExamination ||
                        chargeForm.isRegistration ||
                        chargeForm.isAdditional ||
                        chargeForm.isMaterial ||
                        chargeForm.isPackage ||
                        chargeForm.isOthersFee ||
                        chargeForm.isFamilyDoctor)
                ) {
                    // if (items.length != 0) {
                    //     items.push(<DividerLine key={UniqueKey()} style={{ marginHorizontal: Sizes.listHorizontalMargin }} />);
                    // }

                    if (formItem.isPackage) {
                        items.push(
                            <_TreatmentPackageView
                                key={key}
                                form={chargeForm}
                                formItem={formItem}
                                isWholeChargeRefundEnabled={this.isWholeChargeRefundEnabled}
                            />
                        );
                    } else
                        items.push(
                            <ChargeMedicineRefundListItem
                                key={key}
                                style={styles.itemStyle}
                                chargeForm={chargeForm}
                                chargeItem={formItem}
                                count={this.bloc.currentState.countInputItems.get(formItem)!}
                                showCheckBox={true}
                                enableCheckBox={false}
                                checked={this.bloc.currentState.isChargeItemSelected(formItem)}
                                onCountInputValueChanged={(value, isBtn) => this._onCountInputValueChanged(formItem, value, isBtn)}
                                onClick={() => this._onTapChargeItem(formItem, chargeForm)}
                                onItemEndChanged={() => this.bloc.requestCompareExecuteNumber(formItem)}
                                isShowWarning={
                                    !this.isWholeChargeRefundEnabled &&
                                    ChargeItemModifyUtils._isNeedCheckForModify(formItem) &&
                                    (countInputItemsCopy.get(formItem) ?? 0) >
                                        Math.max(formItem.unitCount! - formItem.executedUnitCount!, 0)
                                }
                                clinicBasicSetting={clinicBasicSetting}
                                isWholeSheetOperateEnabled={this.isWholeChargeRefundEnabled}
                            />
                        );
                }
            });
        });

        if (items.length === 0) return;
        return (
            <View style={{ backgroundColor: Colors.contentBgColor }}>
                <CardHeader
                    showTopDivider={false}
                    showCardLeftLine={false}
                    showBorderLine={false}
                    titleRender={() => {
                        return <Text style={TextStyles.t16MB}>收费项</Text>;
                    }}
                />
                {items}
                <DividerLine lineMargin={Sizes.dp9} style={{ marginHorizontal: Sizes.dp16 }} />
            </View>
        );
    }

    private _renderPrescriptionChargeForm(): JSX.Element {
        const detailData = this.bloc.currentState.detailData;
        const chineseForms: ChargeForm[] = [];
        const airPharmacyForms: ChargeForm[] = [];
        const westernForms: ChargeForm[] = [];
        const infusionForms: ChargeForm[] = [];
        const goodsForms: ChargeForm[] = [];
        const externalForms: ChargeForm[] = [];

        for (const form of detailData.chargeForms ?? []) {
            if (form.isChinesePrescription) chineseForms.push(form);
            else if (form.isAirPharmacy && form.isCanBeRefund == 1) airPharmacyForms.push(form);
            else if (form.isWesternPrescription) westernForms.push(form);
            else if (form.isInfusionPrescription) infusionForms.push(form);
            else if (form.isExternal) externalForms.push(form);
            else if (form.isGoods) goodsForms.push(form);
        }

        return (
            <View>
                {this._createForms("成药处方", westernForms)}
                {this._createForms("输液处方", infusionForms)}
                {this._createForms("中药处方", chineseForms)}
                {this._createForms("外治处方", externalForms)}
                {this._createForms("商品", goodsForms)}
                {this._createAirPharmacyForms(airPharmacyForms)}
            </View>
        );
    }

    private _renderAdjustmentFeeWidget(): JSX.Element | undefined {
        const state = this.bloc.innerState;
        const detailData = state.detailData;

        const adjustmentFee = detailData?.chargeSheetSummary?.netAdjustmentFee ?? 0.0;
        if (adjustmentFee <= 0.0) return;

        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.listHorizontalPadding, { height: Sizes.listItemHeight }]}>
                <AbcCheckbox enable={false} check={state.adjustmentFeeCheck} />
                <Text style={TextStyles.t14NT1}>整单议价</Text>
                <Spacer />
                <Text style={TextStyles.t16MB}>{ABCUtils.formatPrice(adjustmentFee)}</Text>
            </View>
        );
    }

    private _renderTotalChargeWidget(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        if (!state.calculateRspData) return;

        return (
            <View style={{ paddingHorizontal: Sizes.dp16 }}>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            justifyContent: "space-between",
                            backgroundColor: Colors.white,
                            paddingTop: Sizes.dp20,
                            paddingBottom: Sizes.dp9,
                        },
                    ]}
                >
                    <Text style={TextStyles.t16NT1}>选中项目合计</Text>
                    <Text style={TextStyles.t18MY2}>
                        {`${state.calculateRspData.needRefundFee! < 0 ? "-" : ""}${abcI18Next.t("¥")}${ABCUtils.formatPrice(
                            Math.abs(state.calculateRspData.needRefundFee!)
                        )}`}
                    </Text>
                </View>

                {state.owedRefundFee! > 0 && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                justifyContent: "space-between",
                                backgroundColor: Colors.white,
                                paddingVertical: Sizes.dp9,
                            },
                        ]}
                    >
                        <Text style={TextStyles.t16NT1}>欠退金额合计</Text>
                        <Text style={TextStyles.t18MY2}>{`${abcI18Next.t("￥")}${ABCUtils.formatPrice(state.owedRefundFee!)}`}</Text>
                    </View>
                )}

                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            justifyContent: "space-between",
                            backgroundColor: Colors.white,
                            paddingTop: Sizes.dp9,
                            paddingBottom: Sizes.dp20,
                        },
                    ]}
                >
                    <Text style={TextStyles.t16NT1}>实退</Text>
                    <AbcTextInput
                        style={{
                            width: Sizes.dp100,
                            height: Sizes.dp32,
                            borderBottomWidth: Sizes.dp1,
                            borderBottomColor: Colors.bdColor,
                            textAlign: "right",
                            ...TextStyles.t18MT1,
                            color: this.isWholeChargeRefundEnabled ? Colors.T3 : undefined, // 整单收退费模式下使用灰色
                        }}
                        defaultValue={state.realRefundFee != undefined ? ABCUtils.formatPrice(state.realRefundFee) : ""}
                        multiline={false}
                        keyboardType={"numeric"}
                        formatter={[PrecisionLimitFormatter(2)]}
                        editable={!this.isWholeChargeRefundEnabled} // 整单收退费模式下不可编辑
                        onFocus={() => !this.isWholeChargeRefundEnabled && this.bloc.requestUpdateRealRefundFocus(true)}
                        onBlur={() => !this.isWholeChargeRefundEnabled && this.bloc.requestUpdateRealRefundFocus(false)}
                        onChangeText={(text) => {
                            if (this.isWholeChargeRefundEnabled) return; // 整单收退费模式下不可修改
                            const fee = parseFloat(text);
                            this.bloc.requestUpdateRealRefundFee(fee);
                        }}
                    />
                </View>
            </View>
        );
    }

    private _renderCalculateFailedBar(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        if (state.calculateRefundFailed == undefined) return;

        return (
            <Text style={TextStyles.t16NR2} onClick={() => this.bloc.requestReCalculatePrice()}>
                计算费用失败，点击重试
            </Text>
        );
    }

    private _renderBottomBar(): JSX.Element {
        return (
            <ToolBar hideWhenKeyboardShow={true}>
                <ToolBarButtonStyle1
                    text={"退费"}
                    showIndicator={this.bloc.currentState.calculatingRefund}
                    onClick={() => this.bloc.requestSubmit(this.props.successCallBack)}
                />
            </ToolBar>
        );
    }

    private _createFrom(title: string, chargeForm: ChargeForm, index: number): JSX.Element | null {
        const { calculateRspData, chargeConfig } = this.bloc.currentState;
        let currentTotalPrice = 0; // 当前处方的价格
        calculateRspData?.chargeForms?.forEach((item) => {
            if (item.compareKey() == chargeForm.compareKey()) {
                currentTotalPrice = item.totalRealPayPrice;
            }
        });
        let rightTitle: string;
        if (chargeForm.isChinesePrescription) {
            rightTitle = `${
                chargeForm.chargeFormItems?.filter((t) => t.canRefund)!.length
            }味，${chargeForm.currentCanRefundDoseCount!}剂, ${abcI18Next.t("¥")}${ABCUtils.formatPrice(currentTotalPrice)}`;
        } else {
            rightTitle = `${chargeForm.chargeFormItems!.length}种, ${abcI18Next.t("¥")}${ABCUtils.formatPrice(currentTotalPrice!)}`;
        }

        const formItems: ChargeFormItem[] = [];
        for (const item of chargeForm.chargeFormItems ?? []) {
            if (item.canRefund) formItems.push(item);
        }

        if (ABCUtils.isEmpty(formItems)) return null;
        formItems?.forEach((item) => {
            //判断item中isHasDeductItem是否存在，true--存在卡项抵扣，则需要复制一份当前的值，但是需要更改一下id、keyId，避免两者关联,
            if (!!item?.isHasDeductItem) {
                formItems?.push(
                    JsonMapper.deserialize(ChargeFormItem, {
                        ...item,
                        id: !!item.id ? `${item.id}_isHasDeductItem` : item.id,
                        keyId: !!item.keyId ? `${item.keyId}_isHasDeductItem` : item.keyId,
                        isShowDeductTag: true, // 便于展示卡项抵扣标识
                    })
                );
            }
        });

        const { selectedChinesePrescriptionItems } = this.bloc.currentState;
        let checkStatus: number;
        if (selectedChinesePrescriptionItems.size && selectedChinesePrescriptionItems.has(chargeForm)) {
            checkStatus =
                selectedChinesePrescriptionItems.get(chargeForm)?.length == formItems?.length
                    ? IndeterminateCheckboxStatus.all
                    : IndeterminateCheckboxStatus.some;
        } else {
            checkStatus = IndeterminateCheckboxStatus.none;
        }

        //根据chargeForm.isCanRefundSingle字段判断，是否可以单个退
        let isCanCheckbox = false;
        if (chargeForm.isCanRefundSingle == 1) {
            isCanCheckbox = true;
        }
        const tipsContent = chargeConfig?.openAudit ? "审核" : "调配";
        // 对应的处方不可退费
        const canNotRefund = chargeConfig?.openAuditOrCompound && chargeForm?.hasFinished;
        isCanCheckbox = !canNotRefund;

        // 如果开启了整单收退费，则不允许单个选择
        if (this.isWholeChargeRefundEnabled) {
            isCanCheckbox = false;
        }

        const widgets = formItems.map((item, index) => (
            <ChargeMedicineRefundListItem
                key={index.toString()}
                style={{ ...styles.itemStyle }}
                count={this.bloc.currentState.countInputItems.get(item)!}
                chargeForm={chargeForm}
                chargeItem={item}
                showCheckBox={true}
                enableCheckBox={false}
                isCanCheckbox={isCanCheckbox}
                checked={this.bloc.currentState.isChargeItemSelected(item)}
                onCountInputValueChanged={(value, isBtn) =>
                    !canNotRefund && !this.isWholeChargeRefundEnabled && this._onCountInputValueChanged(item, value, isBtn)
                }
                onClick={() => {
                    if (isCanCheckbox) this._onTapChargeItem(item, chargeForm);
                }}
                isWholeSheetOperateEnabled={this.isWholeChargeRefundEnabled}
            />
        ));

        return (
            <View key={index.toString()} style={{ backgroundColor: Colors.white }}>
                <CardHeader
                    right={rightTitle}
                    rightStyle={TextStyles.t16NT6}
                    showCardLeftLine={false}
                    showBorderLine={false}
                    showTopDivider={false}
                    titleRender={() => {
                        return (
                            <View style={[ABCStyles.rowAlignCenter]}>
                                <Text style={TextStyles.t16MB}>{title}</Text>
                                {canNotRefund && (
                                    <AbcView
                                        style={{ marginLeft: Sizes.dp4 }}
                                        onClick={() => {
                                            Toast.show(`处方已${tipsContent}，若需退费请先至药房撤销${tipsContent}`, { warning: true });
                                        }}
                                    >
                                        <Text style={TextStyles.t14NM}>{`已${tipsContent}`}</Text>
                                    </AbcView>
                                )}
                            </View>
                        );
                    }}
                />
                {chargeForm.isChinesePrescription && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenterSpaceBetween,
                            { paddingHorizontal: Sizes.dp16, backgroundColor: Colors.white, paddingVertical: Sizes.dp9 },
                        ]}
                        onClick={() => {
                            !canNotRefund &&
                                !this.isWholeChargeRefundEnabled &&
                                this.bloc.modifySelectAllChineseStatus(chargeForm, checkStatus != IndeterminateCheckboxStatus.all);
                        }}
                    >
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <AbcIndeterminateNewCheckbox
                                size={Sizes.dp16}
                                style={{ marginRight: Sizes.dp8 }}
                                enable={!!formItems.length && !canNotRefund && !this.isWholeChargeRefundEnabled}
                                check={checkStatus != IndeterminateCheckboxStatus.none} // 选中的
                                checkStatus={checkStatus}
                                checkColor={this.isWholeChargeRefundEnabled ? Colors.P1 : undefined}
                                onChange={(statue) => {
                                    !canNotRefund &&
                                        !this.isWholeChargeRefundEnabled &&
                                        this.bloc.modifySelectAllChineseStatus(chargeForm, statue);
                                }}
                            />
                            <Text style={TextStyles.t16NB}>全选</Text>
                        </View>
                        {checkStatus == IndeterminateCheckboxStatus.all && chargeForm.isCanRefundDoseCount == 1 && (
                            <View style={[ABCStyles.rowAlignCenter]}>
                                <Text style={[TextStyles.t16NB]}>{`共${chargeForm.currentCanRefundDoseCount}剂`}</Text>
                                <SizedBox width={Sizes.dp8} />
                                <Text style={[TextStyles.t16NB]}>{`退`}</Text>
                                <SizedBox width={Sizes.dp4} />
                                <View style={{ width: Sizes.dp113 }}>
                                    <NewStyleNumberStepperInputView
                                        value={chargeForm.usageInfo?.doseCount__ ?? chargeForm.currentCanRefundDoseCount}
                                        floatPrecisionLength={0}
                                        maxCount={chargeForm.currentCanRefundDoseCount}
                                        minCount={1}
                                        disable={this.isWholeChargeRefundEnabled}
                                        onChanged={(value) => {
                                            !canNotRefund &&
                                                !this.isWholeChargeRefundEnabled &&
                                                this.bloc.modifyAllDoseCount(chargeForm, !!value ? value : 1);
                                        }}
                                    />
                                </View>
                                <View style={{ marginLeft: Sizes.dp4, alignItems: "flex-end" }}>
                                    <Text style={[TextStyles.t16NB]}>{`剂`}</Text>
                                </View>
                            </View>
                        )}
                    </View>
                )}
                {widgets}
                <DividerLine lineMargin={Sizes.dp9} style={{ marginHorizontal: Sizes.dp16, marginBottom: 0 }} />
            </View>
        );
    }

    private _createForms(titlePrefix: string, forms: ChargeForm[]): JSX.Element {
        if (_.isEmpty(forms)) return <View />;
        const widgets: JSX.Element[] = [];

        let formIndex = 1;
        const length = forms.length;
        forms.forEach((form, index) => {
            if (_.isEmpty(form.chargeFormItems)) return;

            const formView = this._createFrom(titlePrefix + (length > 1 ? `${ABCUtils.toChineseNum(formIndex)}` : ""), form, index);
            if (formView != null) {
                widgets.push(formView);
                formIndex++;
            }
        });

        return <View>{widgets}</View>;
    }

    private _onCountInputValueChanged(item: ChargeFormItem, value: number, isBtn?: boolean): void {
        if (this.isWholeChargeRefundEnabled) return;
        this.bloc.requestUpdateItemInputCount(item, value, isBtn);
    }

    private _onTapChargeItem(item: ChargeFormItem, chargeForm?: ChargeForm): void {
        if (this.isWholeChargeRefundEnabled) return;
        this.bloc.requestToggleItemSelect(item, chargeForm);
    }

    private _renderRefundHintTips(): JSX.Element | undefined {
        return (
            <View
                style={{
                    paddingVertical: Sizes.dp10,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.abnormalColor,
                }}
            >
                <Text style={TextStyles.t12NY2.copyWith({ lineHeight: Sizes.dp16 })}>
                    {"1、已发出的药品，需要在药房完成退药后才能退费 \n2、空中药房药品，需要在空中药房订单中完成退药后才能退费"}
                </Text>
            </View>
        );
    }

    private _createAirPharmacyForms(airPharmacyForms: ChargeForm[]): JSX.Element | undefined {
        if (airPharmacyForms.length == 0) return;
        const self = this;
        function createFrom(chargeForm: ChargeForm, index: number): JSX.Element {
            const { calculateRspData } = self.bloc.currentState;
            let currentTotalPrice = 0; // 当前处方的价格
            calculateRspData?.chargeForms?.forEach((item) => {
                if (item.compareKey() == chargeForm.compareKey()) {
                    currentTotalPrice = item.totalRealPayPrice;
                }
            });
            let rightTitle;
            if (chargeForm.isChinesePrescription) {
                rightTitle = `${chargeForm.chargeFormItems?.filter((t) => t.canRefund)!.length}味，${chargeForm.chargeFormItems![0]!
                    .doseCount!}剂, ${abcI18Next.t("¥")}${ABCUtils.formatPrice(currentTotalPrice)}`;
            } else {
                rightTitle = `${chargeForm.chargeFormItems!.length}种, ${abcI18Next.t("¥")}${ABCUtils.formatPrice(currentTotalPrice!)}`;
            }

            const formItems: ChargeFormItem[] = [];
            for (const item of chargeForm.chargeFormItems ?? []) {
                if (item.canRefund) formItems.push(item);
            }

            if (ABCUtils.isEmpty(formItems)) return <View />;

            const lastIndex = formItems.length - 1;
            const widgets = formItems.map((item, index) => (
                <_ChargeMedicineNonEditableListItem
                    key={index.toString()}
                    style={[styles.itemStyle, index != lastIndex ? ABCStyles.bottomLine : {}, { height: Sizes.listItemHeight }]}
                    count={self.bloc.currentState.countInputItems.get(item)!}
                    chargeForm={chargeForm}
                    chargeItem={item}
                />
            ));

            return (
                <View key={index.toString()} style={{ backgroundColor: Colors.white }}>
                    <CardHeader
                        titleRender={() => {
                            return (
                                <View
                                    style={ABCStyles.rowAlignCenter}
                                    onClick={() => !self.isWholeChargeRefundEnabled && self.bloc.requestToggleChargeFormSelect(chargeForm)}
                                >
                                    <AbcCheckbox
                                        checkIconBgColor={Colors.white}
                                        check={self.bloc.currentState.isChargeItemSelected(_.first(chargeForm.chargeFormItems)!)}
                                        enable={false}
                                        style={{ marginRight: Sizes.dp4 }}
                                    />
                                    <Text style={TextStyles.t16MM.copyWith({ color: Colors.airPharmacyTextColor })}>
                                        {chargeForm.vendorName ?? ""}
                                    </Text>
                                </View>
                            );
                        }}
                        right={rightTitle}
                        rightStyle={TextStyles.t16NT6}
                        showCardLeftLine={false}
                        showTopDivider={false}
                        showBorderLine={false}
                    />
                    {widgets}
                </View>
            );
        }

        return (
            <View>
                {airPharmacyForms.map((form, index) => {
                    return createFrom(form, index);
                })}
            </View>
        );
    }
}

interface _TreatmentPackageViewProps extends AbcViewProps {
    form: ChargeForm;
    formItem: ChargeFormItem;
    isWholeChargeRefundEnabled?: boolean;
}

class _TreatmentPackageView extends BaseComponent<_TreatmentPackageViewProps> {
    static contextType = ChargeInvoiceRefundDialogBloc.Context;

    constructor(props: _TreatmentPackageViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { form, formItem, isWholeChargeRefundEnabled, ...otherProps } = this.props;

        const state = ChargeInvoiceRefundDialogBloc.fromContext(this.context).currentState;
        const canRefundItems: ChargeFormItem[] = [];
        for (const subFormItem of formItem.composeChildren ?? []) {
            if (subFormItem.canRefund) canRefundItems.push(subFormItem);
        }
        const countInputItemsCopy = state.countInputItems;

        return (
            <View {...otherProps}>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            marginHorizontal: Sizes.listHorizontalMargin,
                            paddingVertical: Sizes.dp18,
                        },
                    ]}
                >
                    <Text style={TextStyles.t16NB}>{`${formItem.displayName}【套餐】`}</Text>
                </View>

                {canRefundItems.map((item) => (
                    <ChargeMedicineRefundListItem
                        key={item.compareKey()}
                        style={styles.itemStyle}
                        chargeForm={form}
                        chargeItem={item}
                        count={state.countInputItems.get(item)!}
                        showCheckBox={true}
                        enableCheckBox={false}
                        checked={state.isChargeItemSelected(item)}
                        isWholeSheetOperateEnabled={isWholeChargeRefundEnabled}
                        onClick={() =>
                            !isWholeChargeRefundEnabled &&
                            ChargeInvoiceRefundDialogBloc.fromContext(this.context).requestToggleItemSelect(item)
                        }
                        onCountInputValueChanged={(value, isBtn) => {
                            if (isWholeChargeRefundEnabled) return;
                            ChargeInvoiceRefundDialogBloc.fromContext(this.context).requestUpdateItemInputCount(item, value, isBtn);
                        }}
                        onItemEndChanged={() => ChargeInvoiceRefundDialogBloc.fromContext(this.context).requestCompareExecuteNumber(item)}
                        isShowWarning={
                            !isWholeChargeRefundEnabled &&
                            ChargeItemModifyUtils._isNeedCheckForModify(item) &&
                            (countInputItemsCopy.get(formItem) ?? 0) > Math.max(item.unitCount! - item.executedUnitCount!, 0)
                        }
                    />
                ))}
            </View>
        );
    }
}
// 文字状态标识
class ViewTextTag extends ExecuteChargeStatusViewText {
    statusInfo(): { text: string; textStyle: TextStyle } {
        const { chargeStatus, showChargedStatus } = this.props;
        let statusText = { text: "", textStyle: TextStyles.t12NT1 };
        if (chargeStatus == 1 && showChargedStatus) {
            statusText = {
                text: "抵扣",
                textStyle: TextStyles.t12NT2.copyWith({ color: Colors.chargeRefundTagColor, lineHeight: Sizes.dp16 }),
            };
        }
        return statusText;
    }
}

interface ChargeMedicineRefundListItemProps extends AbcViewProps {
    chargeForm: ChargeForm;
    chargeItem: ChargeFormItem;
    count: number;
    onCountInputValueChanged?: (value: number, isBtn?: boolean) => void;

    checked: boolean;
    enableCheckBox: boolean;
    showCheckBox: boolean;
    isCanCheckbox?: boolean;
    onItemEndChanged?: () => void;
    isShowWarning?: boolean;
    isWholeSheetOperateEnabled?: boolean;
    clinicBasicSetting?: GetClinicBasicSetting;
}

class ChargeMedicineRefundListItem extends BaseComponent<ChargeMedicineRefundListItemProps> {
    static defaultProps = {
        isCanCheckbox: true,
    };
    constructor(props: ChargeMedicineRefundListItemProps) {
        super(props);
    }

    // 获取复选框颜色
    private _getCheckboxColor(): string {
        const { isCanCheckbox, isWholeSheetOperateEnabled } = this.props;

        // 如果开启了整单收退费，则使用灰色
        if (isWholeSheetOperateEnabled) {
            return Colors.P1; // 灰色
        }
        // 否则使用正常颜色
        return isCanCheckbox ? Colors.mainColor : Colors.P1;
    }

    render(): JSX.Element {
        const {
            chargeForm,
            chargeItem,
            checked,
            count,
            onCountInputValueChanged,
            // enableCheckBox,
            showCheckBox,
            style,
            isCanCheckbox,
            onItemEndChanged,
            isShowWarning,
            clinicBasicSetting,
            isWholeSheetOperateEnabled,
            ...otherProps
        } = this.props;

        const onlyOneCount = chargeForm.isRegistration || chargeForm.isConsultation;
        let name = chargeItem.displayName;
        if (chargeItem.isRegistration) name = clinicBasicSetting?.registrationFeeStr ?? "";
        let forceFontColor = false;

        let statusIconName = "";
        if (chargeItem.status == ChargeFormItemStatus.refunded) {
            forceFontColor = true;
            statusIconName = "charge_item_status_refund";
        } else if (chargeItem.status == ChargeFormItemStatus.chargeBack) {
            forceFontColor = true;
            statusIconName = "charge_back";
        }

        let productInfo = "";
        let priceInfo = "";
        if (!chargeItem.isRegistration && chargeItem.productInfo != null) {
            productInfo = (chargeItem.productInfo as GoodsInfo).packageSpec;
            priceInfo = chargeItem.priceSpec;
        }

        return (
            <View style={[{ justifyContent: "center" }, flattenStyles(style)]} {...otherProps}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    {showCheckBox && (
                        <View style={{ paddingRight: Sizes.dp11 }}>
                            {checked ? (
                                <IconFontView
                                    name={"Chosen"}
                                    size={Sizes.dp16}
                                    color={this._getCheckboxColor()}
                                    style={{ position: "absolute" }}
                                />
                            ) : (
                                <View />
                            )}
                            <View
                                style={{
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    borderWidth: Sizes.dp1,
                                    borderRadius: Sizes.dp8,
                                    borderColor: Colors.P1,
                                    backgroundColor: !isCanCheckbox || isWholeSheetOperateEnabled ? Colors.whiteSmoke : undefined,
                                    opacity: checked ? 0 : 1,
                                }}
                            />
                        </View>
                    )}
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={[forceFontColor ? TextStyles.t16NT4 : TextStyles.t16NB, { flexShrink: 1 }]} numberOfLines={1}>
                            {name}
                        </Text>
                        {!onlyOneCount && (
                            <Text style={[forceFontColor ? TextStyles.t14NT4 : TextStyles.t14MB, { marginLeft: Sizes.dp2 }]}>
                                {_.isEmpty(chargeItem.unit) ? "/次" : `/${chargeItem.unit!.substring(0, 3)}`}
                            </Text>
                        )}
                        {!_.isEmpty(statusIconName) && (
                            <AssetImageView name={statusIconName} style={{ width: Sizes.dp41, height: Sizes.dp18 }} />
                        )}
                        {!!chargeItem?.isShowDeductTag && (
                            <ViewTextTag
                                style={{
                                    borderWidth: Sizes.dp1,
                                    borderColor: Colors.chargeRefundTagColor,
                                    borderRadius: Sizes.dp2,
                                    paddingHorizontal: Sizes.dp2,
                                    marginLeft: Sizes.dp2,
                                    marginRight: undefined,
                                }}
                                isRewriteMargin={true}
                                chargeStatus={1}
                                showChargedStatus={true}
                            />
                        )}
                    </View>

                    {!onlyOneCount && !chargeForm.isChinesePrescription && (
                        // eslint-disable-next-line @typescript-eslint/no-empty-function
                        <View
                            onClick={() => ({})}
                            style={{
                                width: Sizes.dp113,
                                marginLeft: !!chargeItem?.isShowDeductTag ? Sizes.dp0 : Sizes.dp20,
                            }}
                        >
                            <NewStyleNumberStepperInputView
                                maxCount={!!chargeItem?.isShowDeductTag ? chargeItem.canRefundDeductCount : chargeItem.canRefundUnitCount!}
                                minCount={0}
                                value={count}
                                floatPrecisionLength={
                                    (chargeItem.goodsInfo?.isChineseMedicine || chargeItem.goodsInfo.isGoods) ?? false
                                        ? Const.chineseMedicineSellPrecision
                                        : 0
                                }
                                onChanged={(value, textInput, isBtn) => onCountInputValueChanged?.(value, isBtn)}
                                onEndEditing={onItemEndChanged}
                                borderColor={isShowWarning ? Colors.Y2 : undefined}
                                disable={!isCanCheckbox || isWholeSheetOperateEnabled}
                            />
                        </View>
                    )}
                    {!onlyOneCount && chargeForm.isChinesePrescription && (
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <Text style={[TextStyles.t16NT6]}>{`${chargeItem.canRefundDoseCount}剂 * `}</Text>
                            <Text style={[TextStyles.t16NT6]}>
                                {`${!!chargeItem?.isShowDeductTag ? chargeItem.canRefundDeductCount : chargeItem.canRefundUnitCount!}`}
                            </Text>
                            <Text style={[TextStyles.t16NT6]}>{chargeItem.unit ?? ""}</Text>
                        </View>
                    )}

                    {onlyOneCount && <SizedBox width={Sizes.dp8} />}
                    {onlyOneCount && (
                        <Text style={TextStyles.t16NT6}>
                            {`${abcI18Next.t("¥")}${ABCUtils.formatPrice((chargeItem.totalPrice ?? 0) + (chargeItem.discountPrice ?? 0))}`}
                        </Text>
                    )}
                </View>
                {chargeForm.isMedicine && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                marginTop: Sizes.dp2,
                                marginLeft: showCheckBox ? Sizes.dp27 : undefined,
                            },
                        ]}
                    >
                        <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })}>{`${productInfo} ${priceInfo}`}</Text>
                    </View>
                )}
            </View>
        );
    }
}

interface _ChargeMedicineNonEditableListItemProps extends AbcViewProps {
    chargeForm: ChargeForm;
    chargeItem: ChargeFormItem;
    count: number;
}
class _ChargeMedicineNonEditableListItem extends BaseComponent<_ChargeMedicineNonEditableListItemProps> {
    constructor(props: ChargeMedicineRefundListItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForm, chargeItem, count, style, ...otherProps } = this.props;
        ignore(count);
        const name = chargeItem.displayName;

        let forceFontColor = false;

        let statusIconName = "";
        if (chargeItem.status == ChargeFormItemStatus.refunded) {
            forceFontColor = true;
            statusIconName = "charge_item_status_refund";
        } else if (chargeItem.status == ChargeFormItemStatus.chargeBack) {
            forceFontColor = true;
            statusIconName = "charge_back";
        }

        let productInfo = "";
        let priceInfo = "";
        if (!chargeItem.isRegistration && chargeItem.productInfo != null) {
            productInfo = (chargeItem.productInfo as GoodsInfo).packageSpec;
            priceInfo = chargeItem.priceSpec;
        }

        return (
            <View style={[{ justifyContent: "center" }, flattenStyles(style)]} {...otherProps}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={forceFontColor ? TextStyles.t16NT4 : TextStyles.t16NB} numberOfLines={1}>
                            {name}
                        </Text>
                        {!_.isEmpty(statusIconName) && (
                            <AssetImageView name={statusIconName} style={{ width: Sizes.dp41, height: Sizes.dp18 }} />
                        )}
                    </View>
                    <Text>{chargeItem.canRefundUnitCount!}</Text>
                    <SizedBox width={Sizes.dp20} />

                    <Text style={forceFontColor ? TextStyles.t14NT4 : TextStyles.t14NB}>
                        {_.isEmpty(chargeItem.unit) ? "次" : chargeItem.unit!}
                    </Text>
                </View>
                {chargeForm.isMedicine && (
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp12 }]}>
                        <Text style={TextStyles.t14NT6}>{`${productInfo} ${priceInfo}`}</Text>
                    </View>
                )}
            </View>
        );
    }
}
