/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/6/18
 *
 * @description
 *
 */
import { DividerLine, IconFontView, SizedBox, ToolBar } from "../base-ui";
import React from "react";
import { Text, View } from "@hippy/react";
import { ChargeInvoicePageBloc, ScrollToErrorViewState } from "./charge-invoice-page-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { BlocBuilder } from "../bloc";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceData,
    ChargeInvoiceDetailData,
    ChargeInvoiceType,
    ChargeSourceFormType,
    ChargeStatus,
    PayMethod,
    RetailType,
} from "./data/charge-beans";
import _ from "lodash";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import {
    ChargeFormItemAmountInputView,
    ChargeFormNewHeader,
    ChargeMedicineListItem,
    ChargeRegistrationListItem,
    ChargeStatusTextView,
    ChargeStatusView,
    DecoctionFeeItem,
    DeliveryFeeItem,
} from "./view/charge-views";
import { BaseComponent } from "../base-ui/base-component";
import { AbcCheckbox } from "../base-ui/views/abc-checkbox";
import { ABCUtils } from "../base-ui/utils/utils";
import { GoodsType, HistoryPermissionModuleType } from "../base-business/data/beans";
import { NewCardFooter } from "../views/card-header";
import { GroupDivider } from "../base-ui/divider-line";
import { ChargeUtils } from "./utils/charge-utils";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { AbcPopMenu, MenuItem } from "../base-ui/views/pop-menu";
import { ValueHolder } from "../base-ui/utils/value-holder";
import { TreeDotView } from "../base-ui/iconfont/iconfont-view";
import { AirPharmacyPrescriptionForm } from "./view/air-pharmacy-prescription-form";
import { ChargeInvoiceEditView } from "./charge-invoice-edit-view";
import { AbcScrollView } from "../base-ui/views/abc-scroll-view";
import { DoctorAndDiagnosisView } from "./view/doctor-and-diagnosis-view";
import { ChargeInvoiceChargeActionDetailDialog } from "./charge-invoice-charge-action-detail-dialog";
import {
    ChinesePrescriptionInfoFooter,
    ChinesePrescriptionNewUsageView,
} from "../outpatient/views/outpatient-chinese-prescription-usage-view";
import { AbcView } from "../base-ui/views/abc-view";
import PatientHospitalDetailView from "../views/patient-hospital-info/patient-hospital-detail-view";
import AbcPatientCardInfoView from "../outpatient/views/new-patient-Info-view";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { LogUtils } from "../common-base-module/log";
import {
    AbcToolBar,
    AbcToolBarButton,
    AbcToolBarButtonStyle1,
    AbcToolBarButtonStyle2,
} from "../base-ui/abc-app-library/tool-bar-button/tool-bar";
import { KeyboardListenerView } from "../base-ui/views/keyboard-listener-view";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import FontWeight from "../theme/font-weights";
import WillPopListener from "../base-ui/views/will-pop-listener";
import abcI18Next from "../language/config";
import { AirPharmacyOrderIdView } from "../views/air-pharmacy-order-id-view";
import { AbcText } from "../base-ui/views/abc-text";
import { userCenter } from "../user-center";
import { ChargeProductEmployeeView } from "./view/charge-product-employee-view";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";
import { PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { DeviceUtils } from "../base-ui/utils/device-utils";

interface ChargeInvoicePageProps {
    chargeId: string;
}

const kFirstChild = "ChargeInvoicePage.firstChild";
const kLastChild = "ChargeInvoicePage.lastChild";

enum ChargeMenuItemValue {
    addNewChargeItem, //新增项目
    shareCharge, //挂单
    closeChargeSheet, //关闭
    send, //推送
    save, //保存
}

export class ChargeInvoicePage extends BaseBlocNetworkPage<ChargeInvoicePageProps, ChargeInvoicePageBloc> {
    private _scrollView?: AbcScrollView | null;

    constructor(props: ChargeInvoicePageProps) {
        super(props);

        this.bloc = new ChargeInvoicePageBloc(this.props.chargeId);
        this.addDisposable(this.bloc);
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const detailData = this.bloc.currentState.detailData;
        return (
            <BlocBuilder
                bloc={this.bloc}
                condition={() => true}
                build={() => (
                    <View style={ABCStyles.rowAlignCenter}>
                        <SizedBox width={Sizes.dp80} />
                        <Text style={TextStyles.t18MT1}>{this.getAppBarTitle()}</Text>
                        <ChargeStatusTextView
                            style={{
                                width: Sizes.dp80,
                                marginLeft: Sizes.dp8,
                            }}
                            chargeStatus={detailData?.status}
                            showChargedStatus={!this.bloc.currentState.canHospitalDischarge}
                            isClosed={!!detailData?.isClosed}
                            isOweList={detailData?.isFromOweList}
                        />
                    </View>
                )}
            />
        );
    }

    getAppBarTitle(): string {
        const detailData = this.bloc.currentState.detailData;
        const { canHospitalDischarge } = this.bloc.currentState;
        if (detailData == undefined) {
            return "收费单";
        }
        const type = detailData.type;
        if (type === ChargeInvoiceType.clonePrescription) {
            return "续方收费";
        } else if (type == ChargeInvoiceType.retail || type == ChargeInvoiceType.therapy) return "零售收费单";
        else if (type === ChargeInvoiceType.consultationScheme) return "咨询方案";
        else if (detailData.isOnline == 1) {
            //网诊收费单
            return "网诊收费单";
        } else if (canHospitalDischarge) {
            return "长护欠费";
        } else return "门诊收费单";
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    onBackClick(): void {
        this.bloc.requestBack();
    }

    componentDidMount(): void {
        this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            if (state.loadingDetailData) {
                status = ABCNetworkPageContentStatus.loading;
            } else if (state.loadingDetailError) {
                status = ABCNetworkPageContentStatus.error;
            }

            this.setContentStatus(status, state.loadingDetailError);

            if (state instanceof ScrollToErrorViewState && state.focusErrorViewKey) {
                this._scrollView?.scrollChildToVisible(state.focusErrorViewKey, kFirstChild, kLastChild);
            }
        });
    }

    reloadData(): void {
        this.bloc.requestReloadData();
    }

    getRightAppBarIcons(): JSX.Element[] {
        const detailData = this.bloc.currentState.detailData;
        if (!detailData) return [];
        return [
            <View key={"all"} style={ABCStyles.rowAlignCenter}>
                <View key={"setting"} style={{ marginRight: Sizes.dp8 }}>
                    {this.chargeSettingIcons()}
                </View>
            </View>,
        ];
    }

    private _createMenuItemsList(): MenuItem<number>[] {
        const state = this.bloc.currentState;
        const detailData = state.detailData;
        const menuItems: MenuItem<number>[] = [];

        if (!detailData) return menuItems;

        const { canEditChargeSheet, isClosed, status: chargeStatus } = detailData;
        const charged = chargeStatus != ChargeStatus.unCharged && !(detailData.reCharge__ ?? false);

        if (chargeStatus == ChargeStatus.unCharged && !detailData.isNetworkDraft && canEditChargeSheet) {
            menuItems.push(
                new MenuItem({
                    value: ChargeMenuItemValue.shareCharge,
                    icon: <IconFontView name={"set"} color={Colors.T1} size={Sizes.dp24} />,
                    text: "挂单",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (detailData.canClose()) {
            menuItems.push(
                new MenuItem({
                    value: ChargeMenuItemValue.closeChargeSheet,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp24} />,
                    text: "关闭",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (!charged) {
            if (canEditChargeSheet)
                menuItems.push(
                    new MenuItem({
                        value: ChargeMenuItemValue.addNewChargeItem,
                        icon: <IconFontView name={"plus_circle"} color={Colors.T1} size={Sizes.dp24} />,
                        text: "新增项目",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
        }
        if (!isClosed && (chargeStatus == ChargeStatus.unCharged || chargeStatus == ChargeStatus.draft)) {
            if (state.canPushChargeOrder && canEditChargeSheet) {
                menuItems.push(
                    new MenuItem({
                        value: ChargeMenuItemValue.send,
                        icon: <IconFontView name={"push"} color={Colors.T1} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                        text: "推送",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
            if (state.hasChanged && canEditChargeSheet) {
                menuItems.push(
                    new MenuItem({
                        value: ChargeMenuItemValue.save,
                        icon: <IconFontView name={"save"} color={Colors.T1} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                        text: "保存",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        } else if (chargeStatus == ChargeStatus.partCharged) {
            if (state.canPushChargeOrder && detailData.chargeSheetSummary?.receivedFee == 0) {
                menuItems.push(
                    new MenuItem({
                        value: ChargeMenuItemValue.send,
                        icon: <IconFontView name={"send"} color={Colors.T1} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                        text: "推送",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        }

        return menuItems;
    }

    private async operateSettingIcon(menuItems: MenuItem<number>[]): Promise<void> {
        const select = await AbcPopMenu.show(
            menuItems,
            { x: Sizes.dp24, y: Sizes.dp42 },
            { x: Sizes.dp30, y: Sizes.dp42 },
            { fullGrid: true }
        );
        switch (select) {
            case ChargeMenuItemValue.shareCharge: {
                this.bloc.requestSavePendingOrder();
                break;
            }
            case ChargeMenuItemValue.closeChargeSheet: {
                this.bloc.requestCloseChargeSheet();
                break;
            }
            case ChargeMenuItemValue.addNewChargeItem: {
                this.bloc.requestAddNewChargeItem();
                break;
            }
            case ChargeMenuItemValue.send: {
                this.bloc.requestSendChargeInvoice();
                break;
            }
            case ChargeMenuItemValue.save: {
                this.bloc.requestSave();
                break;
            }
        }
    }

    chargeSettingIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        const _key = 0;
        if (state.detailData == undefined || state.detailData.orderIsLocking) return [<View key={_key} />];

        const menuItems: MenuItem<number>[] = this._createMenuItemsList();

        if (_.isEmpty(menuItems)) return [<View key={_key} />];
        return [
            <AbcView
                key={_key}
                style={{ paddingHorizontal: Sizes.dp8 }}
                collapsable={false}
                onClick={() => this.operateSettingIcon(menuItems)}
            >
                <TreeDotView color={Colors.T1} />
            </AbcView>,
        ];
    }

    get detailData(): ChargeInvoiceDetailData {
        return this.bloc.currentState.detailData!;
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const detailData = state.detailData;
        if (!detailData) return <View />;

        const { isHospitalizationSheet, hospitalOrderId, copyPatientOrderLocks, lockCopy, orderIsLocking, chargeLockOrder } = detailData;
        const isHasOweBill = detailData.isFromOweList; //当前单子是否为欠费单子
        const { doctorDisplayStr, nurseDisplayStr, treatmentChargeForm, isDentistry, consultantList, shebaoRefundException } = state;
        const hasModify = detailData.canEditChargeSheet && (!detailData.status || detailData.status == ChargeStatus.draft);
        const canModifyDoctorNurse =
            hasModify && (detailData.type == ChargeInvoiceType.retail || detailData.type == ChargeInvoiceType.therapy);
        const consultantInitIndex = consultantList?.findIndex((t) => t?.employeeId == detailData?.consultantId);
        // 如果本单有收费异常内容，又正在收费中，仅展示收费中横幅
        const isExistChargeInProgress =
            copyPatientOrderLocks?.find((t) => t.businessKey == PatientOrderLockType.chargeSheetPay && !!t.status) || chargeLockOrder;
        const isOhos = DeviceUtils.isOhos();
        if (detailData.outpatientStatus == ChargeInvoiceData.outpatientStatusWaiting) {
            return (
                <View style={{ flex: 1, ...(isOhos ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                    <WillPopListener
                        onWillPop={() => {
                            this.onBackClick();
                        }}
                    />
                    {!isExistChargeInProgress && detailData.isSheBaoAbnormal && (
                        <AbcBannerTips tips={`医保${shebaoRefundException ? "退费" : "收费"}异常，请前往客户端处理`} />
                    )}
                    {!isExistChargeInProgress && detailData.isNotSheBaoAbnormal && (
                        <AbcBannerTips
                            tips={"收费单收费异常，请及时处理"}
                            onMaskClick={() => {
                                this.bloc.requestHandleChargeAbnormal(!detailData.isNotSheBaoAbnormal);
                            }}
                        />
                    )}
                    {orderIsLocking && !!lockCopy.tips && (
                        <AbcBannerTips
                            tips={lockCopy.tips}
                            cancelText={lockCopy.isCancel ? "支付遇到问题？" : ""}
                            cancelOperate={() => this.bloc.requestCancelPayment()}
                        />
                    )}
                    <AbcScrollView
                        ref={(ref) => (this._scrollView = ref)}
                        showsVerticalScrollIndicator={false}
                        style={isOhos ? {} : { flex: 1 }}
                    >
                        <View ref={kFirstChild} collapsable={false} />
                        <AbcPatientCardInfoView
                            isEditing={false}
                            patient={detailData.patient}
                            diagnoseCount={state.diagnoseCount}
                            type={HistoryPermissionModuleType.cashier}
                            isCanSeePatientHistoryInCashier={state.canSeePatientHistory}
                            canSeePatientMobileInCashier={state.canSeePatientMobile}
                            canEditPatientInfo={!orderIsLocking}
                        />

                        <ChargeInvoiceEditView bloc={this.bloc.editViewBloc!} />
                        <AirPharmacyOrderIdView orderId={detailData.airPharmacyOrderId} />
                        <View ref={kLastChild} collapsable={false} />
                    </AbcScrollView>

                    {this._renderCalculateFailedBar()}
                    {this._renderBottomBarV2()}
                    <View ref={"ChargeInvoicePage"} collapsable={false} />
                </View>
            );
        }

        const chargeCount = new ValueHolder<number>();
        return (
            <View style={{ flex: 1, ...(isOhos ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                <WillPopListener
                    onWillPop={() => {
                        this.onBackClick();
                    }}
                />
                {!isExistChargeInProgress && detailData.isSheBaoAbnormal && (
                    <AbcBannerTips tips={`医保${shebaoRefundException ? "退费" : "收费"}异常，请前往客户端处理`} />
                )}
                {!isExistChargeInProgress && detailData.isNotSheBaoAbnormal && (
                    <AbcBannerTips
                        tips={"收费单收费异常，请及时处理"}
                        onMaskClick={() => {
                            this.bloc.requestHandleChargeAbnormal(!detailData.isNotSheBaoAbnormal);
                        }}
                    />
                )}
                {orderIsLocking && !!lockCopy.tips && (
                    <AbcBannerTips
                        tips={lockCopy.tips}
                        cancelText={lockCopy.isCancel ? "支付遇到问题？" : ""}
                        cancelOperate={() => this.bloc.requestCancelPayment()}
                    />
                )}
                <AbcScrollView style={isOhos ? {} : { flex: 1 }} ref={(ref) => (this._scrollView = ref)}>
                    <View ref={kFirstChild} collapsable={false} />
                    <AbcPatientCardInfoView
                        isEditing={false}
                        patient={detailData.patient}
                        diagnoseCount={state.diagnoseCount}
                        isCanSeePatientHistoryInCashier={state.canSeePatientHistory}
                        type={HistoryPermissionModuleType.cashier}
                        canSeePatientMobileInCashier={state.canSeePatientMobile}
                        canEditPatientInfo={!orderIsLocking}
                    />
                    {!!detailData?.sellerName &&
                        this._renderSellerInfoWidget({
                            title: "开单人",
                            content: `${this.detailData.sellerName ?? ""}${this.detailData.defaultDepartmentName} `,
                        })}
                    {isHospitalizationSheet && (
                        <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                            <PatientHospitalDetailView
                                orderId={hospitalOrderId}
                                onChange={(detail) => {
                                    !orderIsLocking && this.bloc.handleHospitalDetail(detail);
                                }}
                            />
                        </AbcBasePanel>
                    )}
                    {isDentistry && (
                        <ChargeProductEmployeeView
                            doctorNurseDisplayStr={`${doctorDisplayStr}${!!nurseDisplayStr ? "、" : ""}${nurseDisplayStr}` ?? ""}
                            consultantDisplayStr={detailData?.consultantName ?? ""}
                            showDoctorItem={!!treatmentChargeForm?.length}
                            showConsultantItem={true}
                            canModifyDoctorNurse={canModifyDoctorNurse}
                            canModifyConsultant={hasModify}
                            consultantList={consultantList}
                            consultantInitIndex={consultantInitIndex}
                            isLockingOrder={orderIsLocking}
                            modifyProductEmployee={() => this.bloc.requestModifyProductEmployee()}
                            modifyConsultant={(index) => this.bloc.requestModifyConsultant(index)}
                        />
                    )}
                    <AbcBasePanel
                        panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp20, Sizes.dp8, 0)}
                        contentStyle={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, 0)]}
                    >
                        {this._renderChargeForm(chargeCount)}
                        {this._renderPrescriptionChargeForm(chargeCount.value! > 0)}
                    </AbcBasePanel>
                    {this._renderAirPharmacyPrescriptionChargeForm()}
                    <AirPharmacyOrderIdView orderId={detailData.airPharmacyOrderId} />
                    <GroupDivider />
                    <View ref={kLastChild} collapsable={false} />
                </AbcScrollView>
                {this._renderCalculateFailedBar()}
                {!isHasOweBill && this._renderBottomBarV2()}
                {isHasOweBill && this._renderOweBottomBar()}
            </View>
        );
    }

    _renderSellerInfoWidget(options: { title: string; content: string }): JSX.Element {
        return (
            <AbcBasePanel
                panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}
                contentStyle={Sizes.paddingLTRB(Sizes.dp16, Sizes.dp18)}
            >
                <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                    <Text style={[TextStyles.t16NT1]}>{options?.title}</Text>
                    <Text numberOfLines={1} style={[TextStyles.t16NT1, { flexShrink: 1 }]}>
                        {options?.content}
                    </Text>
                </View>
            </AbcBasePanel>
        );
    }

    _renderChargeForm(childCountOut: ValueHolder<number>): JSX.Element {
        const bloc = this.bloc,
            state = bloc.currentState;
        const self = this;
        const { showErrorHint, pharmacyInfoConfig, detailData, isOpenProcess } = state;
        const { canEditChargeSheet, retailType } = detailData;
        const chargedStatus = detailData.status == ChargeStatus.charged;
        const openWholeSheetOperateEnable = state.chargeConfig?.openWholeSheetOperateEnable;

        function _createItem(chargeForm: ChargeForm, item: ChargeFormItem) {
            const select = state.isChargeItemSelected(chargeForm, item);
            // 一次都没有收费过的单子，如果存在已退单的item，允许可以选择
            const isRefundItem = detailData.status == ChargeStatus.unCharged && item.status == ChargeFormItemStatus.chargeBack;
            const showCheckBox = (canEditChargeSheet && item.status === ChargeFormItemStatus.unCharged) || isRefundItem;
            if (item.localAdd ?? false) {
                return (
                    <ChargeFormItemAmountInputView
                        key={item.compareKey()}
                        chargeFormItem={item}
                        select={true}
                        showCheckBox={true}
                        checked={select}
                        autoFocus={true}
                        onClick={() => bloc.requestToggleChargeItem(chargeForm, item)}
                        onEditFocusChanged={(focus) => bloc.requestChangeFocusMedicineItem(item, focus)}
                        onChanged={(count, unit) => bloc.requestUpdateChargeItemAmount(item, count, unit)}
                        onCloseTap={() => bloc.requestDeleteItem(item)}
                    />
                );
            }

            if (chargeForm.isConsultation)
                return (
                    <View key={item.compareKey()}>
                        <ChargeConsultationListItem
                            chargeForm={chargeForm}
                            chargeItem={item}
                            showCheckBox={false}
                            checked={select}
                            enableCheckBox
                        />
                    </View>
                );

            return (
                <ChargeMedicineListItem
                    style={{
                        paddingVertical: Sizes.dp8,
                    }}
                    key={item.compareKey()}
                    onClick={() => !item.itemIsExcuted && self._onTapChargeItem(chargeForm, item)}
                    chargeForm={chargeForm}
                    chargeItem={item}
                    showCheckBox={showCheckBox}
                    isOpenMultiplePharmacy={state.pharmacyInfoConfig?.isOpenMultiplePharmacy}
                    isHasPharmacyOperate={
                        state.pharmacyInfoConfig?.isOpenMultiplePharmacy &&
                        pharmacyInfoConfig?.getDefaultPharmacy({
                            departmentId: detailData?.departmentId,
                            goodsInfo: {
                                typeId: item.goodsInfo?.typeId,
                            },
                        })?.no != item?.pharmacyNo &&
                        !chargeForm.isChinesePrescription &&
                        item.isCanSpecifyPharmacy
                    }
                    defaultPharmacyNo={
                        pharmacyInfoConfig?.getDefaultPharmacy({
                            departmentId: detailData?.departmentId,
                            goodsInfo: {
                                typeId: item.goodsInfo?.typeId,
                            },
                        })?.no
                    }
                    onChangePharmacyType={() => showCheckBox && bloc.requestChangePharmacyType(chargeForm, item)}
                    onChangeRemark={() => showCheckBox && bloc.requestChangeRemark(chargeForm.sourceFormType, item)}
                    onDeleteRemark={() => showCheckBox && bloc.requestDeleteRemark(chargeForm.sourceFormType, item)}
                    checked={select}
                    chargeSheetStatus={detailData.status}
                    isWholeSheetOperateEnabled={openWholeSheetOperateEnable}
                    onDiscountChange={(discount) => bloc.requestChangeDiscount(item, discount)}
                    onTotalPriceChange={(totalPrice) => bloc.requestChangeTotalPrice(item, totalPrice)}
                />
            );
        }

        const items: JSX.Element[] = [];
        const unCharged = state.detailData.status == ChargeStatus.unCharged;

        let chargeCount = 0;
        let chargePrice = 0;

        const decoctionForms = detailData.chargeForms?.filter((form) => form.isDecoction);

        if (retailType == RetailType.zhuanLu) {
            items.push(<DoctorAndDiagnosisView editable={false} chargeData={detailData} key={"DoctorAndDiagnosisView"} />);
        }

        if (!_.isEmpty(decoctionForms)) {
            decoctionForms?.forEach((form) => {
                const formItem = form.getFormItem(GoodsType.decoctionFee)!;
                items.push(
                    <DecoctionFeeItem
                        key={form.compareKey()}
                        editable={canEditChargeSheet}
                        fee={formItem.unitPrice!}
                        feeMutable={unCharged}
                        chargeStatus={form?.status}
                        bottomLine={true}
                        chargeForm={form}
                        chargeData={detailData}
                        isOpenTakeMedicine={state.isOpenTakeMedicine}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                        onChanged={(contactMobile?: string, decoctionForms?: ChargeForm[]) =>
                            this.bloc.requestUpdateDecoctionInfo(contactMobile, decoctionForms)
                        }
                    />
                );
            });
        }

        if (detailData.deliveryType__ == 1) {
            let errorDeliveryInfo = false;
            if (showErrorHint && detailData.status == ChargeStatus.unCharged) {
                errorDeliveryInfo = !ChargeUtils.validateDeliveryInfoForCharge(detailData);
            }

            const deliveryInfo = detailData.deliveryInfo ?? detailData.deliveryInfoFromChargeForm;
            items.push(
                <DeliveryFeeItem
                    editable={canEditChargeSheet}
                    key={"DeliveryFeeItem"}
                    deliveryInfo={deliveryInfo}
                    chargeData={detailData}
                    patient={detailData.patient}
                    bottomLine={false}
                    chargeStatus={ChargeFormItemStatus.charged}
                    feeMutable={unCharged}
                    charged={!unCharged}
                    isCanCheckDetail={detailData.status == ChargeStatus.charged}
                    style={{
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        ...(errorDeliveryInfo ? ABCStyles.errorBorder : {}),
                    }}
                    onChanged={(deliveryInfo) => {
                        this.bloc.requestUpdateDeliveryInfo(deliveryInfo);
                    }}
                />
            );
        }

        const registrationForm = detailData.getChargeForm(ChargeSourceFormType.registration);
        if (registrationForm && !_.isEmpty(registrationForm.chargeFormItems)) {
            const item = registrationForm.chargeFormItems![0];

            items.push(
                <View
                    key={item.compareKey()}
                    style={[
                        ABCStyles.bottomLine,
                        {
                            justifyContent: "center",
                            paddingVertical: Sizes.listHorizontalMargin,
                            flex: 1,
                        },
                    ]}
                    onClick={() => canEditChargeSheet && self._onTapChargeItem(registrationForm, item)}
                >
                    <ChargeRegistrationListItem
                        chargeForm={registrationForm}
                        chargeFormItem={item}
                        showCheckBox={
                            (canEditChargeSheet && item.status == ChargeFormItemStatus.unCharged) ||
                            (detailData.status == ChargeStatus.unCharged && item.status == ChargeFormItemStatus.chargeBack)
                        }
                        checked={state.isChargeItemSelected(registrationForm, item)}
                        departmentMutable={state.departmentMutable}
                        doctorMutable={state.doctorMutable}
                        onDoctorChanged={(department, doctor) => bloc.requestUpdateDoctor(item, department, doctor)}
                        clinicBasicSetting={state.clinicBasicSetting}
                        chargeSheetStatus={detailData.status}
                        isWholeSheetOperateEnabled={openWholeSheetOperateEnable}
                    />
                </View>
            );
        }

        const doctorFamilyForm = detailData.getChargeForm(ChargeSourceFormType.familyDoctor);
        if (doctorFamilyForm && !_.isEmpty(doctorFamilyForm.chargeFormItems)) {
            const item = doctorFamilyForm.chargeFormItems![0];

            items.push(
                <View
                    key={item.compareKey()}
                    style={[
                        ABCStyles.listHorizontalPadding,
                        {
                            height: Sizes.listItemHeight,
                            justifyContent: "center",
                        },
                    ]}
                    onClick={() => canEditChargeSheet && self._onTapChargeItem(doctorFamilyForm, item)}
                >
                    <ChargeRegistrationListItem
                        chargeForm={doctorFamilyForm}
                        chargeFormItem={item}
                        showCheckBox={canEditChargeSheet && item.status == ChargeFormItemStatus.unCharged}
                        checked={state.isChargeItemSelected(doctorFamilyForm, item)}
                        chargeSheetStatus={detailData.status}
                    />
                </View>
            );
        }
        let isExistMultipleBatch = false;
        const productChargeForm = detailData.chargeForms?.filter(
            (chargeForm) =>
                chargeForm.isConsultation ||
                chargeForm.isTreatment ||
                chargeForm.isExamination ||
                chargeForm.isAdditional ||
                chargeForm.isMaterial ||
                chargeForm.isPackage ||
                chargeForm.isGlasses ||
                chargeForm.isNurseProductFee ||
                chargeForm.isOthersFee ||
                chargeForm.isSurgery
        );

        detailData.chargeForms?.forEach((chargeForm) => {
            if (
                chargeForm.isConsultation ||
                chargeForm.isTreatment ||
                chargeForm.isExamination ||
                chargeForm.isAdditional ||
                chargeForm.isMaterial ||
                chargeForm.isPackage ||
                chargeForm.isGlasses ||
                chargeForm.isNurseProductFee ||
                chargeForm.isOthersFee ||
                chargeForm.isSurgery
            ) {
                chargePrice += chargeForm.totalPrice ?? 0.0;
                chargeForm.chargeFormItems?.forEach((formItem) => {
                    chargeCount++;
                    if (chargeCount == 1) items.push(<SizedBox height={Sizes.dp8} />);
                    items.push(_createItem(chargeForm, formItem));
                });
                isExistMultipleBatch = !!chargeForm?.chargeFormItems?.some((t) => !!t.chargeFormItemBatchInfos?.length);
            }
        });

        childCountOut.value = chargeCount;
        const otherPrescription = detailData.chargeForms?.filter(
            (t) =>
                (t.isChinesePrescription && !t.isVirtualPharmacy) ||
                t.isWesternPrescription ||
                t.isInfusionPrescription ||
                t.isGoods ||
                t.isExternal
        );

        return (
            <View key={"chargeForm"}>
                <ChargeFormNewHeader
                    decoction={!!detailData.getChargeForm(ChargeSourceFormType.decoction)}
                    delivery={detailData.deliveryType__ === 1}
                    mutable={canEditChargeSheet && detailData.status == ChargeStatus.unCharged}
                    onChanged={(decoction, delivery) => bloc.requestToggleDecoctionDelivery(decoction, delivery)}
                    showDoctorDiagnosis={retailType === RetailType.zhuanLu}
                    doctorDiagnosis={retailType === RetailType.zhuanLu}
                    notShowBottomLine={chargeCount == 0}
                    isOpenProcess={isOpenProcess}
                />
                {chargedStatus && (
                    <AssetImageView
                        name={"charge_seal"}
                        style={{
                            position: "absolute",
                            height: Sizes.dp46,
                            width: Sizes.dp53,
                            top: -Sizes.dp16,
                            right: -Sizes.dp16,
                        }}
                    />
                )}
                <View style={{ flexShrink: 1 }}>{items}</View>
                {/*如果当前收费项没有诊疗项目那些，并且各个处方也没有，则底部距离要有16px*/}
                {chargeCount == 0 && !otherPrescription?.length && <SizedBox height={Sizes.dp16} />}
                {chargeCount > 0 && (
                    <AbcView
                        style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}
                        onClick={() =>
                            !!productChargeForm?.length && isExistMultipleBatch && self.bloc.requestCheckGoodsBatchInfo(productChargeForm)
                        }
                    >
                        <NewCardFooter
                            rightTitle={`诊疗项目${chargeCount}项，${abcI18Next.t("¥")}${ABCUtils.formatPrice(chargePrice)}`}
                            containerStyle={{ paddingBottom: Sizes.dp16, paddingTop: Sizes.dp8, paddingRight: Sizes.dp4 }}
                        />
                        {isExistMultipleBatch && (
                            <View style={{ ...ABCStyles.rowAlignCenter, paddingTop: Sizes.dp8, paddingBottom: Sizes.dp16 }}>
                                <SizedBox width={Sizes.dp4} />
                                <IconFontView name={"arrow_down"} color={Colors.T6} size={Sizes.dp16} />
                            </View>
                        )}
                    </AbcView>
                )}
            </View>
        );
    }

    private _getFooterTitle(chargeForm: ChargeForm, title: string) {
        const goodsIds = new Set<string>();
        chargeForm.chargeFormItems?.forEach((item) => {
            goodsIds.add(item.productId!);
        });

        let rightTitle: string;
        if (chargeForm.isChinesePrescription) {
            rightTitle = `${goodsIds.size}味，${chargeForm.chargeFormItems![0]!.doseCount!}剂，${abcI18Next.t("¥")}${ABCUtils.formatPrice(
                chargeForm.totalPrice!
            )}`;
        } else {
            rightTitle = `${title}${goodsIds.size}种，${abcI18Next.t("¥")}${ABCUtils.formatPrice(chargeForm.totalPrice!)}`;
        }

        return rightTitle;
    }

    _renderPrescriptionChargeForm(hasPreChargeItems: boolean): JSX.Element[] {
        const detailData = this.detailData;
        const chineseForms: ChargeForm[] = [];
        const westernForms: ChargeForm[] = [];
        const infusionForms: ChargeForm[] = [];
        const goodsForms: ChargeForm[] = [];
        const externalForms: ChargeForm[] = [];

        for (const form of detailData.chargeForms!) {
            if (form.isChinesePrescription && !form.isVirtualPharmacy) chineseForms.push(form);
            else if (form.isWesternPrescription) westernForms.push(form);
            else if (form.isInfusionPrescription) infusionForms.push(form);
            else if (form.isGoods) goodsForms.push(form);
            else if (form.isExternal) externalForms.push(form);
        }

        const { status: chargeStatus, canEditChargeSheet } = detailData;

        const showCheckBox = (canEditChargeSheet && chargeStatus == ChargeStatus.unCharged) || chargeStatus == ChargeStatus.draft;

        const state = this.bloc.currentState;
        const bloc = this.bloc;

        const self = this;

        let isFirstGroup = !hasPreChargeItems; //前面已经有收费项了，这里第一组就不用显示分割符了

        function createFrom(title: string, chargeForm: ChargeForm) {
            const oldIsFirstGroup = isFirstGroup;
            LogUtils.d(oldIsFirstGroup);
            isFirstGroup = false;
            const isExistMultipleBatch = chargeForm.chargeFormItems?.some((t) => !!t.chargeFormItemBatchInfos?.length);
            return (
                <View
                    style={{
                        backgroundColor: Colors.white,
                        paddingTop: Sizes.dp8,
                    }}
                    key={chargeForm.compareKey()}
                >
                    {chargeForm.chargeFormItems?.map((formItem, index) => {
                        if (formItem.localAdd ?? false)
                            return (
                                <ChargeFormItemAmountInputView
                                    style={flattenStyles([
                                        index != chargeForm.chargeFormItems?.length ? ABCStyles.bottomLine : {},
                                        {
                                            paddingVertical: Sizes.listHorizontalMargin,
                                            paddingHorizontal: Sizes.listHorizontalMargin,
                                        },
                                    ])}
                                    key={index.toString()}
                                    chargeFormItem={formItem}
                                    select={false}
                                    showCheckBox={true}
                                    checked={state.isChargeItemSelected(chargeForm, formItem)}
                                    onClick={() => bloc.requestToggleChargeItem(chargeForm, formItem)}
                                    onEditFocusChanged={(focus) => bloc.requestChangeFocusMedicineItem(formItem, focus)}
                                    onChanged={(count, unit) => bloc.requestUpdateChargeItemAmount(formItem, count, unit)}
                                />
                            );

                        return (
                            <ChargeMedicineListItem
                                chargeForm={chargeForm}
                                chargeItem={formItem}
                                showCheckBox={showCheckBox}
                                enableCheckBox={formItem.stockInfo(chargeForm.pharmacyType).stockEnough}
                                checked={state.isChargeItemSelected(chargeForm, formItem)}
                                key={index.toString()}
                                chargeSheetStatus={chargeStatus}
                                isWholeSheetOperateEnabled={state.chargeConfig?.openWholeSheetOperateEnable}
                                style={{
                                    justifyContent: "center",
                                    paddingVertical: Sizes.dp8,
                                }}
                                onClick={() => self._onTapChargeItem(chargeForm, formItem)}
                                isHasPharmacyOperate={
                                    state.pharmacyInfoConfig?.isOpenMultiplePharmacy &&
                                    state.pharmacyInfoConfig?.getDefaultPharmacy({
                                        departmentId: detailData.departmentId,
                                        goodsInfo: {
                                            typeId: formItem?.goodsInfo?.typeId,
                                        },
                                    })?.no != formItem?.pharmacyNo &&
                                    formItem.isCanSpecifyPharmacy
                                }
                                defaultPharmacyNo={
                                    state.pharmacyInfoConfig?.getDefaultPharmacy({
                                        departmentId: detailData.departmentId,
                                        goodsInfo: {
                                            typeId: formItem?.goodsInfo?.typeId,
                                        },
                                    })?.no
                                }
                                isOpenMultiplePharmacy={state.pharmacyInfoConfig?.isOpenMultiplePharmacy}
                                onChangePharmacyType={() => showCheckBox && bloc.requestChangePharmacyType(chargeForm, formItem)}
                                onChangeRemark={() => showCheckBox && bloc.requestChangeRemark(chargeForm.sourceFormType, formItem)}
                                onDeleteRemark={() => showCheckBox && bloc.requestDeleteRemark(chargeForm.sourceFormType, formItem)}
                                onDiscountChange={(discount) => bloc.requestChangeDiscount(formItem, discount)}
                                onTotalPriceChange={(totalPrice) => bloc.requestChangeTotalPrice(formItem, totalPrice)}
                            />
                        );
                    })}
                    {chargeForm.isChinesePrescription && (
                        <View>
                            {chargeForm.usageInfo && (
                                <ChinesePrescriptionNewUsageView
                                    doseCount={!!chargeForm.leftDoseCount ? chargeForm.leftDoseCount : chargeForm.refundDoseCount}
                                    showErrorHint={false}
                                    dosageCountEditable={showCheckBox}
                                    isEditing={false}
                                    usageInfo={chargeForm.usageInfo}
                                    showRequirement={!_.isEmpty(chargeForm.usageInfo.requirement)}
                                    onChangeDosageCount={(count) => self.bloc.requestChangeDosageCount(chargeForm, count)}
                                />
                            )}
                            <AbcView
                                style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-end" }]}
                                onClick={() => self.bloc.requestCheckGoodsBatchInfo(chargeForm)}
                            >
                                <ChinesePrescriptionInfoFooter chargeForm={chargeForm} showDosageCount={!chargeForm.usageInfo} />
                                {isExistMultipleBatch && (
                                    <View style={{ ...ABCStyles.rowAlignCenter, paddingTop: Sizes.dp8, paddingBottom: Sizes.dp16 }}>
                                        <SizedBox width={Sizes.dp4} />
                                        <IconFontView name={"arrow_down"} color={Colors.T6} size={Sizes.dp16} />
                                    </View>
                                )}
                            </AbcView>
                        </View>
                    )}
                    {!chargeForm.isChinesePrescription && (
                        <AbcView
                            style={[
                                ABCStyles.rowAlignCenter,
                                { flex: 1, justifyContent: "flex-end", paddingBottom: Sizes.dp16, paddingTop: Sizes.dp8 },
                            ]}
                            onClick={() => isExistMultipleBatch && self.bloc.requestCheckGoodsBatchInfo(chargeForm)}
                        >
                            <NewCardFooter rightTitle={self._getFooterTitle(chargeForm, title)} />
                            {isExistMultipleBatch && (
                                <View style={ABCStyles.rowAlignCenter}>
                                    <SizedBox width={Sizes.dp4} />
                                    <IconFontView name={"arrow_down"} color={Colors.T6} size={Sizes.dp16} />
                                </View>
                            )}
                        </AbcView>
                    )}
                </View>
            );
        }

        function createForms(titlePrefix: string, forms: ChargeForm[]) {
            if (_.isEmpty(forms)) return [];
            const widgets: JSX.Element[] = [];

            let formIndex = 1;
            const length = forms.length;
            for (const form of forms) {
                if (_.isEmpty(form.chargeFormItems)) continue;
                widgets.push(<DividerLine />);
                widgets.push(createFrom(titlePrefix + (length > 1 ? `${ABCUtils.toChineseNum(formIndex)}` : ""), form));
                formIndex++;
            }

            return widgets;
        }

        return [
            ...createForms("成药", westernForms),
            ...createForms("输液", infusionForms),
            ...createForms("中药", chineseForms),
            ...createForms("外治", externalForms),
            ...createForms("商品", goodsForms),
        ];
    }

    //空中药方处方单
    _renderAirPharmacyPrescriptionChargeForm(): JSX.Element {
        const { detailData, showErrorHint, chargeConfig } = this.bloc.currentState;
        const airPharmacyForms = detailData.chargeForms?.filter((form) => form.isAirPharmacy || form.isVirtualPharmacy) ?? [];
        if (airPharmacyForms.length == 0) return <View />;
        const { canEditChargeSheet } = detailData;
        const editable = canEditChargeSheet;
        return (
            <View>
                {airPharmacyForms.map((form) => (
                    <AbcBasePanel
                        key={form.compareKey()}
                        panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp20, Sizes.dp8, 0)}
                        contentStyle={{ padding: Sizes.dp16, paddingBottom: 0 }}
                    >
                        <AirPharmacyPrescriptionForm
                            editable={editable}
                            canBeUpdateAirPharmacyMedicalRecord={detailData.isCanBeUpdateAirPharmacyMedicalRecord === 1}
                            chargeForm={form}
                            chargeData={detailData}
                            showErrorHint={showErrorHint}
                            checkStatus={(form, formItem) => this.bloc.currentState.isChargeItemSelected(form, formItem)}
                            onDeliveryInfoChanged={(chargeForm, deliveryInfo) =>
                                this.bloc.requestUpdateDeliveryInfo(deliveryInfo, chargeForm)
                            }
                            onChangeOrderComment={(chargeForm, comment) => this.bloc.requestUpdateComment(form, comment)}
                            onToggleChargeItem={(chargeForm, chargeItem) => this.bloc.requestToggleChargeItem(chargeForm, chargeItem)}
                            onChangeDosageCount={(chargeForm, count) => this.bloc.requestChangeDosageCount(chargeForm, count)}
                            onChangeChiefComplaint={(chargeForm, chiefComplaint) =>
                                this.bloc.requestChangeChiefComplaint(chargeForm, chiefComplaint)
                            }
                            onChangeDiagnosis={(chargeForm, diagnosis) => this.bloc.requestChangeDiagnosis(chargeForm, diagnosis)}
                            onChangeDoctor={(doctor, department) => this.bloc.requestChangeDoctor(doctor, department)}
                            //收费完成后，重新加载数据
                            onFinishPay={(/*form*/) => this.bloc.requestReloadData()}
                            isWholeSheetOperateEnabled={chargeConfig?.openWholeSheetOperateEnable}
                            onChangeDecoctionFee={(chargeForm, decoctionInfo) =>
                                this.bloc.requestModifyDecoctionInfo(chargeForm, decoctionInfo)
                            }
                            onChangeMedicineState={(chargeForm, medicineStateId, medicineStateName, medicineStateList) =>
                                this.bloc.requestChangeMedicineState(chargeForm, medicineStateId, medicineStateName, medicineStateList)
                            }
                            onCheckExpressInfo={(formId, vendorId) =>
                                this.bloc.requestCheckExpressInfo(formId, vendorId, form?.deliveryInfo?.getCompanyNameAndDeliveryNo)
                            }
                        />
                    </AbcBasePanel>
                ))}
            </View>
        );
    }

    private _onTapChargeItem(chargeForm: ChargeForm, item: ChargeFormItem): void {
        // 检查是否开启了整单收退费开关
        const isWholeSheetOperateEnabled = this.bloc.currentState.chargeConfig?.openWholeSheetOperateEnable;
        if (isWholeSheetOperateEnabled) return;

        this.bloc.requestToggleChargeItem(chargeForm, item);
    }

    private _renderCalculateFailedBar() {
        if (this.bloc.currentState.calculateChargeError == undefined) return;

        return (
            <Text
                style={[
                    TextStyles.t16NR2,
                    {
                        textAlign: "center",
                    },
                ]}
                onClick={() => this.bloc.requestCalculatePrice()}
            >
                计算费用失败，点击重试
            </Text>
        );
    }

    private _renderBottomBarV2() {
        const detailData = this.detailData;

        //住院单子 App 不允许操作
        if (detailData.isHospitalizationSheet) {
            return <View />;
        }
        const toolBarItem: JSX.Element[] = [];

        // 添加总价优惠信息
        toolBarItem.push();

        //
        const state = this.bloc.currentState;
        const { status: chargeStatus, isClosed } = detailData;

        const singleBargain =
            (state.chargeConfig?.singleBargainSwitch === 1 || state.chargeConfig?.bargainSwitch === 1) &&
            !userCenter.clinic?.isDentistryClinic;
        // 锁单中不可编辑
        const isLockingOrder = detailData?.orderIsLocking;

        //正常未关闭收费单
        if (!isClosed && (chargeStatus == ChargeStatus.unCharged || chargeStatus == ChargeStatus.draft)) {
            singleBargain &&
                toolBarItem.push(
                    <AbcToolBarButtonStyle2
                        text={"议价"}
                        onClick={() => this.bloc.requestSingleBargain()}
                        showIndicator={state.calculatingCharge}
                        disable={isLockingOrder}
                    />
                );
            toolBarItem.push(
                <AbcToolBarButtonStyle1
                    text={"收费"}
                    onClick={() => this.bloc.requestCharge(true)}
                    showIndicator={state.calculatingCharge}
                    disable={isLockingOrder}
                />
            );
        } else if (chargeStatus == ChargeStatus.partCharged) {
            // 判断当前是否显示"退费"按钮
            const netIncomeFee = detailData.chargeSheetSummary!.netIncomeFee;
            // 沈阳诊间支付不可退费
            const containClinicPayment = !!detailData?.chargeTransactions?.find((t) => t.payMode == PayMethod.outpatientCenterPay);
            toolBarItem.push(
                <AbcToolBarButtonStyle1
                    text={`继续收费`}
                    onClick={() => this.bloc.requestCharge(false)}
                    showIndicator={state.calculatingCharge}
                    disable={isLockingOrder}
                />
            );
            !!netIncomeFee &&
                !containClinicPayment &&
                toolBarItem.push(
                    <AbcToolBarButtonStyle1
                        text={"退费"}
                        onClick={() => this.bloc.requestRefund()}
                        showIndicator={state.calculatingCharge}
                        disable={isLockingOrder}
                    />
                );
        } else if (chargeStatus == ChargeStatus.partRefunded) {
            toolBarItem.push(
                <AbcToolBarButtonStyle2
                    text={"继续退费"}
                    onClick={() => this.bloc.requestRefund()}
                    showIndicator={state.calculatingCharge}
                    disable={isLockingOrder}
                />
            );
        } else if (chargeStatus == ChargeStatus.refunded) {
            toolBarItem.push(
                <AbcToolBarButtonStyle1
                    text={`重新收费`}
                    onClick={() => this.bloc.requestReCharge()}
                    showIndicator={state.calculatingCharge}
                />
            );
        } else if (chargeStatus == ChargeStatus.charged) {
            toolBarItem.push(
                ...[
                    <AbcToolBarButton
                        key={"ChargeInvoiceChargeActionDetailDialog"}
                        text={`已收费`}
                        // onClick={() => {
                        //     ChargeInvoiceChargeActionDetailDialog.show(detailData);
                        // }}
                        showIndicator={state.calculatingCharge}
                        style={{
                            backgroundColor: Colors.bdColor,
                            fontColor: Colors.white,
                            borderColor: Colors.bdColor,
                        }}
                    />,
                    <AbcToolBarButtonStyle1
                        key={"requestRefund"}
                        text={"退费"}
                        onClick={() => this.bloc.requestRefund()}
                        showIndicator={state.calculatingCharge}
                        disable={isLockingOrder}
                    />,
                ]
            );
        } else if (isClosed) {
            // const needPayFee = detailData.chargeSheetSummary!.needPayFee;
            // const needPay = ABCUtils.formatPrice(needPayFee!);
            toolBarItem.push(
                ...[
                    // <AbcToolBarButton
                    //     key={"已关闭"}
                    //     text={`${abcI18Next.t("¥")}${needPay} 已关闭`}
                    //     onClick={() => {
                    //         ignore();
                    //     }}
                    //     showIndicator={state.calculatingCharge}
                    //     style={{
                    //         backgroundColor: Colors.bdColor,
                    //         fontColor: Colors.white,
                    //         borderColor: Colors.bdColor,
                    //     }}
                    // />,
                    <AbcToolBarButtonStyle2
                        key={"requestReOpenToCharge"}
                        text={"重新收费"}
                        onClick={() => this.bloc.requestReOpenToCharge()}
                        showIndicator={state.calculatingCharge}
                    />,
                ]
            );
        }

        return (
            <KeyboardListenerView>
                <View style={[ABCStyles.rowAlignCenter, ABCStyles.topLine, { backgroundColor: Colors.white, paddingVertical: Sizes.dp12 }]}>
                    {this._renderDiscountSummaryView()}
                    <View style={{ flexGrow: 1 }}>
                        {toolBarItem.length && (
                            <AbcToolBar showMoreBtn={true} showCount={2} direction={"right"} lineHeight={0} toolbarHeight={Sizes.dp44}>
                                {toolBarItem}
                            </AbcToolBar>
                        )}
                    </View>
                </View>
            </KeyboardListenerView>
        );
    }

    private _renderOweBottomBar() {
        const detailData = this.detailData;
        const { isCanSeeModifyPayMode, dataPermission } = this.bloc.currentState;

        //住院单子 App 不允许操作
        if (detailData.isHospitalizationSheet) {
            return <View />;
        }

        const state = this.bloc.currentState;
        const smallWidthStyle = {
            width: pxToDp(60),
        };

        // 待还状态
        return (
            <ToolBar hideWhenKeyboardShow={true}>
                {detailData.oweChargeCanRefund && (
                    <AbcToolBarButtonStyle2
                        text={"退费"}
                        onClick={() => this.bloc.requestRefund()}
                        showIndicator={state.calculatingCharge}
                        style={smallWidthStyle}
                    />
                )}
                <AbcToolBarButtonStyle2
                    text={`详情`}
                    onClick={() => {
                        ChargeInvoiceChargeActionDetailDialog.show({
                            detailData: detailData,
                            isCanSeeModifyPayMode: isCanSeeModifyPayMode,
                            modifyPayModeTypeStr: dataPermission?.cashierModifyPayModeTypeStr,
                        });
                    }}
                    showIndicator={state.calculatingCharge}
                    style={smallWidthStyle}
                />

                <AbcToolBarButtonStyle1
                    text={`还款`}
                    onClick={() => this.bloc.requestRePayment()}
                    showIndicator={state.calculatingCharge}
                />
            </ToolBar>
        );
    }

    private _renderDiscountSummaryView(): JSX.Element {
        const detailData = this.detailData;
        const { status: chargeStatus } = detailData;

        const { isCanSeeModifyPayMode, dataPermission } = this.bloc.currentState;

        let needPayFee = detailData.chargeSheetSummary?.needPayFee;
        if ((chargeStatus ?? 0) >= ChargeStatus.charged) {
            needPayFee = detailData.chargeSheetSummary?.receivedFee;
        }

        // 优惠议价费用
        const { oddFee = 0 } = detailData.chargeSheetSummary ?? {};
        const discountFee =
            oddFee +
            detailData.promotionsTotalPrice +
            detailData.computedTotalDeductPrice.totalDeductPrice +
            -(detailData.patientPointsInfo?.checkedDeductionPrice ?? 0) +
            detailData.computedDiscountPricce(detailData.couponPromotions ?? []) +
            detailData.computedDiscountPricce(detailData.giftRulePromotions ?? []) +
            (detailData.chargeSheetSummary?.draftAdjustmentFee ?? 0) +
            (detailData.chargeSheetSummary?.outpatientAdjustmentFee ?? 0);
        const chargedStatus = detailData.status == ChargeStatus.charged || detailData.status == ChargeStatus.partCharged; // 已收费 || 部分收费 (都可修改支付方式)
        // 锁单中不可编辑
        const isLockingOrder = detailData.orderIsLocking;
        return (
            <View style={{ paddingLeft: Sizes.dp16 }}>
                <AbcView
                    style={[{ flexDirection: "row", alignItems: "flex-end", marginBottom: Sizes.dp5 }]}
                    onClick={() => {
                        !isLockingOrder &&
                            chargedStatus &&
                            ChargeInvoiceChargeActionDetailDialog.show({
                                detailData: detailData,
                                isCanSeeModifyPayMode: isCanSeeModifyPayMode,
                                modifyPayModeTypeStr: dataPermission?.cashierModifyPayModeTypeStr,
                            });
                    }}
                >
                    <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp14 })]}>{chargedStatus ? "已收" : "合计"}：</Text>
                    <View style={[ABCStyles.rowAlignCenter, { position: "relative", top: Sizes.dp3 }]}>
                        <View style={{ flexDirection: "row" }}>
                            <Text
                                style={[
                                    TextStyles.t13NM.copyWith({ lineHeight: Sizes.dp20, fontWeight: FontWeight.bold }),
                                    { alignSelf: "flex-end" },
                                ]}
                                numberOfLines={1}
                            >
                                {abcI18Next.t("¥")}
                            </Text>
                            <AbcText
                                airTestKey={`总价:${needPayFee}`}
                                style={[TextStyles.t18MM.copyWith({ lineHeight: Sizes.dp20 })]}
                                numberOfLines={1}
                            >
                                {ABCUtils.formatPrice(needPayFee ?? 0)}
                            </AbcText>
                        </View>
                        {!isLockingOrder && chargedStatus && (
                            <IconFontView name={"arrow_up"} color={Colors.mainColor} size={Sizes.dp12} style={{ marginTop: Sizes.dp2 }} />
                        )}
                    </View>
                </AbcView>
                <AbcView
                    style={[{ flexDirection: "row", alignItems: "flex-end" }]}
                    onClick={() => {
                        !isLockingOrder && this.bloc?.requestShowDiscountSummaryDetail();
                    }}
                >
                    <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp14 })]}>优惠/议价：</Text>
                    <AbcText
                        airTestKey={`优惠:${discountFee}`}
                        style={[TextStyles.t13NT1.copyWith({ color: Colors.R2, lineHeight: Sizes.dp14 })]}
                    >
                        {ABCUtils.formatPriceWithRMB(discountFee)}
                    </AbcText>
                    {!isLockingOrder && <IconFontView name={"arrow_up"} color={Colors.R2} size={Sizes.dp12} />}
                </AbcView>
            </View>
        );
    }
}

interface ChargeConsultationListItemProps {
    chargeForm: ChargeForm;
    chargeItem: ChargeFormItem;

    checked: boolean;
    enableCheckBox?: boolean;
    showCheckBox?: boolean;
}

class ChargeConsultationListItem extends BaseComponent<ChargeConsultationListItemProps> {
    static defaultProps = {
        enableCheckBox: false,
        showCheckBox: false,
    };

    constructor(props: ChargeConsultationListItemProps) {
        super(props);
    }

    render() {
        const nameFlex = 2;

        const { chargeItem, checked, enableCheckBox, showCheckBox } = this.props;
        let forceFontColor = false;
        if (chargeItem.status == ChargeFormItemStatus.refunded) {
            forceFontColor = true;
        } else if (chargeItem.status == ChargeFormItemStatus.chargeBack) {
            forceFontColor = true;
        }

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.listBorderPadding,
                    {
                        height: Sizes.listItemHeight,
                    },
                ]}
            >
                {showCheckBox && <AbcCheckbox check={checked} enable={enableCheckBox} checkIconBgColor={Colors.white} />}
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            flex: nameFlex,
                        },
                    ]}
                >
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={forceFontColor ? TextStyles.t16MT4 : TextStyles.t16MB}>{chargeItem.displayName ?? ""}</Text>
                        <ChargeStatusView
                            chargeStatus={chargeItem.status}
                            style={{
                                marginRight: 8,
                            }}
                        />
                    </View>
                </View>

                <Text style={forceFontColor ? TextStyles.t16MT4 : TextStyles.t16MB}>
                    {`${ABCUtils.formatPrice(chargeItem.unitCount! * (chargeItem.unitPrice ?? 0.0))}`}
                </Text>
            </View>
        );
    }
}
