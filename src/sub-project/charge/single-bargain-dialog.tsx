/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/17
 *
 * @description
 */
import { BaseBlocComponent, BaseComponent } from "../base-ui/base-component";
import React from "react";
import { Text, View } from "@hippy/react";
import { ChargeForm, ChargeFormItem, ChargeInvoiceDetailData } from "./data/charge-beans";
import { SingleBargainDialogBloc } from "./single-bargain-dialog-bloc";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { ABCUtils } from "../base-ui/utils/utils";
import { SizedBox, Spacer, UniqueKey } from "../base-ui";
import { SingleBargainAirPharmacyPrescriptionPriceFooter, SingleBargainPrescriptionPriceFooter } from "./view/charge-views";
import { CustomInput } from "../base-ui/input/custom-input";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { Const } from "../base-ui/utils/consts";
import { StringUtils } from "../base-ui/utils/string-utils";
import { SafeAreaBottomView } from "../base-ui/safe_area_view";
import { AbcScrollView } from "../base-ui/views/abc-scroll-view";
import { BottomSheetHelper } from "../base-ui/abc-app-library";
import { OutpatientUtils } from "../outpatient/utils/outpatient-utils";
import { ChargeSingleBargainBaseView } from "./view/charge-single-bargain-base-view";
import { AbcToolBarButtonStyle1 } from "../base-ui/abc-app-library/tool-bar-button/tool-bar";
import { AbcView } from "../base-ui/views/abc-view";
import { AccessibilityLabelType } from "../data/constants/accessibility-label-type";

interface SingleBargainDialogProps {
    title?: string; //default 修改价格
    editable?: boolean; //default true
    detailData: ChargeInvoiceDetailData;
    //总价使用非折扣后的值，直接使用totalPrice, default false
    useNonDiscountTotalPrice?: boolean;
    // 是否允许查看药品进价（议价时便于参考）
    allowViewMedicinePrice?: boolean;
}

export class SingleBargainDialog extends BaseBlocComponent<SingleBargainDialogProps, SingleBargainDialogBloc> {
    static show(
        detailData: ChargeInvoiceDetailData,
        editable = true,
        useNonDiscountTotalPrice = false,
        title?: string,
        allowViewMedicinePrice?: boolean
    ): Promise<
        | {
              chargeSheet: ChargeInvoiceDetailData;
              itemUnitPriceChanged: boolean; //修改了单项单价
              itemTotalPriceChanged: boolean; //修改了单项总价
              totalPriceChanged: boolean; //总价
          }
        | undefined
    > {
        return ABCNavigator.showBottomSheet(
            <SingleBargainDialog
                detailData={detailData}
                editable={editable}
                useNonDiscountTotalPrice={useNonDiscountTotalPrice}
                title={title}
                allowViewMedicinePrice={allowViewMedicinePrice}
            />,
            {
                dismissWhenTouchOutside: false,
            }
        );
    }

    bloc: SingleBargainDialogBloc;

    constructor(props: SingleBargainDialogProps) {
        super(props);

        this.bloc = new SingleBargainDialogBloc(this.props.detailData, this.props.editable ?? true);
    }

    createForm(chargeForm: ChargeForm[], title: string, editable: boolean): JSX.Element[] | [] {
        const viewItem = [];
        let __index = 0;
        for (const item of chargeForm ?? []) {
            let totalPrice = item.totalPrice;
            if (item.isAirPharmacy) totalPrice = item.medicineTotalPrice;
            if (item.expectedTotalPrice != undefined && item.expectedPriceFlag == undefined) {
                totalPrice = item.expectedTotalPrice;
            }
            const viewList = [];
            __index++;
            for (const subItem of item?.chargeFormItems ?? []) {
                viewList.push(
                    <GoodsItemEditView
                        key={subItem.goodsInfo.scrollKey}
                        formItem={subItem}
                        editable={item.isAirPharmacy ? false : editable && subItem.isCanAdjustment}
                    />
                );
            }
            if (!!item?.chargeFormItems?.length) {
                if (item.isAirPharmacy || item.isVirtualPharmacy) {
                    viewList.push(
                        <SingleBargainAirPharmacyPrescriptionPriceFooter
                            key={item.compareKey()}
                            totalPrice={totalPrice}
                            dosageCount={item.doseCount ?? 0}
                            count={item.chargeFormItems?.filter((t) => !t.isDelivery && !t.isDecoction)?.length ?? 0}
                            vendorName={item.vendorName}
                            editable={
                                item.isAirPharmacy
                                    ? (editable && item.chargeFormItems?.some((item) => item.isChineseMedicine && item.isCanAdjustment)) ??
                                      false
                                    : false
                            }
                            onChangeTotalPrice={(price) => this.bloc.requestUpdateChargeFormPrice(item, price)}
                            isTotalPriceChanged={item.expectedTotalPrice != undefined && item.expectedPriceFlag == undefined}
                        />
                    );
                } else if (!item.isChinesePrescription) {
                    viewList.push(
                        <SingleBargainPrescriptionPriceFooter
                            key={UniqueKey()}
                            totalPrice={item.totalPrice}
                            count={item.chargeFormItems?.length}
                        />
                    );
                } else {
                    viewList.push(
                        <SingleBargainPrescriptionPriceFooter
                            key={UniqueKey()}
                            count={`${item?.doseCount__ ?? item.doseCount}剂${item?.chargeFormItems?.length ?? 0}味`}
                            totalPrice={item.totalPrice}
                            unit={""}
                        />
                    );
                }
            }

            !!viewList.length &&
                viewItem.push(
                    <ChargeSingleBargainBaseView
                        title={`${title}${(chargeForm?.length ?? 0) > 1 ? ABCUtils.toChineseNum(__index) : ""}`}
                        key={`${title}${(chargeForm?.length ?? 0) > 1 ? ABCUtils.toChineseNum(__index) : ""}`}
                        rightInfo={item.isAirPharmacy || item.isVirtualPharmacy ? item?.vendorName : ""}
                    >
                        {viewList}
                    </ChargeSingleBargainBaseView>
                );
        }
        return viewItem;
    }

    renderMedicineItem(): JSX.Element {
        const state = this.bloc.currentState,
            detailData = state.detailData;
        if (!detailData) return <View />;
        const { editable, hasSingleBargainPermission } = state;
        const view = [];
        let index = 0,
            totalPrice = 0;
        const chargeForms = detailData.chargeForms?.map((item) => {
            // 增加t.checked选中条件是解决收费单反选的问题
            item.chargeFormItems = item.chargeFormItems?.filter((t) => OutpatientUtils.canEditWithChargeStatus(t.status) && t.checked);
            return item;
        });
        const finalEditable = editable && hasSingleBargainPermission;
        const _productList: JSX.Element[] = [];

        //诊疗项目
        const productChargeForm = chargeForms?.filter(
            (t) =>
                !t.isChinesePrescription &&
                !t.isWesternPrescription &&
                !t.isInfusionPrescription &&
                !t.isAirPharmacy &&
                !t.isExternal &&
                !t.isVirtualPharmacy
        );
        for (const item of productChargeForm ?? []) {
            totalPrice += item?.totalPrice ?? 0;
            for (const subItem of item?.chargeFormItems ?? []) {
                _productList.push(<GoodsItemEditView key={index} formItem={subItem} editable={finalEditable && subItem.isCanAdjustment} />);
                index++;
            }
        }
        !!productChargeForm?.length &&
            _productList.push(
                <SingleBargainPrescriptionPriceFooter
                    key={"SingleBargainPrescriptionPriceFooter_t"}
                    totalPrice={totalPrice}
                    count={index}
                />
            );
        !!_productList.length &&
            view.push(
                <ChargeSingleBargainBaseView title={"诊疗"} key={"ChargeSingleBargainBaseView"}>
                    {_productList}
                </ChargeSingleBargainBaseView>
            );

        //成药处方
        const westernPrescriptionChargeForm = chargeForms?.filter((t) => t.isWesternPrescription);
        if (!!westernPrescriptionChargeForm?.length) view.push(...this.createForm(westernPrescriptionChargeForm, "成药", finalEditable));

        //中药处方
        const chinesePrescriptionChargeForm = chargeForms?.filter((t) => t.isChinesePrescription);
        if (!!chinesePrescriptionChargeForm?.length) view.push(...this.createForm(chinesePrescriptionChargeForm, "中药", finalEditable));

        //输注处方
        const infusionPrescriptionChargeForm = chargeForms?.filter((t) => t.isInfusionPrescription);
        if (!!infusionPrescriptionChargeForm?.length) view.push(...this.createForm(infusionPrescriptionChargeForm, "输注", finalEditable));

        //外治处方
        const externalPrescriptionChargeForm = chargeForms?.filter((t) => t.isExternal);
        if (!!externalPrescriptionChargeForm?.length) view.push(...this.createForm(externalPrescriptionChargeForm, "外治", finalEditable));

        //空中药房
        const airPharmacyChargeForm = chargeForms?.filter((t) => t.isAirPharmacy);
        if (!!airPharmacyChargeForm?.length) view.push(...this.createForm(airPharmacyChargeForm, "空中药房", finalEditable));

        //代煎代配
        const virtualPharmacyChargeForm = chargeForms?.filter((t) => t.isVirtualPharmacy);
        if (!!virtualPharmacyChargeForm?.length) view.push(...this.createForm(virtualPharmacyChargeForm, "代煎代配", finalEditable));

        return <View style={{ flex: 1, ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8, Sizes.dp16), marginBottom: Sizes.dp50 }}>{view}</View>;
    }

    renderContent(): JSX.Element {
        if (!this.bloc.currentState.detailData) return <View />;
        const { useNonDiscountTotalPrice = false, allowViewMedicinePrice = false } = this.props;
        return (
            <AbcView style={{ flex: 1, backgroundColor: Colors.white }} airTestKey={`${AccessibilityLabelType.PAGE}-整单议价`}>
                {BottomSheetHelper.createTitleBar(this.props.title ?? "整单议价")}
                <AbcScrollView style={{ flex: 1, backgroundColor: Colors.white }} showsVerticalScrollIndicator={false}>
                    {this.renderMedicineItem()}
                </AbcScrollView>

                <_BottomToolBar useNonDiscountTotalPrice={useNonDiscountTotalPrice} allowViewMedicinePrice={allowViewMedicinePrice} />
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </AbcView>
        );
    }
}

interface _BottomToolBarProps {
    useNonDiscountTotalPrice: boolean;
    allowViewMedicinePrice?: boolean;
}
interface _BottomToolBarState {
    totalPriceIsFulled: boolean;
    currentInputValue: number | undefined;
}
class _BottomToolBar extends BaseComponent<_BottomToolBarProps, _BottomToolBarState> {
    static contextType = SingleBargainDialogBloc.Context;

    constructor(props: any) {
        super(props);
        this.state = {
            totalPriceIsFulled: false,
            currentInputValue: undefined, // 添加当前输入值
        };
    }

    _onBargainReduction(): void {
        const bloc = SingleBargainDialogBloc.fromContext(this.context);
        bloc.requestBargainReduction();
    }

    _onTotalPriceFulled(): void {
        this.setState({
            totalPriceIsFulled: true,
        });
    }

    render() {
        const { detailData, editable, hasWholeBargainPermission, totalCostPrice, hasSingleBargainPermission } =
            SingleBargainDialogBloc.fromContext(this.context).currentState;
        const { useNonDiscountTotalPrice = false, allowViewMedicinePrice } = this.props;
        const chargeSummary = detailData!.chargeSheetSummary;
        let needPayFee = chargeSummary?.totalFee ?? 0;
        if (!useNonDiscountTotalPrice) needPayFee = chargeSummary?.expectedTotalPrice__ ?? detailData?.chargeSheetSummary?.needPayFee ?? 0;
        // 整单议价是否可议(开启了整单议价，不管单项议价是否开启，都可以议价)
        const totalPriceEditable = editable && !useNonDiscountTotalPrice && hasWholeBargainPermission;

        const draftAdjustmentFee = chargeSummary?.draftAdjustmentFee;
        const { totalPriceIsFulled } = this.state;
        // 毛利率 = ((销售收入 - 销售成本) / 销售收入) * 100%
        // 毛利率最低为0%，不能为负数
        const calculateGrossProfitRate = (revenue: number, cost: number): number => {
            // 处理边界情况：收入为0时返回0
            if (!revenue) return 0;
            const actualRevenue = revenue ?? 0;
            const actualCost = cost ?? 0;
            // 如果成本大于收入，返回0
            if (actualCost >= actualRevenue) return 0;
            //  计算毛利率并保留2位小数
            return Number((((actualRevenue - actualCost) / actualRevenue) * 100).toFixed(2));
        };
        // 使用函数计算毛利率
        const grossProfitRate = calculateGrossProfitRate(needPayFee, Number(ABCUtils.formatPrice(totalCostPrice ?? 0)));
        // 是否显示保存按钮（有整单议价权限或者单项议价权限）并且需满足有可议价的项目
        const showSaveBtn = totalPriceEditable || (hasSingleBargainPermission && editable);
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.topLine,
                    Sizes.paddingLTRB(Sizes.dp16, Sizes.dp12, Sizes.dp12, Sizes.dp12),
                    { backgroundColor: Colors.white },
                ]}
            >
                <View>
                    <View style={{ flexDirection: "row", alignItems: "center" }}>
                        <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp18 })]}>总价：</Text>
                        {totalPriceEditable && (
                            <CustomInput
                                borderType={"bottom"}
                                type={"input"}
                                style={{ width: Sizes.dp92, marginHorizontal: Sizes.dp8, textAlign: "left" }}
                                value={ABCUtils.formatPrice(needPayFee ?? 0)}
                                formatter={PrecisionLimitFormatter(2)}
                                resizeMode={true}
                                onBlur={(value) => {
                                    if (!value) return;
                                    // 整单议价不考虑单项议价范围限制，平摊所有项目
                                    SingleBargainDialogBloc.fromContext(this.context).requestUpdateTotalPrice(
                                        StringUtils.parseFloat(value)!
                                    );
                                }}
                                textStyle={TextStyles.t18MM}
                                onEndEditing={() => this._onTotalPriceFulled()}
                                onFocus={() => {
                                    this.setState({
                                        totalPriceIsFulled: false,
                                    });
                                }}
                                containerStyle={allowViewMedicinePrice ? { borderBottomWidth: 0 } : {}}
                            />
                        )}
                        {!totalPriceEditable && (
                            <Text style={[TextStyles.t18MM.copyWith({ lineHeight: Sizes.dp18 })]}>
                                {ABCUtils.formatPrice(needPayFee ?? 0)}
                            </Text>
                        )}
                    </View>
                    {allowViewMedicinePrice && (
                        <View style={{ paddingLeft: Sizes.dp42, paddingBottom: Sizes.dp4, ...ABCStyles.bottomLine }}>
                            <Text style={TextStyles.t11NT6.copyWith({ lineHeight: Sizes.dp18 })}>
                                {`预估成本${ABCUtils.formatPrice(totalCostPrice ?? 0)} 预估毛利率${grossProfitRate ?? ""}%`}
                            </Text>
                        </View>
                    )}
                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp6 }]}>
                        <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp18 })]}>议价：</Text>
                        <Text style={[TextStyles.t13NY2.copyWith({ lineHeight: Sizes.dp18 })]}>
                            {`  ${draftAdjustmentFee! >= 0 ? "+" : ""}`}
                            {ABCUtils.formatPrice(draftAdjustmentFee!)}
                        </Text>
                    </View>
                </View>
                <Spacer />
                {!!draftAdjustmentFee && totalPriceIsFulled && (
                    <Text style={[TextStyles.t16NM]} onClick={() => this._onBargainReduction()}>
                        取消议价
                    </Text>
                )}
                <SizedBox width={Sizes.dp8} />
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    {showSaveBtn && (
                        <AbcToolBarButtonStyle1
                            text={"保存"}
                            onClick={() => {
                                const bloc = SingleBargainDialogBloc.fromContext(this.context);
                                bloc.requestSave();
                            }}
                        />
                    )}
                </View>
            </View>
        );
    }
}

interface GoodsItemEditViewProps {
    editable?: boolean; //default true
    formItem: ChargeFormItem;
}

class GoodsItemEditView extends BaseComponent<GoodsItemEditViewProps> {
    static contextType = SingleBargainDialogBloc.Context;

    constructor(props: GoodsItemEditViewProps) {
        super(props);
    }

    render() {
        const { formItem, editable = true } = this.props;
        const state = SingleBargainDialogBloc.fromContext(this.context).currentState;
        const unitPrice = formItem.expectedUnitPrice ?? formItem.unitPrice;
        const count = formItem.doseCount ?? 1.0;
        const totalPrice = formItem?.totalPrice ?? (formItem.unitCount ?? 0) * (formItem.unitPrice ?? 0) * count;
        const isUnitPriceChanged = !!formItem.expectedUnitPrice && formItem.expectedUnitPrice != formItem.sourceUnitPrice; //单价是否议价
        const isTotalPriceChanged = !!formItem.expectedTotalPrice && formItem.expectedTotalPrice != formItem.sourceTotalPrice; //总价是否议价
        let _displayName = formItem?.displayName;
        if (formItem.isRegistration) _displayName = state.clinicBasicSetting?.registrationFeeStr ?? "";

        return (
            <AbcView airTestKey={formItem?.displayName} style={[ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp8 }]}>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            flex: 140,
                            paddingLeft: Sizes.dp14,
                        },
                    ]}
                >
                    <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]} numberOfLines={1}>
                        {_displayName}
                    </Text>
                    {formItem.isPackage && <Text style={[TextStyles.t14NT4]}>【套餐】</Text>}
                    <SizedBox width={Sizes.dp10} />
                </View>
                <View style={{ flex: 60 }}>
                    <CustomInput
                        style={{}}
                        borderType={"bottom"}
                        disable={!editable}
                        placeholder={"单价"}
                        value={ABCUtils.formatPrice(unitPrice ?? 0)}
                        formatter={PrecisionLimitFormatter(
                            formItem.isChineseMedicine ? Const.chineseMedicineSellPricePrecision : Const.sellPricePrecision
                        )}
                        onBlur={(value) => {
                            if (editable) {
                                const num = StringUtils.parseFloat(value, undefined);
                                if (num != undefined && num != unitPrice) {
                                    SingleBargainDialogBloc.fromContext(this.context).requestChangeItemUnitPrice(formItem, num);
                                }
                            }
                        }}
                        textStyle={{ color: isUnitPriceChanged ? Colors.Y2 : undefined }}
                        containerStyle={{ borderColor: Colors.bdColor }}
                        disableStyle={{ fontSize: Sizes.dp14 }}
                        disableColor={Colors.T4}
                    />
                </View>
                <View
                    style={{
                        flex: 60,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <Text style={TextStyles.t14NB}>{`${formItem?.unitCount}`}</Text>
                </View>
                <View style={{ flex: 60 }}>
                    <CustomInput
                        style={{ marginRight: Sizes.dp8 }}
                        borderType={"bottom"}
                        disable={!editable}
                        placeholder={"总价"}
                        value={ABCUtils.formatPrice(totalPrice ?? 0)}
                        formatter={PrecisionLimitFormatter(Const.sellPricePrecision)}
                        onBlur={(value) => {
                            if (editable) {
                                const num = StringUtils.parseFloat(value, undefined);
                                if (num != undefined && num != totalPrice) {
                                    SingleBargainDialogBloc.fromContext(this.context).requestChangeItemTotalPrice(formItem, num);
                                }
                            }
                        }}
                        textStyle={{ color: isTotalPriceChanged ? Colors.Y2 : undefined }}
                        containerStyle={{ borderColor: Colors.bdColor }}
                        disableStyle={{ fontSize: Sizes.dp14 }}
                        disableColor={Colors.T4}
                    />
                </View>
            </AbcView>
        );
    }
}
