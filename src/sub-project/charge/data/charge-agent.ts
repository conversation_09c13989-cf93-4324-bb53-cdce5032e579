/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/22
 *
 * @description 对接收费后台接口api
 */

import { ABCApiNetwork } from "../../net";
import {
    ABCScanPayMethod,
    AvailableCompanyParams,
    CanPaidPatientCard,
    ChargeBargainingRule,
    ChargeForm,
    ChargeFormItem,
    ChargeInvoiceData,
    ChargeInvoiceDetailData,
    ChargeOweCombinePaidRsp,
    ChargeOweInfo,
    ChargeOweSinglePaidRsp,
    ChargePatientCards,
    ChargePaymentMethodRsp,
    ChargeRegistrationInfo,
    ChargeSettlementExceptionItem,
    ChargeSourceFormType,
    ChargeStatus,
    CouponPromotion,
    DecoctionInfo,
    DeductItem,
    DeliveryFeeRule,
    DispensingSettingConfig,
    ExtendDiagnosisInfos,
    GetAddressListParams,
    GiftPromotion,
    NewPayModes,
    OutpatientCalculateProcessRsp,
    OwePayItem,
    OweSheetItems,
    PatientCardPromotion,
    PatientPointDeductProductPromotions,
    PatientPointsInfo,
    PayChargeOweSheetReq,
    PaymentSummaryInfo,
    PayMethod,
    ProcessInfo,
    ProcessRule,
    Promotion,
    PromotionInfo,
    PushScanCodeInfo,
    RefundAuditorItem,
    RetailType,
    SourceClientType,
    ChargeRefundPreCheckReq,
    ChargeRefundPreCheckRsp,
} from "./charge-beans";
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { AbcMap } from "../../base-ui/utils/abc-map";
import {
    ChineseGoodType,
    DeliveryCompany,
    DeliveryInfo,
    GoodsInfo,
    GoodsType,
    IngredientProductInfo,
    MemberInfo,
    Patient,
    PrescriptionFormLogisticsTraceRsp,
    UsageInfo,
} from "../../base-business/data/beans";
import { ApiMixService } from "../../data/ApiMixService";
import { LogUtils } from "../../common-base-module/log";
import { ChargeUtils } from "../utils/charge-utils";
import _ from "lodash";
import { BehaviorSubject, Subject } from "rxjs";
import { clinicScopeMemoryCache } from "../../common-base-module/cleanup/memory-cache";
import { PrescriptionChineseForm, PrescriptionFormItem } from "../../outpatient/data/outpatient-beans";
import {
    AirPharmacyCalculateReq,
    AirPharmacyCalculateRsp,
    AirPharmacyCalculateRspForm,
    AirPharmacyDeliveryRule,
    AirPharmacyOrderDetail,
    AirPharmacyVendor,
    AirPharmacyWaitPayOrderDetail,
    PharmacyType,
    VirtualPharmacyConfig,
} from "./charge-bean-air-pharmacy";
import { DiseasesCode } from "../../base-business/mix-agent/data/shebao-bean";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { RegistrationAgent } from "../../registration/data/registration-agent";
import { TimeUtils } from "../../common-base-module/utils";

export class ChargeCalculatePayType {
    static paid = 0; //收纳
    static refund = 1; //退费
    static reNewPaid = 2; //重新收费
}

export class PayStatus {
    //  int payStatus; //// 支付状态（1：等待三方支付中；2：支付成功；3：支付失败）
    static waitingThirdPay = 1;
    static success = 2;
    static failed = 3;
}

export class DiscountType {
    static chineseMedicine = 1; // 中药
    static westernMedicine = 2; // 西药
}

export class ChargeCalculateRspProcessRule {
    id?: string;
    vendorId?: string;
    businessScopeId?: string;
    description?: string;
    type?: number;
    minimum?: number;
    finishedRate?: number;
    // ladderInfo: Null;
    @JsonProperty({ type: IngredientProductInfo })
    ingredient?: IngredientProductInfo;
    ruleInfo?: string;
    permanentPrice?: number;
}

export class ChargeCalculateRspData {
    totalFee?: number;
    adjustmentFee?: number;
    oddFee?: number;
    discountFee?: number;
    receivableFee?: number;
    draftAdjustmentFee?: number;
    excludeDraftAndOddFee?: number;
    needPayFee?: number;
    needRefundFee?: number;
    canAdjustment?: number;
    canAdjustmentFee?: number; // 能够议价的最低金额
    @JsonProperty({ type: Array, clazz: ChargeForm })
    chargeForms?: ChargeForm[];

    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];

    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];

    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];

    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    registrationDiscountFee?: number;
    westernMedicineDiscountFee?: number;
    chineseMedicineDiscountFee?: number;
    treatmentDiscountFee?: number;
    examinationDiscountFee?: number;
    materialDiscountFee?: number;

    @JsonProperty({ type: ChargeCalculateRspProcessRule })
    processRule?: ChargeCalculateRspProcessRule;

    afterRoundingDiscountedTotalFee?: number;

    @JsonProperty({ type: Array, clazz: CanPaidPatientCard })
    canPaidPatientCards?: CanPaidPatientCard[];

    deductedTotalCount?: number;

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];

    /**
     * 会员卡id - 为当前使用id/共享会员卡id
     */
    memberId?: string;
    /**
     * 会员卡信息 - 为当前使用卡信息/共享会员卡信息
     */
    @JsonProperty({ type: MemberInfo })
    memberInfo?: MemberInfo;
    goodsLockingTaskId?: string;
    totalCostPrice?: number; // 成本
}

export class CalculateDeliverRsp {
    isMarkRule?: number;
    ruleExpressDeliveryInfo?: {
        id: string;
        name: string;
        ruleInfo: string;
    };

    expressDeliveryFee?: number;
}

class ChargeCalculateRequest {
    payType?: number;
    payMode?: number; // 支付方式
    chargeSheetId?: string;
    memberId?: string;
    /**
     * 是否使用会员卡
     */
    useMemberFlag?: number;
    patientId?: string;
    @JsonProperty({ type: Array, clazz: ChargeForm })
    chargeForms?: ChargeForm[];

    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];

    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];

    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];
    dataSignature?: string;

    // deliveryType?: number;
    deliveryInfo?: DeliveryInfo;

    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;

    expectedAdjustmentFee?: number;

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    isNeedPatientCardBalance?: number;

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];
    withLock?: boolean;
}

export interface SearchMemberItem {
    id?: string;
    name?: string;
    sex?: string;
    mobile?: string;
    birthday?: string;
    ownRank?: number;
    memberTypeId?: string;
    memberTypeName?: string;
    principal?: number;
    present?: number;
    remark?: string;
    points?: number;
    pointsTotal?: number;
}

export interface PatientCardList {
    id?: string;
    beginDate?: string;
    endDate?: string;
    cardBalance?: number;
    cardFee?: number;
    cardId?: string;
    cardName?: string;
    color?: string;
    ownRights?: string;
    ownRightsFlag?: number;
    patientId?: string;
    present?: number;
    principal?: number;
    status?: number;
    validityPeriod?: number;
    validityPeriodStr?: string;
    checked?: boolean;
    patient?: Patient;
    patientName?: string;
    patientMobile?: string;
    isOutOfUseRangeCard?: number;
    isAvailable?: boolean;
    deductItems?: DeductItem[];
}

export interface WesternGoods {
    name?: string;
}
export interface GoodsList {
    discount?: number;
    discountType?: number;
    goodsCMSpec?: string;
    goodsId?: string;
    goodsSubType?: number;
    goodsType?: number;
    id?: string;
    isAirPharmacy?: number;
    name?: string;
    type?: number;
    goods?: WesternGoods;
}
interface DiscountList {
    default?: boolean;
    value?: string;
}
export interface Preferential {
    goodsList?: GoodsList[];
    discountList?: DiscountList[];
}
export interface CustomRight {
    isOpen?: number;
    detail?: Preferential;
}
export interface DiscountRight {
    isOpen?: number;
    detail?: Preferential;
}
export interface PromotionCardInfo {
    id?: string;
    customRights?: CustomRight;
    discountRights?: DiscountRight;
    isNeedCardFee?: number;
    isShare?: number;
    isShowInWeClinic?: number;
    name?: string;
    ownRights?: string;
    cardFee?: number;
    cardBalance?: number;
    validityPeriod?: number;
    validityPeriodStr?: string;
}

export interface PatientPresents {
    goods?: GoodsInfo;
    goodsId?: string;
    id?: string;
    totalCount?: number;
    usedCount?: number;
    isGivingLimit?: number;
}

export interface PatientCardInfo {
    id?: string;
    cardBalance?: number;
    cardInfo?: PromotionCardInfo;
    patientPresents?: PatientPresents[];
    endDate?: string;
}

export class AbnormalTransactionList {
    amount?: number;
    chargePayTransactionId?: string;
    chargeSheetId?: string;
    id?: string;
    paidTime?: string;
    payMode?: number;
    payModeDisplayName?: string;
    paySubMode?: number;
    status?: number;
    type?: number;
}
export class AbnormalRefundResult {
    chargeSheetId?: string;
    recordId?: number;
    status?: number;
    statusName?: string;
}
export class PreCheckPaidResult {
    chargePayTransactionIds?: Array<string>;
    isCanPaid?: number;
}

class ChargePayErrorDetail {
    id?: number;
    clinicId?: number;
    chainId?: number;
    patientOrderId?: number;
    chargeSheetId?: number;
    chargeFormId?: number;
    sourceFormItemId?: number;
    status?: number;
    unit?: number;
    name?: number;
    unitCostPrice?: number;
    unitCount?: number;
    doseCount?: number;
    unitPrice?: number;
    discountPrice?: number;
    totalPrice?: number;
    discountedTotalPrice?: number;
    useDismounting?: number;
    productType?: number;
    productSubType?: number;
    productId?: string;
    groupId?: string;
    sort?: number;
    paySource?: number;
    productInfo?: GoodsInfo;
    usageInfo?: UsageInfo;
    promotionInfo?: PromotionInfo;
    stockPieceCount?: number;
    stockPackageCount?: number;
    canRefundUnitCount?: number;
    canRefundDoseCount?: number;
}

class ChargePayError {
    static CODE_INVENTORY_SHORT = 470;
    static CODE_INVENTORY_CHANGE = 17013; //库存发生变化

    code?: number;
    message?: string;
    detail?: ChargePayErrorDetail;
}

export class ChargePayRsp {
    error?: ChargePayError;
    id?: string;
    patientOrderId?: string;
    status?: number;
    statusName?: string;
    needPay?: number; //// 还需支付
    receivedFee?: number;
    netIncomeFee?: number;
    chargeActionId?: string;
    payStatus?: number; //// 支付状态（1：等待三方支付中；2：支付成功；3：支付失败）
    isNotDispensed?: number;

    //空中药房
    isContainAirPharmacy?: number;
    airPharmacyOrderIds?: string[];
    chargePayTransactionId?: string; //用来查询支付状态的订单id
    isAirPharmacyCanPay?: number; //空中药房可支付
}

export class WechatPayInfo {
    appId?: string;
    codeUrl?: string;
    nonceStr?: string;
    paySign?: string;
    prepayId?: string;
    signType?: string;
    timeStamp?: number;
}
export class AbcRetailChargeRsp {
    airPharmacyOrderIds?: string[];
    autoUnlockTime?: string;
    canUnLockChargeSheet?: number;
    chargeActionId?: string;
    chargePayTransactionId?: string;
    dataSignature?: string;
    id?: string;
    isContainAirPharmacy?: number;
    isNotDispensed?: number;
    lockStatus?: number;
    needPay?: number;
    netIncomeFee?: number;
    patientOrderId?: string;
    payStatus?: number;
    receivedFee?: number;
    status?: number;
    statusName?: string;
    thirdPartyPayTaskId?: string;
    thirdPartyPayTaskType?: number;
    weChatPayInfo?: WechatPayInfo;
    isAirPharmacyCanPay?: number; //空中药房可支付
}

class ChargePayRegistration {
    id?: string;
    departmentId?: string;
    doctorId?: string;
    departmentName?: string;
    doctorName?: string;
    fee?: number;
}

export class ChargePayReq {
    chargeForms?: ChargeForm[];
    promotions?: Promotion[];
    giftRulePromotions?: GiftPromotion[];
    couponPromotions?: CouponPromotion[];
    patientPointsInfo?: PatientPointsInfo;
    patient?: Patient;
    memberInfo?: string;
    memberId?: string;
    sellerId?: string;
    receivableFee?: number;
    cash?: number;
    cashDiff?: number;

    registration?: ChargePayRegistration;
    dispensing?: boolean;
    chargeComment?: string;
    combinedPayItems?: CombinedPayItem[];

    deliveryType?: number;
    deliveryInfo?: DeliveryInfo;
    dataSignature?: string;
    contactMobile?: string; //电话

    expectedAdjustmentFee?: number;

    retailType?: RetailType;
    diagnosisInfos?: DiseasesCode[];
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    diagnosis?: string;
    chiefComplaint?: string;

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    /**
     * 是否使用会员卡
     */
    useMemberFlag?: number;

    sellerDepartmentId?: string;

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];
    goodsLockingTaskId?: string; // 批次锁库的id
    consultantId?: string; // 咨询师id
}

export class ChargeRenewpaidRsp {
    error?: ChargePayError;
    id?: string;
    patientOrderId?: string;
    status?: number;
    statusName?: string;
    needPay?: number;
    receivedFee?: number;
    netIncomeFee?: number;
    chargeActionId?: string;
    payStatus?: number;
    thirdPartyPayTaskId?: string;
    thirdPartyPayTaskType?: number;

    //空中药房
    isContainAirPharmacy?: number;
    airPharmacyOrderIds?: string[];

    chargePayTransactionId?: string; //用来查询支付状态的订单id
    isAirPharmacyCanPay?: number; //空中药房可支付
}

export class RetailPayReq {
    chargeComment?: string;
    combinedPayItems?: CombinedPayItem[];
    receivableFee?: number;
    sellerDepartmentId?: string;
    sellerId?: string;
    consultantId?: string;
}

class CombinedPayItem {
    payMode?: number;
    paySubMode?: number;
    amount?: number;
    thirdPartyPayCardId?: string; // 第三方支付cardId
    transactionIds?: string[]; // 退费新增透传字段
    authCode?: string; // 扫码后得到的支付码
    memberCardPassword?: string; //会员卡支付密码

    constructor(
        payMode?: number,
        paySubMode?: number,
        amount?: number,
        thirdPartyPayCardId?: string,
        transactionIds?: string[],
        authCode?: string,
        memberCardPassword?: string
    ) {
        this.payMode = payMode;
        this.paySubMode = paySubMode;
        this.amount = amount;
        this.thirdPartyPayCardId = thirdPartyPayCardId;
        this.transactionIds = transactionIds;
        this.authCode = authCode;
        this.memberCardPassword = memberCardPassword;
    }
}

/// 创建收费单请求
export class ChargeInvoiceCreateReq {
    patient?: Patient;
    sourceClientType?: number;
    chargeForms?: ChargeForm[];
    promotions?: Promotion[];
    giftRulePromotions?: GiftPromotion[];
    couponPromotions?: CouponPromotion[];
    patientPointsInfo?: PatientPointsInfo;
    patientCardPromotions?: PatientCardPromotion[];
    memberId?: string;
    sellerId?: string;
    receivableFee?: number;
    cash?: number;
    cashDiff?: number;
    payMode?: number;
    draftId?: number;
    dispensing?: boolean;
    memberInfo?: MemberInfo;
    /**
     * 是否使用会员卡
     */
    useMemberFlag?: number;
    chargeComment?: string; // 收费时录入备注
    combinedPayItems?: CombinedPayItem[];

    deliveryType?: number;
    deliveryInfo?: DeliveryInfo;
    contactMobile?: string;

    retailType?: RetailType;
    diagnosisInfos?: DiseasesCode[]; //第二版的
    @JsonProperty({ type: Array, clazz: ExtendDiagnosisInfos })
    extendDiagnosisInfos?: ExtendDiagnosisInfos[];
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    diagnosis?: string;
    chiefComplaint?: string;

    expectedAdjustmentFee?: number;

    sellerDepartmentId?: string;

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];

    consultantId?: string;
    goodsLockingTaskId?: string;
}

export class ChargeRefundRsp {
    error?: ChargePayError;
    id?: string;
    status?: number;
    statusName?: string;
    refundFee?: number;
    owedRefundFee?: number;
    refundedFee?: number;
    totalOwedRefundFee?: number;
    chargeActionId?: string;
    payStatus?: number;
    paymentSummaryInfos?: PaymentSummaryInfo[];
}

export class ChargePaidBackRsp {
    error?: ChargePayError;
    id?: string;
    status?: number;
    statusName?: string;
    refundFee?: number;
    owedRefundFee?: number;
    refundedFee?: number;
    totalOwedRefundFee?: number;
    chargeActionId?: number;
    payStatus?: number;
    paymentSummaryInfos?: PaymentSummaryInfo[];
}

//空中药房订单支付结果
export interface AirPharmacyPayRsp {
    id: string;
    accountId: string;
    accountBalance: number;
    totalFee: number;
    status: number;
    airPharmacyVendorType?: number;
}

export interface CloseChargeSheetRsp {
    id: string;
    patientOrderId: string;
    status: ChargeStatus;
    statusName: string;
    isClosed: number;
}

class ChargeEvent {}

export class ChargeEventDirectCharge extends ChargeEvent {
    draftId: string;
    payRsp: ChargePayRsp;

    constructor(draftId: string, payRsp: ChargePayRsp) {
        super();
        this.draftId = draftId;
        this.payRsp = payRsp;
    }
}

export class ChargeEventRenewpaid extends ChargeEvent {
    rsp: ChargeRenewpaidRsp;

    constructor(rsp: ChargeRenewpaidRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeEventPaid extends ChargeEvent {
    rsp: ChargePayRsp;

    constructor(rsp: ChargePayRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeEventRepaid extends ChargeEvent {
    rsp: ChargeRepaidRsp;

    constructor(rsp: ChargeRepaidRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeEventRefund extends ChargeEvent {
    rsp: ChargeRefundRsp;

    constructor(rsp: ChargeRefundRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeEventPaidBack extends ChargeEvent {
    rsp: ChargePaidBackRsp;

    constructor(rsp: ChargePaidBackRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeOweSinglePaid extends ChargeEvent {
    rsp: ChargeOweSinglePaidRsp;

    constructor(rsp: ChargeOweSinglePaidRsp) {
        super();
        this.rsp = rsp;
    }
}

export class ChargeEventDeleteNetworkDraft extends ChargeEvent {
    chargeId: string;

    constructor(chargeId: string) {
        super();
        this.chargeId = chargeId;
    }
}

export class ChargeEventSafeChargeInvoice extends ChargeEvent {
    constructor(data: ChargeInvoiceDetailData, newData: ChargeInvoiceDetailData) {
        super();
        this.data = data;
        this.newData = newData;
    }

    data: ChargeInvoiceDetailData;
    newData: ChargeInvoiceDetailData;
}

export class ChargeEventPushOrder extends ChargeEvent {
    constructor(chargeSheetId: string) {
        super();
        this.chargeSheetId = chargeSheetId;
    }

    chargeSheetId: string;
}

export class ChargeEventSafeDraft extends ChargeEvent {
    constructor(data: ChargeInvoiceDetailData) {
        super();
        this.data = data;
    }

    data: ChargeInvoiceDetailData;
}

/**
 * 关闭收收费单
 */
export class ChargeEventClosed extends ChargeEvent {
    constructor(rsp: CloseChargeSheetRsp) {
        super();
        this.rsp = rsp;
    }

    rsp: CloseChargeSheetRsp;
}

class ChargeRepaidRsp {
    error?: ChargePayError;
    id?: string;
    patientOrderId?: string;
    status?: number;
    statusName?: string;
    needPay?: number;
    receivedFee?: number;
    netIncomeFee?: number;
    chargeActionId?: string;
    payStatus?: number;

    //空中药房
    isContainAirPharmacy?: number;
    airPharmacyOrderIds?: string[];
    chargePayTransactionId?: string;
}

class ChargeRepaidRequest {
    receivableFee?: number;
    combinedPayItems?: CombinedPayItem[];
    chargeComment?: string;
    dispensing?: boolean;

    deliveryInfo?: DeliveryInfo;

    deliveryType?: number; //1 送药上门

    dataSignature?: string;
}

export class ChargeInvoiceSaveDraftReq {
    patient?: Patient;

    id?: string;

    //  int sourceClientType;
    chargeForms?: ChargeForm[];
    promotions?: Promotion[];
    giftRulePromotions?: GiftPromotion[];
    couponPromotions?: CouponPromotion[];
    patientPointsInfo?: PatientPointsInfo;
    memberId?: string;
    sellerId?: string;
    // adjustmentFee?: number;
    receivableFee?: number;
    cash?: number;
    cashDiff?: number;
    payMode?: number;
    draftId?: number;
    dispensing?: number;
    draftTime?: number;
    memberInfo?: MemberInfo;
    chargeComment?: string;
    isRenew?: number; //重新收费时，再次挂单时为1

    // deliveryType?: number;
    deliveryInfo?: DeliveryInfo;

    isDecoction?: number;
    decoctionInfo?: DecoctionInfo;

    dataSignature?: string;
    contactMobile?: string;
    expectedAdjustmentFee?: number;

    retailType?: RetailType;
    diagnosisInfos?: DiseasesCode[];
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    diagnosis?: string;
    chiefComplaint?: string;

    sellerDepartmentId?: string; // 执行站、收费处添加科室
    useMemberFlag?: number;
}

export class GetPushOrderReq {
    /// chargeSheetId
    id?: string;

    patient?: Patient;
    adjustmentFee?: number;
    chargeForms?: ChargeForm[];
    promotions?: Promotion[];
    giftRulePromotions?: GiftPromotion[];
    couponPromotions?: CouponPromotion[];
    patientPointsInfo?: PatientPointsInfo;
    memberId?: string;
    cash?: number;
    combinedPayItems?: CombinedPayItem[];

    dataSignature?: string;

    deliveryType?: number; //1 送药上门
    deliveryInfo?: DeliveryInfo;

    isDecoction?: number;
    decoctionInfo?: DecoctionInfo;
    isRenew?: number;
    expectedAdjustmentFee?: number;

    retailType?: RetailType;
    diagnosisInfos?: DiseasesCode[];
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    diagnosis?: string;
    chiefComplaint?: string;

    sellerDepartmentId?: string; // 执行站、收费处添加科室

    useMemberFlag?: number;
}

class CreatePushOrderReq {
    patient?: Patient;
    sourceClientType?: number;
    chargeForms?: ChargeForm[];
    promotions?: Promotion[];
    giftRulePromotions?: GiftPromotion[];
    couponPromotions?: CouponPromotion[];
    memberId?: string;
    id?: string;
    sellerId?: string;
    receivableFee?: number;
    cash?: number;
    draftId?: number;
    memberInfo?: MemberInfo;
    isRenew?: number;

    deliveryType?: number; //1 送药上门
    deliveryInfo?: DeliveryInfo;

    isDecoction?: number;
    decoctionInfo?: DecoctionInfo;

    dataSignature?: string;
    combinedPayItems?: CombinedPayItem;

    contactMobile?: string;

    expectedAdjustmentFee?: number;

    sellerDepartmentId?: string; // 执行站、收费处添加科室

    useMemberFlag?: number;
}

export class GetPushOrderRsp {
    needPay?: string;
    time?: string;
    title?: string;
    remark?: string;
    nowTime?: string;
    isAttention?: number;
    detail?: string;
    clinicName?: string;
    orderId?: string;
    medicalItems?: string;
}

class ChargeRefundFormItem {
    id?: string;

    //  String keyId;

    unit?: string;
    name?: string;
    unitCount?: number;
    doseCount?: number;

    //  double unitPrice;
    productId?: string;
    productType?: number;
    productSubType?: number;
    deductTotalCount?: number;

    //  int useDismounting;
    //  double totalPrice;
    //  double discountedTotalPrice;

    //  double expectedUnitPrice;
    //  double expectedTotalPrice;

    //  double sourceUnitPrice;
    //  double sourceTotalPrice;

    composeChildren?: ChargeRefundFormItem[];

    static from(item: ChargeFormItem): ChargeRefundFormItem {
        const rsp = new ChargeRefundFormItem();

        rsp.id = item.id;
        rsp.unit = item.unit;
        //    rsp.name = item.name
        rsp.unitCount = item.unitCount;
        rsp.doseCount = item.doseCount;
        //    rsp.unitPrice = item.unitPrice
        rsp.productId = item.productId;
        rsp.productType = item.productType;
        rsp.productSubType = item.productSubType;

        return rsp;
    }
}

export class ChargeRefundForm {
    id?: string;
    chargeFormItems?: ChargeRefundFormItem[];

    sourceFormType?: number;
}

class ChargeRefundRequest {
    chargeComment?: string;
    combinedPayItems?: CombinedPayItem[];
    needRefundFee?: number;
    chargeForms?: ChargeRefundForm[];
    adjustmentFee?: number;
    refundCheckReq?: {
        // 退费审核验证通过所需的参数
        accessToken?: string;
    };
}

export class GetDeliverInfoListRsp {
    @JsonProperty({ type: Array, clazz: DeliveryInfo })
    deliveryInfos?: Array<DeliveryInfo>;
}

export interface CalculateProcessPriceRsp {
    processInfos: ProcessInfo[];
    processTotalFee: number;
}

export class SummaryParams {
    allCount?: number;
    draftCount?: number;
    normalCount?: number;
    onlineCount?: number;
    owedCount?: number; // 未处理的欠费收费单
    unchargedCount?: number;
}
export class SearchChargeInvoiceResult {
    keyword?: string;
    @JsonProperty({ type: Array, clazz: ChargeInvoiceData })
    result?: ChargeInvoiceData[];
    offset?: number;
    limit?: number;
    totalCount?: number;

    @JsonProperty({ ignore: true })
    fromDraft = false; //本字段为本地，表示结果来自于本地草稿

    summary?: SummaryParams;
}

class GetSearchAirPharmacyVendorsRsp {
    @JsonProperty({ type: Array, clazz: AirPharmacyVendor })
    vendors?: AirPharmacyVendor[];
}

function fromJSonUsageScopes(json: any) {
    return JsonMapper.deserialize(UsageScopesItem, json);
}

export class UsageScopesItem {
    id?: string;
    type?: number;
    parentId?: string;
    name?: string;
    cisDisplayName?: string;
    isCanProcess?: number;
    @JsonProperty({ type: Array, fromJson: fromJSonUsageScopes })
    children?: UsageScopesItem[];
}

class GetAbnormalTransactionListRsp {
    @JsonProperty({ type: Array, clazz: AbnormalTransactionList })
    records?: AbnormalTransactionList[];
}

class GetAirPharmacyOrdersRsp {
    orders?: AirPharmacyWaitPayOrderDetail[];
}

export class ChargeAgent {
    static chargeStatusPublisher = new Subject<ChargeEvent>();
    static chargeListTotalProvider = new BehaviorSubject({ unchargedCount: 0, owedCount: 0 });

    static async getChargeInvoiceList(params: {
        offset: number;
        pageSize?: number;
        keyword?: string;
        chargeStatus?: string;
        beginDate?: Date;
        endDate?: Date;
        tab?: number;
    }): Promise<SearchChargeInvoiceResult> {
        return ABCApiNetwork.get("charge", {
            queryParameters: {
                offset: (params?.offset ?? 0).toString(),
                limit: (params.pageSize ?? 20).toString(),
                keyword: params.keyword ?? "",
                chargeStatus: params.chargeStatus ?? "",
                beginDate: params.beginDate?.format("yyyy-MM-dd") ?? "",
                endDate: params.endDate?.format("yyyy-MM-dd") ?? "",
                tab: params.tab?.toString() ?? "",
            },
            clazz: SearchChargeInvoiceResult,
            clearUndefined: true,
        });
    }

    /**
     * 获取收费单详情
     * @param chargeId
     */
    static getChargeInvoiceDetail(chargeId: string): Promise<ChargeInvoiceDetailData> {
        return ABCApiNetwork.get(`charge/${chargeId}`, {
            clazz: ChargeInvoiceDetailData,
        }).then((rsp) => {
            // ----productionInfos在调整批次需求时，库存不再是最新的了，此时外层这个字段没用了，最新的库存信息返回在每个item中的productInfo中
            // 使用productionInfos里的最新的商品信息
            // const productInfos = new Map<string, GoodsInfo>(
            //     rsp.productInfos?.map((info) => {
            //         return [info.id!, info];
            //     }) ?? []
            // );
            // // rsp.chargeForms = rsp.chargeForms?.sort((form1, form2) => (form1.sort ?? 0) - (form2.sort ?? 0));
            // rsp.chargeForms?.forEach((form) => {
            //     form.chargeFormItems?.forEach((formItem) => {
            //         const newGoodsInfo = productInfos.get(formItem.productId ?? "");
            //         if (newGoodsInfo !== undefined) {
            //             if (!formItem.productInfo) {
            //                 newGoodsInfo._isFrom = 1;
            //             }
            //             formItem.productInfo = newGoodsInfo;
            //         }
            //     });
            // });
            rsp.fillKeyIds();
            return rsp;
        });
    }

    /**
     * 继续收费--获取卡项支付方式
     * @param detailId
     */
    static async getChargePatientCard(detailId: string): Promise<ChargePatientCards[]> {
        const rsp: { patientCards: ChargePatientCards[] } = await ABCApiNetwork.get(`charge/${detailId}/patient-card`);
        return rsp?.patientCards ?? [];
    }

    /**
     * 对加工取药时间的格式进行转换
     * @param params
     */
    static processWithdrawalTimeToChange(chargeForm?: ChargeForm[]): void {
        // 采取new Date()包裹的原因是form?.processInfo?.takeMedicationTime可能是字符串的格式
        chargeForm
            ?.filter((form) => form.isChinesePrescription || form.isDecoction)
            ?.forEach((form) => {
                if (!!form?.processInfo?.takeMedicationTime) {
                    Object.assign(form.processInfo, {
                        takeMedicationTime: !!form?.processInfo?.takeMedicationTime
                            ? TimeUtils.formatTimeForDateParse(form?.processInfo?.takeMedicationTime)?.format("yyyy-MM-dd HH:mm")
                            : undefined,
                    });
                }
            });
    }

    /// 计算收费单
    /// @param payMode// 0表示收费、1表示退费，2, 表示重新收费 退费时必须有chargeSheetId
    /// @param useMemberFlag// 0：匹配默认会员id，10：使用指定会员，20：不使用会员
    static async postChargeInvoiceCalculate(params: {
        payMode: number;
        paymentMethod?: number; // 支付方式（其实应该用payMode来表示，但是payType之前用了payMode字段，为了避免影响之前的逻辑，所以保留）
        chargeSheetId?: string;
        selects?: AbcMap<ChargeForm, ChargeFormItem[]>;
        promotions?: Promotion[];
        giftPromotions?: GiftPromotion[];
        couponPromotions?: CouponPromotion[];
        memberId?: string;
        useMemberFlag?: number;
        patientId?: string;
        expectedAdjustmentFee?: number;
        refundItemInputCount?: AbcMap<ChargeFormItem, number>;
        dataSignature?: string;
        deliveryInfo?: DeliveryInfo;
        patientPointsInfo?: PatientPointsInfo;
        patientCardPromotions?: PatientCardPromotion[];
        isNeedPatientCardBalance?: number;
        patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];
    }): Promise<ChargeCalculateRspData> {
        const {
            payMode,
            chargeSheetId,
            selects,
            promotions,
            giftPromotions,
            couponPromotions,
            memberId,
            useMemberFlag,
            patientId,
            refundItemInputCount,
            dataSignature,
            expectedAdjustmentFee,
            patientPointsInfo,
            patientCardPromotions,
            isNeedPatientCardBalance,
            patientPointDeductProductPromotions,
            paymentMethod,
        } = params;
        let { deliveryInfo } = params;

        const chargeForms: ChargeForm[] = [];

        function getUnitCount(item: ChargeFormItem): number {
            let unitCount = item.unitCount;
            if (refundItemInputCount != null && refundItemInputCount.has(item)) {
                unitCount = refundItemInputCount.get(item);
            }

            return unitCount ?? 0;
        }

        selects?.forEach((chargeFormItems, chargeForm) => {
            const deductedList = chargeFormItems.filter((subitem) => subitem.compareKey().indexOf("_isHasDeductItem") > -1); // 判断是否存在折扣项
            const normalList = chargeFormItems.filter((subitem) => subitem.compareKey().indexOf("_isHasDeductItem") < 0);

            const formItems = normalList.map((item) => {
                const unitCount = getUnitCount(item);
                const formItem = JsonMapper.deserialize(ChargeFormItem, item);
                if (item.isHasDeductItem) {
                    const deductedItem = deductedList.find((i) => i.compareKey() == `${item.compareKey()}_isHasDeductItem`);
                    formItem.deductedToTalCount = deductedItem?.deductTotalCount ?? 0;
                }
                formItem.unitCount = unitCount;
                formItem.productInfo = undefined;

                formItem.composeChildren = item.composeChildren?.map((subFormItem) => {
                    const subItem = JsonMapper.deserialize(ChargeFormItem, subFormItem);
                    subItem.unitCount = getUnitCount(subFormItem);

                    subItem.productInfo = undefined;
                    return subItem;
                });

                return formItem;
            });

            deductedList.forEach((i) => {
                if (!normalList.find((ii) => i.compareKey() == `${ii.compareKey()}_isHasDeductItem`)) {
                    const formItem = JsonMapper.deserialize(ChargeFormItem, {
                        ...i,
                        id: i.id?.replace("_isHasDeductItem", ""),
                        keyId: i.keyId?.replace("_isHasDeductItem", ""),
                    });
                    formItem.deductedToTalCount = i.deductTotalCount ?? 0;
                    formItem.unitCount = 0;
                    formItem.productInfo = undefined;

                    formItems.push(formItem);
                }
            });

            const form = JsonMapper.deserialize(ChargeForm, chargeForm);
            form.chargeFormItems = formItems;
            form.vendor__ = undefined;
            chargeForms.push(form);
        });

        if (deliveryInfo) {
            deliveryInfo = JsonMapper.deserialize(DeliveryInfo, deliveryInfo);
            deliveryInfo.deliveryFee__ = undefined;
        }
        //检查检验不传composeChildren，只有套餐才有
        const copyChargeForms = _.cloneDeep(chargeForms);
        copyChargeForms?.forEach((form) => {
            if (!form.isPackage) {
                form.chargeFormItems?.forEach((formItem) => {
                    formItem.composeChildren = undefined;
                });
            }
            // 去掉form上的chargeFormItems中的status字段，避免算费的时候有问题
            form.chargeFormItems?.forEach((formItem) => {
                formItem.status = undefined;
                formItem.composeChildren?.forEach((childItem) => {
                    childItem.status = undefined;
                });
            });
            form.status = undefined;
        });
        this.processWithdrawalTimeToChange(copyChargeForms);
        const request = new ChargeCalculateRequest();
        request.payType = payMode;
        request.payMode = paymentMethod;
        request.chargeSheetId = chargeSheetId;
        request.chargeForms = copyChargeForms;
        request.memberId = memberId;
        request.useMemberFlag = useMemberFlag;
        request.patientId = patientId;
        request.promotions = promotions;
        request.giftRulePromotions = giftPromotions;
        request.couponPromotions = couponPromotions;
        request.patientPointsInfo = patientPointsInfo;
        request.expectedAdjustmentFee = expectedAdjustmentFee;
        request.dataSignature = dataSignature;
        request.deliveryInfo = deliveryInfo;
        request.patientCardPromotions = patientCardPromotions;
        if (isNeedPatientCardBalance == 1) {
            request.isNeedPatientCardBalance = isNeedPatientCardBalance;
        }
        request.patientPointDeductProductPromotions = patientPointDeductProductPromotions;

        return ABCApiNetwork.post("charge/calculate", {
            clazz: ChargeCalculateRspData,
            body: request,
        }).then((rsp) => {
            return rsp;
        });
    }

    /**
     * 计算加工费
     */
    static calculateProcess(chargeData: ChargeInvoiceDetailData): Promise<CalculateProcessPriceRsp> {
        chargeData = JsonMapper.deserialize(ChargeInvoiceDetailData, chargeData);
        this.processWithdrawalTimeToChange(chargeData.chargeForms);
        return ABCApiNetwork.post(`charge/calculate/process`, {
            body: {
                isRenew: chargeData.reCharge__ ? 1 : 0,
                chargeSheetId: chargeData.id,
                chargeForms: chargeData.chargeForms
                    ?.filter((form) => form.isChinesePrescription || form.isDecoction)
                    .map((form) => {
                        return {
                            keyId: form.keyId,
                            sourceFormType: form.sourceFormType,
                            processInfo: !!form.processInfo ? form.processInfo : undefined,
                            id: form.sourceFormType != ChargeSourceFormType.decoction ? form.id : undefined,
                            chargeFormItems: form.chargeFormItems?.map((formItem) => {
                                formItem.productInfo = undefined;
                                formItem.usageInfo = undefined;
                                return formItem;
                            }),
                        };
                    }),
            },
            clearUndefined: undefined,
        });
    }

    /**
     * 计算快递费
     * @param chargeData
     * @param deliveryInfo
     * @param scene
     */
    static calculateDelivery(
        chargeData?: ChargeInvoiceDetailData,
        deliveryInfo?: DeliveryInfo,
        scene?: number
    ): Promise<CalculateDeliverRsp> {
        chargeData = JsonMapper.deserialize(ChargeInvoiceDetailData, chargeData);
        return ABCApiNetwork.post(`charge/calculate/delivery`, {
            body: {
                isRenew: chargeData.reCharge__ ? 1 : 0,
                deliveryInfo: deliveryInfo ?? chargeData.deliveryInfo, // 本地药房需要的快递信息
                chargeSheetId: chargeData?.chargeForms?.[0]?.chargeSheetId,
                chargeForms: chargeData.chargeForms?.map((form) => {
                    const newChargeForm = {
                        id: form.id,
                        keyId: form.keyId,
                        sourceFormType: form.sourceFormType,
                        pharmacyType: form.pharmacyType,
                        pharmacyNo: form.pharmacyNo,
                        chargeFormItems: form.chargeFormItems?.map((formItem) => {
                            formItem.productInfo = undefined;
                            return formItem;
                        }),
                    };
                    if (form.pharmacyType == PharmacyType.virtual) {
                        _.assign(newChargeForm, { deliveryInfo: form.deliveryInfo ?? deliveryInfo ?? chargeData?.deliveryInfo }); //虚拟药房需要的快递信息
                    }
                    return newChargeForm;
                }),
                scene: scene ?? 0,
            },
        });
    }

    static async searchClinicMembersByPhone(phoneNum: string): Promise<SearchMemberItem[]> {
        const rsp: {
            rows: SearchMemberItem[];
        } = await ABCApiNetwork.get(`patients/query/bymobilelast4`, {
            queryParameters: { key: phoneNum },
        });

        return rsp.rows;
    }

    static async canShowChargePush(): Promise<boolean> {
        const list = await Promise.all([ApiMixService.getMCConfig(), ApiMixService.getWeChatPayConfig()]);
        const mcConfig = list[0];
        const weChatPlayConfig = list[1];

        LogUtils.d(`canShowChargePush weclinicopen = ${mcConfig?.isWeClinicOpen}， wechatpayEnable = ${weChatPlayConfig?.weChatPayEnable}`);
        return (mcConfig?.isWeClinicOpen ?? false) && (weChatPlayConfig?.weChatPayEnable ?? false);
    }

    static async createChargeInvoice(options: {
        draftChargeInvoiceDetail?: ChargeInvoiceDetailData;
        payMode: number;
        amount?: number;
        adjustmentFee?: number;
        expectedAdjustmentFee?: number;
        receivableFee?: number;
        sellerId?: string;
        dispensing?: boolean;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        useMemberFlag?: number;
        chargeComment?: string; // 收费时录入备注
    }): Promise<ChargePayRsp> {
        const {
            draftChargeInvoiceDetail,
            payMode,
            amount,
            expectedAdjustmentFee,
            receivableFee,
            sellerId,
            dispensing,
            thirdPartyPayCardId,
            authCode,
            memberPassword,
            chargeComment,
        } = options;
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, draftChargeInvoiceDetail);

        const req = new ChargeInvoiceCreateReq();
        ChargeUtils.fillChargeRequest(req, detailClone);
        detailClone.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                const type = formItem.productType;
                if (type != GoodsType.deliveryFee && type != GoodsType.decoctionFee) {
                    formItem.productInfo = undefined;
                }

                formItem._computeGoodsInfo = undefined;
                //如果usageInfo不存在，需要构造一个（原因:pc药房发药单会根据formItem上的usageInfo中的dailyDosage字段来控制显示）
                if (!formItem.usageInfo) {
                    formItem.usageInfo = new UsageInfo();
                }
            });
        });
        this.processWithdrawalTimeToChange(detailClone.chargeForms);
        req.sourceClientType = SourceClientType.mobile;
        req.patient = detailClone.patient;
        req.chargeForms = detailClone.chargeForms;
        req.memberId = detailClone.memberId ?? "";
        req.cash = 0;
        req.cashDiff = 0;
        req.dispensing = dispensing ?? false;
        req.expectedAdjustmentFee = expectedAdjustmentFee;
        req.receivableFee = receivableFee;
        req.sellerId = sellerId;
        req.contactMobile = detailClone.contactMobile;
        req.combinedPayItems = [
            new CombinedPayItem(
                payMode,
                payMode == PayMethod.payABCPay ? ABCScanPayMethod.scanQrCode : undefined,
                amount,
                !_.isNil(thirdPartyPayCardId) ? thirdPartyPayCardId : undefined,
                undefined,
                payMode == PayMethod.payABCPay ? authCode : undefined,
                payMode == PayMethod.payMemberCard ? memberPassword : undefined
            ),
        ];
        req.patientCardPromotions = detailClone.patientCardPromotions;
        req.patientPointDeductProductPromotions = detailClone.patientPointDeductProductPromotions;

        //诊断上传调整为数组
        //@ts-ignore
        req.diagnosis = JSON.stringify((req.diagnosis ?? "").split(StringUtils.specialComma));
        req.extendDiagnosisInfos = [
            {
                value: req.diagnosisInfos,
            },
        ];

        req.sellerDepartmentId = detailClone.sellerDepartmentId;
        req.useMemberFlag = options.useMemberFlag ?? 0;
        req.consultantId = detailClone?.consultantId ?? "";
        req.chargeComment = chargeComment ?? "";

        const rsp = await ABCApiNetwork.post(`charge/create`, {
            clazz: ChargePayRsp,
            body: req,
        });
        ChargeAgent.chargeStatusPublisher.next(new ChargeEventDirectCharge(draftChargeInvoiceDetail!.id!, rsp));

        return rsp;
    }

    static async putChargeInvoiceRenewpaid(options: {
        detailData?: ChargeInvoiceDetailData;
        payMethod: number;
        receivable?: number;
        cash?: number;
        cashDiff: number;
        dispensing: boolean;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        chargeComment?: string;
    }): Promise<ChargeRenewpaidRsp> {
        const { detailData, payMethod, receivable, cash, dispensing, thirdPartyPayCardId, authCode, memberPassword, chargeComment } =
            options;
        const cloneData = JsonMapper.deserialize(ChargeInvoiceDetailData, detailData);
        const request = new ChargePayReq();
        ChargeUtils.fillChargeRequest(request, cloneData);
        ChargeUtils.removeUnCheckFormItem(cloneData);

        cloneData.chargeForms?.forEach((chargeForm) => {
            chargeForm.chargeFormItems?.forEach((item) => {
                item.productInfo = undefined;
                item.usageInfo = undefined;

                item.composeChildren?.forEach((child) => {
                    child.productInfo = undefined;
                    child.usageInfo = undefined;
                });
            });
        });
        this.processWithdrawalTimeToChange(cloneData.chargeForms);
        const payModeItem = new CombinedPayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }
        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }

        const payModes: CombinedPayItem[] = [];
        payModes.push(payModeItem);

        request.memberId = cloneData.memberId ?? "";
        request.sellerId = cloneData.sellerId;
        request.chargeForms = cloneData.chargeForms;
        request.receivableFee = receivable;
        request.dispensing = dispensing;
        request.combinedPayItems = payModes;
        request.dataSignature = cloneData.dataSignature;
        request.contactMobile = cloneData.contactMobile;
        request.expectedAdjustmentFee = cloneData.chargeSheetSummary?.expectedAdjustmentFee__ ?? 0;
        request.patientCardPromotions = cloneData.patientCardPromotions;
        request.patientPointDeductProductPromotions = cloneData.patientPointDeductProductPromotions;
        request.consultantId = cloneData.consultantId ?? "";
        request.chargeComment = chargeComment ?? "";
        request.sellerDepartmentId = cloneData.sellerDepartmentId;
        const rsp = await ABCApiNetwork.put(`charge/${cloneData.id}/renewpaid`, {
            clazz: ChargeRenewpaidRsp,
            body: request,
        });

        ChargeAgent.chargeStatusPublisher.next(new ChargeEventRenewpaid(rsp));

        return rsp;
    }

    //收费，挂单收费
    static putChargeInvoicePaid(options: {
        chargeSheet: ChargeInvoiceDetailData;
        payMethod: number;
        receivable?: number;
        cash?: number;
        cashDiff?: number;
        dispensing?: boolean;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        useMemberFlag?: number;
        chargeComment?: string;
    }): Promise<ChargePayRsp> {
        const { chargeSheet, payMethod, receivable, cash, dispensing, thirdPartyPayCardId, authCode, memberPassword, chargeComment } =
            options;
        const path = `charge/${chargeSheet.id!}/paid`;

        const payForms: ChargeForm[] = [];

        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, chargeSheet);

        const request = new ChargePayReq();
        ChargeUtils.fillChargeRequest(request, detailClone);

        let registrationItem: ChargeFormItem | undefined;
        detailClone.chargeForms!.forEach((chargeForm) => {
            if (chargeForm.isRegistration && !_.isEmpty(chargeForm.chargeFormItems)) {
                registrationItem = _.first(chargeForm.chargeFormItems)!;
            }

            if (chargeForm.chargeFormItems) _.remove(chargeForm.chargeFormItems, (item) => item.checked == false);
            if (_.isEmpty(chargeForm.chargeFormItems)) return;

            chargeForm.chargeFormItems!.forEach((item) => {
                const payFormItem = JsonMapper.deserialize(ChargeFormItem, item);
                item.usageInfo = undefined;
                item.productInfo = undefined;

                item.composeChildren?.forEach((child) => {
                    child.usageInfo = undefined;
                    item.productInfo = undefined;
                });

                return payFormItem;
            });

            payForms.push(chargeForm);
        });
        this.processWithdrawalTimeToChange(payForms);
        const payModeItem = new CombinedPayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }
        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }

        const payModes: CombinedPayItem[] = [];
        payModes.push(payModeItem);

        request.memberId = detailClone.memberId ?? "";
        request.sellerId = detailClone.sellerId ?? "";
        request.patient = detailClone.patient;
        request.chargeForms = payForms;
        request.receivableFee = receivable;
        request.combinedPayItems = payModes;
        request.dispensing = dispensing;
        request.contactMobile = detailClone.contactMobile;
        request.dataSignature = detailClone.dataSignature;
        request.expectedAdjustmentFee = detailClone.chargeSheetSummary?.expectedAdjustmentFee__ ?? 0;
        request.patientCardPromotions = detailClone.patientCardPromotions;
        request.sellerDepartmentId = detailClone.sellerDepartmentId;
        request.patientPointDeductProductPromotions = detailClone?.patientPointDeductProductPromotions;
        request.useMemberFlag = options.useMemberFlag ?? 0;
        request.consultantId = detailClone.consultantId ?? "";
        request.chargeComment = chargeComment ?? "";

        if (
            registrationItem &&
            registrationItem.productInfo instanceof ChargeRegistrationInfo &&
            (registrationItem.registrationLocalModified ?? false)
        ) {
            const productionInfo = registrationItem.productInfo;
            const registration = new ChargePayRegistration();
            registration.id = registrationItem.id;
            registration.doctorId = productionInfo.doctorId;
            registration.doctorName = productionInfo.doctorName;
            registration.departmentName = productionInfo.departmentName;
            registration.departmentId = productionInfo.departmentId;
            registration.fee = registrationItem.unitPrice;
            request.registration = registration;
        }

        return ABCApiNetwork.put(path, {
            clazz: ChargePayRsp,
            body: request,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventPaid(rsp));
            return rsp;
        });
    }

    static putChargeInvoiceRepaid(options: {
        chargeId: string;
        receivableFee: number;
        payMethod: number;
        amount: number;
        dispensing?: boolean;
        deliveryInfo?: DeliveryInfo;
        deliveryType?: number;
        dataSignature?: string;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        chargeComment?: string;
    }): Promise<ChargeRepaidRsp> {
        const {
            chargeId,
            receivableFee,
            payMethod,
            amount,
            dispensing,
            deliveryInfo,
            deliveryType,
            dataSignature,
            thirdPartyPayCardId,
            authCode,
            memberPassword,
            chargeComment,
        } = options;
        const path = `charge/${chargeId}/repaid`;

        const payModes: CombinedPayItem[] = [
            new CombinedPayItem(
                payMethod,
                payMethod == PayMethod.payABCPay ? ABCScanPayMethod.scanQrCode : undefined,
                amount,
                !_.isNil(thirdPartyPayCardId) ? thirdPartyPayCardId : undefined,
                undefined,
                payMethod == PayMethod.payABCPay ? authCode : undefined,
                payMethod == PayMethod.payMemberCard ? memberPassword : undefined
            ),
        ];

        const request = new ChargeRepaidRequest();
        request.receivableFee = receivableFee;
        request.combinedPayItems = payModes;
        request.dispensing = dispensing;
        request.deliveryType = deliveryType;
        request.deliveryInfo = deliveryInfo;
        request.dataSignature = dataSignature;
        request.chargeComment = chargeComment;

        return ABCApiNetwork.put(path, {
            clazz: ChargeRepaidRsp,
            body: request,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventRepaid(rsp));
            return rsp;
        });
    }

    static deleteDraft(chargeId: string): Promise<boolean> {
        return ABCApiNetwork.delete(`charge/draft/${chargeId}`).then((/*rsp*/) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventDeleteNetworkDraft(chargeId));
            return true;
        });
    }

    static saveChargeInvoice(chargeInvoice: ChargeInvoiceDetailData): Promise<ChargeInvoiceDetailData> {
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, chargeInvoice);

        const req = new ChargeInvoiceSaveDraftReq();
        ChargeUtils.fillChargeRequest(req, detailClone);
        detailClone.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                formItem.productInfo = undefined;
            });
        });
        this.processWithdrawalTimeToChange(detailClone.chargeForms);
        req.id = detailClone.id;
        req.patient = detailClone.patient;
        req.chargeForms = detailClone.chargeForms;
        req.memberId = detailClone.memberId ?? "";
        req.receivableFee = chargeInvoice.chargeSheetSummary?.receivableFee;
        req.sellerId = detailClone.sellerId;
        req.cash = 0;
        req.cashDiff = 0;
        req.isRenew = detailClone.reCharge__ ?? false ? 1 : 0;
        req.draftTime = new Date().getTime();
        // req.deliveryType = detailClone.deliveryType__
        req.deliveryInfo = detailClone.deliveryInfo;
        req.dataSignature = detailClone.dataSignature;
        req.contactMobile = detailClone.contactMobile;
        req.expectedAdjustmentFee = detailClone.chargeSheetSummary?.expectedAdjustmentFee__;
        req.sellerDepartmentId = detailClone.sellerDepartmentId;
        req.useMemberFlag = detailClone.useMemberFlag;

        return ABCApiNetwork.put("charge/save", {
            clazz: ChargeInvoiceDetailData,
            body: req,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeChargeInvoice(rsp, detailClone));
            return rsp;
        });
    }

    static getPushOrderDetail(invoiceData: ChargeInvoiceDetailData): Promise<GetPushOrderRsp> {
        const cloneData = JsonMapper.deserialize(ChargeInvoiceDetailData, invoiceData);

        cloneData.chargeForms?.forEach((form) => {
            if (form.chargeFormItems) _.remove(form.chargeFormItems, (item) => item.checked == false);
            form.chargeFormItems?.forEach((item) => {
                item.productInfo = undefined;
            });
        });

        const request = new GetPushOrderReq();
        ChargeUtils.fillChargeRequest(request, invoiceData);

        cloneData.chargeForms?.forEach((form) => {
            _.remove(form.chargeFormItems!, (item) => item.checked == false);
            form.chargeFormItems!.forEach((item) => {
                item.productInfo = undefined;
            });
        });
        this.processWithdrawalTimeToChange(cloneData.chargeForms);
        request.chargeForms = cloneData.chargeForms;
        request.adjustmentFee = invoiceData.chargeSheetSummary?.adjustmentFee ?? 0.0;
        request.patient = cloneData.patient;
        request.memberId = cloneData.memberId;
        request.cash = 0;
        request.combinedPayItems = [];
        request.id = cloneData.id;
        request.isRenew = cloneData.reCharge__ ?? false ? 1 : 0;
        request.dataSignature = cloneData.dataSignature;
        request.expectedAdjustmentFee = cloneData.chargeSheetSummary?.expectedAdjustmentFee__;
        request.sellerDepartmentId = cloneData.sellerDepartmentId;
        request.useMemberFlag = cloneData.useMemberFlag;

        return ABCApiNetwork.post("charge/pushorderdetail", {
            clazz: GetPushOrderRsp,
            body: request,
        });
    }

    static pushOrder(options: {
        invoiceData: ChargeInvoiceDetailData;
        expectedAdjustmentFee?: number;
        receivableFee?: number;
        sellerId?: string;
    }): Promise<string> {
        const { invoiceData, expectedAdjustmentFee, receivableFee, sellerId } = options;
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, invoiceData);
        const req = new CreatePushOrderReq();
        ChargeUtils.fillChargeRequest(req, detailClone);

        detailClone.chargeForms?.forEach((form) => {
            _.remove(form.chargeFormItems!, (item) => item.checked == false);
            form.chargeFormItems!.forEach((formItem) => {
                formItem.productInfo = undefined;
            });
        });
        this.processWithdrawalTimeToChange(detailClone.chargeForms);
        req.sourceClientType = SourceClientType.mobile;
        req.patient = detailClone.patient;
        req.chargeForms = detailClone.chargeForms;
        req.memberId = detailClone.memberId ?? "";
        req.id = detailClone.id;
        req.cash = 0;
        req.receivableFee = receivableFee;
        req.sellerId = sellerId;
        req.isRenew = detailClone.reCharge__ ?? false ? 1 : 0;
        req.dataSignature = invoiceData.dataSignature;
        req.contactMobile = invoiceData.contactMobile;
        req.expectedAdjustmentFee = expectedAdjustmentFee;
        req.sellerDepartmentId = invoiceData.sellerDepartmentId;
        req.useMemberFlag = invoiceData.useMemberFlag;

        return ABCApiNetwork.post("charge/pushorder", {
            body: req,
        }).then((rsp: any) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventPushOrder(rsp.chargeSheetId));
            return rsp.chargeSheetId;
        });
    }

    static saveDraft(draftChargeInvoiceDetail: ChargeInvoiceDetailData): Promise<string> {
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, draftChargeInvoiceDetail);
        const req = new ChargeInvoiceSaveDraftReq();
        ChargeUtils.fillChargeRequest(req, detailClone);

        detailClone.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                formItem.productInfo = undefined;
            });
        });
        this.processWithdrawalTimeToChange(detailClone.chargeForms);
        req.id = detailClone.id;
        req.patient = detailClone.patient;
        req.chargeForms = detailClone.chargeForms;
        req.memberId = detailClone.memberId ?? "";
        req.receivableFee = draftChargeInvoiceDetail.chargeSheetSummary?.receivableFee;
        req.sellerId = detailClone.sellerId;
        req.cash = 0;
        req.cashDiff = 0;
        req.isRenew = detailClone.reCharge__ ?? false ? 1 : 0;
        req.draftTime = new Date().getTime();
        req.dataSignature = detailClone.dataSignature;
        req.contactMobile = detailClone.contactMobile;
        req.expectedAdjustmentFee = detailClone.chargeSheetSummary?.expectedAdjustmentFee__;
        req.sellerDepartmentId = detailClone.sellerDepartmentId;
        req.useMemberFlag = detailClone.useMemberFlag;

        return ABCApiNetwork.post("charge/draft/save", {
            body: req,
        }).then((rsp: any) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventSafeDraft(draftChargeInvoiceDetail));
            return rsp.chargeSheetId;
        });
    }

    /**
     * 关闭收费单
     * @param id 订单id
     */
    static closeChargeSheet(id: string): Promise<CloseChargeSheetRsp> {
        return ABCApiNetwork.put<CloseChargeSheetRsp>(`charge/${id}/close`).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventClosed(rsp));
            return rsp;
        });
    }

    static putChargeInvoiceRefund(options: {
        chargeId: string;
        payMethod: number;
        selects: AbcMap<ChargeForm, Array<ChargeFormItem>>;
        refundItemInputCount: AbcMap<ChargeFormItem, number>;
        needRefundFee?: number;
        amount?: number;
        feeAdjustment?: number;
        dispensing?: boolean;
        memberId?: string;
        paySubMode?: number;
        thirdPartyPayCardId?: string;
        transactionIds?: string[];
        accessToken?: string;
        chargeComment?: string;
    }): Promise<ChargeRefundRsp> {
        const {
            chargeId,
            payMethod,
            selects,
            refundItemInputCount,
            needRefundFee,
            feeAdjustment,
            paySubMode,
            amount,
            thirdPartyPayCardId,
            transactionIds,
            accessToken,
            chargeComment,
        } = options;
        const path = `charge/${chargeId}/refund`;
        const payForms: ChargeRefundForm[] = [];
        selects.forEach((chargeFormItems, chargeForm) => {
            const deductedList = chargeFormItems.filter((subitem) => subitem.compareKey().indexOf("_isHasDeductItem") > -1); // 判断是否存在折扣项
            const normalList = chargeFormItems.filter((subitem) => subitem.compareKey().indexOf("_isHasDeductItem") < 0);

            const payFormItems = normalList.map((item) => {
                const refundFormItem = ChargeRefundFormItem.from(item);
                if (item.isHasDeductItem) {
                    const deductedItem = deductedList.find((i) => i.compareKey() == `${item.compareKey()}_isHasDeductItem`);
                    refundFormItem.deductTotalCount = deductedItem?.deductTotalCount ?? 0;
                }
                refundFormItem.unitCount = refundItemInputCount.get(item);
                if (chargeForm.isChinesePrescription) {
                    refundFormItem.doseCount = item.canRefundDoseCount;
                }

                refundFormItem.composeChildren = item.composeChildren?.map((child) => {
                    const refundItem = ChargeRefundFormItem.from(child);
                    refundItem.unitCount = refundItemInputCount.get(child);

                    return refundItem;
                });

                return refundFormItem;
            });

            deductedList.forEach((i) => {
                if (!normalList.find((ii) => i.compareKey() == `${ii.compareKey()}_isHasDeductItem`)) {
                    const formItem = JsonMapper.deserialize(ChargeFormItem, {
                        ...i,
                        id: i.id?.replace("_isHasDeductItem", ""),
                        keyId: i.keyId?.replace("_isHasDeductItem", ""),
                    });
                    const refundFormItem = ChargeRefundFormItem.from(formItem);
                    refundFormItem.deductTotalCount = i.deductTotalCount ?? 0;
                    refundFormItem.unitCount = 0;
                    if (chargeForm.isChinesePrescription) {
                        refundFormItem.doseCount = i.canRefundDoseCount;
                    }

                    payFormItems.push(refundFormItem);
                }
            });

            const payForm = new ChargeRefundForm();
            payForm.id = chargeForm.id;
            payForm.chargeFormItems = payFormItems;
            payForm.sourceFormType = chargeForm.sourceFormType;

            payForms.push(payForm);
        });

        const payModeItem = new CombinedPayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = amount;
        payModeItem.paySubMode = paySubMode;
        payModeItem.transactionIds = transactionIds;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }

        const payModes: CombinedPayItem[] = [];
        payModes.push(payModeItem);

        //检查检验不传composeChildren，只有套餐才有
        const copyChargeForms = _.cloneDeep(payForms);
        copyChargeForms?.forEach((form) => {
            if (!(form?.sourceFormType == ChargeSourceFormType.package)) {
                form.chargeFormItems?.forEach((formItem) => {
                    formItem.composeChildren = undefined;
                });
            }
        });

        const request = new ChargeRefundRequest();
        request.chargeForms = copyChargeForms;
        request.adjustmentFee = feeAdjustment;
        request.combinedPayItems = payModes;
        request.needRefundFee = needRefundFee;
        request.chargeComment = chargeComment ?? "";
        if (!!accessToken) {
            request.refundCheckReq = {
                accessToken: accessToken,
            };
        }

        return ABCApiNetwork.put(path, {
            body: request,
            clazz: ChargeRefundRsp,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventRefund(rsp));
            RegistrationAgent.changeObserver.next();
            return rsp;
        });
    }

    static putChargeInvoicePaidBack(
        chargeId: string,
        payMethod: number,
        amount: number,
        paySubMode?: number,
        thirdPartyPayCardId?: string,
        transactionIds?: string[],
        chargeComment?: string
    ): Promise<ChargePaidBackRsp> {
        const path = `charge/${chargeId}/paidback`;
        return ABCApiNetwork.put(path, {
            body: {
                combinedPayItems: [new CombinedPayItem(payMethod, paySubMode, amount, thirdPartyPayCardId, transactionIds)],
                chargeComment: chargeComment ?? "",
            },
            clazz: ChargePaidBackRsp,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(new ChargeEventPaidBack(rsp));
            return rsp;
        });
    }

    /**
     * 获取快递公司列表
     */
    static async getDeliverCompanies(
        addressProvinceId: string,
        addressCityId: string,
        addressDistrictId: string,
        scene: number
    ): Promise<DeliveryCompany[]> {
        return await ABCApiNetwork.get("charge/delivery/companies", {
            queryParameters: {
                addressProvinceId: addressProvinceId ?? "", // 省ID
                addressDistrictId: addressDistrictId ?? "", // 区ID
                addressCityId: addressCityId ?? "", // 市ID
                scene: scene ?? 0, // 场景
            },
        }).then((res) => {
            return ((res as any)?.deliveryCompanies).map((item: any) => JsonMapper.deserialize(DeliveryCompany, item));
        });
    }

    /**
     * 检查配送地址是否在有效范围内
     * @param info
     */
    static async deliveryScopeCheck(info: DeliveryInfo): Promise<boolean> {
        const ret: boolean = await ABCApiNetwork.post("charge/delivery/checkscope", {
            body: info,
        });
        LogUtils.d("检查配送地址是否在有效范围内" + ret);
        return ret;
    }

    /**
     * 获取患者收货地址
     */
    static async getDeliverList(params: GetAddressListParams): Promise<Array<DeliveryInfo>> {
        const rsp: GetDeliverInfoListRsp = await ABCApiNetwork.get("charge/delivery/address", {
            queryParameters: params,
            clazz: GetDeliverInfoListRsp,
            clearUndefined: true,
        });
        return rsp.deliveryInfos ?? [];
    }

    /**
     * 根据基本配送信息查询配送规则
     */
    static getDeliveryRule(addressProvinceId: string, addressCityId: string, addressDistrictId: string): Promise<DeliveryFeeRule> {
        return ABCApiNetwork.get("charge/rule/express/find-company", {
            clazz: DeliveryFeeRule,
            queryParameters: {
                addressProvinceId: addressProvinceId ?? "",
                addressDistrictId: addressDistrictId ?? "",
                addressCityId: addressCityId ?? "",
            },
        });
    }

    /**
     * 根据地址信息查询可用的快递公司列表
     * @param addressProvinceId---省id
     * @param addressCityId----市id
     * @param addressDistrictId----区id
     * @param scene---查询快递公司的场景，0：本地药房，1：虚拟药房
     */
    static async getAvailableCompaniesByExpress(
        addressProvinceId: string,
        addressCityId: string,
        addressDistrictId: string,
        scene: number
    ): Promise<AvailableCompanyParams[]> {
        const rsp: { companies: AvailableCompanyParams[] } = await ABCApiNetwork.get("charge/rule/express/available-companies", {
            queryParameters: {
                addressProvinceId: addressProvinceId ?? "",
                addressDistrictId: addressDistrictId ?? "",
                addressCityId: addressCityId ?? "",
                scene: scene ?? 0,
            },
        });
        return rsp.companies ?? [];
    }

    static key = "ProcessRules";

    static getProcessRulesSync(): ProcessRule[] | undefined {
        return clinicScopeMemoryCache.get(ChargeAgent.key);
    }

    /**
     * 获取药品加工规则
     * @param canUseCache 是否可以从缓存中获取
     */
    static getProcessRules(canUseCache = true): Promise<ProcessRule[]> {
        if (canUseCache) {
            const cache = clinicScopeMemoryCache.get(ChargeAgent.key);
            if (cache) return Promise.resolve(cache);
        }

        return ABCApiNetwork.get("charge/rule/process/usages/available").then((rsp: any) => {
            clinicScopeMemoryCache.set(ChargeAgent.key, rsp);
            return rsp;
        });
    }

    //////空中药房相关
    /**
     * 获取供应商列表
     * @param params
     */
    static async getVendors(params: {
        airPharmacyFormItems?: Array<PrescriptionFormItem>;
        doseCount?: number;
        goodsTypeId: ChineseGoodType;
        medicineStateScopeId?: string; //MedicalStateType
        usageScopeId?: string;
        vendorId?: string | null;
        pharmacyNo?: number;
    }): Promise<AirPharmacyVendor[]> {
        const rsp = await ABCApiNetwork.post("charge/air-pharmacy/list-available-vendors", {
            body: params,
            clazz: GetSearchAirPharmacyVendorsRsp,
        }).catchIgnore();
        return rsp?.vendors ?? [];
    }

    /**
     * 获取虚拟药房供应商列表
     */
    static async getVirtualVendorList(): Promise<VirtualPharmacyConfig> {
        return ABCApiNetwork.get("charge/air-pharmacy/list-virtual-pharmacy-vendors", { clazz: VirtualPharmacyConfig });
    }

    private static _usageScopeRsp: UsageScopesItem[];

    /**
     * 获取制法列表
     */
    static async getUsageScopes(withCache = false): Promise<UsageScopesItem[]> {
        if (this._usageScopeRsp && withCache) {
            return this._usageScopeRsp;
        }
        const _usageScopeRsp = await ABCApiNetwork.get<{ rows: UsageScopesItem[] }>(`charge/air-pharmacy/vendors/business-scopes`);
        this._usageScopeRsp = _usageScopeRsp.rows;
        return _usageScopeRsp.rows;
    }

    // /**
    //  * 获取空中药房订单数
    //  */
    // static async getAirPharmacyOrderCount() {
    //     return await ABCApiNetwork.get(`charge/air-pharmacy/count-wait-pay-order`);
    // }

    /**
     * 匹配可用的快递公司规则
     */
    static async getAirPharmacyAvailableCompany(params: {
        vendorId?: string;
        usageScopeId?: string;
        addressCityId?: string;
        addressCityName?: string;
        addressDistrictId?: string;
        addressDistrictName?: string;
        addressProvinceId?: string;
        addressProvinceName?: string;
        goodsTypeId: number;
        orderItems: {
            productId?: string;
            name?: string;
            unitPrice?: number;
            unit?: string;
            unitCount?: number;
            doseCount?: number;
            totalPrice?: number;
        }[];
        medicineStateScopeId?: string;
    }): Promise<AirPharmacyDeliveryRule[] | undefined> {
        return await ABCApiNetwork.post<{ companies?: AirPharmacyDeliveryRule[] }>(`charge/air-pharmacy/available-company`, {
            body: params,
        }).then((rsp) => {
            return rsp.companies;
        });
    }

    /**
     * 空中药房算费
     * @param params
     */
    static async getAirPharmacyCalculate(params?: AirPharmacyCalculateReq): Promise<AirPharmacyCalculateRspForm[]> {
        const rsp = await ABCApiNetwork.post("charge/air-pharmacy/calculate", {
            body: params,
            clazz: AirPharmacyCalculateRsp,
        });
        return rsp?.forms ?? [];
    }

    /**
     * 查询系统配置
     */
    static fetchAirPharmacySystemConfig(useCache = true): Promise<{
        servicePriceRate: number;
        riseRateForYinPian: number;
        riseRateForKeLi: number;
    }> {
        const kKey = "airPharmacySystemConfig";
        if (useCache) {
            const result = clinicScopeMemoryCache.get(kKey);
            if (result) return Promise.resolve(result);
        }
        return ABCApiNetwork.get(`charge/air-pharmacy/system-config`).then((rsp: any) => {
            clinicScopeMemoryCache.set(kKey, rsp);
            return rsp;
        });
    }

    /**
     * 空中药房订单支付
     */
    static airPharmacyPay(orderIds: string[], isAllPaid: number): Promise<AirPharmacyPayRsp> {
        return ABCApiNetwork.post<AirPharmacyPayRsp>(`charge/air-pharmacy/pay`, {
            body: {
                orderIds: orderIds,
                isAllPaid: isAllPaid,
            },
        });
    }

    /**
     * 获取空中药房订单详情
     * @param id 订单id
     */
    static getAirPharmacyOrder(id: string): Promise<AirPharmacyOrderDetail> {
        return ABCApiNetwork.get<AirPharmacyOrderDetail>(`charge/air-pharmacy/orders/${id}`);
    }

    static getAirPharmacyOrders(id: string): Promise<GetAirPharmacyOrdersRsp> {
        return ABCApiNetwork.get<GetAirPharmacyOrdersRsp>(`charge/air-pharmacy/orders/wait-pay/${id}`);
    }

    /////空中药房相关 end

    /**
     * 获取用户卡项列表
     * @param chainId
     * @param patientId
     */
    static async getChargePatientCardList(chainId: string, patientId: string): Promise<PatientCardList[]> {
        const rsp: { rows: PatientCardList[] } = await ABCApiNetwork.get(`promotions/card/patient/${patientId}/list`, {
            queryParameters: {
                chainId: chainId,
            },
        });
        return rsp.rows ?? [];
    }

    /**
     * 卡详情
     * @param id
     */
    static getPromotionCardDetail(cardId: string, chainId: string): Promise<PromotionCardInfo> {
        return ABCApiNetwork.get(`promotions/card/${cardId}`, {
            queryParameters: {
                chainId: chainId,
            },
        });
    }

    /**
     * 通过完整手机号查询其他卡项
     * @param mobile
     */
    static async getOtherPatientCardListByMobile(mobile: string): Promise<PatientCardList[]> {
        const rsp: { rows: PatientCardList[] } = await ABCApiNetwork.get(`promotions/card/patients/list-by-mobile`, {
            queryParameters: {
                mobile,
            },
        });
        return rsp?.rows ?? [];
    }

    /**
     * 根据卡项id查询卡项详情信息
     * @param id
     */
    static async queryPatientCardDetailById(id: string): Promise<PatientCardInfo> {
        return ABCApiNetwork.get(`promotions/card/patients/${id}`);
    }

    /**
     * 根据chargePayTransactionId查询支付状态
     * @param chargePayTransactionId
     */
    static async checkChargePayStatus(
        chargePayTransactionId: string
    ): Promise<{ payStatus?: number; message?: string; chargeSheetId?: string }> {
        return ABCApiNetwork.get(`charge/paystatus/${chargePayTransactionId}`);
    }

    /**
     *查询收费单入账异常列表
     * @param chargeSheetId--收费单id
     */
    static async getListAbnormaltransaction(chargeSheetId: string): Promise<AbnormalTransactionList[]> {
        const rsp = await ABCApiNetwork.get(`charge/${chargeSheetId}/list-abnormal-transactions`, {
            clazz: GetAbnormalTransactionListRsp,
        });
        return rsp?.records ?? [];
    }

    /**
     * 收费单入账异常列表下--异常退费
     * @param chargeSheetId--收费单id
     * @param abnormalTransactionId----异常项的id
     */
    static async dealAbnormalRefund(chargeSheetId: string, abnormalTransactionId: string): Promise<AbnormalRefundResult> {
        return ABCApiNetwork.put(`charge/${chargeSheetId}/${abnormalTransactionId}/abnormal-refund`);
    }

    /**
     *获取收费单是否可以收费
     * @param chargeSheetId---收费单id
     */
    static async getChargePaidPreCheck(chargeSheetId: string): Promise<PreCheckPaidResult> {
        return ABCApiNetwork.get(`charge/${chargeSheetId}/paid-pre-check`);
    }

    /**
     * 强制取消在收费中的异步支付单
     * @param chargeSheetId---收费单id
     * @param chargePayTransactionIds
     */
    static async absoluteCancelCharge(
        chargeSheetId: string,
        chargePayTransactionIds: Array<string>
    ): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.put(`charge/${chargeSheetId}/absolute-cancel`, { body: { chargePayTransactionIds } });
    }

    /**
     * 零售单选择abc支付时，先锁单且保存
     * @param options
     */
    static async retailChargeLockSave(options: {
        detailData?: ChargeInvoiceDetailData;
        payMethod: number;
        receivableFee?: number;
        cash?: number;
        cashDiff?: number;
        dispensing?: boolean;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        chargeComment?: string;
    }): Promise<ChargePayRsp> {
        const { detailData, payMethod, receivableFee, cash, dispensing, thirdPartyPayCardId, authCode, memberPassword, chargeComment } =
            options;
        const payForms: ChargeForm[] = [];
        const detailClone = JsonMapper.deserialize(ChargeInvoiceDetailData, detailData);
        const req = new ChargePayReq();
        ChargeUtils.fillChargeRequest(req, detailClone);

        let registrationItem: ChargeFormItem | undefined;
        detailClone.chargeForms!.forEach((chargeForm) => {
            if (chargeForm.isRegistration && !_.isEmpty(chargeForm.chargeFormItems)) {
                registrationItem = _.first(chargeForm.chargeFormItems)!;
            }

            if (chargeForm.chargeFormItems) _.remove(chargeForm.chargeFormItems, (item) => item.checked == false);
            if (_.isEmpty(chargeForm.chargeFormItems)) return;

            chargeForm.chargeFormItems!.forEach((item) => {
                const payFormItem = JsonMapper.deserialize(ChargeFormItem, item);
                item.usageInfo = undefined;
                item.productInfo = undefined;

                item.composeChildren?.forEach((child) => {
                    child.usageInfo = undefined;
                    item.productInfo = undefined;
                });

                return payFormItem;
            });

            payForms.push(chargeForm);
        });

        const payModeItem = new CombinedPayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }
        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }

        const payModes: CombinedPayItem[] = [];
        payModes.push(payModeItem);

        req.patient = detailClone.patient;
        req.chargeForms = payForms;
        req.memberId = detailClone.memberId ?? "";
        req.sellerId = detailClone.sellerId;
        req.sellerDepartmentId = detailClone.sellerDepartmentId;
        req.dispensing = dispensing;
        req.expectedAdjustmentFee = detailClone.chargeSheetSummary?.expectedAdjustmentFee__ ?? 0;
        req.receivableFee = receivableFee;
        req.dataSignature = detailClone.dataSignature;
        req.contactMobile = detailClone.contactMobile;
        req.combinedPayItems = payModes;
        req.patientCardPromotions = detailClone.patientCardPromotions;
        req.chargeComment = chargeComment ?? "";

        if (
            registrationItem &&
            registrationItem.productInfo instanceof ChargeRegistrationInfo &&
            (registrationItem.registrationLocalModified ?? false)
        ) {
            const productionInfo = registrationItem.productInfo;
            const registration = new ChargePayRegistration();
            registration.id = registrationItem.id;
            registration.doctorId = productionInfo.doctorId;
            registration.doctorName = productionInfo.doctorName;
            registration.departmentName = productionInfo.departmentName;
            registration.departmentId = productionInfo.departmentId;
            registration.fee = registrationItem.unitPrice;
            req.registration = registration;
        }
        const rsp = await ABCApiNetwork.post("charge/lock-save", { body: req, clazz: ChargePayRsp });
        ChargeAgent.chargeStatusPublisher.next(new ChargeEventPaid(rsp));
        return rsp;
    }

    /**
     *在收费单为本地草稿时，选择ABC支付、会员卡支付、卡项支付时，调用锁单且保存后，调用的支付接口
     * @param options
     */
    static async retailChargeAbcPay(options: {
        chargeSheetId: string;
        payMethod: number;
        receivableFee?: number;
        cash?: number;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        chargeComment?: string;
        sellerId?: string;
        sellerDepartmentId?: string;
        consultantId?: string;
    }): Promise<AbcRetailChargeRsp> {
        const {
            chargeSheetId,
            payMethod,
            receivableFee,
            cash,
            thirdPartyPayCardId,
            authCode,
            memberPassword,
            chargeComment,
            sellerId,
            sellerDepartmentId,
            consultantId,
        } = options;
        const req = new RetailPayReq();
        const payModeItem = new CombinedPayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }

        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }

        const payModes: CombinedPayItem[] = [];
        payModes.push(payModeItem);
        req.receivableFee = receivableFee;
        req.combinedPayItems = payModes;
        req.chargeComment = chargeComment ?? "";
        req.sellerId = sellerId ?? "";
        req.sellerDepartmentId = sellerDepartmentId ?? "";
        req.consultantId = consultantId ?? "";
        return ABCApiNetwork.put(`charge/${chargeSheetId}/pay`, { body: req, clazz: AbcRetailChargeRsp });
    }

    /**
     * 根据患者id查询当前会员信息
     * @param memberId---会员id
     */
    static async getMemberInfo(memberId: string): Promise<Patient> {
        return ABCApiNetwork.get(`patients/${memberId}/member`);
    }

    /**
     * 验证当前输入的会员卡密码是否正确
     * @param memberId---会员id
     * @param pwd---密码
     * @constructor
     */
    static async VerificationMemberPwd(memberId: string, pwd: string): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.post(`patients/${memberId}/member/password/verification`, {
            body: { password: pwd },
        });
    }

    /**
     *根据patientOrderId查询推送支付需要的订单详情
     * @param patientOrderId
     * @param type  场景类型：1：执行站，2：门诊
     */
    static async getPushScanCodeDetail(patientOrderId: string, type: number): Promise<PushScanCodeInfo> {
        return ABCApiNetwork.get(`charge/patientorder/${patientOrderId}/push-scan-code`, {
            queryParameters: { type },
            clazz: PushScanCodeInfo,
        });
    }

    /**
     * 推送支付--推送订单给患者
     * @param chargeSheetId--收费单id
     */
    static async pushOrdertoPatient(chargeSheetId: string): Promise<{ chargeSheetId?: string }> {
        return ABCApiNetwork.put(`charge/${chargeSheetId}/push-order`);
    }

    /**
     * 退费重收时获取详情单
     * @param chargeSheetId--- 详情单id
     * @constructor
     */
    static async getRechargeDetail(chargeSheetId: string): Promise<ChargeInvoiceDetailData> {
        return ABCApiNetwork.get(`charge/${chargeSheetId}/renew`, { clazz: ChargeInvoiceDetailData });
    }

    /**
     * 根据患者id查询待还的欠费单列表
     * @param patientId
     */
    static async getOweStayPaidList(patientId: string): Promise<ChargeOweInfo> {
        const rsp = await ABCApiNetwork.get(`charge/owe/patient/${patientId}`, {
            clazz: ChargeOweInfo,
        });
        return rsp;
    }

    /**
     * 多个欠费单还款组合收费
     */
    static async chargeOweCombinePaid(options: {
        patientId?: string;
        payMethod: number;
        cash?: number;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        transactionIds?: string[];
        oweSheetItems?: OweSheetItems[];
        memberId?: string;
        chargeComment?: string;
    }): Promise<ChargeOweCombinePaidRsp> {
        const {
            patientId,
            payMethod,
            cash,
            thirdPartyPayCardId,
            authCode,
            memberPassword,
            transactionIds,
            oweSheetItems,
            memberId,
            chargeComment,
        } = options;
        const request = new PayChargeOweSheetReq();
        request.patientId = patientId;

        const payModeItem = new OwePayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        payModeItem.transactionIds = transactionIds;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }
        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }
        if (payMethod == PayMethod.payMemberCard) {
            request.memberId = memberId;
        }
        request.payItem = payModeItem;
        request.oweSheetItems = oweSheetItems;
        request.chargeComment = chargeComment ?? "";
        return ABCApiNetwork.put(`charge/owe/combine-paid`, {
            body: request,
            clazz: ChargeOweCombinePaidRsp,
        }).then((rsp) => {
            return rsp;
        });
    }

    /**
     * 单个欠费单收费接口
     * @param options
     * @constructor
     */
    static async ChargeOweSinglePaid(options: {
        chargeOweSheetId: string;
        patientId?: string;
        payMethod: number;
        cash?: number;
        thirdPartyPayCardId?: string;
        authCode?: string;
        memberPassword?: string;
        transactionIds?: string[];
        oweSheetItems?: OweSheetItems[];
        chargeId?: string; //收费单id，用于刷新详情页面
        memberId?: string; //会员支付会员id
        chargeComment?: string;
    }): Promise<ChargeOweSinglePaidRsp> {
        const {
            chargeOweSheetId,
            patientId,
            payMethod,
            cash,
            thirdPartyPayCardId,
            authCode,
            memberPassword,
            transactionIds,
            oweSheetItems,
            chargeId,
            memberId,
            chargeComment,
        } = options;
        const request = new PayChargeOweSheetReq();
        request.patientId = patientId;

        const payModeItem = new OwePayItem();
        payModeItem.payMode = payMethod;
        payModeItem.amount = cash;
        payModeItem.transactionIds = transactionIds;
        if (!_.isNil(thirdPartyPayCardId)) {
            payModeItem.thirdPartyPayCardId = thirdPartyPayCardId;
        }
        if (payMethod == PayMethod.payABCPay) {
            payModeItem.authCode = authCode;
            payModeItem.paySubMode = ABCScanPayMethod.scanQrCode;
        }
        if (payMethod == PayMethod.payMemberCard && !!memberPassword) {
            payModeItem.memberCardPassword = memberPassword;
        }
        if (payMethod == PayMethod.payMemberCard) {
            request.memberId = memberId;
        }
        request.payItem = payModeItem;
        request.oweSheetItems = oweSheetItems;
        request.chargeComment = chargeComment ?? "";
        return ABCApiNetwork.put(`charge/owe/${chargeOweSheetId}/paid`, {
            body: request,
            clazz: ChargeOweSinglePaidRsp,
        }).then((rsp) => {
            ChargeAgent.chargeStatusPublisher.next(
                new ChargeOweSinglePaid(JsonMapper.deserialize(ChargeOweSinglePaidRsp, { id: chargeId }))
            );
            return rsp;
        });
    }

    /**
     * 还款-查询第三方支付订单的支付状态
     * @param combineOrderPayTransactionId
     */
    static async queryCombineOrderPayStatus(
        combineOrderPayTransactionId: string
    ): Promise<{ message?: string; payStatus?: number; chargeSheetId?: string }> {
        return ABCApiNetwork.get(`charge/combine-order/pay-status/${combineOrderPayTransactionId}`);
    }

    /**
     * 修改门诊单时进行锁单操作
     * @param orderId
     *
     * 弃用
     */
    static async putPatientOrderLock(orderId: string): Promise<void> {
        return ABCApiNetwork.put(`charge/patientorders/${orderId}/lock`);
    }

    /**
     * 保存门诊单后进行解锁
     * @param orderId
     *
     * 弃用
     */
    static async putPatientOrderUnLock(orderId: string): Promise<void> {
        return ABCApiNetwork.put(`charge/patientorders/${orderId}/un-lock`);
    }

    /**
     * 获取---收费时同时发药配置保存策略
     */
    static async getChargePaidDispensingSetting(): Promise<DispensingSettingConfig> {
        return await ApiMixService.getPropertyV3("cli_emp", "", "clinicEmployeeChargeSettings", DispensingSettingConfig);
    }

    /**
     * 更新---收费时同时发药配置保存策略
     * @param data---当前发药设置状态
     */
    static async setChargePaidDispensingSetting(data: DispensingSettingConfig): Promise<DispensingSettingConfig> {
        return await ApiMixService.updateProper("cli_emp", "", "clinicEmployeeChargeSettings", data, DispensingSettingConfig);
    }

    /**
     * 门诊加工费计算
     * @param chargeData
     */
    static outpatientCalculateProcess(chineseForms: PrescriptionChineseForm[]): Promise<OutpatientCalculateProcessRsp> {
        return ABCApiNetwork.post(`charge/outpatient/calculate/process`, {
            body: {
                forms: chineseForms?.map((form) => {
                    return {
                        doseCount: form.doseCount,
                        keyId: form.keyId,
                        items: form.prescriptionFormItems?.map((formItem) => {
                            return {
                                name: formItem.displayName,
                                productId: formItem.productId,
                                productSubType: formItem?.subType,
                                productType: formItem?.type,
                                unit: formItem?.unit,
                                unitCount: formItem?.unitCount,
                                unitPrice: formItem?.unitPrice,
                            };
                        }),
                        processInfo: {
                            processBagUnitCount: form?.processBagUnitCount,
                            subType: form?.usageSubType,
                            type: form?.usageType,
                            totalProcessCount: form?.totalProcessCount,
                        },
                    };
                }),
            },
        });
    }

    /**
     * 根据患者id查询待还的单据数量
     * @param patientId
     * @param excludeChargeSheetIds---排除掉的收费单id列表
     */
    static async queryPatientOwingCount(patientId: string, excludeChargeSheetIds: string[]): Promise<{ owingCount?: number }> {
        return await ABCApiNetwork.post(`charge/patient/${patientId}/owing-count`, {
            body: {
                excludeChargeSheetIds: excludeChargeSheetIds,
            },
        });
    }

    /**
     * 收费处空中药房物流信息
     * @param formId
     */
    static getAirPharmacyLogisticsTrace(formId: string, vendorId: string): Promise<PrescriptionFormLogisticsTraceRsp | undefined> {
        if (!formId) return Promise.resolve(undefined);
        return ABCApiNetwork.get(`charge/air-pharmacy/${formId}/logistics-trace`, {
            queryParameters: { vendorId },
            clazz: PrescriptionFormLogisticsTraceRsp,
        });
    }

    /**
     * 计算加工袋数
     * @param params
     */
    static processBagCountCalculate(params: {
        dailyDosage?: string; //x日y剂
        doseCount?: number; //剂数
        freq?: string; //x日y次
        bagUnitCount?: number; //1剂x袋 为空时将通过freq dailyDosage doseCount计算
        pharmacyType?: number;
        type?: number; //药品类型（1-饮片，2--颗粒）
        usageLevel?: string; //每次x袋
    }): Promise<{ bagTotalCount?: number; bagUnitCount?: number }> {
        return ABCApiNetwork.post(`charge/rule/process/bag-count/calculate`, {
            body: params,
            clearUndefined: true,
        });
    }

    /**
     * 根据patientOrderId查询收费单列表
     * @param patientOrderId
     */
    static async queryChargeSheetsByPatientOrderId(patientOrderId: string): Promise<ChargeInvoiceData[]> {
        const rsp: { rows: ChargeInvoiceData[] } = await ABCApiNetwork.get(`charge/patientorders/${patientOrderId}/charge-sheets`);
        return rsp?.rows ?? [];
    }

    /**
     * 收费详情-修改支付方式
     */
    static putChargePayMode(options: {
        chargeId: string;
        chargeComment: string;
        newPayModes: NewPayModes[];
    }): Promise<ChargePaymentMethodRsp> {
        const { chargeId, ...others } = options;
        return ABCApiNetwork.put(`charge/${chargeId}/changepaymode`, {
            body: others,
            clazz: ChargePaymentMethodRsp,
        });
    }

    /**
     * 收费处批次解锁
     * @param goodsLockingTaskId
     */
    static async putChargeUnlockGoods(goodsLockingTaskId: string): Promise<{ chargeSheetId?: string; isDraft?: number; taskId?: string }> {
        return ABCApiNetwork.put(`charge/goodslocking/unlock/${goodsLockingTaskId}`);
    }

    /**
     * 已关闭收费单重新收费
     * @param chargeSheetId
     */
    static async putChargeClosedBillOpen(chargeSheetId: string): Promise<ChargeInvoiceDetailData> {
        return ABCApiNetwork.put(`charge/${chargeSheetId}/open`, { clazz: ChargeInvoiceDetailData });
    }

    /**
     * 已退费收费单重新收费
     * @param chargeSheetId
     */
    static async putChargeRefundBillRenew(chargeSheetId: string): Promise<ChargeInvoiceDetailData> {
        return ABCApiNetwork.put(`charge/${chargeSheetId}/renew`, { clazz: ChargeInvoiceDetailData });
    }

    /**
     * 根据chargePayTransactionId锁续期(医保需要，app不用)
     * @param chargePayTransactionId
     */
    static async putChargeLockRenew(chargePayTransactionId: string): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.put(`charge/${chargePayTransactionId}/lock-renew`);
    }

    /**
     * 根据chargePayTransactionId解锁
     * @param chargePayTransactionId
     */
    static async putChargeUnlock(chargePayTransactionId: string): Promise<{ code?: number; message?: string; dataSignature?: string }> {
        return ABCApiNetwork.put(`charge/${chargePayTransactionId}/cancel-by-charge-pay-transaction-id`);
    }

    /**
     * 查询收费单的结算异常列表
     * @param chargeSheetId
     */
    static async getChargeSettlementExceptionList(chargeSheetId: string): Promise<ChargeSettlementExceptionItem[]> {
        const rsp: { rows: ChargeSettlementExceptionItem[] } = await ABCApiNetwork.get(
            `charge/charge-pay-transaction/list-error-pay-transactions-by-charge-sheet-id/${chargeSheetId}`
        );
        return rsp?.rows?.map((item) => JsonMapper.deserialize(ChargeSettlementExceptionItem, { ...item })) ?? [];
    }
    /**
     * 退费审核人列表
     * @returns
     */
    static async getChargeRefundAuditorList(): Promise<RefundAuditorItem[]> {
        const rsp: { rows: RefundAuditorItem[] } = await ABCApiNetwork.get(`charge/config/refund-check-employees`);
        return rsp?.rows?.map((item) => JsonMapper.deserialize(RefundAuditorItem, { ...item })) ?? [];
    }
    /**
     *  退费时查询收费单发药/执行等详情数据信息
     * @param options
     * @param options.chargeSheetId
     * @param options.dispensingQueryCheck (0:不查询关于发药/执行等详情数据 , 1:会查询发药执行等详情数据,但是不会去做校验   2:会查询发药执行等详情数据，也会做校验)
     * @returns
     */
    static async queryChargeRefundDetail(options: {
        chargeSheetId: string;
        dispensingQueryCheck: number;
    }): Promise<ChargeInvoiceDetailData> {
        const { chargeSheetId, dispensingQueryCheck } = options;
        return ABCApiNetwork.get(`charge/${chargeSheetId}/refund-detail`, {
            clazz: ChargeInvoiceDetailData,
            queryParameters: { dispensingQueryCheck },
        });
    }
    /**
     * 查询门诊、收费议价（整单、单项）议价规则(批量根据业务键查询产品类型匹配列表)
     * @param params
     * @returns
     */
    static async queryChargeBargainingRules(params: { businessKeys: string[] }): Promise<ChargeBargainingRule[]> {
        const rsp: { rows: ChargeBargainingRule[] } = await ABCApiNetwork.post("charge/product-type-match/business-keys", {
            body: params,
        });
        return rsp?.rows?.map((item) => JsonMapper.deserialize(ChargeBargainingRule, { ...item })) ?? [];
    }

    /**
     * 退费前校验
     * @param id
     * @returns
     */
    static async checkChargeRefundPreCheck(id: string, params: ChargeRefundPreCheckReq): Promise<ChargeRefundPreCheckRsp> {
        return ABCApiNetwork.post(`charge/${id}/refund/pre-check`, {
            body: params,
            clazz: ChargeRefundPreCheckRsp,
        });
    }
}
