/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */
import {
    DecoctionProductInfo,
    DeliveryCompany,
    DeliveryInfo,
    DeliveryPayType,
    diagnosisFromJson,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    IngredientProductInfo,
    MedicalRecord,
    MemberInfo,
    Patient,
    StockInfo,
    TraceableCodeList,
    UsageInfo,
} from "../../base-business/data/beans";
import _ from "lodash";
import { ABCUtils } from "../../base-ui/utils/utils";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Const } from "../../base-ui/utils/consts";
import { UUIDGen } from "../../common-base-module/utils";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { DataBase } from "../../views/list-data-holder";
import { AirPharmacyVendor, MedicineScopeId, PharmacyType, UsageScopeId } from "./charge-bean-air-pharmacy";
import { DiseasesCode } from "../../base-business/mix-agent/data/shebao-bean";
import { AnyType } from "../../common-base-module/common-types";
import { WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { PatientOrderLockDetail, PatientOrderLockType } from "../../base-business/data/patient-order/patient-order-bean";
import { PharmacyListConfig } from "../../inventory/data/inventory-bean";
import { ChargeItemModifyUtils } from "../utils/charge-item-modify-utils";
import { Department, Doctor } from "../../registration/data/registration";
import abcI18Next from "../../language/config";
import { ToothNos } from "../../data/tooth-bean";
import { userCenter } from "../../user-center";

export enum ExecuteInfusionSkinTestStatus {
    needSkin = 1, // 皮试
    continuedUse = 2, // 续用
    noSkin = 3, // 免试
}

export enum ChargeFormItemComposeType {
    compose = 1, //复合
    nonCompose = 2, //非复合
}

export class ChargeSheetCheckStatus {
    static waitingPatientPay = 0; //等待用户支付
    static waitingPatientFillInfo = 1; //需要用户补充信息，如快递费
    static waitingPatientInfoReview = 2; //诊所审核快递信息，也叫等待划价
}

// 收费来源form类型（1: 挂号，2：检查，3：治疗，4：成药处方，5：输液处方，6：中药处方，7：附加收费项目）
export enum ChargeSourceFormType {
    registration = 1, // 挂号
    examination = 2, // 检查检验
    treatment = 3, // 治疗理疗
    westernPrescription = 4, // 成药处方
    infusionPrescription = 5, // 输液处方
    chinesePrescription = 6, // 中药处方
    additional = 7, // 附加收费项目
    goods = 8, // 商品
    material = 9, // 医疗器械
    gift = 10, // 赠品
    package = 11, // 套餐
    consultation = 12, //咨询
    delivery = 13, // 快递
    decoction = 14, // 代煎
    airPharmacy = 15, // 空中药房处方
    externalPrescription = 16, // 外治处方
    familyDoctor = 17, // 家庭医生
    promotionCardOpen = 18, // 营销卡项开卡
    promotionCardRecharge = 19, // 营销卡项充值
    memberCardRecharge = 20, // 营销卡项充值
    otherFee = 21, // 其他费用
    glasses = 22, // 眼镜
    nurseProductFee = 23, // 护理项目费用
    surgeryFee = 26, // 手术费用

    airPharmacyAndVirtual = 929, // 前端获取虚拟药房和空中药房
}

export enum DispensingFormItemsSourceItemType {
    default = 0, // 默认
    noCharge = 1, // 门诊标记自备，不参与划价
}

export enum ChargeInvoiceSource {
    registration = 1, // 预约挂号再门诊
    outpatient = 2, // 门诊开单
    retail = 3, // 零售(直接收费）
    memberRecharge = 5, // 会员充值
    therapy = 6, // 执行站开单
    onlineOutpatient = 7, // 网诊
    clonePrescription = 8, // 克隆处方(续方)
    familyDoctorSign = 9, // 家庭医生标志 (签约)
    examinationInspection = 12, // 检查检验(检查站开单)
}

export enum ChargePromotionUseType {
    canUsed = 1, // 可使用
    cannotUsed = 0, // 不可使用
}
export enum RefundRestrictionStatus {
    drugs, //发药
    audit, //审核
    deployment, //调配
}
export enum AuditOrCompoundStatus {
    unaudited, //未审核
    audited, //已审核
}
export enum CalculateLockFlag {
    unlock = 0, // 其他算费页面
    lock = 1, //收费页面需要锁库
}

export class PayMethod {
    static payCash = 1; //现金
    static payWechatPay = 2; //微信
    static payAlipay = 3; //支付宝
    static payBankCard = 4; //银行卡
    static payHealthCard = 5; // 医保卡
    static payMemberCard = 6; //会员卡
    static payCoupon = 7; //代金券
    static payMeituanPay = 8; //美团
    static payKoubiePay = 9; //口碑
    static payNuomiPay = 10; //糯米
    static payShouqianbaPay = 11; //收钱吧
    static payDianpingPay = 12; //大众
    static payFubeiPay = 13; //付呗
    static payLakalaPay = 14; //拉卡拉
    static payLianlianPay = 15; //联联
    static payYouzanPay = 16; // 有赞零售
    static payThirdCardPay = 18; // 第三方支付方式（卡项）基础卡
    static payABCPay = 19; // ABC支付（聚合支付）
    static payOweWay = 20; //欠费支付方式
    static payAIRPay = 21; //空中药房支付
    static payDepositPay = 22; //押金支付
    static payProxyPay = 23; // 代支支付
    static outpatientCenterPay = 24; //沈阳诊间支付
}

export class PaySubModeMethod {
    static healthInsurancePayHealthCard = 101; //  医保移动支付医保部分
    static healthInsurancePayWechatPay = 120; //    医保移动支付微信部分
}

//来源端
export class SourceClientType {
    //0: PCWeb；1: 微诊所；2：移动端
    static mobile = 2;
}

function convertToComposeChildren(json: any) {
    let children: ChargeFormItem[] = [];
    if (json) children = json.map((item: any) => JsonMapper.deserialize(ChargeFormItem, item));

    return children;
}

export enum ComposeType {
    notPackage,
    PackageMain,
    PackageSub,
}

export enum RetailType {
    normal = 1, //正常开方
    zhuanLu, //转录，需要添加医生，诊断等
}

//外治处方里穴位项
interface ChargeFormItemAupointItem {
    id: string;
    name: string;
    position: string;
    type: number;
}

//外治处方现配贴项
class ExternalGoodsItem {
    medicineCadn?: string;
    name?: string;
    manufacturer?: string;
    type?: number;
    subType?: number;
    unit?: string;
    useDismounting?: number;
    keyId?: string;
    goodsId?: string;
    unitCount?: number;
    sort?: number;
    cMSpec?: "中药饮片";

    get isChineseMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinese == this.subType;
    }

    get displayName(): string {
        if (this.isChineseMedicine) {
            if (this.medicineCadn) return this.medicineCadn;

            if (this.name) return this.name;
            return "";
        }

        let fullName = "";
        if (this.medicineCadn) {
            fullName = this.medicineCadn;
        }

        if (this.name) {
            if (fullName) {
                fullName += `(${this.name})`;
            } else fullName = this.name;
        }

        return fullName;
    }
}
class batchPromotionInfo {
    discountPromotionInfos?: PromotionProductItem[];
    giftRulePromotionInfos?: PromotionProductItem[];
    isMarkedByHealthCardPay?: number;
}
class ChargeFormItemBatchInfos {
    associateItemBatchInfoId?: string;
    batchId?: string;
    batchNo?: string;
    canRefundCount?: number;
    chainId?: string;
    chargeFormId?: string;
    chargeFormItemId?: string;
    chargeSheetId?: string;
    clinicId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdBy?: string;
    dispensedRefundUnitCount?: number;
    dispensedUnitCount?: number;
    expectedTotalPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date;
    id?: string;
    isDeleted?: number;
    isNotCharged?: number;
    isOld?: number;
    isUseLimitPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date;
    lastModifiedBy?: string;
    limitInfo?: string;
    patientOrderId?: string;
    productId?: string;
    @JsonProperty({ type: batchPromotionInfo })
    promotionInfo?: batchPromotionInfo;
    receivablePrice?: number;
    refundTotalPrice?: number;
    refundUnitCount?: number;
    sourceTotalPrice?: number;
    sourceUnitPrice?: number;
    stockId?: string;
    totalCostPrice?: number;
    totalPrice?: number;
    traceableCodes?: string;
    unitCostPrice?: number;
    unitCount?: number;
    unitPrice?: number;
}
class ChargeFormItemDoctorInfo {
    departmentId?: string;
    doctorId?: string;
    registrationCategory?: number; // 号种类型
}
export class ChargeFormItem {
    id?: string;
    keyId?: string;
    clinicId?: string;
    chainId?: string;
    patientOrderId?: string;
    chargeSheetId?: string;
    chargeFormId?: string;
    sourceFormItemId?: string;
    status?: ChargeFormItemStatus;
    unit?: string;
    name?: string;
    unitCostPrice?: number;
    eqCoefficient?: number | undefined;
    eqUnitCount?: number | undefined;
    unitCount?: number;
    //终端自定义字段，用来存储套餐执行次数
    @JsonProperty({ name: "unitCount" })
    __unitCount?: number;
    doseCount?: number;
    unitPrice?: number;
    discountPrice?: number;
    totalPrice?: number;
    discountedTotalPrice?: number;
    useDismounting?: number;
    productType?: number;
    productSubType?: number;
    productId?: string;
    groupId?: number;
    sort?: number;
    paySource?: number;
    outpatientRemark?: string; //门诊备注

    //处方议价修改-期望doseCount
    expectedDoseCount?: number;
    @JsonProperty({ type: ChargeFormItemDoctorInfo })
    doctorInfo?: ChargeFormItemDoctorInfo;

    //  ChargeRegistrationInfo|GoodsInfo
    @JsonProperty({ fromJson: toProductionInfo })
    productInfo?: GoodsInfo | ChargeRegistrationInfo | DeliveryInfo | DecoctionProductInfo | IngredientProductInfo; //在收费单里可能出现不同类型，特殊解析
    usageInfo?: UsageInfo;
    promotionInfo?: PromotionInfo;
    stockPieceCount?: number;
    stockPackageCount?: number;
    canRefundUnitCount?: number;
    canRefundDoseCount?: number;

    sourceItemType?: DispensingFormItemsSourceItemType;
    /**
     * 后台返回值，不改变
     */
    @JsonProperty({ name: "canRefundDoseCount" })
    __allCanRefundDoseCount?: number;

    examinationResult?: ExaminationResult;
    executedUnitCount?: number;
    executedActionCount?: number;
    executeStatus?: number;
    executeStatusName?: string;
    needExecutive?: number; // 0：不可执行 1：可执行
    ast?: number | null;
    astResult?: AstResult;

    sourceUnitPrice?: number;
    sourceTotalPrice?: number;

    //在单项议价时用于后台算费（这三者之间互斥，只要其中一个有值，另外两个要清空）
    expectedUnitPrice?: number;
    expectedTotalPrice?: number;
    expectedTotalPriceRatio?: number; //单项折扣（后台接受0-1之间的数，页面显示1-100之间的数）
    totalPriceRatio?: number; //折扣-页面用显示
    unitAdjustmentFee?: number; //单项议价
    unitAdjustmentFeeLastModifiedBy?: string; //议价修改人id(当前登录人id)
    unitAdjustmentFeeLastModifiedByName?: string;

    canAdjustment?: number; // 是否可以议价 0：不可议价 1：可议价
    get isCanAdjustment(): boolean {
        return this.canAdjustment == 1;
    }

    lastExecutedByName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastExecutedDate?: Date;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    packageUnit?: string;

    composeType?: ChargeFormItemComposeType;

    @JsonProperty({ fromJson: convertToComposeChildren })
    composeChildren?: ChargeFormItem[];

    ///商品信息
    medicineCadn?: string;
    manufacturer?: string;
    dismounting?: number;

    packagePrice?: number;
    packageCostPrice?: number;
    piecePrice?: number;
    pieceNum?: number;
    pieceUnit?: string;

    medicineDosageNum?: number;
    medicineDosageUnit?: string;
    medicineNmpn?: string;
    materialSpec?: string;
    cMSpec?: string;
    grade?: string;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];
    shebaoDismountingFlag?: number;

    //
    isAirPharmacy?: number;

    //外治处方时添加
    acupoints?: ChargeFormItemAupointItem[];
    /**
     * 计算穴位数量
     */
    get acuPointsCount(): number {
        let count = 0;
        this.acupoints?.forEach((it) => {
            if (it.name) {
                if (it.position === "双") {
                    count += 2;
                } else {
                    count++;
                }
            }
        });
        return count || 1;
    }
    specialRequirement?: string;
    @JsonProperty({ type: Array, clazz: ExternalGoodsItem })
    externalGoodsItems?: ExternalGoodsItem[]; //现配贴药品相关

    //可退的抵扣数量
    canRefundDeductCount?: number;
    //总共抵扣的数量
    deductedTotalCount?: number;
    //总共抵扣的数量 - 回传
    deductedToTalCount?: number;
    // 退的时候，卡项抵扣的数量
    deductTotalCount?: number;
    get isHasDeductItem(): boolean {
        return (this.deductedTotalCount ?? 0) > 0;
    } //用于判断是否是卡项抵扣
    isShowDeductTag?: boolean; // 是否显示卡项抵扣标识

    //【feature】诊疗项目支持医生护士添加
    nurseId?: string;
    nurseName?: string;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;

    pharmacyNo?: number;
    pharmacyType?: PharmacyType;
    pharmacyName?: string;

    //诊疗项目的备注
    remark?: string;
    // 目前这个字段，只用于套餐中composeChildren，这个字段唯一，因为goodsId有可能不唯一了，做透传
    productPrimaryId?: string;

    toothNos?: ToothNos;
    // 是否可以被删除（0--可以，1--不可以）----检验检查执行了 isCanNotDelete 就是1
    isCanNotDelete?: number;
    @JsonProperty({ type: Array, clazz: ChargeFormItemBatchInfos })
    chargeFormItemBatchInfos?: ChargeFormItemBatchInfos[];
    isFixedData?: CalculateLockFlag; //是否需要锁库（1--需要（为1时，chargeFormItemBatchInfos必传），0---不需要）

    isMarkedByHealthCardPay?: number; // 医保结算项目
    itemRefundFlag?: number; // 医保退费项目

    get itemIsExcuted(): boolean {
        return !!this.isCanNotDelete;
    }

    //议价折扣进行百分比换算
    get totalPriceRatioPercent(): number {
        return Number(((this.expectedTotalPriceRatio ?? this.totalPriceRatio ?? 0) * 100).toFixed(0));
    }

    //单项议总价
    get _displayTotalPrice(): number {
        return this.expectedTotalPrice ?? this.totalPrice ?? (this.unitPrice ?? 0) * (this.unitCount ?? 0);
    }

    // 外治处方总执行次数计算方式
    get externalPrescriptionUnitCount(): number {
        const perCount = (this.unitCount ?? 1) / (Number(this.usageInfo?.dosage) ?? 1) / (this.usageInfo?.externalUnitCount ?? 1); // 每次贴数
        return perCount * (Number(this.usageInfo?.dosage) ?? 1); // 总执行次数
    }

    //设置新的单位，并更新单价和拆零标志
    setUnit(unit: string): void {
        const info = this.productInfo as GoodsInfo;
        this.unit = unit;

        if (this.isExamination || this.isExaminationTest) return;

        if (unit == info.packageUnit) {
            this.unitPrice = info.packagePrice;
            this.useDismounting = 0;
        } else {
            this.unitPrice = info.piecePrice;
            this.useDismounting = 1;
        }
    }

    compareKey(): string {
        if (!_.isEmpty(this.keyId)) return this.keyId!;

        if (!_.isEmpty(this.id)) return this.id!;

        return "";
    }

    // 皮试能否执行
    get astCanExecuted(): boolean {
        return !this.astResult && this.ast == ExecuteInfusionSkinTestStatus.needSkin;
    }

    // 判断药品是可否执行 排除已执行
    get drugsCanExecutive(): boolean {
        return (this.needExecutive ?? 0) > 0 && (this.executeStatus ?? 1) > 0 && (this.executeStatus ?? 0) != 2;
    }

    // 药品用法是否可执行
    get drugsUsageExecutive(): boolean {
        let westernUsageCanExecutive = false;
        WesternMedicine.usage
            .filter((usg) => usg.type == 2 || usg.name == "外用")
            .forEach((item) => {
                if (item.name == this.usageInfo?.usage) {
                    westernUsageCanExecutive = true;
                }
            });
        return westernUsageCanExecutive;
    }

    // 药品是否有未执行次数
    get drugsExecutiveCount(): boolean {
        return (this.executedUnitCount ?? 0) < (this.usageInfo?.executedTotalCount ?? 0);
    }

    // 药品是否已被执行
    get drugsHasBeenExecuted(): boolean {
        let hasExecutedItem;
        if (this.isPackage) {
            hasExecutedItem = !!this.composeChildren
                ?.filter(ChargeItemModifyUtils._isNeedCheckForModify)
                ?.some((subItem) => subItem && (subItem.executedUnitCount ?? 0) > 0);
        } else {
            if ((this.executedUnitCount ?? 0) > 0 && this.drugsCanExecutive) {
                hasExecutedItem = true;
            }
        }
        return !!hasExecutedItem;
    }

    sourceFormType__?: number;

    //清除议价标记
    clearAdjustmentFee(): void {
        this.expectedUnitPrice = undefined;
        this.expectedTotalPrice = undefined;
    }

    // 是自备药品吗
    get isSelfProvided(): boolean {
        return this.sourceItemType == DispensingFormItemsSourceItemType.noCharge;
    }

    //判断当前项是否有议价桥标记
    hasAdjustmentFee(): boolean {
        return this.expectedUnitPrice != undefined || this.expectedTotalPrice != undefined;
    }

    get isFamilyDoctorSign(): boolean {
        return this.productType == GoodsType.familyDoctorFee;
    }

    get isCompose(): boolean {
        return this.composeType == ChargeFormItemComposeType.compose;
    }

    get isWesternMedicine(): boolean {
        return this.productType == GoodsType.medicine && GoodsSubType.medicineWestern == this.productSubType;
    }

    get isChineseWesternMedicine(): boolean {
        return this.productType == GoodsType.medicine && GoodsSubType.medicineChinesePatent == this.productSubType;
    }

    get isChineseMedicine(): boolean {
        return this.productType == GoodsType.medicine && GoodsSubType.medicineChinese == this.productSubType;
    }

    get isTreatment(): boolean {
        return this.productType == GoodsType.treatment && GoodsSubType.treatment == this.productSubType;
    }

    get isPhysiotherapy(): boolean {
        return this.productType == GoodsType.treatment && GoodsSubType.treatmentPhysiotherapy == this.productSubType;
    }

    get isExamination(): boolean {
        return this.productType == GoodsType.examination && GoodsSubType.examination == this.productSubType;
    }

    get isExaminationTest(): boolean {
        return this.productType == GoodsType.examination && GoodsSubType.examinationTest == this.productSubType;
    }

    get isMaterialMedical(): boolean {
        return this.productType == GoodsType.material && GoodsSubType.materialMedical == this.productSubType;
    }

    get isGoods(): boolean {
        return this.productType == GoodsType.goods;
    }

    get isRegistration(): boolean {
        return this.productType == GoodsType.registration;
    }

    get isPackage(): boolean {
        // return this.productType == GoodsType.package && GoodsSubType.packageTreatment == this.productSubType;
        return this.productType == GoodsType.package;
    }

    get isOtherFee(): boolean {
        return this.productType == GoodsType.otherGoods49;
    }

    get isDelivery(): boolean {
        return this.productType == GoodsType.deliveryFee;
    }

    get isDecoction(): boolean {
        return this.productType == GoodsType.decoctionFee;
    }

    get isIngredient(): boolean {
        return this.productType == GoodsType.ingredient;
    }

    get isNurseProductFee(): boolean {
        return this.productType == GoodsType.nurseProduct;
    }

    /**
     * 当前药品是否支持修改商品来源
     */
    get isCanSpecifyPharmacy(): boolean {
        return !(
            this.isExaminationTest ||
            this.isTreatment ||
            this.isPhysiotherapy ||
            this.isExamination ||
            this.isRegistration ||
            this.isDelivery ||
            this.isDecoction ||
            this.isIngredient ||
            this.isPackage ||
            this.isNurseProductFee ||
            this.isOtherFee
        );
    }

    get priceSpec(): string {
        return `${abcI18Next.t("¥")}${ABCUtils.formatPrice(this.sourceUnitPrice ?? 0)}/${this.displayUnit}`;
    }

    get displayUnit(): string {
        if (!_.isEmpty(this.unit)) return this.unit!;
        return Const.defaultUnit;
    }

    displayUsageInfo(noIvgtt = false, separator = "，", noDosage = false): string {
        if (this.usageInfo == null) return "";

        const usageItems: string[] = [];
        if (this.usageInfo.usage != null) usageItems.push(this.usageInfo.usage);
        if (this.usageInfo.freq != null) usageItems.push(this.usageInfo.freq);
        if (!noDosage && this.usageInfo.dosage != null && this.usageInfo.dosageUnit != null)
            usageItems.push(`${this.usageInfo.dosage}${this.usageInfo.dosageUnit}`);
        if (this.usageInfo.days != null) usageItems.push(`${this.usageInfo.days}天`);
        if (!noIvgtt && this.usageInfo.ivgtt != null && !_.isEmpty(this.usageInfo.ivgttUnit))
            usageItems.push(`${this.usageInfo.ivgtt?.toString()}${this.usageInfo.ivgttUnit}`);

        return usageItems.join(separator);
    }

    /// 以下字段为本地字段，非后台返回协议字段
    //本地添加项，还没有提交到服务端
    localAdd?: boolean;
    registrationLocalModified?: boolean; //本地修改了挂号信息，在门诊收费时，修改了挂号的医生
    checked = true; //本地值，用于标志当前项是否选中

    get displayName(): string {
        if (this.productType == GoodsType.registration) return "挂号费";
        if (this.productType == GoodsType.decoctionFee) return this.name ?? "煎药费";
        if (this.productType == GoodsType.deliveryFee) return "快递费";
        if (this.productType == GoodsType.familyDoctorFee) return this.name ?? "";

        return (!!this.goodsInfo?.displayName ? this.goodsInfo?.displayName : this.name) ?? "";
    }

    @JsonProperty({ type: GoodsInfo })
    _computeGoodsInfo?: GoodsInfo;

    get goodsInfo(): GoodsInfo {
        let goods: GoodsInfo | null;
        do {
            if (this.productInfo && this.productInfo instanceof GoodsInfo) {
                goods = Object.assign(this.productInfo, {
                    stockPackageCount: this.stockPackageCount ?? this.productInfo.stockPackageCount,
                    stockPieceCount: this.stockPieceCount ?? this.productInfo.stockPieceCount,
                });
                break;
            }

            if (this._computeGoodsInfo) {
                goods = this._computeGoodsInfo;
                break;
            }

            this._computeGoodsInfo = new GoodsInfo();
            this._computeGoodsInfo.id = this.productId;
            this._computeGoodsInfo.name = this.name;
            this._computeGoodsInfo.medicineCadn = this.medicineCadn;
            this._computeGoodsInfo.type = this.productType;
            this._computeGoodsInfo.subType = this.productSubType;
            this._computeGoodsInfo.manufacturer = this.manufacturer;
            this._computeGoodsInfo.dismounting = this.dismounting;
            this._computeGoodsInfo.packagePrice = this.packagePrice;
            this._computeGoodsInfo.packageCostPrice = this.packageCostPrice;
            this._computeGoodsInfo.pieceNum = this.pieceNum;
            this._computeGoodsInfo.pieceUnit = this.pieceUnit;
            this._computeGoodsInfo.packageUnit = this.packageUnit;
            this._computeGoodsInfo.medicineDosageNum = this.medicineDosageNum;
            this._computeGoodsInfo.medicineDosageUnit = this.medicineDosageUnit;
            this._computeGoodsInfo.medicineNmpn = this.medicineNmpn;
            this._computeGoodsInfo.materialSpec = this.materialSpec;
            this._computeGoodsInfo.cMSpec = this.cMSpec;
            this._computeGoodsInfo.grade = this.grade;
            this._computeGoodsInfo.stockPackageCount = this.stockPackageCount;
            this._computeGoodsInfo.stockPieceCount = this.stockPieceCount;
            this._computeGoodsInfo.pharmacyName = this.pharmacyName;

            goods = this._computeGoodsInfo;
        } while (0);

        if (goods!.type == undefined) {
            goods!.type = this.productType;
            goods!.subType = this.productSubType;
        }

        return goods!;
    }

    stockInfo(pharmacyType?: PharmacyType): StockInfo {
        if (this.isRegistration) {
            return new StockInfo(true);
        }

        if (pharmacyType == PharmacyType.virtual) {
            return new StockInfo(true);
        }

        if (
            this.isDelivery ||
            this.isDecoction ||
            this.isTreatment ||
            this.isExamination ||
            this.isExaminationTest ||
            this.isPhysiotherapy ||
            this.isNurseProductFee ||
            this.isSelfProvided
        )
            return new StockInfo(true);

        let doseCount = this.doseCount;
        if (doseCount == undefined || isNaN(doseCount)) doseCount = 0;
        return this.goodsInfo.stockInfo(this.unit, this.unitCount, doseCount, undefined, undefined, undefined, pharmacyType);
    }

    get canRefund(): boolean {
        // !!this.isShowDeductTag && this.canRefundDeductCount! > 0 卡项抵扣的可退次数大于0时，也需要显示
        return (
            (this.status == ChargeFormItemStatus.charged || this.status == ChargeFormItemStatus.partRefund) &&
            ((!this.isShowDeductTag && this.canRefundUnitCount! > 0 && this.canRefundDoseCount! > 0) ||
                (!!this.isShowDeductTag && this.canRefundDeductCount! > 0 && this.canRefundDoseCount! > 0)) &&
            !this.isSelfProvided
        );
    }

    //@override
    //bool operator ==(Object other) =>
    //other is ChargeFormItem && ((isNotEmpty(keyId) && isNotEmpty(other.keyId) && keyId == other.keyId) || (isNotEmpty(id) && isNotEmpty(other.id) && id == other.id));

    //@override
    //int get hashCode {
    //    if (isNotEmpty(keyId)) return keyId.hashCode;
    //
    //    if (isNotEmpty(id)) return id.hashCode;
    //
    //    return super.hashCode;
    //}
    /**
     * 根据药房号重置库存
     */
    resetStockByPharmacyNo(): void {
        if (!this.goodsInfo) return;
        const res = this.goodsInfo?.pharmacyGoodsStockList?.find((item) => item?.pharmacyNo == this.pharmacyNo);
        const { stockPackageCount = 0, stockPieceCount = 0, pharmacyName = "", pharmacyNo } = res || {};
        this.stockPackageCount = stockPackageCount;
        this.stockPieceCount = stockPieceCount;
        this.goodsInfo.stockPackageCount = stockPackageCount;
        this.goodsInfo.stockPieceCount = stockPieceCount;
        this.goodsInfo.pharmacyNo = pharmacyNo;
        this.goodsInfo.pharmacyName = pharmacyName;
    }
}

export interface ChargeFormMedicalRecord {
    id?: string;
    chiefComplaint?: string;
    diagnosis?: string;
    doctorId?: string;
    doctorName?: string;

    comment?: string; //订单备注
}

export class ChargeForm {
    id?: string;
    keyId?: string;

    patientOrderId?: string;
    clinicId?: string;
    chainId?: string;
    chargeSheetId?: string;
    sourceFormId?: string;
    sourceFormType?: ChargeSourceFormType;
    status?: number;
    sort?: number;

    @JsonProperty({ type: Array, clazz: ChargeFormItem })
    chargeFormItems?: ChargeFormItem[];
    usageInfo?: UsageInfo;
    totalPrice?: number;
    sourceTotalPrice?: number;
    totalDiscountPrice?: number;

    //空中药房药品价格
    medicineTotalPrice?: number;
    sourceMedicineTotalPrice?: number;

    //v1.4空中药房添加
    specification?: string; //中药饮片,中药颗粒
    usageScopeId?: UsageScopeId;
    usageScopeName__?: string;
    medicineStateScopeId?: MedicineScopeId;
    medicineStateScopeName?: string;
    vendorId?: string;
    vendorName?: string;
    @JsonProperty({ type: DeliveryInfo })
    deliveryInfo?: DeliveryInfo;
    processRule?: ProcessRule;

    isCanBeRefund?: number; //是否可以退

    medicalRecord?: ChargeFormMedicalRecord;

    expectedTotalPrice?: number;
    expectedPriceFlag?: number;

    //剂数
    doseCount__?: number;

    @JsonProperty({ type: AirPharmacyVendor })
    vendor__?: AirPharmacyVendor;

    //加工信息
    processInfo?: ProcessInfo;

    /**
     * 空中药房是否需要支付
     */
    isAirPharmacyNeedPaid?: number;
    /**
     * 空中药房订单号，用于调起支付
     */
    airPharmacyOrderId?: string;

    /**
     * 虚拟药房
     */
    pharmacyNo?: number;
    pharmacyType?: PharmacyType;
    pharmacyName?: string;

    isCanRefundDoseCount?: number; //中药处方的时候才生效，如果等于1，就表示可以按剂退
    isCanRefundSingle?: number; //是否可以退单个,1：可以，0：不可以
    leftDoseCount?: number; //剩余的剂量
    refundDoseCount?: number; //已退的剂量

    auditOrCompoundStatus?: AuditOrCompoundStatus; //审核或调配状态,开启了审核或调配配置开关才有值，审核调配互斥, 0=未审核/调配 1=已审核/调配
    // 已审核/调配，（若设置为审核/调配后不可退费，退费页面处方不可选择）
    get hasFinished(): boolean {
        return !!this.auditOrCompoundStatus;
    }

    displayUsageInfo(separator = "，"): string {
        if (this.usageInfo == null) return "";

        if (this.isChinesePrescription) {
            const usageItems: string[] = [];
            if (this.usageInfo.freq != null) usageItems.push(this.usageInfo.freq);
            if (this.usageInfo.dailyDosage != null) usageItems.push(this.usageInfo.dailyDosage);
            if (this.usageInfo.usage != null) usageItems.push(this.usageInfo.usage);
            if (this.usageInfo.usageLevel != null) usageItems.push(`${this.usageInfo.usageLevel}`);

            return usageItems.join(separator);
        }

        return "";
    }

    getDeliveryInfo(): DeliveryInfo | undefined {
        return this.deliveryInfo ?? (this.getFormItem(GoodsType.deliveryFee)?.productInfo as DeliveryInfo) ?? this.deliveryInfo;
    }

    get totalRealPayPrice(): number {
        return (this.totalPrice ?? 0.0) + (this.totalDiscountPrice ?? 0.0);
    }

    get isRegistration(): boolean {
        return this.sourceFormType == ChargeSourceFormType.registration;
    }

    get isDecoction(): boolean {
        return this.sourceFormType == ChargeSourceFormType.decoction;
    }

    get isDelivery(): boolean {
        return this.sourceFormType == ChargeSourceFormType.delivery;
    }

    get isConsultation(): boolean {
        return this.sourceFormType == ChargeSourceFormType.consultation;
    }

    get isTreatment(): boolean {
        return this.sourceFormType == ChargeSourceFormType.treatment;
    }

    get isExamination(): boolean {
        return this.sourceFormType == ChargeSourceFormType.examination;
    }

    get isWesternPrescription(): boolean {
        return this.sourceFormType == ChargeSourceFormType.westernPrescription;
    }

    get isInfusionPrescription(): boolean {
        return this.sourceFormType == ChargeSourceFormType.infusionPrescription;
    }

    get isChinesePrescription(): boolean {
        return this.sourceFormType == ChargeSourceFormType.chinesePrescription && this.pharmacyType != PharmacyType.virtual;
    }

    get isAdditional(): boolean {
        return this.sourceFormType == ChargeSourceFormType.additional;
    }

    get isGoods(): boolean {
        return this.sourceFormType == ChargeSourceFormType.goods;
    }

    // 医疗器械
    get isMaterial(): boolean {
        return this.sourceFormType == ChargeSourceFormType.material;
    }

    //套餐
    get isPackage(): boolean {
        return this.sourceFormType == ChargeSourceFormType.package;
    }

    //其他费用
    get isOthersFee(): boolean {
        return this.sourceFormType == ChargeSourceFormType.otherFee;
    }

    get isGlasses(): boolean {
        return this.sourceFormType == ChargeSourceFormType.glasses;
    }

    get isNurseProductFee(): boolean {
        return this.sourceFormType == ChargeSourceFormType.nurseProductFee;
    }
    get isSurgery(): boolean {
        return this.sourceFormType == ChargeSourceFormType.surgeryFee;
    }

    //空中药房处方
    get isAirPharmacy(): boolean {
        return this.sourceFormType == ChargeSourceFormType.airPharmacy;
    }

    //虚拟药房处方
    get isVirtualPharmacy(): boolean {
        return this.sourceFormType == ChargeSourceFormType.chinesePrescription && this.pharmacyType == PharmacyType.virtual;
    }

    //本地药房
    get isLocalPharmacy(): boolean {
        return this.sourceFormType == ChargeSourceFormType.chinesePrescription && this.pharmacyType == PharmacyType.normal;
    }

    // 外置处方
    get isExternal(): boolean {
        return this.sourceFormType == ChargeSourceFormType.externalPrescription;
    }

    get isFamilyDoctor(): boolean {
        return this.sourceFormType == ChargeSourceFormType.familyDoctor;
    }

    get isMedicine(): boolean {
        return this.isWesternPrescription || this.isChinesePrescription || this.isInfusionPrescription;
    }

    getFormItem(productType: GoodsType): ChargeFormItem | undefined {
        return this.chargeFormItems?.find((item) => item.productType == productType);
    }

    compareKey(): string {
        if (!_.isEmpty(this.keyId)) return this.keyId!;

        if (!_.isEmpty(this.id)) return this.id!;

        return "";
    }

    get doseCount(): number | undefined {
        const item = this.chargeFormItems?.find((item) => !item.isDecoction && !item.isDelivery && !item.isIngredient);
        return item?.doseCount ?? this.doseCount__;
    }

    fixAirPharmacyPrescriptionInfo(): void {
        if (!this.isAirPharmacy && !this.isVirtualPharmacy) return;
        const deliveryItem = this.getFormItem(GoodsType.deliveryFee);
        if (!deliveryItem) {
            const chargeFormItems = (this.chargeFormItems = this.chargeFormItems ?? []);
            const formItem = new ChargeFormItem();
            formItem.name = "快递费";
            formItem.unit = "次";
            formItem.productType = GoodsType.deliveryFee;
            formItem.keyId = UUIDGen.generate();
            formItem.doseCount = 1;
            formItem.unitCount = 1;
            chargeFormItems.push(formItem);
        }

        //自煎，不需要加工费
        if (this.medicineStateScopeId == undefined || this.medicineStateScopeId === MedicineScopeId.zhiJian.toString()) {
            _.remove(this.chargeFormItems ?? [], (item) => item.isDecoction);
            return;
        }

        const decoctionInfo = this.getFormItem(GoodsType.decoctionFee);
        if (!decoctionInfo) {
            const chargeFormItems = (this.chargeFormItems = this.chargeFormItems ?? []);
            const formItem = new ChargeFormItem();
            formItem.name = "加工费";
            formItem.unit = "次";
            formItem.productType = GoodsType.decoctionFee;
            formItem.keyId = UUIDGen.generate();
            formItem.doseCount = 1;
            formItem.unitCount = 1;
            chargeFormItems.push(formItem);
        }
    }

    // 统计当前中药处方中已发的中药数量
    get __ChinesePrescriptionDispensedUnit(): number {
        return this.chargeFormItems?.filter((t) => t.canRefund)?.length ?? 0;
    }

    // 当前中药处方可退的剂数
    get currentCanRefundDoseCount(): number {
        return _.first(this.chargeFormItems?.filter((t) => t.canRefund))?.__allCanRefundDoseCount ?? 0;
    }

    /**
     * 中药批量切换药房
     * @param val
     */
    handleChangePharmacy(val: PharmacyListConfig): void {
        this.pharmacyType = val?.type;
        this.pharmacyNo = val?.no;
        this.pharmacyName = val?.name;
        this.chargeFormItems?.forEach((item) => {
            item.pharmacyNo = val?.no;
            item.pharmacyType = val?.type;
            item.pharmacyName = val?.name ?? item.pharmacyName;
            item.resetStockByPharmacyNo();
        });
    }
}

export class AirPharmacyMedicineState {
    medicalStatId?: MedicineScopeId;
    medicalStatName?: string;
}

export enum CanRefundType {
    notCanRefund = 0, // 不可退
    canRefund = 1, // 可退
}

export class PaymentSummaryInfo {
    payMode?: number;
    amount?: number;
    presentAmount?: number;
    principalAmount?: number;
    thirdPartyPayCardId?: string;
    payModeName?: string;
    paidAmount?: number;
    refundedAmount?: number;
    transactionIds?: string[];
    isCanRefund?: CanRefundType;
    paySubModeName?: string;
    paySubMode?: number;
    chargePayTransactionId?: string;
    payModeDisplayName?: string;
    //如果isCanRefund == 0，disableRefundCode才会有值|disableRefundCode取值：0已退完，10卡项不可用,20，表示会员卡不可退
    disableRefundCode?: number;
    //会员卡不可退
    get memberCardNotRefund(): boolean {
        return this?.disableRefundCode == 20;
    }
    //卡项不可退
    get thirdPartyNotRefund(): boolean {
        return this?.disableRefundCode == 10;
    }
}

export class ChargeSheetSummary {
    totalFee?: number;
    adjustmentFee?: number;
    oddFee?: number; //系统议价（抹零，凑整数）
    discountFee?: number;
    draftAdjustmentFee?: number; //收费处议价
    outpatientAdjustmentFee?: number; //门诊议价
    excludeDraftAndOddFee?: number;
    receivableFee?: number;
    receivedFee?: number;
    refundFee?: number;
    needPayFee?: number;
    owedRefundFee?: number;
    netIncomeFee?: number;
    netIncomeIgnoreOweFee?: number;
    netTotalFee?: number;
    netAdjustmentFee?: number;
    netDiscountFee?: number;
    payModes?: number[];
    payModeViews?: PayModeView[];
    canAdjustment?: number; // 是否可议价
    canAdjustmentFee?: number; // 和原价格相比的最大可议价差值

    afterRoundingDiscountedTotalFee?: number;

    registrationFee?: number;
    westernMedicineFee?: number;
    chineseMedicineFee?: number;
    examinationTreatmentFee?: number;
    materialFee?: number;
    registrationDiscountFee?: number;
    westernMedicineDiscountFee?: number;
    chineseMedicineDiscountFee?: number;
    treatmentDiscountFee?: number;
    examinationDiscountFee?: number;
    materialDiscountFee?: number;
    memberCardBalance?: number;
    memberCardMobile?: string;
    createdByName?: string;
    created?: string;
    lastModifiedByName?: string;
    lastModified?: string;
    chargedByName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    chargedTime?: Date;
    doctorName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    diagnosedDate?: Date;

    @JsonProperty({ type: Array, clazz: PaymentSummaryInfo })
    paymentSummaryInfos?: PaymentSummaryInfo[];

    expectedTotalPrice__?: number; //临时用于保存整单价格

    oweFee?: number; //当前欠费

    sourceTotalFee?: number; //原价
    totalAdjustmentFee?: number; //总议价值
    unitAdjustmentFee?: number; //单项议价的和

    get expectedAdjustmentFee__(): number {
        let expectedAdjustmentFee = this.draftAdjustmentFee ?? 0;
        if (!_.isNil(this.expectedTotalPrice__)) {
            if (_.isNil(this.afterRoundingDiscountedTotalFee)) {
                throw new Error("议价之前须先进行一次算费");
            }

            expectedAdjustmentFee = this.expectedTotalPrice__! - this.afterRoundingDiscountedTotalFee!;
        }

        return parseFloat(expectedAdjustmentFee.toFixed(2));
    }
}

export interface TherapySheet {
    id?: string;
    chainId?: string;
    clinicId?: string;
    chargeSheetId?: string;
    diagnosis?: string; //诊断
    diagnosisInfos?: DiseasesCode[]; //诊断
    doctorId?: string;
    doctorName?: string;

    departmentId?: string;
    departmentName?: string;
    sellerDepartmentId?: string;
    sellerDepartmentName?: string;
}

export enum ChargeInvoiceType {
    outpatient = 2, //门诊
    retail = 3, //纯零售
    therapy = 6, //护士站理疗开单
    onlineConsultation = 7, //咨询
    clonePrescription = 8, //在线续方
    consultationScheme = 15, //咨询方案
}

export enum QueryExceptionType {
    shebaoPay = 1, //社保
    abcPay = 2, //Abc
    wechatPay = 4, //微信
    promotionCardPay = 8, //卡项
    memberCardPay = 16, //会员
}

export class ChargeInvoiceData extends DataBase {
    static outpatientStatusWaiting = 1; //待诊
    static outpatientStatusVisited = 2; //已诊

    id?: string;
    patientOrderId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastExecuteTime?: Date;
    chainId?: string;
    clinicId?: string;
    doctorName?: string;
    status?: number;
    executeStatus?: number; //ChargeExecuteStatus
    outpatientStatus?: number;
    @JsonProperty({ type: Patient })
    patient?: Patient;
    statusName?: string;

    sendToPatientStatus?: number; //1 表示已经推送给用户了
    //1.2版本添加
    isDraft?: number;

    //是否是关闭门诊单
    isClosed?: number;
    //网诊
    isOnline?: number;

    //本地添加字段
    localDraftId?: string;

    type?: ChargeInvoiceType; //用于判断是零售单子或是门诊单子， 零售是3/(纯零售）6(执行站开单, 门诊2， 8:续方

    //理疗预约挂号i
    registrationId__?: string;

    //判断该收费单是否有异常(有社保异常的判断：(queryExceptionType & 1 ) > 0,有非社保异常的判断：(queryExceptionType - 1 ) > 0)
    queryExceptionType?: number;

    //欠费列表
    owedStatus?: number;

    //是否是空中药房订单
    //列表数据使用
    isAirPharmacyOrder?: number;
    sourceFromOpenApi?: number; // 是否为开放平台单据

    nurseAbstractInfo?: string; // 摘要信息

    //是否从欠费列表进入
    get isFromOweList(): boolean {
        return this.owedStatus == 10;
    }

    //社保异常判断
    get isSheBaoAbnormal(): boolean {
        return (this.queryExceptionType ?? 0) == QueryExceptionType.shebaoPay ?? false;
    }

    //非社保异常判断
    get isNotSheBaoAbnormal(): boolean {
        return (this.queryExceptionType ?? 0) > 1 ?? false;
    }

    //挂单的单子
    get isNetworkDraft(): boolean {
        return this.isDraft == 1;
    }

    //本地草稿
    get isLocalDraft(): boolean {
        return !this.isNetworkDraft && !_.isEmpty(this.localDraftId);
    }

    public dataTime(): Date | undefined {
        return this.created;
    }
}

export class PayActionInfo {
    payMode?: number;
    payModeName?: string;
    amount?: number;
    paySubModeName?: string;
    payModeDisplayName?: string;
}

export class ChargeAction {
    id?: string;
    type?: number; //ChargeActionType
    payStatus?: number;
    dispensingStatus?: number;
    amount?: number;

    chargeComment?: string;
    createdByName?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    @JsonProperty({ type: Array, clazz: PayActionInfo })
    payActionInfos?: PayActionInfo[];

    get payActionInfoDisplay(): string {
        return (
            this.payActionInfos
                ?.map(({ payModeName, paySubModeName }) => {
                    let name = payModeName;
                    if (paySubModeName) {
                        name += `-${paySubModeName}`;
                    }
                    return name;
                })
                .join("+") ?? ""
        );
    }

    get actionAmountDisplay(): string {
        return (
            this.payActionInfos
                ?.map(({ amount }) => {
                    return `${abcI18Next.t("￥")}${Math.abs(amount ?? 0).toFixed(2)}`;
                })
                .join("+") ?? ""
        );
    }

    payMethod?: number; //支付方式，前台proxy字段，预埋字段，目前没有下发，如果后观有新的支付方式时再使用
}

export class PromotionInfo {
    ids?: string[];
    adjustmentFee?: number;
    discountPromotionInfos?: PromotionProductItem[];
}

export class PromotionProductItem {
    id?: string;
    name?: string;
    discountPrice?: number;
    type?: number;
}

export class ChargeCalculateMemberInfo {
    memberId?: string;
    mobile?: string;
    memberTypeName?: string;
}

export class Promotion {
    id?: string;
    name?: string;
    checked?: boolean;
    discountPrice?: number;
    type?: number;
    isCannotBeUsed?: number;

    @JsonProperty({ type: Array, clazz: PromotionProductItem })
    productItems?: Array<PromotionProductItem>;

    @JsonProperty({ type: ChargeCalculateMemberInfo })
    memberInfo?: ChargeCalculateMemberInfo;
}

export class GiftPromotion {
    id?: string;
    name?: string;
    checked?: boolean;
    orderThresholdPrice?: number;
    discountPrice?: number;
    expectedChecked?: boolean;

    giftGoodItems?: GiftGoodItems[];
    productItems?: PromotionProductItem[];
    giftCoupons?: GiftCoupon[];
    isCanBeUsed?: number;
}

export class GiftGoodItems {
    id?: string;
    name?: string;
    type?: number;
    subType?: number;
    count?: number;
    unit?: string;
    reason?: string;
}

export class GiftCoupon {
    id?: string;
    promotionId?: string;
    name?: string;
    unitCount?: number;
    count?: number;
    reason?: string;
}

export class CouponDetails {
    id?: string;
    patientId?: string;
    orderThresholdPrice?: number;
    discountedPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    expireTime?: Date;
    name?: string;
    summary?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    validEnd?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    validBegin?: Date;
}

export class CouponPromotion {
    id?: string;
    couponId?: string;
    couponIds?: string[];
    name?: string;
    checked?: boolean;
    expectedChecked?: boolean;
    discountPrice?: number;
    totalCount?: number;
    availableCount?: number;
    currentCount?: number;
    productItems?: PromotionProductItem[];
    isCanBeUsed?: number;
    discountedPrice?: number;

    compareKey(): string {
        return this.id!;
    }

    coupon?: CouponDetails;
}
export class ItemDeductedDetails {
    availableDeductCount?: number;
    deductedCount?: number;
    id?: string;
    presentId?: string;
    promotionId?: string;
}
export class PatientPointDeductProductPromotions {
    checked?: boolean; //是否选中
    currentCount?: number; //本次已选中的次数
    goodsId?: string; //商品id
    deductionUnitCount?: number; //积分抵扣单位数量：多少积分抵扣一次
    goodsName?: string; //商品名称
    isCanBeUsed?: ChargePromotionUseType; //是否能被使用(0：不能被使用， 1：能被使用)
    @JsonProperty({ type: Array, clazz: ItemDeductedDetails })
    itemDeductedDetails?: ItemDeductedDetails[]; //抵扣的详情，具体到收费项上，那几个收费项被抵扣了
    maxDeductCount?: number; //最多可抵扣的数量
    totalDeductPrice?: number; //抵扣金额（实际存储的是折扣金额）
}
export class PatientCardPromotion {
    id?: string;
    name?: string;
    checked?: boolean;
    totalDeductPrice?: number; // 总共抵扣的金额，负值
    patientId?: string;
    type?: number;
    deductItems?: DeductItem[];
    isOutOfUseRangeCard?: number; // 是否超出使用范围的卡
    patientName?: string;
    patientMobile?: string;
    patient?: Patient;
    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[]; //积分抵扣列表
}

export class ServiceDeductCollection {
    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[]; //积分抵扣列表
}

interface ProductItem {
    goodsId?: string;
    goodsName?: string;
    receivablePrice?: number;
    totalPrice?: number;
}
export class CanPaidPatientCard {
    id?: string;
    present?: number; // 卡赠金
    name?: string;
    principal?: number; // 卡本金
    availableBalance?: number; // 可用于支付的金额
    cardBalance?: number; // 卡余额
    endDate?: string; // 卡失效时间
    productItems?: ProductItem[];
    payModeId?: number;
}

export class DeductItem {
    id?: string;
    checked?: boolean;
    goodsName?: string;
    deductPrice?: number;
    isCanDeduct?: number;
    availableDeductTotalCount?: number;
    checkedCount?: number;
    leftCount?: number;
    isGivingLimit?: number;
}

//收费单锁单状态
export enum ChargeLockStatus {
    unlock, //解锁可编辑状态
    patientWeclinic = 2, //病人锁单状态 --在微诊所锁单
    patientDevice = 4, //病人锁单状态 --在取号机锁单
    chargeStation = 8, //医生锁单--在收费台锁单
    chargeRefund = 16, // 退费锁单
}

export class PatientPointsInfo {
    pointsDeductionRat?: number; //多少积分=1元
    maxDeductionPrice?: number; //最大可折扣金额
    totalPoints?: number; //总积分数
    checked?: boolean;
    checkedDeductionPrice?: number; //折扣金额
}

export class ExtendDiagnosisInfosItem {
    code?: string;
    diseaseType?: number;
    name?: string;
}

export class ExtendDiagnosisInfos {
    @JsonProperty({ type: Array, clazz: ExtendDiagnosisInfosItem })
    value?: ExtendDiagnosisInfosItem[];
    toothNos?: [];
}

export class UpdatedRecordsOldCurrent {
    payMode?: number; // PayMethod
    amount?: number;
    payModeDisplayName?: string;
}
export class UpdatedRecords {
    businessTransactionId?: string;
    old?: UpdatedRecordsOldCurrent;
    current?: UpdatedRecordsOldCurrent;
}
export class UpdatePayModeInfo {
    @JsonProperty({ type: Array, clazz: UpdatedRecords })
    updatedRecords?: UpdatedRecords[];
}
export class ChangePayModeRecord {
    id?: string;
    chainId?: string;
    clinicId?: string;
    businessId?: string;
    updatePayModeInfo?: UpdatePayModeInfo;
    remark?: string;
    createdByName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdBy?: string;
}
export enum InvoiceStatusFlag {
    //     待开票
    toBeInvoiced = 1,
    //     已开票
    invoiceHasBeenIssued = 2,
    // 开票金额异常
    invoiceAmountAbnormal = 4,
}
export class ChargeInvoiceDetailData {
    id?: string;
    patientOrderId?: string;
    patientId?: string;
    chainId?: string;
    clinicId?: string;
    clinicName?: string;
    memberId?: string;
    sellerId?: string;
    doctorId?: string;
    doctorName?: string;
    sellerName?: string;
    status?: ChargeStatus;
    executeStatus?: number; //ChargeExecuteStatus
    dispensingStatus?: number; //
    outpatientStatus?: number; //ChargeInvoiceData.outpatientStatusWaiting...
    isDispensing?: number;
    source?: ChargeInvoiceSource;
    @JsonProperty({ type: MedicalRecord })
    medicalRecord?: MedicalRecord;
    onlyExecuteAfterPaid?: number; //支付后执行（状态开关 0：未收费可执行 1：未收费不可执行）
    _payMode?: number; // 支付方式(终端自定义字段，用于收费选择支付方式计算)

    //诊断调整-使用数组形式
    @JsonProperty({ fromJson: diagnosisFromJson })
    diagnosis?: string;
    chiefComplaint?: string;

    @JsonProperty({ type: Array, clazz: ChargeForm })
    chargeForms?: ChargeForm[];
    chargeTransactions?: ChargeTransaction[];

    @JsonProperty({ type: Array, clazz: ChargeAction })
    chargeActions?: ChargeAction[];

    @JsonProperty({ type: Array, clazz: ChangePayModeRecord })
    changePayModeRecords?: ChangePayModeRecord[];

    memberDiscountInfo?: string;
    memberInfo?: MemberInfo;
    @JsonProperty({ type: Array, clazz: Promotion })
    promotions?: Promotion[];

    @JsonProperty({ type: Array, clazz: GiftPromotion })
    giftRulePromotions?: GiftPromotion[];

    @JsonProperty({ type: Array, clazz: CouponPromotion })
    couponPromotions?: CouponPromotion[];

    @JsonProperty({ type: PatientPointsInfo })
    patientPointsInfo?: PatientPointsInfo;

    @JsonProperty({ type: Array, clazz: PatientCardPromotion })
    patientCardPromotions?: PatientCardPromotion[];

    isNeedPatientCardBalance?: number;
    @JsonProperty({ type: Array, clazz: CanPaidPatientCard })
    canPaidPatientCards?: CanPaidPatientCard[];

    @JsonProperty({ type: Array, clazz: PatientPointDeductProductPromotions })
    patientPointDeductProductPromotions?: PatientPointDeductProductPromotions[];

    // get patientPointsInfo(): PatientPointsInfo {
    //     if (this.__patientPointsInfo) return this.__patientPointsInfo;
    //     return JsonMapper.deserialize(PatientPointsInfo, {
    //         pointsDeductionRat: 2,
    //         checked: 1,
    //         maxDeductionPrice: 300,
    //         totalPoints: 100,
    //         maxPoints: 500,
    //         checkedDeductionPrice: 120,
    //     });
    // }

    // set patientPointsInfo(promotions: PatientPointsInfo) {
    //     this.__patientPointsInfo = promotions;
    // }

    //药敏相关的提示
    disabledPayReason?: string;

    public markPatientPointsInfoChecked(checked: boolean): void {
        if (this.patientPointsInfo) this.patientPointsInfo.checked = checked;
    }

    created?: string;
    lastModified?: string;
    orderByDate?: string;
    @JsonProperty({ type: Patient })
    patient?: Patient;
    statusName?: string;
    sendToPatientStatus?: number; //有这个字段，只有这个字段为0时才表示可以推送哦，并且是待支付状态
    @JsonProperty({ type: ChargeSheetSummary })
    chargeSheetSummary?: ChargeSheetSummary;
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    productInfos?: GoodsInfo[];

    dataSignature?: string;

    @JsonProperty({ type: DeliveryInfo })
    deliveryInfo?: DeliveryInfo;

    //1.2 版本添加
    isDraft?: number;

    isOnline?: number; //网诊单子

    type?: ChargeInvoiceType;

    checkStatus?: number; //ChargeSheetCheckStatus

    //1.2 版本添加
    localDraftId?: string; //本地草稿id

    //代煎联系电话
    contactMobile?: string;

    //是否允许修改空中药房中的主诉
    isCanBeUpdateAirPharmacyMedicalRecord?: number;

    //理疗开单时
    therapySheet?: TherapySheet;

    //收费单锁单相关(锁单状态第二版，位存储，0未锁单据，1锁单据，2：微诊所患者锁单，4：自助服务机锁单，8：收费员锁单, 16：退费锁单)
    lockStatus?: ChargeLockStatus;
    // 收费员支付锁单(0x0008)
    get tollCollectorCharges(): boolean {
        return ((this.lockStatus ?? 0) & 0x0008) > 0;
    }
    // 微诊所患者支付锁单(0x0002)或者自助服务机支付锁单(0x0004)
    get microclinicsOrSelfServiceMachines(): boolean {
        return ((this.lockStatus ?? 0) & 0x0002) > 0 || ((this.lockStatus ?? 0) & 0x0004) > 0;
    }
    // 收费锁单中
    get chargeLockOrder(): boolean {
        return this.tollCollectorCharges || this.microclinicsOrSelfServiceMachines;
    }
    // 退费锁单(0x0010)
    get chargebackLockSlip(): boolean {
        return ((this.lockStatus ?? 0) & 0x0010) > 0;
    }
    canUnLockChargeSheet?: number;
    autoUnlockRemainTimeSecond?: number;
    canEditChargeSheetForPC?: number;
    lockPayTransactionInfo?: {
        // 解锁所需信息（锁单了才会有）
        id?: string;
        payTransactionId?: string;
        payMode?: number;
        paySubMode?: number;
        payType?: number;
    };

    // 锁单相关
    @JsonProperty({ type: Array, clazz: PatientOrderLockDetail })
    patientOrderLocks?: PatientOrderLockDetail[];

    @JsonProperty({ name: "patientOrderLocks", clazz: PatientOrderLockDetail, type: Array })
    copyPatientOrderLocks?: PatientOrderLockDetail[]; // 终端自定义字段，避免直接修改详情上的patientOrderLocks

    /**
     * 收费锁单中条件
     * 1、patientOrderLocks中含有chargeSheet并且status为1
     * 2、lockStatus为2、4、8、16
     */
    get orderIsLocking(): boolean {
        const isExistLockList = !!this.copyPatientOrderLocks?.filter((t) => t.businessKey == PatientOrderLockType.chargeSheet && t.status)
                ?.length,
            isExistLockStatus = this.chargeLockOrder || this.chargebackLockSlip;
        return isExistLockList || isExistLockStatus;
    }
    // 收费处锁单显示文案
    get lockCopy(): { tips: string; isCancel?: boolean } {
        let tips = "",
            isCancel = false;
        const lockList = this.copyPatientOrderLocks?.filter((t) => t.status);
        if (lockList?.length) {
            this.copyPatientOrderLocks
                ?.filter((t) => t.businessKey == PatientOrderLockType.chargeSheet && t.status)
                ?.forEach((item) => {
                    if (item?.value?.doctorEditPrescription) {
                        tips = `医生${item?.employeeName ?? ""}正在调整处方医嘱，请稍后再进行收费`;
                    } else if (item?.value?.chargeInProgress) {
                        if (this.tollCollectorCharges) {
                            tips = `${!!item?.employeeName ? "收费员" + item?.employeeName : "本单"}正在收费中，暂不可编辑`;
                            isCancel = userCenter.employee?.id == item.employeeId && !!this.lockPayTransactionInfo?.id;
                        } else if (this.microclinicsOrSelfServiceMachines) {
                            tips = "患者正在自助支付，请稍候";
                            isCancel = !!this.lockPayTransactionInfo?.id; // 交易是微诊所/服务机发起的，可以操作取消支付
                        }
                    } else if (item?.value?.chargeRefundOrder) {
                        tips = `${!!item?.employeeName ? "收费员" + item?.employeeName : "本单"}正在退费中，请稍候`;
                    }
                });
        } else if (this.lockStatus != 0) {
            if (this.tollCollectorCharges) {
                tips = "本单正在收费中，暂不可编辑";
            } else if (this.microclinicsOrSelfServiceMachines) {
                tips = "患者正在自助支付，";
                isCancel = !!this.lockPayTransactionInfo?.id;
            } else if (this.chargebackLockSlip) {
                tips = "本单正在退费中，请稍候";
            }
        }

        return { tips, isCancel };
    }
    // 收费单退费锁单(执行站锁单目前只针对退费锁单)
    get isRefundLocking(): boolean {
        const isExistLockList = !!this.copyPatientOrderLocks?.filter(
                (t) => t.businessKey == PatientOrderLockType.chargeSheet && t.status && t?.value?.chargeRefundOrder
            )?.length,
            istExistLockStatus = this.chargebackLockSlip;
        return isExistLockList || istExistLockStatus;
    }

    get defaultDepartmentName(): string {
        return !!this.sellerDepartmentName ? ` - ${this.sellerDepartmentName}` : "";
    }

    sellerDepartmentId?: string; // 执行站、收费处添加科室
    sellerDepartmentName?: string;

    //杭州社保要求直接收费也需要诊断相关信息
    departmentId?: string;
    departmentName?: string; //
    diagnosisInfos?: DiseasesCode[]; //第二版

    @JsonProperty({
        type: Array,
        clazz: ExtendDiagnosisInfos,
    })
    extendDiagnosisInfos?: ExtendDiagnosisInfos[];

    reCharge__?: boolean; //重新收费
    reChargeFromClosedChargeSheet__?: boolean; //已经关闭的单子，重新打开收费

    //长护相关信息
    hospitalOrderId?: string; //获取登记相关信息
    hospitalSheetId?: string;

    //判断是否是住院的收费单
    get isHospitalizationSheet(): boolean {
        return !!this.hospitalSheetId;
    }

    //理疗预约id,本地使用
    registrationId__?: string;

    retailType?: RetailType;
    isClosed?: number; //1 已关闭

    //限制操作相关属性
    isDisabledOperate?: number;
    disabledOperateReason?: string;

    //社保异常与非社保异常判断
    queryExceptionType?: QueryExceptionType;

    //欠费状态（10：欠费列表）
    owedStatus?: number;

    isDisabledRefund?: number; //欠费单子是否可退费(0:可退，1：不可退）
    disabledRefundReason?: string; //欠费单子不可退费的原因

    /**
     * 是否使用会员卡
     */
    useMemberFlag?: number;
    /**
     * 本地手动选择的会员信息
     */
    _localMemberInfo?: MemberInfo;

    //空中药房订单id
    airPharmacyOrderId?: string;

    consultantId?: string; //咨询师id
    consultantName?: string;

    invoiceStatusFlag?: InvoiceStatusFlag; // 发票状态
    // 已开票
    get isInvoiceHasBeenIssued(): boolean {
        return this.invoiceStatusFlag == InvoiceStatusFlag.invoiceHasBeenIssued;
    }

    //欠费单子是否可退
    get oweChargeCanRefund(): boolean {
        return this.isDisabledRefund == 0;
    }

    //是否从欠费列表进入
    get isFromOweList(): boolean {
        return this.owedStatus == 10;
    }

    //社保异常判断
    get isSheBaoAbnormal(): boolean {
        return (this.queryExceptionType ?? 0) == QueryExceptionType.shebaoPay ?? false;
    }

    //非社保异常判断
    get isNotSheBaoAbnormal(): boolean {
        return (this.queryExceptionType ?? 0) > 1 ?? false;
    }
    //尝试从charge form item中提取快递信息
    get deliveryInfoFromChargeForm(): DeliveryInfo | undefined {
        const formItem = this.getDeliveryChargeFormItem();
        if (formItem?.productInfo) return formItem.productInfo as DeliveryInfo;

        return undefined;
    }

    //1 送药上门
    get deliveryType__(): number {
        const formItem = this.getDeliveryChargeFormItem();
        if (!formItem) return 0;

        return formItem.checked ?? true ? 1 : 0;
    }

    //挂单
    get isNetworkDraft(): boolean {
        return this.isDraft == 1;
    }

    //本地草稿
    get isLocalDraft(): boolean {
        return !this.isNetworkDraft && !_.isEmpty(this.localDraftId);
    }

    get decoctionFee(): number | undefined {
        let fee = 0;
        this.chargeForms?.forEach((form) => {
            form.isDecoction &&
                form.chargeFormItems?.forEach((item) => {
                    if (item.isDecoction) {
                        fee += item.unitPrice!;
                    }
                });
        });

        return fee;
    }

    canDelete(): boolean {
        return (
            (this.status === ChargeStatus.unCharged || this.status === ChargeStatus.draft) &&
            this.type === ChargeInvoiceType.retail &&
            this.isNetworkDraft &&
            (_.isNil(this.lockStatus) || this.lockStatus == ChargeLockStatus.unlock) &&
            !this.reCharge__
        );
    }

    /**
     * 单子是否可关闭
     */
    canClose(): boolean {
        return this.status === ChargeStatus.unCharged && !this.canDelete() && !this.isClosed && !this.reCharge__;
    }

    get deliveryFee(): number | undefined {
        return this.getDeliveryChargeFormItem()?.unitPrice;
    }

    getDeliveryChargeFormItem(): ChargeFormItem | undefined {
        return this.getChargeFormItem(ChargeSourceFormType.delivery, GoodsType.deliveryFee);
    }

    getChargeFormItem(sourceFormType: number, productType: number): ChargeFormItem | undefined {
        const deliveryForm = this.chargeForms?.find((item) => item.sourceFormType == sourceFormType);
        const deliveryFormItem = deliveryForm?.chargeFormItems?.find((item) => item.productType == productType);
        return deliveryFormItem!;
    }

    getChargeForm(sourceFormType: number): ChargeForm | undefined {
        switch (sourceFormType) {
            case ChargeSourceFormType.airPharmacyAndVirtual:
                return this.chargeForms?.find(
                    (item) =>
                        item.sourceFormType == ChargeSourceFormType.airPharmacy ||
                        (item.sourceFormType == ChargeSourceFormType.chinesePrescription && item.pharmacyType == PharmacyType.virtual)
                );
            default:
                return this.chargeForms?.find((item) => item.sourceFormType == sourceFormType);
        }
    }
    //零售收费复制医嘱处方，可能含有多个同类型的处方
    getFilterChargeForm(sourceFormType: number): ChargeForm[] | undefined {
        switch (sourceFormType) {
            case ChargeSourceFormType.airPharmacyAndVirtual:
                return this.chargeForms?.filter(
                    (item) =>
                        item.sourceFormType == ChargeSourceFormType.airPharmacy ||
                        (item.sourceFormType == ChargeSourceFormType.chinesePrescription && item.pharmacyType == PharmacyType.virtual)
                );
            default:
                return this.chargeForms?.filter((item) => item.sourceFormType == sourceFormType);
        }
    }

    fillKeyIds(): void {
        this.chargeForms?.forEach((form) => {
            form.keyId = form.keyId ?? UUIDGen.generate();
            form.chargeFormItems?.forEach((formItem) => {
                formItem.keyId = formItem.keyId ?? UUIDGen.generate();
            });
        });
    }

    getProcessInfo(chargeFormId?: string): ProcessInfo | undefined {
        return this.createDecoctionForm(chargeFormId).processInfo;
    }

    /**
     * 补全空中药房信息
     */
    fixAirPharmacyPrescriptionInfo(): void {
        this.chargeForms?.forEach((form) => {
            form.fixAirPharmacyPrescriptionInfo();
        });
    }

    public hasContent(): boolean {
        if (!_.isEmpty(this.chargeForms)) return true;

        if (!_.isEmpty(this.patient?.name)) return true;
        if (!_.isEmpty(this.sellerId)) return true;
        if (!_.isEmpty(this.patient?.mobile)) return true;
        return !_.isEmpty(this.patient?.age?.displayAge);
    }

    public get canEditChargeSheet(): boolean {
        //锁单状态不可编辑
        return !(this.isClosed ?? 0) && !this.isDisabledOperate && !this.orderIsLocking;
    }

    public createDecoctionForm(chargeFormId?: string): ChargeForm {
        let chargeForm = this.chargeForms?.find(
            //chargeFormId为undefined说明是直接收费草稿状态，后台还没有分配id
            (form) => form.isDecoction && (form.processInfo?.chargeFormId == chargeFormId || chargeFormId === undefined)
        );
        if (chargeForm) return chargeForm;
        chargeForm = new ChargeForm();
        chargeForm.keyId = UUIDGen.generate();
        chargeForm.chargeSheetId = chargeFormId;
        chargeForm.sourceFormType = ChargeSourceFormType.decoction;
        const formItem = new ChargeFormItem();
        formItem.productType = GoodsType.decoctionFee;
        formItem.name = "加工费";
        formItem.unitCount = 1;
        formItem.keyId = UUIDGen.generate();
        formItem.checked = true;
        chargeForm.chargeFormItems = [];
        chargeForm.chargeFormItems.push(formItem);
        chargeForm.processInfo = {
            chargeFormId: chargeFormId,
        };
        this.chargeForms?.push(chargeForm);
        return chargeForm;
    }

    /**
     * 优惠金额
     */
    get promotionsTotalPrice(): number {
        let totalPrice = 0;
        if (this.promotions && !_.isEmpty(this.promotions)) {
            this.promotions
                ?.filter((item) => item.checked)
                ?.forEach((item) => {
                    totalPrice += item?.discountPrice ?? 0;
                });
        }
        return totalPrice;
    }

    /**
     * 计算服务抵扣总价格以及当前抵扣的卡项名称
     */
    get computedTotalDeductPrice(): { totalDeductPrice: number; serviceDeductName: string[]; isExistService: boolean } {
        const patientCardPromotions = this.patientCardPromotions ?? [];
        let totalDeductPrice = 0;
        const serviceDeductName: string[] = [];
        let isExistService = false; // 是否存在服务抵扣选项
        if (!_.isEmpty(patientCardPromotions)) {
            patientCardPromotions.forEach((item) => {
                if (item.checked) {
                    totalDeductPrice += item?.totalDeductPrice ?? 0;
                    if (item?.deductItems && !_.isEmpty(item?.deductItems)) {
                        const isCheckedDeduct = item?.deductItems.some((t) => t.checked);
                        if (isCheckedDeduct) {
                            serviceDeductName.push(item?.name ?? "");
                        } else {
                            isExistService = true;
                        }
                    }
                }
            });
        }
        return { totalDeductPrice, serviceDeductName, isExistService };
    }

    /**
     * 计算是否存在积分项目抵扣
     */
    get computedPointDeductProductInfo(): {
        pointDeductProductName: string[];
        hasPointDeduct: boolean;
        pointTotalDeductPrice: number;
    } {
        const pointDeductProductPromotions = this.patientPointDeductProductPromotions ?? [];
        let pointTotalDeductPrice = 0,
            hasPointDeduct = false;
        const pointDeductProductName: string[] = [];
        if (!_.isEmpty(pointDeductProductPromotions)) {
            pointDeductProductPromotions?.forEach((item) => {
                if (item.checked) {
                    pointTotalDeductPrice += item?.totalDeductPrice ?? 0;
                    !!item.goodsName && pointDeductProductName.push(item.goodsName);
                }
                hasPointDeduct = pointDeductProductPromotions?.some((t) => t.isCanBeUsed != ChargePromotionUseType.cannotUsed);
            });
        }
        return { pointDeductProductName, hasPointDeduct, pointTotalDeductPrice };
    }

    computedDiscountPricce(promotion: Promotion[] | CouponPromotion[] | GiftPromotion[]): number {
        let totalPrice = 0;
        if (promotion && !_.isEmpty(promotion)) {
            //@ts-ignore
            [...promotion]
                ?.filter((item) => item.checked)
                ?.forEach((item) => {
                    totalPrice += item?.discountPrice ?? 0;
                });
        }
        return totalPrice;
    }
}

export class ChargePatientCards {
    availableBalance?: number;
    cardBalance?: number;
    endDate?: string;
    id?: string;
    name?: string;
    patientId?: string;
    patientMobile?: string;
    patientName?: string;
    present?: number;
    principal?: number;
    payModeId?: number;
}

// 修改支付方式

export class NewPayModes {
    amount?: number;
    disabled?: boolean;
    id?: string;
    payMode?: number;
    thirdPartyPayTransactionId?: string;
    _payMode?: PayMethod;
    _payModeName?: string;
}

export class ChargeTransaction {
    payModeId?: number;
    id?: string;
    thirdPartyPayTransactionId?: string;
    thirdPartyPayCardId?: string;
    thirdPartyPayInfo?: ThirdPartyPayInfo;
    payMode?: number;
    payModeName?: string;
    paySubMode?: number;
    paySubModeName?: string;
    payModeDisplayName?: string;
    amount?: number;
    refundedAmount?: number;
    presentAmount?: number;
    principalAmount?: number;
    income?: number;
    change?: number;
    needPay?: number;
    changePayMode?: number;
    changePayModeName?: string;
    paySource?: number;
    isPaidback?: number;
    isRefunded?: number;
    created?: string;
    chargeType?: number;

    _payMode?: PayMethod;
    _payModeName?: string;
}

export class PayModeView {
    payMode?: number;
    payModeName?: string;
}
export class ChargePaymentMethodRsp {
    @JsonProperty({ type: Array, clazz: ChangePayModeRecord })
    changePayModeRecords?: ChangePayModeRecord[];
    @JsonProperty({ type: Array, clazz: ChargeAction })
    chargeActions?: ChargeAction[];
    @JsonProperty({ type: Array, clazz: ChargeTransaction })
    chargeTransactions?: ChargeTransaction[];
    @JsonProperty({ type: Array, clazz: PayModeView })
    payModeViews?: PayModeView[];
    payModes?: [];
}

export interface ThirdPartyPayInfo {
    transactionId: string;

    //  Null cardId;
    cardBalance: number;
    cardId?: string;
}

export class ChargeActionType {
    static charge = 0; //收费
    static refund = 1; //退费
    static change = 2; //修改
    static repayment = 4; //还款
}

export enum ChargeFormStatus {
    unCharged = 0, //未收费
    charged = 1, //已收费
    refunded = 4, //已退费
    chargeBack = 3, //已退单

    //终端自定义显示
    partCharged = 1001,
}

export enum ChargeFormItemStatus {
    unCharged = 0, //未收费
    charged = 1, //已收费
    refunded = 2, //已退费
    chargeBack = 3, //已退单
    partRefund = 4, //部份退费

    //终端自定义显示
    partCharged = 1001,
}

export class ChargeRegistrationInfo {
    id?: string;
    fee?: number;
    type?: number;
    expire?: number;
    status?: number;
    orderNo?: number;
    clinicId?: string;
    doctorId?: string;
    patientId?: string;
    payStatus?: number;
    doctorName?: string;
    isReserved?: number;
    reserveEnd?: string;
    reserveDate?: string;
    departmentId?: string;
    reserveShift?: number;
    reserveStart?: string;
    costUnitPrice?: number;
    departmentName?: string;
    patientOrderId?: string;
    consultingRoomId?: string;
    consultingRoomName?: string;
    registrationFormId?: string;
    registrationSheetId?: string;

    get displayName(): undefined {
        return;
    }
}

function toProductionInfo(json: any, parent: ChargeFormItem) {
    if (_.isNil(json)) return;

    const productType = parent.productType;
    if (json["registrationFormId"]) {
        return JsonMapper.deserialize(ChargeRegistrationInfo, json);
    } else if (productType == GoodsType.deliveryFee) {
        return JsonMapper.deserialize(DeliveryInfo, json);
    } else if (productType == GoodsType.decoctionFee) {
        return JsonMapper.deserialize(DecoctionProductInfo, json);
    } else if (productType === GoodsType.ingredient) {
        return JsonMapper.deserialize(IngredientProductInfo, json);
    }

    return JsonMapper.deserialize(GoodsInfo, json);
}

export interface ExaminationResult {
    examinationSheetId: string;
    outpatientFormItemId: string;
    chargeFormItemId: string;
    status: number;
}

export interface AstResult {
    result: string;
    description: string;
}

//收费状态，对应后台的值
//状态（0: 未收费，1：部分收费，2：已收费，3：部分退费，4：已退费）
export enum ChargeStatus {
    unCharged = 0, //未收费
    partCharged = 1, //1部份收费
    charged = 2, //已收费
    partRefunded = 3, //部份退费
    refunded = 4, //已退费
    draft = 1000, // 客户自已定义，草稿状态
}

export class ChargePayModeConfigs {
    disableIcon?: string;
    shortcutKey?: string;
    isEnable?: number;
    sort?: number;
    type?: number;
    payModeId?: number;
    name?: string;
    enableIcon?: string;
}

enum ChargeRoundingType {
    keep = 0, //保持不变
    upToJiao = 1, //向上到角
    upToYuan = 2, //向上到元
    downToJiao = 3, //向下到角
    downToYuan = 4, //向下到元
    roundToJiao = 5, //四舍五入到角
    roundToYuan = 6, //四舍五入到元
}

//ABC支付开通状态
export enum WeChatPaySwitch {
    notOpen = -1, // 未开通
    normal = 2, //正常
    abnormal = 3, // 异常
}

export enum PayConfigType {
    wechat = 0, // 微信
    allinpay = 1, // 通联
}

//ABC扫码支付的各种方式
export enum ABCScanPayMethod {
    wechatNative = 5, // ABC微信扫码支付
    wechatJs = 6, //ABC微信JSAPI支付
    wechatMini = 7, //ABC微信小程序支付
    aliNative = 8, //ABC支付宝扫码支付
    qqNative = 9, //ABC手机QQ扫码支付
    unionNative = 10, //ABC银联扫码支付
    scanQrCode = 11, //ABC主扫支付（扫码枪）
}
export enum SendOrderInfoModes {
    manualPush = 0, //收费人员人工推送
    systemAutomaticPush = 1, //系统自动推送
    patientScan = 2, //患者扫码获取
}
export class RefundAuditorItem {
    id?: string; // 员工id
    name?: string; // 员工姓名
    mobile?: string; // 手机号码/登录名
    countryCode?: string; // 员工手机号国家码
    status?: number; // 员工状态 1：正常；90及以上表示无效
    created?: string; // 员工创建时间
    shortId?: string;
    namePy?: string; // 员工姓名拼音
    namePyFirst?: string;
    headImgUrl?: string;
    handSign?: string;
    wechatOpenIdMp?: string; // 有值的话，表示绑定了openid
    wechatNickName?: string;
    wechatUnionId?: string;
    hasPassword?: number; // 该账号是否设置了密码（1--设置了密码，0--未设置密码）
    wechatSubscribe?: number; // 是否绑定过微信 // 是否关注（1：关注；0：未关注）
    // 终端自定义字段，对手机号码进行脱敏处理
    @JsonProperty({
        name: "mobile",
        fromJson: (str: string) => {
            if (!str) return "";
            // 移除所有非数字字符（包括+号和空格）
            const numbers = str.replace(/\D/g, "");
            // 如果长度小于5，无法进行有效脱敏，直接返回原值
            if (numbers.length < 5) return str;
            // 计算需要保留的前后位数
            const frontDigits = Math.min(3, Math.floor((numbers.length - 4) / 2));
            const backDigits = numbers.length - frontDigits - 4;

            return `${numbers.substring(0, frontDigits)}****${numbers.substring(numbers.length - backDigits)}`;
        },
    })
    phoneNumber?: string;
    // 绑定了微信
    get isBindWechat(): boolean {
        return this.wechatSubscribe == 1;
    }
    // 设置了密码
    get passwordSet(): boolean {
        return this.hasPassword === 1;
    }
}

export class ChargeConfig {
    roundingType?: ChargeRoundingType;
    bargainSwitch?: number;
    singleBargainSwitch?: number;
    doctorBargainSwitch?: number;
    doctorSingleBargainSwitch?: number;
    autoSendOrderInfoSwitch?: number; //门诊结束后自动发送支付订单, 0表示手动推送，1表示自动推送
    @JsonProperty({ type: Array, clazz: ChargePayModeConfigs })
    chargePayModeConfigs?: Array<ChargePayModeConfigs>;
    chargeNeedAstPassSwitch?: number;
    doctorCanPushChargeSheetSwitch?: number; // 是否开启扫码推送按钮
    assignEmployeeBargain?: number; // 收费处议价配置 0.有收费权限的成员均可议价 1.指定成员可议价
    doctorAssignEmployeeBargain?: number; // 门诊处议价配置 0：有收费权限的成员均可议价 1.指定成员可议价

    doctorRegisteredBargainSwitch?: number; //挂号费议价开关
    reservationRegisteredBargainSwitch?: number; //挂号处挂号费议价开关
    oweSheetSwitch?: number; // 欠费支付方式是否开启
    refundRestriction?: RefundRestrictionStatus; //退费条件限制 0=发药 1=审核 2=调配
    sendOrderInfoModes?: SendOrderInfoModes[];
    oddFeeDealType?: number; // 凑整抹零指定支付方式（0：全部支付方式，1：指定支付方式）
    oddFeeDealPayModes?: string[]; // 凑整抹零指定支付方式列表
    // 是否指定了凑整抹零支付方式
    get isOddFeeDeal(): boolean {
        return this.oddFeeDealType == 1;
    }
    refundCheck?: number; // 退费审核 0=关闭 1=开启
    @JsonProperty({ type: Array, clazz: RefundAuditorItem })
    refundCheckEmployees?: RefundAuditorItem[]; // 退费审核人员
    wholeSheetOperateEnable?: number; // 整单收退费开关（开启后，收费单内项目仅能全部收费/退费，不能选择收费/退费项目）
    //门诊执行是否显示推送支付(暂时舍弃)
    get isShowPushBtn(): boolean {
        return !!this.sendOrderInfoModes?.includes(SendOrderInfoModes.systemAutomaticPush);
    }
    // 开启了审核/调配后不可退费
    get openAuditOrCompound(): boolean {
        return !!this.refundRestriction;
    }
    // 开启审核后不可以退费
    get openAudit(): boolean {
        return this.refundRestriction == 1;
    }
    // 开启调配后不可以退费
    get openCompound(): boolean {
        return this.refundRestriction == 2;
    }
    // 系统自动抹零，用户不可以取消，修改
    get isSystemAutoOdd(): boolean {
        return (this.roundingType ?? 0) > 0;
    }
    // 系统议价文字描述
    get sysAdjustmentDesc(): string {
        switch (this.roundingType) {
            case ChargeRoundingType.upToJiao:
                return "凑整到角";
            case ChargeRoundingType.upToYuan:
                return "凑整到元";
            case ChargeRoundingType.downToJiao:
                return "抹零到角";
            case ChargeRoundingType.downToYuan:
                return "抹零到元";
            case ChargeRoundingType.roundToJiao:
                return "四舍五入到角";
            case ChargeRoundingType.roundToYuan:
                return "四舍五入到元";
            default:
                // 未设置或不处理
                return "";
        }
    }
    // 开启退费审核
    get openRefundCheck(): boolean {
        return this.refundCheck == 1;
    }
    // 开启整单收退费
    get openWholeSheetOperateEnable(): boolean {
        return !!this.wholeSheetOperateEnable;
    }
}

export class WechatpayConfig {
    apiRefundStatus?: number;
    bankCard?: string;
    chainId?: string;
    clinicAddress?: string;
    clinicId?: string;
    contactEmail?: string;
    depositBank?: string;
    jsapiPayStatus?: number;
    merchantShortName?: string;
    microPayStatus?: number;
    platformRefundStatus?: number;
    rate?: number;
    weChatPaySwitch?: WeChatPaySwitch;
    type?: PayConfigType;
}

export class DecoctionInfo {
    decoctionFee?: number;

    constructor(decoctionFee?: number) {
        this.decoctionFee = decoctionFee;
    }
}

export class ChargePayData {
    chargeInvoiceDetailData?: ChargeInvoiceDetailData;
    selects?: AbcMap<ChargeForm, ChargeFormItem[]>;
    receivableFee?: number;
    totalFee?: number;
    adjustmentFee?: number; //议价
    promotions?: Promotion[];
    memberId?: string;
    reCharge?: boolean;
    directCharge?: boolean; //直接收费
}

export class ChargeRefundData {
    chargeInvoiceDetailData?: ChargeInvoiceDetailData;
    selects?: AbcMap<ChargeForm, Array<ChargeFormItem>>;

    needRefundFee?: number; //后台根据当前选择项计算出来的需要退的费用
    refundAdjustmentFee?: number; //退费，议价部份
    owedRefundFee?: number; //已经退掉的项中，还有多少钱没有退给用户
    promotions?: Promotion[];
    refundItemInputCount?: AbcMap<ChargeFormItem, number>;
    memberId?: string;
    clinicBargain?: boolean;
    realRefundFee?: number; //用户输入实际要退的全额
    deductTotalCount?: number; //退的时候本次退的抵扣的数量
}

////中药加工相关数据结构

export interface ProcessUsage {}

export interface ProcessUsageInfo {
    processUsage?: ProcessUsage;
}

export class ProcessInfo {
    id?: string;
    chargeFormId?: string;
    checked?: boolean;
    type?: number;
    subType?: number;
    processBagUnitCount?: number;
    keyId?: string;

    contactMobile?: string;
    ruleInfo?: string;
    isMarkRule?: number;
    processFee?: number;
    name?: string;

    processUsageInfo?: {
        subType?: number;
        type?: number;
        price?: number;
        processName?: string;
    };
    totalProcessCount?: number;
    processRemark?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    takeMedicationTime?: Date; //取药时间
    isNeedAssignmentTime?: boolean; //终端自定义（用于判断在取药时间没有值时，是否需要赋值时间）
}

//加工类型
export enum MedicineProcessType {
    decoction = 1, //蕉药
}

export class ProcessRule {
    id?: string;
    name?: string;
    type?: number;
    subType?: number;

    children?: ProcessRule[];

    //辅料费
    vendorId?: string;
    businessScopeId?: string;
    description?: string;
    ladderInfo?: AnyType;
    permanentPrice?: number;
    minimum?: number;
    finishedRate?: number;

    @JsonProperty({ type: IngredientProductInfo })
    ingredient?: IngredientProductInfo;
}

export class DeliveryFeeRule {
    id?: string;
    chainId?: string;
    clinicId?: string;
    name?: string;

    @JsonProperty({ type: DeliveryCompany })
    deliveryCompany?: DeliveryCompany;

    // addresses?: any;
    // ladderInfo?: any;
    ruleInfo?: string;
    type?: number;
    price?: number;
    isFreePostage?: number;
    freePostageType?: number;
    freePostageUnitCount?: number;
    freePostageUnit?: number;
    status?: number;
}

export interface GetAddressListParams {
    patientId?: string;
    type?: number; //0：本地地址，1：空中药房地址
    vendorId?: string;
    usageScopeId?: string;
}

export class PatientParams {
    autoUpgradeMemberType?: number;
    enableAutoUpgradeMember?: number;
    enableMemberPassword?: number; // 会员卡支付是否需要密码（1：需要，0：不需要）
    enableMemberRecharge?: number;
    memberRechargeMinAmount?: string;
}
export class CrmPatientParams {
    @JsonProperty({ type: PatientParams })
    patient?: PatientParams;
}
export class PatientChainProperty {
    @JsonProperty({ type: CrmPatientParams })
    crm?: CrmPatientParams;
    scope?: string;
    scopeId?: string;
}

export class QrCodeInfo {
    expireSeconds?: number;
    key?: string;
    url?: string;
}
export class PushScanCodeInfo {
    abstractInfo?: string;
    chargeSheetId?: string;
    diagnosis?: string;
    patientId?: string;
    patientName?: string;
    @JsonProperty({ type: QrCodeInfo })
    qrCodeInfo?: QrCodeInfo;
    totalFee?: string;
    needPayFee?: number; //收费金额
}

export class AvailableCompanyParams {
    @JsonProperty({ type: Array, clazz: DeliveryPayType })
    availablePayTypes?: Array<DeliveryPayType>;
    id?: string;
    name?: string;
}

export class ChargeOweSheets {
    chainId?: string;
    chargeSheetId?: string;
    clinicId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    id?: string;
    isOldRecord?: number;
    needPay?: number; //应还金额
    patientId?: string;
    receivedPrice?: number;
    refundedPrice?: number;
    status?: number;
    totalPrice?: number;
    checked__?: boolean; //自定义字段，用于判断当前选中项
    memberId?: string;
    memberInfo?: MemberInfo;
}
export class ChargeOweInfo {
    @JsonProperty({ type: Array, clazz: ChargeOweSheets })
    chargeOweSheets?: ChargeOweSheets[];
    totalOweFee?: number; //当前患者已欠费金额
}

export class OweSheetItems {
    amount?: number;
    oweSheetId?: string;
    receivableFee?: number;
}
export class OwePayItem {
    amount?: number;
    authCode?: string;
    change?: number;
    memberCardPassword?: string;
    payMode?: number;
    paySubMode?: number;
    presentAmount?: number;
    principalAmount?: number;
    thirdPartyPayCardBalance?: number;
    thirdPartyPayCardId?: string;
    thirdPartyPayCardOwner?: string;
    thirdPartyPayIdCardNum?: string;
    thirdPartyPayTransactionId?: string;
    transactionIds?: string[];
}
export class PayChargeOweSheetReq {
    @JsonProperty({ type: Array, clazz: OweSheetItems })
    oweSheetItems?: OweSheetItems[];
    patientId?: string;
    @JsonProperty({ clazz: OwePayItem })
    payItem?: OwePayItem;
    memberId?: string;
    chargeComment?: string;
}

export class TransactionRecords {
    amount?: number;
    businessId?: string;
    id?: number;
    orderId?: number;
    orderItemId?: number;
    orderTransactionId?: number;
}
export class CqShebaoExtraInfo {
    accountBalance?: number;
    accountPaymentFee?: number;
    cashPaymentFee?: number;
    civilAidAmount?: number;
    civilAidOutpatientBalance?: number;
    civilServiceBack?: number;
    civilServiceSubsidy?: number;
    fundPaymentFee?: number;
    healthHelpFundFee?: number;
    largeClaimAmount?: number;
    otherPovertyReliefFee?: number;
    precisionPovertyFee?: number;
    singleDiseaseSupport?: number;
    totalSubstituteFee?: number;
}
export class HzShebaoExtraInfo {
    allYearBalance?: number;
    beforeAllYearBalance?: number;
    beforeCurYearBalance?: number;
    curYearBalance?: number;
    invoiceRemark?: string;
}
export class WechatPayInfo {
    appId?: string;
    codeUrl?: string;
    nonceStr?: string;
    paySign?: string;
    prepayId?: string;
    signType?: string;
    timeStamp?: number;
}
export class CombinePaidthirdPartyPayInfo {
    accountPaymentFee?: number;
    cardBalance?: number;
    cardId?: string;
    cardOwner?: string;
    cardOwnerType?: string;
    @JsonProperty({ clazz: CqShebaoExtraInfo })
    cqShebaoExtraInfo?: CqShebaoExtraInfo;
    fundPaymentFee?: number;
    @JsonProperty({ clazz: HzShebaoExtraInfo })
    hzShebaoExtraInfo?: HzShebaoExtraInfo;
    idCardNum?: string;
    medType?: string;
    otherPaymentFee?: number;
    receivableFee?: number;
    relationToPatient?: string;
    selfConceitFee?: number;
    selfPayFee?: number;
    thirdPartyTransactionId?: string;
    transactionId?: string;
    @JsonProperty({ clazz: WechatPayInfo })
    wechatPayInfo?: WechatPayInfo;
}
export class CombineOrderTransaction {
    amount?: number;
    id?: number;
    orderId?: number;
    payMode?: number;
    payModeDisplayName?: string;
    paySubMode?: number;
    presentAmount?: number;
    principalAmount?: number;
    refundedAmount?: number;
    thirdPartyPayCardBalance?: number;
    thirdPartyPayCardId?: string;
    @JsonProperty({ clazz: CombinePaidthirdPartyPayInfo })
    thirdPartyPayInfo?: CombinePaidthirdPartyPayInfo;
    thirdPartyPayOrderId?: string;
    thirdPartyPayTransactionId?: string;
    @JsonProperty({ type: Array, clazz: TransactionRecords })
    transactionRecords?: TransactionRecords[];
    type?: number;
}
export class CombinePaidItems {
    businessId?: string;
    id?: number;
    price?: number;
}
export class ChargeOweCombinePaidRsp {
    combineOrderId?: string;
    combineOrderPayTransactionId?: string;
    @JsonProperty({ clazz: CombineOrderTransaction })
    combineOrderTransaction?: CombineOrderTransaction;
    @JsonProperty({ type: Array, clazz: CombinePaidItems })
    items?: CombinePaidItems[];
    payStatus?: number;
    status?: number;
    thirdPayTaskId?: string;
    id?: string; //自定义，存储收费单id
}

export class ChargeOweSinglePaidRsp {
    combineOrderId?: string;
    combineOrderPayTransactionId?: string;
    id?: string;
    needPay?: number;
    payStatus?: number;
    receivedFee?: number;
    status?: number;
    thirdPayTaskId?: string;
    statusName?: string;
    //空中药房
    isContainAirPharmacy?: number;
    airPharmacyOrderIds?: string[];
    isAirPharmacyCanPay?: number; //空中药房可支付
}

export class DispensingSwitch {
    sameTimeDispensing?: number; //1--开启发药开关，0--关闭发药开关
}
export class DispensingSettingConfig {
    @JsonProperty({ type: DispensingSwitch })
    paid?: DispensingSwitch;
}

export interface DoctorDiagnosis {
    diagnosis: string;
    doctorInfo: Doctor;
    departmentInfo: Department;
}

class OutpatientCalculateProcessForms {
    keyId?: string;
    processPrice?: number;
}
export class OutpatientCalculateProcessRsp {
    @JsonProperty({ type: Array, clazz: OutpatientCalculateProcessForms })
    forms?: OutpatientCalculateProcessForms[];
}

export class ChineseAirPharmacyBagsParam {
    selectIndex?: number;
    processBagUnitCount?: number;
    totalProcessCount?: number;
    processRemark?: string;
}
export class ChargeSettlementExceptionItem {
    id?: string; // 第三方支付id
    payMode?: PayMethod; // 支付方式
    paySubMode?: number; // 支付子方式
    payTransactionId?: string; // 渠道的支付单id，比如是shebao渠道，就是taskId，如果是wechatpay渠道，就是wechatPay服务生成的订单号，如果是chargeCenter渠道，就是chargeCenter生成的订单号
    payType?: number; // 支付类型，1:支付，2：收费单支付完成后再退费，3：部分支付状态下退费，（2和3都表示退费，只是收费单的原始状态不同）
    //  医保收费异常
    get shebaoChargeAbnormal(): boolean {
        return this.payMode == PayMethod.payHealthCard && this.payType == 1;
    }
    //  医保退费异常
    get shebaoRefundAbnormal(): boolean {
        return this.payMode == PayMethod.payHealthCard && (this.payType == 2 || this.payType == 3);
    }
}
// 门诊、收费整单单项议价对应的key
export const BargainingBusinessKey = Object.freeze({
    // single_adjust_goods_type_match  收费处可单项议价范围类型key
    singleAdjustGoodsTypeMatch: "single_adjust_goods_type_match",
    // outpatient_single_adjust_goods_type_match 门诊处可单项议价范围类型key
    outpatientSingleAdjustGoodsTypeMatch: "outpatient_single_adjust_goods_type_match",
    // adjust_employee_match  收费处可议价人员key
    adjustEmployeeMatch: "adjust_employee_match",
    // adjust_employee_match  门诊处可议价人员key
    outpatientAdjustEmployeeMatch: "outpatient_adjust_employee_match",
});
export enum MatchType {
    GOODS_TYPE = 1, // 商品类型
    GOODS = 2, // 只是先定义出来，暂时还没有赋值含义用法
    EMPLOYEE = 3, // 人员
}
export class ProductTypeMatch {
    id?: number;
    type?: MatchType;
    typeRefId?: string;
}
export class ChargeBargainingRule {
    businessKey?: string;
    chainId?: string;
    clinicId?: string;
    @JsonProperty({ type: Array, clazz: ProductTypeMatch })
    productTypeMatches?: ProductTypeMatch[];
}

export enum PreCheckItemType {
    UNRETURNED = 1, // 未退药
    UNSELECTED = 2, // 医保项目未选择
    UNSELECTED_ALL = 3, // 未选择全部数量
}
export class PreCheckItem {
    chargeFormItemId?: string;
    doseCount?: number;
    name?: string;
    type?: PreCheckItemType; //类型 1：未退药 2：医保项目未选择 3:未选择全部数量
    unitCount?: number;
}
export class ChargeRefundPreCheckRsp {
    canRefundShebao?: number; // 是否可以医保退费
    chargeSheetId?: string;
    @JsonProperty({ type: Array, clazz: PreCheckItem })
    items?: PreCheckItem[];
}

export class PreCheckReqItem {
    chargeFormItemId?: string;
    doseCount?: number;
    unitCount?: number;
}
export class ChargeRefundPreCheckReq {
    chargeSheetId?: string;
    @JsonProperty({ type: Array, clazz: PreCheckReqItem })
    items?: PreCheckReqItem[];
}
