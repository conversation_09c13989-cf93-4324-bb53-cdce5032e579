/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/3/24
 */
import { UrlRoute } from "../url-dispatcher/url-router";
import React from "react";
import { Route } from "../url-dispatcher/router-decorator";
import { URLProtocols } from "../url-dispatcher";

@Route({ path: URLProtocols.CHARGE_TAB })
export class ChargeTabRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        const { ChargeTabPage } = require("./charge-tab-page");
        return <ChargeTabPage />;
    }
}

@Route({ path: URLProtocols.CHARGE_SHEET })
export class ChargeSheetRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const { ChargeAbcUrlUtils } = require("./charge-abc-url-utils");
        return ChargeAbcUrlUtils.actionToPage(action);
    }
}
