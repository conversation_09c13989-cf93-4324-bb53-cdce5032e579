/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/19
 *
 * @description
 */
import { BaseComponent } from "../base-ui/base-component";
import { Text, View } from "@hippy/react";
import React from "react";
import { GetPushOrderRsp } from "./data/charge-agent";
import { OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { ChargeConfig, ChargeInvoiceDetailData } from "./data/charge-beans";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { SizedBox, Spacer } from "../base-ui";
import { ABCUtils } from "../base-ui/utils/utils";
import { ChargePayAdjustFeeDialog } from "./charge-pay-adjust-fee-dialog";
import { DialogBuilder, DialogIndex } from "../base-ui/dialog/dialog-builder";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ValueHolder } from "../base-ui/utils/value-holder";
import { runtimeConstants } from "../data/constants/runtime-constants-manager";
import abcI18Next from "../language/config";

interface ChargeSendDialogProps {
    pushOrder: GetPushOrderRsp;
    chargeData: ChargeInvoiceDetailData;
    wxBindStatus?: boolean;
}

export class ChargeSendDialog extends BaseComponent<ChargeSendDialogProps> {
    static show(pushOrder: GetPushOrderRsp, chargeData: ChargeInvoiceDetailData, wxBindStatus?: boolean): Promise<number | undefined> {
        const dialogHolder = new ValueHolder<ChargeSendDialog | null>();
        const builder = new DialogBuilder();
        builder.content = (
            <ChargeSendDialog
                pushOrder={pushOrder}
                wxBindStatus={wxBindStatus}
                chargeData={chargeData}
                ref={(ref) => (dialogHolder.value = ref)}
            />
        );
        builder.positive = "确定";
        builder.negative = "取消";
        builder.containerOverflowStyle = undefined;
        builder.positiveTextStyle = pushOrder.isAttention == 0 ? TextStyles.t16MT2 : undefined;
        builder.contentVerticalPadding = 12;
        builder.contentHorizontalPadding = 16;
        builder.onClick = (index) => {
            if (index == DialogIndex.negative) {
                ABCNavigator.pop();
                return;
            }
            if (index == DialogIndex.positive) ABCNavigator.pop(dialogHolder.value!.currentPrice);
        };

        return builder.show({ dialogMaskBg: "rgba(0,0,0,0.9)" });
    }

    currentPrice?: number;

    private _chargeConfig?: ChargeConfig;

    private _srcPrice?: number;

    constructor(props: ChargeSendDialogProps) {
        super(props);

        const _initFromChargeConfig = () => {
            this._srcPrice = parseFloat(this.props.pushOrder.needPay!);
            this.currentPrice = this._srcPrice;
        };
        this._chargeConfig = OnlinePropertyConfigProvider.instance.getChargeConfigSync();
        if (this._chargeConfig) {
            _initFromChargeConfig();
        } else {
            OnlinePropertyConfigProvider.instance
                .getChargeConfig()
                .toObservable()
                .subscribe((config) => {
                    this._chargeConfig = config;
                    _initFromChargeConfig();
                    this.setState({});
                })
                .addToDisposableBag(this);
        }
    }

    render(): JSX.Element {
        const { pushOrder, wxBindStatus } = this.props;
        const bargain = this._chargeConfig?.bargainSwitch == 1;
        return (
            <View style={{ alignItems: "center" }}>
                <View
                    style={{
                        width: 42,
                        height: 42,
                        borderRadius: 21,
                        backgroundColor: Colors.P4,
                        marginTop: 12,
                        marginBottom: 16,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <AssetImageView name={"push"} style={{ width: 24, height: 24 }} />
                </View>

                <Text style={[TextStyles.t18MT1, { lineHeight: Sizes.dp28 }]}>推送支付订单</Text>

                <Text style={{ ...TextStyles.t14NT2, marginTop: Sizes.dp3, marginBottom: Sizes.dp12 }}>
                    {`通过${runtimeConstants.PRODUCT_NAME_ONLINE}公众号，推送支付订单到患者微信`}
                </Text>

                <View style={[ABCStyles.rowAlignCenter, { alignSelf: "flex-end" }]}>
                    {bargain && <Spacer />}
                    <Text style={[TextStyles.t18MY2, { lineHeight: Sizes.dp28 }]}>
                        {abcI18Next.t("¥")}
                        {ABCUtils.formatPrice(this.currentPrice!)}
                    </Text>

                    {bargain && (
                        <View
                            style={{
                                marginHorizontal: 25,
                                padding: 6,
                                backgroundColor: Colors.white,
                                borderRadius: 2,
                                borderColor: Colors.P1,
                                borderWidth: 1,
                            }}
                            onClick={() => this._onAdjustmentFeeTap()}
                        >
                            <Text style={TextStyles.t14NT1}>整单议价</Text>
                        </View>
                    )}
                </View>

                <SizedBox height={30} />
                <View style={{ padding: 10, borderRadius: 3, backgroundColor: "#F8F9FB" }}>
                    <Text style={{ ...TextStyles.t12NT1, lineHeight: Sizes.dp16 }}>订单待支付通知</Text>
                    <SizedBox height={Sizes.dp4} />
                    <Text style={{ ...TextStyles.t12NT2, lineHeight: Sizes.dp16 }}>{pushOrder.nowTime ?? ""}</Text>

                    <Text style={{ ...TextStyles.t12NT2, paddingVertical: Sizes.dp10, lineHeight: Sizes.dp16 }}>
                        {pushOrder.detail ?? ""}
                    </Text>

                    <Text style={{ ...TextStyles.t14NT2, lineHeight: Sizes.dp16 }}>{`订单项目：${pushOrder.medicalItems}`}</Text>
                    <SizedBox height={Sizes.dp4} />
                    <Text style={{ ...TextStyles.t14NT2, lineHeight: Sizes.dp16 }}>{`支付金额：${ABCUtils.formatPrice(
                        this.currentPrice!
                    )}元`}</Text>
                    <SizedBox height={Sizes.dp4} />
                    <Text style={{ ...TextStyles.t14NT2, lineHeight: Sizes.dp16 }}>{`订单日期：${pushOrder.time}`}</Text>
                    <SizedBox height={Sizes.dp4} />
                    <Text style={{ ...TextStyles.t14NT2, lineHeight: Sizes.dp16 }}>{pushOrder.remark ?? ""}</Text>
                </View>

                <SizedBox height={12} />
                {!wxBindStatus && (
                    <Text style={TextStyles.t12NY2}>
                        {`患者需关注${runtimeConstants.PRODUCT_NAME_ONLINE}公众号并注册微信就诊卡，方可推送`}
                    </Text>
                )}
            </View>
        );
    }

    private async _onAdjustmentFeeTap() {
        //原价为凑整抹零后的价格
        const currentPrice = await ChargePayAdjustFeeDialog.show({
            // receivableSrcPrice: this._srcPrice! - (this.props.chargeData.chargeSheetSummary?.adjustmentFee ?? 0),
            receivableSrcPrice: this.props.chargeData.chargeSheetSummary?.afterRoundingDiscountedTotalFee ?? 0,
            currentPrice: this.currentPrice!,
            inputAutoFocus: true,
        });

        if (currentPrice == null) return;

        this.currentPrice = currentPrice;
        this.setState({});
    }
}
