/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import {
    CalculateLockFlag,
    ChargeConfig,
    ChargeForm,
    ChargeFormItem,
    ChargeInvoiceDetailData,
    ChargeOweCombinePaidRsp,
    ChargeOweSheets,
    ChargeOweSinglePaidRsp,
    ChargePatientCards,
    ChargePayData,
    ChargeStatus,
    DispensingSettingConfig,
    OweSheetItems,
    PatientChainProperty,
    PayConfigType,
    PayMethod,
    WechatpayConfig,
    WeChatPaySwitch,
} from "./data/charge-beans";
import { ABCPayMethod } from "./view/paymethod";
import { actionEvent, EventName } from "../bloc/bloc";
import { Subject } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { userCenter } from "../user-center";
import { OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { ABCError } from "../common-base-module/common-error";
import _ from "lodash";
import { PatientAgent } from "../base-business/data/patient-agent";
import { Toast } from "../base-ui/dialog/toast";
import { ABCUtils } from "../base-ui/utils/utils";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { AbcRetailChargeRsp, ChargeAgent, ChargePayRsp, ChargeRenewpaidRsp } from "./data/charge-agent";
import { errorSummary } from "../common-base-module/utils";
import { ChargePayAdjustFeeDialog } from "./charge-pay-adjust-fee-dialog";
import { ABCNavigator, TransitionType } from "../base-ui/views/abc-navigator";
import { URLProtocols } from "../url-dispatcher";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { ChargeAirPharmacyPayPage } from "./charge-air-pharmacy-pay-page";
import { GoodsInfo } from "../base-business/data/beans";
import { GoodsAgent, QueryGoodsListItem, SearchGoodsByIdsReq } from "../data/goods/goods-agent";
import { ShebaoAgent } from "../base-business/mix-agent/shebao-agent";
import { ClinicShebaoConfig } from "../base-business/mix-agent/data/shebao-bean";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { ChargeUtils } from "../charge/utils/charge-utils";
import { AbcMap } from "../base-ui/utils/abc-map";
import { ApiMixService } from "../data/ApiMixService";
import { AbcPayIntegrationMode, ScanOptionProps } from "./view/abc-pay-integration-mode";
import { showConfirmDialog, showDialog } from "../base-ui/dialog/dialog-builder";
import { ChargePayMemberPwdDialog } from "./view/charge-pay-member-pwd-dialog";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { ChargeRefundDialog } from "./view/charge-refund-dialog";
import { PharmacyDataAgent } from "../pharmacy/data/pharmacy-data-agent";
import { PharmacyType } from "./data/charge-bean-air-pharmacy";
import { ABCApiError } from "../net";
import { BatchesInsufficientInventory } from "../base-business/drug-batches-insufficient-inventory/batches-beans";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { DrugBatchesInsufficientInventoryDialog } from "../base-business/drug-batches-insufficient-inventory/drug-batches-insufficient-inventory-dialog";
import { InventoryCheckAgent } from "../inventory/inventory-check/data/inventory-check-agent";

//收费后自动发药开关
const KEnableSendDrugSwitchKey = "enableSendDrugWhenCharge";

class DispensingStatus {
    static noMedicine = -1; //收费单里未包含需要发送的项目
    static waiting = 0; //待发
    static dispensed = 1; //已发
    static undispensed = 2; //已退
    static canceled = 3; //取消
}

class State {
    chargePayData?: ChargePayData;

    //当前收费单支持抵扣的会员卡项
    patientCardList?: ChargePatientCards[];

    selectPayMethod?: ABCPayMethod;
    methodBalance?: Map<number, number>;
    payMethods?: ABCPayMethod[]; //当前诊所支持的支付方式
    disableMethods?: Set<number>; //不能使用的支付方式（可能会显示）

    canSendDrug = false; //只负责同时发药按钮是否显示隐藏，不参与逻辑判断
    enableSendDrug = false;
    cashPrice?: number;
    receivableSrcPrice?: number;
    receivablePrice?: number;
    otherAdjustmentFee?: number = 0.0; //其它模块直接设置的议价
    totalPrice?: number;
    memberCardBalance?: number;

    initing = true; //正在初始化
    initError: any;
    calculatingCharge = false; // 正在计算费用

    init = false;

    chargeConfig?: ChargeConfig;

    shebaoConfig?: ClinicShebaoConfig;

    wechatpayConfig?: WechatpayConfig; // ABC支付相关配置

    isRepayment = false; //是否是欠费还款
    chargeOweSheets?: ChargeOweSheets[]; //欠费还款列表
    isSingleOweCharge?: boolean; //是否是单个欠费还款
    isHideOweMethod?: boolean; //是否隐藏欠费支付方式
    totalOweFee?: number; //当前欠费金额
    // 零头处理规则对应的金额
    oddFeePrice?: number;
    // 系统抹零展示
    get systemAutoOddText(): string {
        if (this.chargeConfig?.isSystemAutoOdd && this.oddFeePrice) {
            let str = `${this.chargeConfig?.sysAdjustmentDesc}：`;
            str += this.oddFeePrice >= 0 ? "+" : "-";
            str += ABCUtils.formatMoney(Math.abs(this.oddFeePrice));
            return str;
        }
        return "";
    }

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    chargeComment?: string; // 收费时录入备注

    clone(): State {
        return Object.assign(new State(), this);
    }

    // 判断是否开通通联支付
    get isOpenAllinpay(): boolean {
        return this.wechatpayConfig?.weChatPaySwitch == WeChatPaySwitch.normal && this.wechatpayConfig?.type == PayConfigType.allinpay;
    }

    //判断是否开启欠费支付方式
    get isOpenOwePay(): boolean {
        return this.chargeConfig?.oweSheetSwitch == 1;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventUpdatePayMethod extends _Event {
    payMethod: ABCPayMethod;

    constructor(payMethod: ABCPayMethod) {
        super();

        this.payMethod = payMethod;
    }
}

class _EventUpdateCashPrice extends _Event {
    cashPrice: number;

    constructor(cashPrice: number) {
        super();
        this.cashPrice = cashPrice;
    }
}

class _EventToggleSendDrugSwitch extends _Event {
    on: boolean;

    constructor(on: boolean) {
        super();
        this.on = on;
    }
}

class _EventTipsNotPaymentReason extends _Event {
    payMethod: ABCPayMethod;
    constructor(payMethod: ABCPayMethod) {
        super();
        this.payMethod = payMethod;
    }
}

class _EventUpdateChargeComment extends _Event {
    chargeComment: string;

    constructor(chargeComment: string) {
        super();
        this.chargeComment = chargeComment;
    }
}

class _EventCharge extends _Event {
    callback?(): void;
    requestCallback?(): void;

    constructor(callback?: () => void, requestCallback?: () => void) {
        super();
        this.callback = callback;
        this.requestCallback = requestCallback;
    }
}

class _EventAdjustmentFee extends _Event {}

class ChargePayPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ChargePayPageBloc | undefined>(undefined);

    static fromContext(context: ChargePayPageBloc): ChargePayPageBloc {
        return context;
    }

    chargePayData: ChargePayData;
    chargeDetailData?: ChargeInvoiceDetailData;
    hasPartedPaid?: boolean;

    initTrigger = new Subject<number>();
    dispensingSetting?: DispensingSettingConfig;
    // 算费trigger
    calculateTrigger = new Subject<void>();

    constructor(options: {
        chargePayData: ChargePayData;
        chargeDetailData?: ChargeInvoiceDetailData;
        hasPartedPaid?: boolean;
        isRepayment?: boolean;
        ChargeOweSheets?: ChargeOweSheets[];
        isSingleOweCharge?: boolean;
        isHideOweMethod?: boolean;
    }) {
        super();

        this.chargePayData = options.chargePayData;
        this.chargeDetailData = options.chargeDetailData;
        this.hasPartedPaid = options.hasPartedPaid;
        this.innerState.isRepayment = options.isRepayment ?? false;
        this.innerState.chargeOweSheets = options.ChargeOweSheets;
        this.innerState.isSingleOweCharge = options.isSingleOweCharge;
        this.innerState.isHideOweMethod = options.isHideOweMethod ?? false;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdatePayMethod, this._mapEventUpdatePayMethod);
        map.set(_EventUpdateCashPrice, this._mapEventUpdateCashPrice);
        map.set(_EventToggleSendDrugSwitch, this._mapEventToggleSendDrugSwitch);
        map.set(_EventCharge, this._mapEventCharge);
        map.set(_EventAdjustmentFee, this._mapEventAdjustmentFee);

        return map;
    }

    private _initPageTrigger(): void {
        this.calculateTrigger
            .pipe(
                debounceTime(300),
                switchMap(() => {
                    this.innerState.calculatingCharge = true;
                    return ChargeUtils.calculatingPrice(
                        JsonMapper.deserialize(ChargeInvoiceDetailData, {
                            ...this.innerState.chargePayData?.chargeInvoiceDetailData,
                            _payMode: this.innerState.selectPayMethod?.method,
                        })
                    )
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.calculatingCharge = false;
                if (rsp instanceof ABCError) {
                } else {
                    this.innerState.receivablePrice = rsp?.receivableFee ?? 0;
                    this.innerState.cashPrice = rsp?.receivableFee ?? 0;
                    this.innerState.receivableSrcPrice = rsp?.receivableFee ?? 0;
                    this.innerState.oddFeePrice = rsp?.oddFee ?? 0; // 零头处理规则对应的金额
                    this.update();
                }
            })
            .addToDisposableBag(this);
    }

    private async *_mapEventInit(/*ignored: _EventInit*/): AsyncGenerator<State> {
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        // 判断收费详情是否存在，若存在，判断是第一次收费还是继续收费，若是第一次收费（则需要先走算费接口，获取卡项其他支付方式）
        // 若是继续收费（需要根据列表详情id去调用卡项支付方式接口），然后在拼凑参数，赋值给chargePayData
        if (!_.isEmpty(this.chargeDetailData)) {
            if (this.hasPartedPaid) {
                const otherCard = await ChargeAgent.getChargePatientCard(this.chargeDetailData?.id ?? "").catchIgnore();
                // 拼凑其他卡项支付方式
                if (!_.isEmpty(otherCard)) {
                    this.chargeDetailData!.canPaidPatientCards = otherCard;
                }
            }
            // 拼凑收费需要的参数
            const payData = this.chargePayData;
            payData.reCharge = false;
            payData.chargeInvoiceDetailData = this.chargeDetailData;
            payData.selects = new AbcMap<ChargeForm, ChargeFormItem[]>();

            payData.promotions = this.chargeDetailData?.promotions;
            payData.receivableFee = this.chargeDetailData?.chargeSheetSummary?.needPayFee;
            payData.totalFee = this.chargeDetailData?.chargeSheetSummary?.totalFee;
            payData.adjustmentFee = this.chargeDetailData?.chargeSheetSummary?.adjustmentFee;
            payData.memberId = this.chargeDetailData?.memberId;

            this.innerState.chargePayData = payData;
        } else {
            if (this.hasPartedPaid) {
                const otherCard = await ChargeAgent.getChargePatientCard(
                    this.chargePayData.chargeInvoiceDetailData?.id ?? ""
                ).catchIgnore();
                // 拼凑其他卡项支付方式
                if (!_.isEmpty(otherCard)) {
                    this.chargePayData.chargeInvoiceDetailData!.canPaidPatientCards = otherCard;
                }
            }
            this.innerState.chargePayData = this.chargePayData;
        }
        if (!this.hasPartedPaid) {
            this._calculateDesignationBatch(true);
            const rsp = await ChargeUtils.calculatingPrice(
                JsonMapper.deserialize(ChargeInvoiceDetailData, { ...this.innerState.chargePayData?.chargeInvoiceDetailData })
            ).catchIgnore();
            // 拼凑其他卡项支付方式
            if (!_.isEmpty(this.chargeDetailData) && !_.isEmpty(rsp?.canPaidPatientCards)) {
                this.chargeDetailData!.canPaidPatientCards = rsp?.canPaidPatientCards;
            }
        }

        this.innerState.methodBalance = new Map();
        this.innerState.disableMethods = new Set();

        //当前收费项中含有代煎中心(只针对本地药房)的处方,收费时不展示同时发药选项
        const chargeForm = this.innerState.chargePayData?.chargeInvoiceDetailData?.chargeForms?.filter((t) => !t.pharmacyType),
            pharmacyListConfig = this.innerState.pharmacyInfoConfig?.pharmacyList;
        let isHideDispenseBtn = false;
        for (const sub of pharmacyListConfig ?? []) {
            for (const item of chargeForm ?? []) {
                //只有中药处方，比较外层就行，其他处方，需要比较内层的formItem
                if (item.isChinesePrescription) {
                    if (item?.pharmacyNo == sub?.no) {
                        isHideDispenseBtn = sub.externalPharmacyConfig?.chargeDisableAutoDispense == 1;
                        if (isHideDispenseBtn) break;
                    }
                } else {
                    for (const subItem of item?.chargeFormItems ?? []) {
                        if (subItem?.pharmacyNo == sub?.no) {
                            isHideDispenseBtn = sub.externalPharmacyConfig?.chargeDisableAutoDispense == 1;
                            if (isHideDispenseBtn) break;
                        }
                    }
                }
            }
        }
        //收费时同时发药配置
        const dispensingSetting = await ChargeAgent.getChargePaidDispensingSetting().catchIgnore(),
            switchSetting = dispensingSetting?.paid?.sameTimeDispensing;
        //switchSetting初始化为null，此时读取本地存储的发药设置，如果为0，1则读取线上配置
        this.innerState.enableSendDrug = !_.isNil(switchSetting)
            ? !!switchSetting
            : clinicSharedPreferences.getBool(KEnableSendDrugSwitchKey) ?? false;
        //含有代煎中心的单子，收费时不展示同时发药按钮，相当于不能发药，虽然隐藏了按钮，但是有个记忆上次能否发药状态的功能所以需要强制设置为false
        this.innerState.enableSendDrug = isHideDispenseBtn ? false : this.innerState.enableSendDrug;

        //获取欠费收费方式下，当前欠费金额
        const patientId =
            this.innerState.chargePayData.chargeInvoiceDetailData?.patientId ??
            this.innerState.chargePayData.chargeInvoiceDetailData?.patient?.id ??
            "";
        const result = await ChargeAgent.getOweStayPaidList(patientId).catchIgnore();
        this.innerState.totalOweFee = result?.totalOweFee ?? 0;

        this.initTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.initing = true;
                    this.innerState.initError = undefined;
                    this.update();

                    return Promise.all([
                        ShebaoAgent.getClinicShebaoConfig(),
                        OnlinePropertyConfigProvider.instance.getChargeConfig(false),
                        ApiMixService.getWeChatPayConfig(),
                    ])
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(async (rsp) => {
                this.innerState.initing = false;
                if (rsp instanceof ABCError) {
                    this.innerState.initError = rsp.detailError;
                } else {
                    this.innerState.shebaoConfig = rsp[0] as ClinicShebaoConfig;
                    this.innerState.chargeConfig = rsp[1];
                    this.innerState.wechatpayConfig = rsp[2];
                    this.innerState.payMethods = ABCPayMethod.methodsFromChargeConfig(this.innerState.chargeConfig);
                    //若存在其他收费卡项支付方式，则需要追加到支付方式中
                    const canPaidPatientCard =
                        this.innerState.chargePayData?.chargeInvoiceDetailData?.canPaidPatientCards?.filter(
                            (t) => (t.payModeId = PayMethod.payThirdCardPay)
                        ) ?? [];
                    if (!_.isEmpty(canPaidPatientCard)) {
                        canPaidPatientCard.forEach((item) => {
                            this.innerState?.payMethods!.push({
                                method: Number(item.id),
                                title: item?.name ?? "",
                                icon: "",
                                isCard: true,
                                ...item,
                            });
                        });
                    }
                    //计算收费单里是否包含需要发送的药品，以显示或隐藏发药开关
                    let hasMedicine = false;
                    //过滤掉空中药房pharmacyType==1的处方(空中药房没有同时发药)
                    const selects = new AbcMap<ChargeForm, ChargeFormItem[]>();
                    this.innerState.chargePayData?.selects?.forEach((value, key) => {
                        if (key?.pharmacyType != PharmacyType.air) selects.set(key, value);
                    });

                    function isMedicine(goodsInfo: GoodsInfo): boolean {
                        return (
                            goodsInfo.isChineseMedicine ||
                            goodsInfo.isWesternMedicine ||
                            goodsInfo.isChineseWesternMedicine ||
                            goodsInfo.isMedicalMaterial ||
                            goodsInfo.isGoods
                        );
                    }

                    if (selects != undefined) {
                        for (const form of selects.keys()) {
                            //空中药房没有同时发药
                            const formItems = selects.get(form)?.filter((t) => t.pharmacyType != PharmacyType.air);
                            if (_.isEmpty(formItems)) continue;

                            for (const formItem of formItems!) {
                                if (isMedicine(formItem.goodsInfo)) {
                                    hasMedicine = true;
                                    break;
                                }

                                //检测套餐里否包含药品
                                if (formItem.isPackage) {
                                    for (const child of formItem.goodsInfo.children ?? []) {
                                        if (isMedicine(child)) {
                                            hasMedicine = true;
                                            break;
                                        }
                                    }
                                }
                            }

                            if (hasMedicine) break;
                        }
                    } else {
                        //零售收费dispensingStatus为undefined，应该默认无药品
                        hasMedicine =
                            !_.isUndefined(this.innerState.chargePayData?.chargeInvoiceDetailData?.dispensingStatus) &&
                            this.innerState.chargePayData?.chargeInvoiceDetailData?.dispensingStatus != DispensingStatus.noMedicine;
                    }

                    this.innerState.canSendDrug = userCenter.clinic!.canDeliverMedicine && hasMedicine && !isHideDispenseBtn;
                    this.innerState.init = true;

                    await this._reloadData();
                }

                this.update();
            })
            .addToDisposableBag(this);

        this._initPageTrigger();

        this.initTrigger.next(0);
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    private async *_mapEventUpdatePayMethod(event: _EventUpdatePayMethod): AsyncGenerator<State> {
        if (event.payMethod.method == PayMethod.payABCPay) {
            if (!this.innerState.isOpenAllinpay) {
                //未开通或者异常
                AbcPayIntegrationMode.pay(this.innerState.isOpenAllinpay);
            }
        }
        AbcTextInput.focusInput?.blur();
        this.innerState.selectPayMethod = event.payMethod;
        // 开启了零头处理规则并且是待收或者草稿时，才调用算费（凑单抹零会根据支付方式进行计算）
        const chargeSheetStatus = this.innerState.chargePayData?.chargeInvoiceDetailData?.status;
        if (
            this.innerState.chargeConfig?.isSystemAutoOdd &&
            (chargeSheetStatus == ChargeStatus.unCharged || chargeSheetStatus == ChargeStatus.draft)
        ) {
            this.calculateTrigger.next();
        }
        this._adjustPayCashPrice();
        yield this.innerState.clone();
    }

    private async *_mapEventUpdateCashPrice(event: _EventUpdateCashPrice): AsyncGenerator<State> {
        this.innerState.cashPrice = event.cashPrice;
        //如果是部分收费，则不允许同时发药
        if (this.innerState.cashPrice < (this.innerState.receivablePrice ?? 0)) {
            this.innerState.canSendDrug = false;
        } else {
            this.innerState.canSendDrug = true;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventToggleSendDrugSwitch(event: _EventToggleSendDrugSwitch): AsyncGenerator<State> {
        this.innerState.enableSendDrug = event.on;
        clinicSharedPreferences.setBool(KEnableSendDrugSwitchKey, event.on);
        //更新收费时发药配置
        await ChargeAgent.setChargePaidDispensingSetting({
            paid: { sameTimeDispensing: this.innerState.enableSendDrug ? 1 : 0 },
        }).catchIgnore();
        yield this.innerState.clone();
    }

    // 筛选出含有追溯码的药品
    private _filterTraceCodeGoods(invoiceDetailData: ChargeInvoiceDetailData): ChargeFormItem[] {
        // 判断invoiceDetailData.chargeForms中的chargeFormItems中是否存在traceableCodeList数据，套餐检验chargeFormItems中的composeChildren中的traceableCodeList数据
        // 含有追溯码药品集合
        const flattenedItems: ChargeFormItem[] = [];
        for (const form of invoiceDetailData?.chargeForms ?? []) {
            for (const item of form.chargeFormItems ?? []) {
                const goodsId = item.productId ?? item.goodsInfo?.id ?? item.goodsInfo?.goodsId;
                if (!goodsId) continue;
                // 如果是套餐，只添加套餐子项目，不添加套餐本身
                if (item.isPackage && item.composeChildren?.length) {
                    item.composeChildren.forEach((subItem) => {
                        if (!!subItem.traceableCodeList?.length) flattenedItems.push(subItem);
                    });
                } else {
                    // 非套餐项目直接添加
                    if (!!item.traceableCodeList?.length) flattenedItems.push(item);
                }
            }
        }
        return flattenedItems;
    }
    // 组装查询药品具体的盘点信息的请求参数
    private combineQueryStocksReq(needQueryStockGoods: ChargeFormItem[]): SearchGoodsByIdsReq {
        // 组装数据去拉取goods接口
        const req = new SearchGoodsByIdsReq();
        req.queryGoodsList = needQueryStockGoods?.map((item) => {
            let packageCount;
            let pieceCount;
            if (item.isChineseMedicine) {
                pieceCount = item.unitCount;
            } else {
                if (item.unit == item.goodsInfo?.packageUnit) {
                    packageCount = item.unitCount;
                } else {
                    pieceCount = item.unitCount;
                }
            }
            return JsonMapper.deserialize(QueryGoodsListItem, {
                stockCheckSuggestBatches: 1,
                pharmacyType: item.pharmacyType,
                pharmacyNo: item.pharmacyNo,
                goodsIds: [
                    {
                        goodsId: item.productId ?? item.goodsInfo?.id ?? item.goodsInfo?.goodsId,
                        keyId: item.keyId,
                        pieceCount: pieceCount,
                        packageCount: packageCount,
                        noList: item.traceableCodeList?.map((t) => t.no ?? "") ?? [],
                    },
                ],
            });
        });
        return req;
    }

    // 收费同时发药
    // 检查goods是否需要盘点批次、注意下范围：收费单里面有追溯码
    private async verifyNeedCheckByTraceCode(invoiceDetailData: ChargeInvoiceDetailData): Promise<boolean> {
        const needQueryStockGoods = this._filterTraceCodeGoods(invoiceDetailData);
        if (!needQueryStockGoods?.length) return true;
        // 组装数据去拉取goods接口
        const req = this.combineQueryStocksReq(needQueryStockGoods);
        // 调用接口获取商品盘点信息
        const goodsInfoList = await GoodsAgent.searchGoodsByIds(req);
        const list = BatchesInsufficientInventory.batchInsufficientList({
            goodsInfoList: goodsInfoList,
            needQueryStockGoods: needQueryStockGoods ?? [],
            type: "charge",
        });
        if (!!list?.length) {
            const result = await showBottomPanel(<DrugBatchesInsufficientInventoryDialog list={list} />);
            if (!result) return false;
            const postReq = BatchesInsufficientInventory.batchInsufficientListForAutoCheck({
                goodsInfoList: goodsInfoList,
                type: "charge",
            });
            if (!postReq) return true;
            const loadingDialog = new LoadingDialog();
            loadingDialog.show(100);
            try {
                const res = await InventoryCheckAgent.postGoodsStocksCheckOrders(postReq);
                if (res) {
                    await loadingDialog.success("提交成功");
                    return true;
                }
            } catch (error) {
                await showConfirmDialog("提交失败", error?.detail?.error?.message);
                return false;
            } finally {
                await loadingDialog.hide();
            }
        }
        return true;
    }

    private async *_mapEventCharge(event: _EventCharge): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (innerState.selectPayMethod == undefined) {
            await Toast.show("请选择支付方式", { warning: true });
            return;
        }

        if (innerState.cashPrice! > innerState.receivablePrice! && innerState.selectPayMethod?.method != PayMethod.payCash) {
            await Toast.show(`金额不能大于应收金额${ABCUtils.formatPrice(innerState.receivablePrice!)}`, { warning: true });
            return;
        }

        const chargePayData = innerState.chargePayData!;
        const invoiceDetailData = chargePayData.chargeInvoiceDetailData;

        const _chargeStatus = chargePayData.chargeInvoiceDetailData?.status;
        const feeAdjustment =
            _chargeStatus == ChargeStatus.unCharged || _chargeStatus == ChargeStatus.draft
                ? innerState.receivablePrice! - innerState.receivableSrcPrice! + innerState.otherAdjustmentFee!
                : 0;
        const cashDiff = innerState.cashPrice! - innerState.receivablePrice!;
        innerState.cashPrice = Number(ABCUtils.formatMoney(innerState.cashPrice));
        innerState.receivablePrice = Number(ABCUtils.formatMoney(innerState.receivablePrice));

        //如果当前选择的收费方式为会员卡支付，需要判断是否需要输入会员卡密码
        let memberPassword = "";
        if (innerState.selectPayMethod?.method == PayMethod.payMemberCard) {
            const memberPwdConfig = await ApiMixService.getProperty("chain", "", "crm", PatientChainProperty).catchIgnore();
            if (memberPwdConfig?.crm?.patient?.enableMemberPassword == 1) {
                const memberInfo = await ChargeAgent.getMemberInfo(invoiceDetailData?.memberId ?? "").catchIgnore();
                const memberInfoDisplay = {
                    name: memberInfo?.name,
                    mobile: memberInfo?.memberInfo?.memberCardId,
                    amount: innerState.cashPrice,
                };
                memberPassword = await showDialog(
                    <ChargePayMemberPwdDialog memberId={invoiceDetailData?.memberId ?? ""} memberInfoDisplay={memberInfoDisplay} />,
                    {
                        dialogMaskBg: "rgba(0,0,0,0.6)",
                    }
                );
                if (!memberPassword) return;
            }
        }
        // 方案调整为：选择医保支付方式才需要去查询批次信息，app不支持医保支付方式，暂时注释掉
        // 如果收费单中药品含有追溯码，并且勾选了同时发药，需要拉取goods数据，校验当前药品是否存在批次库存不足，能否自动盘点发药
        // if (invoiceDetailData && this.innerState.enableSendDrug) {
        //     const isNeedCheck = await this.verifyNeedCheckByTraceCode(invoiceDetailData);
        //     if (!isNeedCheck) return;
        // }

        const dialog = new LoadingDialog(`正在${innerState.isRepayment ? "还款" : "收费"}`);
        dialog.show();

        const self = this;

        /**
         * 部份收份等，返回后需要更新界面
         * @param rsp
         * @param dispense
         * @param method
         * @returns true 未完成收费，需要刷新界面
         */
        async function handlePayRsp(
            rsp: ChargePayRsp | ChargeRenewpaidRsp | AbcRetailChargeRsp | ChargeOweSinglePaidRsp | ChargeOweCombinePaidRsp | ABCError,
            dispense: boolean,
            method: PayMethod
        ): Promise<boolean | undefined> {
            if (rsp instanceof ABCError) {
                await dialog.hide();
                await ChargeRefundDialog.showConfirmPopup({
                    logo: "image_dlg_fail",
                    title: `${self.innerState.isRepayment ? "还款" : "收费"}失败`,
                    content: `${errorSummary(rsp)}`,
                });
                return;
            }
            // 清空备注
            self.innerState.chargeComment = undefined;
            //ABC扫码支付，需要轮询接口，判断当前支付状态
            let paySuccess = false,
                _checkCount = 0;
            const newDate = new Date().getTime();
            if (method == PayMethod.payABCPay) {
                dialog.dialogShowState?.subscribe((status) => {
                    //弹窗异常关闭（用户返回）的时候，才退出此次循环
                    if (status == false) {
                        paySuccess = true;
                    }
                });
                do {
                    if (new Date().getTime() - newDate - _checkCount * 1000 < 1000) {
                        continue;
                    }
                    if (_checkCount > 180) {
                        await dialog.hide();
                        await ChargeRefundDialog.showConfirmPopup({
                            logo: "image_dlg_fail",
                            title: "支付失败",
                            content: "",
                        });
                        paySuccess = true;
                        return;
                    }
                    //还款时选择ABC支付，查询第三方支付订单状态
                    if (self.innerState.isRepayment && (rsp instanceof ChargeOweCombinePaidRsp || rsp instanceof ChargeOweSinglePaidRsp)) {
                        await ChargeAgent.queryCombineOrderPayStatus(rsp.combineOrderPayTransactionId!)
                            .then(async (result) => {
                                if (result.payStatus == 2) {
                                    paySuccess = true;
                                }
                                if (result.payStatus == 3) {
                                    throw result?.message;
                                }
                            })
                            .catch(async (mes) => {
                                await dialog.hide();
                                await ChargeRefundDialog.showConfirmPopup({
                                    logo: "image_dlg_fail",
                                    title: typeof mes == "string" ? mes : "支付失败",
                                    content: "",
                                });
                                paySuccess = true;
                                return;
                            })
                            .finally(() => {
                                _checkCount++;
                            });
                    } else {
                        if (!(rsp instanceof ChargeOweCombinePaidRsp) && !(rsp instanceof ChargeOweSinglePaidRsp))
                            await ChargeAgent.checkChargePayStatus(rsp.chargePayTransactionId!)
                                .then(async (result) => {
                                    if (result.payStatus == 2) {
                                        paySuccess = true;
                                        // 支付状态是异步的，所以需要在收费成功后，再调用一个详情接口，避免收费详情数据更新不及时，导致支付报错
                                        if (!!result?.chargeSheetId) {
                                            const updateDetailData = await ChargeAgent.getChargeInvoiceDetail(
                                                result.chargeSheetId
                                            ).catchIgnore();
                                            if (!!updateDetailData) {
                                                // 给rsp赋值是因为下面还有rsp给chargeInvoiceDetailData赋值，为了避免影响原有逻辑，先在此处进行处理
                                                rsp.status = updateDetailData.status;
                                                rsp.statusName = updateDetailData.statusName;
                                                chargePayData.chargeInvoiceDetailData = updateDetailData;
                                            }
                                        }
                                    }
                                    if (result.payStatus == 3) {
                                        throw result?.message;
                                    }
                                })
                                .catch(async (mes) => {
                                    await dialog.hide();
                                    await ChargeRefundDialog.showConfirmPopup({
                                        logo: "image_dlg_fail",
                                        title: typeof mes == "string" ? mes : "支付失败",
                                        content: "",
                                    });
                                    paySuccess = true;
                                    return;
                                })
                                .finally(() => {
                                    _checkCount++;
                                });
                    }
                } while (!paySuccess);
            }

            if ((rsp instanceof ChargePayRsp || rsp instanceof ChargeRenewpaidRsp) && dispense) {
                await dialog.success("收费成功");
                //如果是收费同时发药，还是先调收费接口，如果收费成功，再轮询发药的接口查询发药是否成功，
                // 当然要设置一个最大轮询时间，这个感觉设置个5秒或者10秒感觉就够了
                let dispenseStatus = false,
                    _checkStatusCount = 0;
                const dispenseNewDate = new Date().getTime();
                const dispenseDialog = new LoadingDialog("正在发药");
                dispenseDialog.show();

                dispenseDialog.dialogShowState?.subscribe((status) => {
                    //弹窗异常关闭（用户返回）的时候，才退出此次循环
                    if (status == false) {
                        dispenseStatus = true;
                    }
                });
                do {
                    if (new Date().getTime() - dispenseNewDate - _checkStatusCount * 1000 < 1000) {
                        continue;
                    }
                    if (_checkStatusCount > 6) {
                        await dispenseDialog.hide();
                        dispenseStatus = true;
                        break;
                    }
                    //查询发药单状态
                    await PharmacyDataAgent.queryDispensingStatus(rsp.id!)
                        .then(async (rsp) => {
                            if (rsp?.status == DispensingStatus.dispensed) {
                                await dispenseDialog.success("发药成功");
                                dispenseStatus = true;
                            }
                        })
                        .catch(async (error) => {
                            if (error instanceof ABCApiError) {
                                // 84109就是没有发药单，还需要继续轮询，其他错误则抛出
                                if (error?.detail?.error?.code != "84109") {
                                    dispenseStatus = true;
                                }
                                // 只针对84108才抛出发药失败
                                if (error?.detail?.error?.code == "84108") {
                                    const { name = "" } = self.innerState.chargePayData?.chargeInvoiceDetailData?.patient || {};
                                    await dispenseDialog.hide();
                                    await ChargeRefundDialog.showConfirmPopup({
                                        logo: "image_dlg_fail",
                                        title: "同时发药失败",
                                        content: `患者${!!name ? "【" + name + "】" : ""}同时发药失败。请前往药房发药`,
                                    });
                                    return;
                                }
                            }
                        })
                        .finally(() => {
                            _checkStatusCount++;
                        });
                } while (!dispenseStatus);
            } else {
                await dialog.success(`${self.innerState.isRepayment ? "还款" : "收费"}成功`);
            }

            //有欠费单未还清的患者支付新的收费单完成时提示（仅在第一次支付该收费单时提示）
            // const isFirstPay = _chargeStatus == ChargeStatus.unCharged || _chargeStatus == ChargeStatus.draft;
            // if (rsp instanceof ChargePayRsp && isFirstPay) {
            //     const patientId =
            //         self.innerState?.chargePayData?.chargeInvoiceDetailData?.patientId ??
            //         self.innerState.chargePayData?.chargeInvoiceDetailData?.patient?.id;
            //     if (!!patientId) {
            //         const result = await ChargeAgent.queryPatientOwingCount(patientId, [rsp?.id ?? ""]).catchIgnore();
            //         const patientName = self.innerState?.chargePayData?.chargeInvoiceDetailData?.patient?.name;
            //         if (result instanceof ABCError) return;
            //         if ((result?.owingCount ?? 0) > 0) {
            //             await showConfirmDialog("欠费提醒", `患者【${patientName}】有欠费单未还，请搜索患者查看欠费情况`, undefined);
            //         }
            //     }
            // }

            //更新收费时发药配置
            await ChargeAgent.setChargePaidDispensingSetting({
                paid: { sameTimeDispensing: self.innerState.enableSendDrug ? 1 : 0 },
            }).catchIgnore();

            if (!(rsp instanceof ChargeOweCombinePaidRsp)) {
                if (rsp.needPay == 0) {
                    //包含空中药房处方
                    if (rsp.isAirPharmacyCanPay == 1) {
                        await ABCNavigator.navigateToPage(<ChargeAirPharmacyPayPage orderIds={rsp.airPharmacyOrderIds!} />, {
                            transitionType: TransitionType.inFromBottom,
                        });
                        return;
                    }
                    if (event.callback) {
                        event.callback();
                    } else ABCNavigator.popUntil(URLProtocols.CHARGE_TAB, URLProtocols.CHARGE_TAB).then();
                } else {
                    chargePayData.chargeInvoiceDetailData!.statusName = rsp.statusName;
                    chargePayData.chargeInvoiceDetailData!.status = rsp.status;
                    chargePayData.receivableFee = rsp.needPay;
                    //部分收费时，需要更新当前单子不为本地草稿单，暂且先设置为1，只要不为0就行,否则会继续调用锁单的情况，导致不能收费
                    chargePayData.chargeInvoiceDetailData!.type = 1;
                    // 如果是本地草稿的单子，此时id为undefined，在收费完成之后，点击返回，拿不到id，会导致收费单页面报错
                    //还款返回的id不是收费单详情id
                    if (!self.innerState.isRepayment) chargePayData.chargeInvoiceDetailData!.id = rsp.id;

                    // 若是挂号页面，在收费结束后，需调用一个挂号详情接口，刷新详情信息
                    if (event.requestCallback) {
                        event.requestCallback();
                    }

                    //还款时，查询卡项流程不需要调用
                    if (!innerState.isRepayment) {
                        //继续收费时，重新拉取当前单子所支持的卡项抵扣价格
                        const thirdCardList = await ChargeAgent.getChargePatientCard(
                            self.innerState.chargePayData?.chargeInvoiceDetailData?.id ?? ""
                        ).catchIgnore();
                        self.innerState.patientCardList = thirdCardList;

                        // 同时需要更新当前所有支付方式中卡项的支付方式
                        const otherCardMethods = self.innerState?.payMethods?.filter((t) => !t.isCard);
                        thirdCardList
                            ?.filter((t) => (t.payModeId = PayMethod.payThirdCardPay))
                            ?.forEach((item) => {
                                otherCardMethods?.push({
                                    method: Number(item.id),
                                    title: item?.name ?? "",
                                    icon: "",
                                    isCard: true,
                                    ...item,
                                });
                            });

                        self.innerState.payMethods = otherCardMethods;
                        //解决---部分收费后，同时发药按钮状态更新
                        self.initTrigger.next();
                    }
                    await self._reloadData();
                    return true;
                }
            } else {
                if (event.callback) {
                    event.callback();
                }
            }
        }

        const enableSendDrug = innerState.canSendDrug ? innerState.enableSendDrug : false;
        let needUpdate: boolean | undefined;
        const selectPayMethod = innerState.selectPayMethod;
        const { isCard, payModeId, method } = selectPayMethod;

        let authCodeOption: ScanOptionProps,
            authCode = "";
        if (method == PayMethod.payABCPay) {
            authCodeOption = await AbcPayIntegrationMode.pay(innerState.isOpenAllinpay);
            if (authCodeOption?.action == "user_cancel") {
                //用户未进行扫码，直接点击返回按钮
                await dialog.hide();
                return;
            } else {
                if (!authCodeOption.barCode) {
                    await dialog.hide();
                    await Toast.show("未识别到付款码,请重新扫码", { warning: true });
                    return;
                }
            }
            authCode = authCodeOption.barCode;
        }
        //收费单为本地草稿时，选择abc支付、会员卡支付、卡项支付时，先锁单且保存，再调用支付接口
        const type = chargePayData.chargeInvoiceDetailData?.type;
        const payMethod = isCard ? payModeId! : method;
        const isRetail = !type; //判断是否为本地草稿

        //会员卡使用状态，默认为使用，有memberId时为指定会员卡，为memberInfo为null时，为不使用会员卡
        let useMemberFlag = 0;
        if (invoiceDetailData?._localMemberInfo === null) {
            useMemberFlag = 20;
        } else if (!!invoiceDetailData?._localMemberInfo) {
            useMemberFlag = 10;
        } else if (!!invoiceDetailData?.useMemberFlag) {
            useMemberFlag = invoiceDetailData?.useMemberFlag;
        }

        if (
            isRetail &&
            (payMethod == PayMethod.payABCPay || payMethod == PayMethod.payMemberCard || payMethod == PayMethod.payThirdCardPay)
        ) {
            const lockSaveResult = await ChargeAgent.retailChargeLockSave({
                detailData: invoiceDetailData,
                payMethod: isCard ? payModeId! : method,
                receivableFee: innerState.receivablePrice,
                cash: innerState.cashPrice,
                cashDiff: cashDiff,
                dispensing: enableSendDrug,
                thirdPartyPayCardId: selectPayMethod?.id,
                authCode: authCode,
                memberPassword: memberPassword,
                chargeComment: innerState.chargeComment,
            }).catch((error) => new ABCError(error));
            if (lockSaveResult instanceof ABCError) {
                return await handlePayRsp(lockSaveResult, enableSendDrug, method);
            }
            const rsp = await ChargeAgent.retailChargeAbcPay({
                chargeSheetId: lockSaveResult!.id!,
                payMethod: isCard ? payModeId! : method,
                receivableFee: innerState.receivablePrice,
                cash: innerState.cashPrice,
                thirdPartyPayCardId: selectPayMethod?.id,
                authCode: authCode,
                memberPassword: memberPassword,
                chargeComment: innerState.chargeComment,
                sellerId: invoiceDetailData?.sellerId,
                sellerDepartmentId: invoiceDetailData?.sellerDepartmentId,
                consultantId: invoiceDetailData?.consultantId,
            }).catch((error) => new ABCError(error));
            needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
        } else {
            //还款
            if (innerState.isRepayment) {
                const oweSheetItems: OweSheetItems[] = [];
                // const actualPrice = 0;
                innerState.chargeOweSheets?.forEach((item) => {
                    oweSheetItems.push(
                        JsonMapper.deserialize(OweSheetItems, {
                            amount: innerState.isSingleOweCharge ? innerState.cashPrice : Number(ABCUtils.formatMoney(item.needPay)),
                            oweSheetId: item.id,
                            receivableFee: innerState.isSingleOweCharge
                                ? innerState.receivablePrice
                                : Number(ABCUtils.formatMoney(item.needPay)),
                        })
                    );
                });
                // oweSheetItems.forEach((item) => (actualPrice += item.amount ?? 0));
                if (innerState.isSingleOweCharge) {
                    //单个还款
                    const rsp = await ChargeAgent.ChargeOweSinglePaid({
                        chargeOweSheetId: innerState.chargeOweSheets![0].id!,
                        patientId: chargePayData.chargeInvoiceDetailData?.patientId ?? chargePayData.chargeInvoiceDetailData?.patient?.id,
                        payMethod: isCard ? payModeId! : method,
                        cash: innerState.cashPrice,
                        thirdPartyPayCardId: selectPayMethod?.id,
                        authCode: authCode,
                        memberPassword: memberPassword,
                        oweSheetItems: oweSheetItems,
                        chargeId: chargePayData.chargeInvoiceDetailData?.id,
                        memberId: innerState.chargeOweSheets![0].memberId,
                        chargeComment: innerState.chargeComment,
                    }).catch((error) => new ABCError(error));
                    needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                } else {
                    //组合还款
                    const rsp = await ChargeAgent.chargeOweCombinePaid({
                        patientId: chargePayData.chargeInvoiceDetailData?.patientId ?? chargePayData.chargeInvoiceDetailData?.patient?.id,
                        payMethod: isCard ? payModeId! : method,
                        cash: innerState.cashPrice,
                        thirdPartyPayCardId: selectPayMethod?.id,
                        authCode: authCode,
                        memberPassword: memberPassword,
                        oweSheetItems: oweSheetItems,
                        memberId: innerState.chargeOweSheets![0].memberId,
                        chargeComment: innerState.chargeComment,
                    }).catch((error) => new ABCError(error));
                    needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                }
            } else {
                //直接收费
                if (_chargeStatus == ChargeStatus.draft) {
                    const rsp = await ChargeAgent.createChargeInvoice({
                        draftChargeInvoiceDetail: chargePayData.chargeInvoiceDetailData,
                        payMode: isCard ? payModeId! : method,
                        amount: innerState.cashPrice,
                        adjustmentFee: feeAdjustment,
                        expectedAdjustmentFee: chargePayData.chargeInvoiceDetailData?.chargeSheetSummary?.expectedAdjustmentFee__,
                        receivableFee: innerState.receivablePrice,
                        sellerId: chargePayData.chargeInvoiceDetailData?.sellerId,
                        dispensing: enableSendDrug,
                        thirdPartyPayCardId: selectPayMethod?.id,
                        authCode: authCode,
                        memberPassword: memberPassword,
                        useMemberFlag,
                        chargeComment: innerState.chargeComment,
                    }).catch((error) => new ABCError(error));
                    needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                } else if (_chargeStatus == ChargeStatus.unCharged) {
                    if (chargePayData.reCharge) {
                        const rsp = await ChargeAgent.putChargeInvoiceRenewpaid({
                            detailData: invoiceDetailData,
                            payMethod: isCard ? payModeId! : method,
                            receivable: innerState.receivablePrice,
                            cash: innerState.cashPrice,
                            cashDiff: cashDiff,
                            dispensing: enableSendDrug,
                            thirdPartyPayCardId: selectPayMethod?.id,
                            authCode: authCode,
                            memberPassword: memberPassword,
                            chargeComment: innerState.chargeComment,
                        }).catch((error) => new ABCError(error));

                        needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                    } else {
                        const rsp = await ChargeAgent.putChargeInvoicePaid({
                            chargeSheet: invoiceDetailData!,
                            payMethod: isCard ? payModeId! : method,
                            receivable: innerState.receivablePrice,
                            cash: innerState.cashPrice,
                            cashDiff: cashDiff,
                            dispensing: enableSendDrug,
                            thirdPartyPayCardId: selectPayMethod?.id,
                            authCode: authCode,
                            memberPassword: memberPassword,
                            useMemberFlag,
                            chargeComment: innerState.chargeComment,
                        }).catch((error) => new ABCError(error));
                        needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                    }
                } else if (_chargeStatus == ChargeStatus.partCharged) {
                    const rsp = await ChargeAgent.putChargeInvoiceRepaid({
                        chargeId: invoiceDetailData!.id!,
                        receivableFee: innerState.receivablePrice!,
                        payMethod: isCard ? payModeId! : method,
                        amount: innerState.cashPrice!,
                        dispensing: enableSendDrug,
                        dataSignature: invoiceDetailData!.dataSignature!,
                        deliveryInfo: invoiceDetailData?.deliveryInfo,
                        thirdPartyPayCardId: selectPayMethod?.id,
                        authCode: authCode,
                        memberPassword: memberPassword,
                        chargeComment: innerState.chargeComment,
                    }).catch((error) => new ABCError(error));
                    needUpdate = await handlePayRsp(rsp, enableSendDrug, method);
                }
            }
        }

        if (needUpdate) yield innerState.clone();
    }

    private async *_mapEventAdjustmentFee(/*ignored: _EventAdjustmentFee*/): AsyncGenerator<State> {
        const fee = await ChargePayAdjustFeeDialog.show({
            receivableSrcPrice: this.innerState.receivableSrcPrice!,
            currentPrice: this.innerState.receivablePrice!,
            inputAutoFocus: false,
        });

        if (fee != undefined) {
            this._setReceivablePrice(fee);
        }

        yield this.innerState.clone();
    }

    @actionEvent(_EventTipsNotPaymentReason)
    async *_mapEventTipsNotPaymentReason(event: _EventTipsNotPaymentReason): AsyncGenerator<State> {
        if (_.isUndefined(event.payMethod.method)) return;
        if (event.payMethod.method == PayMethod.payMemberCard) {
            await Toast.show("欠费单享受了不同会员卡折扣，请组合相同会员卡的欠费单还款", { warning: true });
        }
        this.update();
    }

    @actionEvent(_EventUpdateChargeComment)
    async *_mapEventUpdateChargeComment(event: _EventUpdateChargeComment): AsyncGenerator<State> {
        this.innerState.chargeComment = event.chargeComment ?? "";
        this.update();
    }

    /**
     * 算费时，药品锁库操作
     * @private
     * @param isFixedData --- 是否需要锁库
     */
    private _calculateDesignationBatch(isFixedData: boolean): void {
        const chargeInvoiceDetailData = this.innerState.chargePayData?.chargeInvoiceDetailData;
        chargeInvoiceDetailData?.chargeForms?.forEach((form) => {
            if (form?.chargeFormItems?.length) {
                this._processFormItems(form?.chargeFormItems, isFixedData);
                if (form.chargeFormItems?.some((item) => item.composeChildren)) {
                    form.chargeFormItems?.forEach((item) => {
                        item.isFixedData = isFixedData ? CalculateLockFlag.lock : CalculateLockFlag.unlock;
                        if (item.composeChildren) {
                            this._processFormItems(item.composeChildren, isFixedData);
                        }
                    });
                }
            }
        });
    }
    private _processFormItems(items: ChargeFormItem[], isFixedData: boolean): void {
        items?.forEach((item) => {
            if (isFixedData) {
                item.isFixedData = CalculateLockFlag.lock;
            } else {
                item.isFixedData = CalculateLockFlag.unlock;
            }
        });
    }

    private _restoreReceivablePriceWithClinicChargeRoundType(): void {
        const innerState = this.innerState;
        const chargePayData = innerState.chargePayData!;
        const receivablePrice = chargePayData.receivableFee;

        //只在首次允许议价
        const status = chargePayData.chargeInvoiceDetailData!.status;
        if (status != ChargeStatus.unCharged && status != ChargeStatus.draft && !this.innerState.isRepayment) {
            this._setReceivablePrice(receivablePrice!);

            return;
        }

        this._setReceivablePrice(receivablePrice!);
    }

    private _setReceivablePrice(price: number): void {
        this.innerState.receivablePrice = price;

        this._adjustPayCashPrice();
    }

    private _adjustPayCashPrice(): void {
        const innerState = this.innerState;
        let maxPayCash = innerState.receivablePrice;
        if (innerState.selectPayMethod?.method == PayMethod.payMemberCard) {
            maxPayCash = Math.min(maxPayCash!, innerState.memberCardBalance!);
        }
        //卡项支付方式，根据可用余额进行计算
        if (innerState.selectPayMethod?.isCard) {
            maxPayCash = Math.min(maxPayCash!, innerState.selectPayMethod?.availableBalance ?? 0);
        }
        this._setPayCashPrice(maxPayCash!);
    }

    private _setPayCashPrice(price: number): void {
        this.innerState.cashPrice = price;
    }

    private async _reloadData(): Promise<void> {
        const innerState = this.innerState;
        const chargePayData = innerState.chargePayData!;
        innerState.receivableSrcPrice = chargePayData.receivableFee;
        innerState.otherAdjustmentFee = chargePayData.adjustmentFee ?? 0.0;
        innerState.totalPrice = chargePayData.totalFee;
        this._restoreReceivablePriceWithClinicChargeRoundType();

        innerState.cashPrice = innerState.receivablePrice;

        //挂号预约处，需要隐藏欠费支付方式
        if (innerState.isHideOweMethod) {
            const owePayIndex = innerState.payMethods?.findIndex((t) => t.method == PayMethod.payOweWay);
            if (owePayIndex != -1) {
                innerState.payMethods?.splice(
                    innerState.payMethods?.findIndex((t) => t.method == PayMethod.payOweWay),
                    1
                );
            }
        }

        //初始时，禁用会员卡
        innerState.disableMethods = new Set([PayMethod.payMemberCard]);
        innerState.selectPayMethod = undefined;

        // 查看会员卡信息
        if (!_.isEmpty(chargePayData.memberId)) {
            const data = await PatientAgent.getMemberInfoById(chargePayData.memberId!);
            if (data && !_.isNil(data.present)) {
                innerState.memberCardBalance = data.present! + data.principal!;
                innerState.methodBalance = new Map([[PayMethod.payMemberCard, innerState.memberCardBalance!]]);

                //会员卡有钱，可以使用会员卡
                if (innerState.memberCardBalance > 0) {
                    innerState.disableMethods = new Set();
                } else {
                    innerState.disableMethods = new Set([PayMethod.payMemberCard]);
                }

                this._adjustPayCashPrice();
            }
        }

        const payHealthCardEnable = this.innerState.shebaoConfig?.shebaoPayMethodEnable ?? false;
        if (!payHealthCardEnable) {
            innerState.disableMethods.add(PayMethod.payHealthCard);
        }

        //是否开启欠费支付方式，如果未开启，欠费方式需要禁用
        if (!this.innerState.isOpenOwePay) {
            innerState.disableMethods.add(PayMethod.payOweWay);
        }

        //还款不支持欠费支付方式
        if (innerState.isRepayment && !innerState.disableMethods.has(PayMethod.payOweWay)) {
            innerState.disableMethods.add(PayMethod.payOweWay);
        }

        //还款只能选择相同会员卡，不能选择不同的会员卡（因为会员卡对应的折扣不一样）
        if (innerState.isRepayment && !innerState.disableMethods.has(PayMethod.payMemberCard)) {
            const oweList = innerState?.chargeOweSheets;
            let isExistDiffMember = false;
            if ((oweList?.length ?? 0) > 1) {
                for (let i = 0; i < oweList!.length - 1; i++) {
                    isExistDiffMember = false;
                    if (oweList![i]?.memberInfo?.memberType?.id != oweList![i + 1]?.memberInfo?.memberType?.id) {
                        isExistDiffMember = true;
                        break;
                    }
                }
            }
            if (isExistDiffMember) {
                innerState.disableMethods.add(PayMethod.payMemberCard);
            }
        }

        // 若存在卡项支付方式，则需要根据卡项余额，进行初始化，判断是否可用，以及可用金额
        const cardMethods = innerState.payMethods?.filter((t) => t.isCard) ?? [];
        if (!_.isEmpty(cardMethods)) {
            cardMethods.forEach((item) => {
                const afterPayPatientCard = this.innerState.patientCardList?.find((card) => card.id == item.id);
                if (afterPayPatientCard) {
                    item.availableBalance = afterPayPatientCard.availableBalance;
                }
                if (item.id == this.innerState.selectPayMethod?.id) {
                    this.innerState.disableMethods?.add(item.method);
                }

                if (item && !_.isNil(item.availableBalance)) {
                    //显示卡项可用余额
                    if (!_.isEmpty(innerState.methodBalance)) {
                        innerState.methodBalance!.set(item.method, item.availableBalance!);
                    } else {
                        innerState.methodBalance = new Map();
                        innerState.methodBalance!.set(item.method, item.availableBalance!);
                    }

                    if (item.availableBalance == 0) {
                        //如果可用余额为0，则禁用点击
                        innerState.disableMethods!.add(item.method);
                    }

                    this._adjustPayCashPrice();
                }
            });
            this.update();
        }
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    public requestUpdatePayMethod(method: ABCPayMethod): void {
        this.dispatch(new _EventUpdatePayMethod(method));
    }

    public updateCashPrice(cashPrice: number): void {
        this.dispatch(new _EventUpdateCashPrice(cashPrice));
    }

    public requestToggleSendDragSwitch(on: boolean): void {
        this.dispatch(new _EventToggleSendDrugSwitch(on));
    }

    public requestCharge(callback?: () => void, requestCallback?: () => void): void {
        this.dispatch(new _EventCharge(callback, requestCallback));
    }

    requestTipsNotPaymentReason(method: ABCPayMethod): void {
        this.dispatch(new _EventTipsNotPaymentReason(method));
    }

    requestUpdateChargeComment(chargeComment: string): void {
        this.dispatch(new _EventUpdateChargeComment(chargeComment));
    }
}

export { ChargePayPageBloc, State };
