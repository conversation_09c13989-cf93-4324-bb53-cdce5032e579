/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/10/9
 *
 * @description 收费时，药品禁用后提示后禁止收费，库存不足药品收费进行提示，根据开关"允许在门诊、收费处开出"来决定是否允许收费
 */
import { BaseComponent } from "../../base-ui/base-component";
import { ChargeFormItem, ChargeInvoiceDetailData, ChargeStatus } from "../data/charge-beans";
import { ListView, Text, View } from "@hippy/react";
import React from "react";
import { showConfirmDialog, showDialog } from "../../base-ui/dialog/dialog-builder";
import { BottomSheetCloseButton } from "../../base-ui/dialog/bottom_sheet";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { GoodsInfo, GoodsType } from "../../base-business/data/beans";
import { UIUtils } from "../../base-ui/utils";
import { userCenter } from "../../user-center";
import { AbcBasePanel } from "../../base-ui/abc-app-library";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { SizedBox } from "../../base-ui";
import { AbcToolBarButtonStyle1, AbcToolBarButtonStyle2 } from "../../base-ui/abc-app-library/tool-bar-button/tool-bar";

interface StockNotEnoughDialogProps {
    list: Array<{
        title: string;
        items: ChargeFormItem[];
    }>;
    // 如果是反选，不需要校验库存不足的开关设置
    notVerifyStocks?: boolean;
}

export class StockDisableCheckDialog extends BaseComponent<StockNotEnoughDialogProps> {
    /**
     *如果收费单里存在禁用或库存不足的项，则显示对话框
     * @param chargeData 收费单
     * @param isOpenSingleChargeRefund 是否开启整单收退费
     * @return Promise<boolean> true 确认继续收费，其它中断继续收费
     */
    public static async checkIfCanCharge(
        chargeData: ChargeInvoiceDetailData,
        isOpenSingleChargeRefund?: boolean,
        // 门诊收费单与零售收费单，提示文案有点区别
        isOutpatient = true
    ): Promise<boolean | undefined> {
        const status = chargeData.status;
        //只有在完全未收费状态时才检查库存不足的情况
        if (status != ChargeStatus.unCharged && status != ChargeStatus.draft) return true;

        //检查是否有停用的药品
        const items: ChargeFormItem[] = [];
        StockDisableCheckDialog.visitDisableSellItem(chargeData, (item) => {
            if (!item.isSelfProvided) items.push(item);
            return true;
        });

        if (items.length) {
            if (!!isOpenSingleChargeRefund) {
                await showConfirmDialog(
                    "提示",
                    isOutpatient ? "收费单内包含停售商品，请医生调整门诊后再进行收费" : "收费单内包含停售商品，请调整后再收费",
                    "知道了"
                );
                return false;
            } else {
                return showDialog(
                    <StockDisableCheckDialog
                        list={[
                            {
                                title: "以下商品未创建或者已被停用",
                                items: items,
                            },
                        ]}
                    />
                ).then(() => false);
            }
        }

        //检查是否有库存不足的药品
        const stockNotEnoughItems: ChargeFormItem[] = [];
        StockDisableCheckDialog.visitStockNotEnoughItem(chargeData, (item) => {
            if (!item.isSelfProvided) stockNotEnoughItems.push(item); // 自备药品忽略库存不足
            return true;
        });
        // 检查是否有未勾选的项
        const unCheckedItems: ChargeFormItem[] = [];
        chargeData.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((item) => {
                const checked = item.checked ?? true;
                if (!checked) unCheckedItems.push(item);
            });
        });

        let needShowDialog = false;
        const dialogItems: Array<{ title: string; items: ChargeFormItem[] }> = [];

        // 处理库存不足的项
        if (stockNotEnoughItems.length) {
            if (!!isOpenSingleChargeRefund) {
                await showConfirmDialog(
                    "提示",
                    isOutpatient ? "收费单内包含库存不足商品，请医生调整门诊后再进行收费" : "收费单内包含库存不足商品，请调整后再收费",
                    "知道了"
                );
                return false;
            } else if (userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods != 2) {
                needShowDialog = true;
                dialogItems.push({
                    title: "以下药品库存不足",
                    items: stockNotEnoughItems,
                });
            }
        }

        // 处理未勾选的项
        if (unCheckedItems.length) {
            needShowDialog = true;
            dialogItems.push({
                title: "以下药品未勾选，将退单不再收费",
                items: unCheckedItems,
            });
        }

        if (needShowDialog) {
            return showDialog(
                <StockDisableCheckDialog list={dialogItems} notVerifyStocks={!stockNotEnoughItems.length && !!unCheckedItems.length} />
            ).then((rsp) => !!rsp);
        }

        return true;
    }

    /**
     * 遍历收费单中被禁用的项
     * @param chargeData 收费单
     * @param visitor 每找一个禁用的项，都会进行回调，返回false，表示中断遍历
     */
    private static visitDisableSellItem(chargeData: ChargeInvoiceDetailData, visitor: (formItem: ChargeFormItem) => boolean) {
        for (const chargeForm of chargeData.chargeForms!) {
            for (const formItem of chargeForm.chargeFormItems!) {
                const checked = formItem.checked ?? true;
                if (!checked) continue;
                const goodsInfo = formItem.goodsInfo;
                if (
                    (goodsInfo.type == GoodsType.medicine ||
                        goodsInfo.type == GoodsType.material ||
                        goodsInfo.type == GoodsType.examination ||
                        goodsInfo.type == GoodsType.treatment ||
                        goodsInfo.type == GoodsType.goods ||
                        goodsInfo.type == GoodsType.package) &&
                    (goodsInfo.disableSell == 1 || !formItem.productInfo)
                ) {
                    if (!visitor(formItem)) {
                        break;
                    }
                }
            }
        }
    }

    /**
     * 遍历收费单里的库存不足的项
     * @param chargeData 收费单
     * @param visitor 每找到一个库存不足项，都会进行回调，返回false，表示中断遍历
     */
    private static visitStockNotEnoughItem(chargeData: ChargeInvoiceDetailData, visitor: (formItem: ChargeFormItem) => boolean) {
        for (const chargeForm of chargeData.chargeForms!) {
            for (const formItem of chargeForm.chargeFormItems!) {
                const checked = formItem.checked ?? true;
                if (!checked || formItem.isFamilyDoctorSign) continue;
                const stockInfo = formItem.goodsInfo.stockInfo(
                    formItem.unit,
                    formItem.unitCount,
                    formItem.doseCount,
                    undefined,
                    undefined,
                    undefined,
                    chargeForm.pharmacyType
                );
                if (!stockInfo.stockEnough) {
                    if (!visitor(formItem)) {
                        break;
                    }
                }
            }
        }
    }

    render(): JSX.Element {
        const { list, notVerifyStocks } = this.props;
        const needTips = userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods != 1 || notVerifyStocks;
        return (
            <AbcBasePanel
                panelStyle={{
                    backgroundColor: Colors.white,
                    marginHorizontal: Sizes.dp24,
                    maxHeight: UIUtils.getScreenHeight() * 0.8,
                }}
                contentStyle={{
                    paddingTop: Sizes.dp8,
                    paddingHorizontal: Sizes.dp16,
                }}
            >
                <View style={[{ position: "absolute", right: 0, top: 0, zIndex: 1 }]}>
                    <BottomSheetCloseButton onTap={() => ABCNavigator.pop()} />
                </View>
                <View
                    style={[
                        {
                            flexDirection: "row",
                            justifyContent: "center",
                            backgroundColor: Colors.white,
                            borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
                            paddingVertical: Sizes.dp16,
                        },
                    ]}
                >
                    <Text style={TextStyles.t18MB.copyWith({ lineHeight: Sizes.dp28 })}>{"收费提醒"}</Text>
                </View>
                <View style={[{ paddingHorizontal: Sizes.dp8, flex: 1 }]}>
                    {list?.map((item, index) => {
                        const data = item.items;
                        return (
                            <View key={index} style={{ marginTop: index < list.length - 1 ? Sizes.dp8 : 0 }}>
                                <Text style={[TextStyles.t16NY2.copyWith({ lineHeight: Sizes.dp24 })]}>{item.title}</Text>
                                <SizedBox height={Sizes.dp8} />
                                <ListView
                                    style={{ flex: 1 }}
                                    numberOfRows={data.length}
                                    dataSource={data}
                                    scrollEventThrottle={300}
                                    showScrollIndicator={false}
                                    getRowKey={(index) => index.toString()}
                                    renderRow={(item, others, index) => (
                                        <_ListItem
                                            key={item.compareKey()}
                                            goods={item.goodsInfo}
                                            bottomLine={index != data.length - 1}
                                            chargeFormItem={item}
                                            type={item.title == "以下药品库存不足" ? 1 : 0}
                                        />
                                    )}
                                />
                            </View>
                        );
                    })}
                </View>
                <SizedBox height={Sizes.dp24} />

                <View style={[ABCStyles.rowAlignCenter, Sizes.paddingLTRB(Sizes.dp8, 0, Sizes.dp8, Sizes.dp20)]}>
                    {needTips && (
                        <AbcToolBarButtonStyle2
                            text={"取消"}
                            style={{ fontColor: Colors.black, borderColor: Colors.bdColor }}
                            onClick={() => ABCNavigator.pop(false)}
                        />
                    )}
                    {needTips && <SizedBox width={Sizes.dp24} />}
                    <AbcToolBarButtonStyle1 text={needTips ? "继续收费" : "我知道了"} onClick={this._onConfirmClick.bind(this)} />
                </View>
            </AbcBasePanel>
        );
    }

    //点击确认
    private _onConfirmClick(): void {
        const needTips = userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods != 1 || !!this.props?.notVerifyStocks;
        ABCNavigator.pop(needTips);
    }
}

interface _ListItemProps {
    goods: GoodsInfo;
    chargeFormItem?: ChargeFormItem;
    bottomLine: boolean;
    packageName?: string;
    type?: number; // 库存不足还是未勾选
}

class _ListItem extends BaseComponent<_ListItemProps> {
    public render(): JSX.Element {
        const { goods, bottomLine, chargeFormItem, packageName, type } = this.props;
        const packageSpec = !goods.isChineseMedicine ? goods.packageSpec : "";

        if (goods.isPackage) {
            if (!!type) {
                return (
                    <View>
                        {goods.children
                            ?.filter((item) => {
                                const stockInfo = item.stockInfoForPackageItem(chargeFormItem?.unitCount, chargeFormItem?.doseCount);
                                return !stockInfo.stockEnough;
                            })
                            .map((item) => {
                                return (
                                    <_ListItem
                                        key={item.compareKey()}
                                        goods={item}
                                        bottomLine={bottomLine}
                                        packageName={goods.displayName}
                                    />
                                );
                            })}
                    </View>
                );
            }
        }
        return (
            <View style={[bottomLine ? ABCStyles.bottomLine : {}, { justifyContent: "center", paddingVertical: Sizes.dp8 }]}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, marginRight: Sizes.dp6 }]}>
                        <Text style={[TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                            {chargeFormItem?.displayName ?? ""}
                        </Text>
                        {packageName && (
                            <Text style={[TextStyles.t16NT4, { flexShrink: 1 }]} numberOfLines={1}>{`【${packageName}】`}</Text>
                        )}
                    </View>
                    <Text style={TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24 })}>
                        {chargeFormItem?.isRegistration ? "" : goods.displayStockInfo()}
                    </Text>
                </View>
                {packageSpec.length > 0 && <SizedBox height={Sizes.dp4} />}
                {packageSpec.length > 0 && (
                    <Text numberOfLines={1} style={[TextStyles.t14Nt2.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]}>
                        {packageSpec}
                    </Text>
                )}
            </View>
        );
    }
}
