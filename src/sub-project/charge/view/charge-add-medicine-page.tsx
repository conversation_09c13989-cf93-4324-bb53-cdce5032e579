import { BaseBlocPage } from "../../base-ui/base-page";
import { View } from "@hippy/react";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import { AbcTextInput, AbcToggleSystemKeyboardPanel } from "../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AlphabetKeyboardBuilder } from "../../base-ui/views/keyboards/alphabet-keyboard";
import { MedicineAddPageCategoryItem } from "../../outpatient/product-add-page/product-add-page";
import { DividerLine, IconFontView, SizedBox, Tab, Tabs } from "../../base-ui";
import React from "react";
import { DirectChargeMedicineAddPageBloc } from "../direct-charge-medicine-add-page-bloc";
import _ from "lodash";
import { userCenter } from "../../user-center";
import {
    _CategoryPicker,
    _CategoryPickerProps,
    _GoodsSearchDisplayView,
    DirectChargeMedicineAddPageCategoryItem,
    DirectChargeMedicineInvokeParams,
    NurseParams,
} from "../direct-charge-medicine-add-page";
import { GoodsInfo, GoodsType } from "../../base-business/data/beans";
import { AbcView } from "../../base-ui/views/abc-view";
import { ValueHolder } from "../../base-ui/utils/value-holder";
import { AbcPrescriptionItemView, PrescriptionAdditionalInfo } from "../../base-ui/abc-prescription-item-view/abc-prescription-item-view";
import { PsychotropicNarcoticTagView } from "../../views/business-tags";

interface ChargeAddMedicinePageProps {
    params: DirectChargeMedicineInvokeParams;
    nurseFilterInfo?: NurseParams; //是否从执行站页面进入
    showScanIcon?: boolean; //是否显示扫码图标
}

const kHospitalCategoryItems = [
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdExamination, "检查检验"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdTreatment, "治疗项目"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.nurseProduct, "护理医嘱"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdPackage, "套餐"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdGoods, "商品"),
    new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial, "医用物资"),
];

const kCategoryItems = userCenter.clinic?.isNormalHospital
    ? kHospitalCategoryItems
    : [
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdWestern, "中西成药"),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdChinese, "中药"),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdTreatment, "治疗理疗"),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdExamination, "检查检验", true),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdPackage, "套餐", true),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdGoods, "商品"),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial, "医用物资"),
          new DirectChargeMedicineAddPageCategoryItem(DirectChargeMedicineAddPageCategoryItem.otherGoods, "其他", true),
      ];

interface ChargeCategoryPickerProps extends _CategoryPickerProps {
    categoryItemList?: DirectChargeMedicineAddPageCategoryItem[];
}
class ChargeCategoryPicker extends _CategoryPicker<ChargeCategoryPickerProps> {
    render(): JSX.Element {
        const { selectCategory, categoryItemList } = this.props;
        const state = DirectChargeMedicineAddPageBloc.fromContext(this.context).currentState;
        let __kCategoryItems = !!categoryItemList && !_.isEmpty(categoryItemList) ? _.cloneDeep(categoryItemList) : [...kCategoryItems];
        if (userCenter.clinic?.isDentistryClinic) {
            __kCategoryItems.splice(
                4,
                0,
                ...state.kCustomCategoryItems.map(
                    (item) => new DirectChargeMedicineAddPageCategoryItem(item.customTypeId, item.customTypeName, true)
                )
            );
            __kCategoryItems = __kCategoryItems.filter((item) => item.isDentistry);
        }

        if (state._fromNursePage) {
            //是否存在药品（中西成药、中药）
            const isExistMedicine = state._openGoodsTypes?.includes(1);

            //是否存在商品、耗材（商品、检查检验、医用物资）
            const isExistGoods = state._openGoodsTypes?.includes(2);
            _.remove(
                __kCategoryItems,
                (item) =>
                    (!isExistMedicine &&
                        (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdWestern ||
                            item.id == DirectChargeMedicineAddPageCategoryItem.naviIdChinese)) ||
                    (!isExistGoods &&
                        (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdGoods ||
                            item.id == DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial))
            );
        }
        return (
            <AbcView style={{ flex: 1, paddingHorizontal: Sizes.dp12 }}>
                <Tabs
                    scrollBeginDragInputBlur={false}
                    lineMargin={0}
                    initialPage={__kCategoryItems.findIndex((t) => t.id == selectCategory) ?? 0}
                    onChange={(idx) => this.props.onChanged(__kCategoryItems[idx])}
                    tabsStyle={{
                        justifyContent: "center",
                        paddingHorizontal: Sizes.dp4,
                        flexDirection: "row",
                        alignItems: "center",
                        flex: undefined,
                    }}
                    lineColor={Colors.mainColor}
                    tabStyle={{
                        marginRight: Sizes.dp24,
                        ...TextStyles.t16NT3.copyWith({ color: Colors.t2 }),
                    }}
                    currentStyle={{
                        ...TextStyles.t16MT1,
                    }}
                >
                    {__kCategoryItems.map((item) => {
                        return <Tab key={item.id.toString()} title={item.title} />;
                    })}
                </Tabs>
            </AbcView>
        );
    }
}

interface ChargeListItemProps {
    goodsInfo: GoodsInfo;
    select: boolean;
    allowZeroStockGoods?: boolean; //是否允许开0的药品
}
const ChargeListItem: React.FC<ChargeListItemProps> = ({ goodsInfo, allowZeroStockGoods }) => {
    const drugClassification =
            goodsInfo.isChineseMedicine ||
            goodsInfo.isWesternMedicine ||
            goodsInfo.isChineseWesternMedicine ||
            goodsInfo.isGoods ||
            goodsInfo.isGlasses ||
            goodsInfo.isMedicalMaterial,
        projectClassification =
            goodsInfo.type == GoodsType.treatment ||
            goodsInfo.type == GoodsType.examination ||
            goodsInfo.isNurseFee ||
            goodsInfo.isOtherFee;
    let specification = goodsInfo.packageSpec;
    if (_.isEmpty(specification)) specification = `${goodsInfo.cMSpec} ${goodsInfo.pieceUnit}`;

    const stock = goodsInfo.displayStockInfo();
    const hasStock = goodsInfo.hasStock || userCenter.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 2;
    if (!drugClassification && !projectClassification && !goodsInfo.isPackage) return <View />;
    const titleColor = { color: !goodsInfo?.hasStock && !allowZeroStockGoods ? Colors.T4 : Colors.T1 },
        subTitleColor = { color: !goodsInfo?.hasStock && !allowZeroStockGoods ? Colors.T4 : Colors.T6 };
    return (
        <AbcPrescriptionItemView
            displayName={goodsInfo.displayName ?? ""}
            unit={projectClassification ? goodsInfo.unit : undefined}
            typeName={goodsInfo.isPackage ? "【套餐】" : ""}
            price={drugClassification ? goodsInfo.unitPricePreferPackage : goodsInfo.unitPrice}
            displayNameStyle={drugClassification ? titleColor : undefined}
            priceStyle={drugClassification ? titleColor : undefined}
            typeStyle={{
                padding: 0,
                backgroundColor: undefined,
                borderRadius: 0,
            }}
            expandContent={() => (
                <PsychotropicNarcoticTagView dangerIngredient={goodsInfo.getIngredientArray} antibiotic={goodsInfo.antibiotic} />
            )}
        >
            {drugClassification && (
                <PrescriptionAdditionalInfo
                    specification={specification}
                    manufacturer={goodsInfo.manufacturer}
                    hasStock={hasStock}
                    remainStockTextFlag={"余"}
                    surplusStockDisplay={stock}
                    specificationStyle={drugClassification ? subTitleColor : undefined}
                    manufacturerStyle={drugClassification ? subTitleColor : undefined}
                    surplusStockDisplayStyle={
                        drugClassification
                            ? [hasStock ? TextStyles.t14NT6 : allowZeroStockGoods ? TextStyles.t14NY2 : TextStyles.t14NT4]
                            : undefined
                    }
                />
            )}
        </AbcPrescriptionItemView>
    );
};

class ChargeGoodsSearchDisplayView extends _GoodsSearchDisplayView {
    _renderRow(data: GoodsInfo, index: number, totalCount: number, select: boolean) {
        const valueHolder = new ValueHolder<AbcView | null>();
        const { allowZeroStockGoods } = this.props;
        return (
            <AbcView
                collapsable={false}
                ref={(ref) => (valueHolder.value = ref)}
                style={{
                    backgroundColor: Colors.white,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                }}
                onClick={() => this.props.onClickItem?.(data, valueHolder.value!)}
            >
                <ChargeListItem goodsInfo={data} select={select} allowZeroStockGoods={allowZeroStockGoods} />
                {index != totalCount - 1 && <DividerLine />}
            </AbcView>
        );
    }
}
const inputStyle = DeviceUtils.isOhos()
    ? {
          height: Sizes.dp54,
          ...TextStyles.t18NT1,
          fontSize: Sizes.dp16,
          flex: 1,
      }
    : {
          flex: 1,
          height: Sizes.dp54,
          paddingLeft: DeviceUtils.isAndroid() ? -6 : undefined,
          paddingVertical: Sizes.dp16,
          ...TextStyles.t18NT1,
          borderWidth: Sizes.dp1,
          borderColor: Colors.transparent,
          fontSize: Sizes.dp16,
      };
export class ChargeAddMedicinePage extends BaseBlocPage<ChargeAddMedicinePageProps, DirectChargeMedicineAddPageBloc> {
    _keyword?: string;
    _currentCategory: DirectChargeMedicineAddPageCategoryItem;
    copyKCategoryItems?: DirectChargeMedicineAddPageCategoryItem[];

    pageName(): string | undefined {
        return "添加收费项";
    }

    static defaultProps = {
        showScanIcon: true,
    };

    constructor(props: ChargeAddMedicinePageProps) {
        super(props);
        //是否存在药品（中西成药、中药）
        const isExistMedicine = props?.nurseFilterInfo?.openGoodsTypes?.includes(1);

        //是否存在商品、耗材（商品、医用物资）
        const isExistGoods = props?.nurseFilterInfo?.openGoodsTypes?.includes(2);
        this.copyKCategoryItems = [...kCategoryItems];
        _.remove(
            this.copyKCategoryItems,
            (item) =>
                (!isExistMedicine && item.id == DirectChargeMedicineAddPageCategoryItem.naviIdWestern) ||
                (!isExistMedicine && item.id == DirectChargeMedicineAddPageCategoryItem.naviIdChinese) ||
                (!isExistGoods &&
                    (item.id == DirectChargeMedicineAddPageCategoryItem.naviIdGoods ||
                        item.id == DirectChargeMedicineAddPageCategoryItem.naviIdMedicalMaterial))
        );

        this._currentCategory =
            props.nurseFilterInfo?.fromNursePage && !userCenter.clinic?.isDentistryClinic
                ? this.copyKCategoryItems[0]
                : (!_.isEmpty(props.params.goodsTypes) ? this.copyKCategoryItems : kCategoryItems)[
                      userCenter.clinic?.isDentistryClinic ? 3 : 0
                  ];
        this.bloc = new DirectChargeMedicineAddPageBloc(this._currentCategory.id, this.props.params, this.props?.nurseFilterInfo);

        this.addDisposable(this.bloc);
    }
    getAppBar(): JSX.Element {
        const { showScanIcon } = this.props;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    { marginLeft: Sizes.dp16, marginRight: Sizes.dp26, flexDirection: "row", alignItems: "center" },
                ]}
            >
                <AbcTextInput
                    autoFocus={this.props.params.autoFocusSearch}
                    multiline={false}
                    placeholder={"请输入项目拼音码"}
                    style={inputStyle}
                    placeholderTextColor={Colors.t3}
                    customPanelBuilder={(textInput) => <AbcToggleSystemKeyboardPanel textInput={textInput} />}
                    customKeyboardBuilder={new AlphabetKeyboardBuilder()}
                    onChangeText={(text) => {
                        this._keyword = text;
                        this.bloc.requestUpdateSearchText(text);
                    }}
                    onFocus={() => {
                        this.bloc.requestUpdateSearchFocus(true);
                    }}
                    onBlur={() => this.bloc.requestUpdateSearchFocus(false)}
                />
                <SizedBox width={Sizes.dp12} />
                {showScanIcon && (
                    <IconFontView
                        name={"scan"}
                        key={"RightAppBarIcons"}
                        size={Sizes.dp20}
                        color={Colors.mainColor}
                        onClick={() => this.bloc.requestAddGoodsFromQRScan()}
                    />
                )}
            </View>
        );
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    private _onCategoryChanged(category: MedicineAddPageCategoryItem): void {
        this._currentCategory = category;
        this.bloc.requestUpdateGoodsType(category.id);
    }
    private async _onClickItem(goods: GoodsInfo) {
        if (!goods.hasStock && !this.bloc.currentState.allowZeroStockGoods) return;
        await this.bloc.requestToggleMedicineSelectState(goods);
        this.bloc.requestSave();
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { showCategorySearchNavi, goodsTypes } = this.props.params;
        return (
            <View style={{ flex: 1, paddingHorizontal: Sizes.dp16 }}>
                <View
                    style={{
                        flex: 1,
                        marginHorizontal: -Sizes.dp16,
                    }}
                >
                    {!state.isInSearchMode && showCategorySearchNavi && (
                        <View>
                            <ChargeCategoryPicker
                                selectCategory={this._currentCategory.id}
                                onChanged={(newCategory) => this._onCategoryChanged(newCategory)}
                                categoryItemList={!_.isEmpty(goodsTypes) ? this.copyKCategoryItems : undefined}
                            />
                        </View>
                    )}
                    {!state.isInSearchMode && showCategorySearchNavi && (
                        <DividerLine lineHeight={Sizes.dpHalf} style={{ marginHorizontal: Sizes.dp16 }} />
                    )}
                    <ChargeGoodsSearchDisplayView
                        onClickItem={(goods) => this._onClickItem(goods)}
                        hintContent={this.props.params.searchHintContent}
                        allowZeroStockGoods={state.allowZeroStockGoods}
                    />
                </View>
            </View>
        );
    }
}
