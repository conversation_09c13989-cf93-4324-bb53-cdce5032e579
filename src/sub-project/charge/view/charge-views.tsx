/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/5/26
 */
import React from "react";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ChargeRegistrationInfo,
    ChargeStatus,
    PayMethod,
} from "../data/charge-beans";
import { Style, Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import {
    ChineseGoodType,
    ChineseMedicineSpecType,
    DecoctionProductInfo,
    DeliveryInfo,
    DeliveryPayType,
    GoodsInfo,
    GoodsType,
    IngredientProductInfo,
    Patient,
} from "../../base-business/data/beans";
import { ABCStyles, AbcViewProps, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { MinMaxLimitFormat, PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { CustomInput } from "../../base-ui/input/custom-input";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import _ from "lodash";
import { Department, Doctor, RegistrationDataProvider } from "../../registration/data/registration";
import { DividerLine, IconFontView, SizedBox, Spacer } from "../../base-ui";
import { ABCUtils } from "../../base-ui/utils/utils";
import { AbcView } from "../../base-ui/views/abc-view";
import { DeliveryInfoEditPage, DeliveryInfoEditResult } from "../delivery-info-edit-page";

import { AbcCheckbox } from "../../base-ui/views/abc-checkbox";
import { CloseBtn } from "../../base-ui/views/close-btn";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { DoctorDepartmentSelectDialog } from "../../base-ui/doctor-department-select-view";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";
import { MedicineProcessCostPage } from "../medicine-process-cost-page";
import { ChargeUtils } from "../utils/charge-utils";
import { AirPharmacyDeliveryInfoEditPage } from "../air-pharmacy-delivery-info-edit-page";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { ActionBtn, StockNotEnoughTextView } from "../../base-ui/views/stock-not-enough-text-view";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { TextStyle } from "../../theme/text-styles";
import { GoodsUtils } from "../../base-business/utils/utils";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import { PharmacyTagView } from "../../outpatient/views/pharmacy-tag-view";
import { userCenter } from "../../user-center";
import { runtimeConstants } from "../../data/constants/runtime-constants-manager";
import { ChargeListSettingItem } from "./charge-base-operate-item";
import FontWeight from "../../theme/font-weights";
import { AbcText } from "../../base-ui/views/abc-text";
import { LogUtils } from "../../common-base-module/log";
import abcI18Next from "../../language/config";
import { AbcToothView } from "../../base-ui/abc-app-library/tooth-panel/abc-tooth-view";
import { ToothNosDetailInfo } from "../../data/tooth-bean";
import { MedicineScopeId } from "../data/charge-bean-air-pharmacy";
import { GetClinicBasicSetting } from "../../data/online-property-config-provder";

interface ChargeStatusViewProps {
    chargeStatus?: number | ChargeFormItemStatus;
    showChargedStatus?: boolean;
    isClosed?: boolean;
    style?: Style;
    isRewriteMargin?: boolean; // 是否重写左右外边距
    isOweList?: boolean; //是否是欠费列表详情
    isAbbr?: boolean; // 是否缩写图标文案
}

export class ChargeStatusView extends React.Component<ChargeStatusViewProps> {
    statusIcon(): string {
        const { chargeStatus, showChargedStatus, isAbbr } = this.props;
        let statusIconName = "";
        if (chargeStatus == ChargeFormItemStatus.refunded) {
            statusIconName = !!isAbbr ? "charge_item_status_refund_abbr" : "charge_item_status_refund";
        } else if (chargeStatus == ChargeFormItemStatus.chargeBack) {
            statusIconName = !!isAbbr ? "charge_back_abbr" : "charge_back";
        } else if (chargeStatus == ChargeFormItemStatus.partRefund) {
            statusIconName = !!isAbbr ? "charge_item_status_part_refund_abbr" : "charge_item_status_part_refund";
        }
        if (showChargedStatus && chargeStatus == ChargeFormItemStatus.charged) {
            statusIconName = "charge_status_charged";
        }

        return statusIconName;
    }

    render(): JSX.Element {
        const { style, isAbbr } = this.props;
        const statusIconName = this.statusIcon();
        if (!statusIconName) return <View />;
        return (
            <View
                style={[
                    // style ?? {},
                    {
                        marginHorizontal: Sizes.dp4,
                        justifyContent: "center",
                        alignItems: "center",
                    },
                    flattenStyles(style),
                ]}
            >
                {statusIconName && (
                    <AssetImageView
                        name={statusIconName}
                        style={[
                            statusIconName == "charge_item_status_part_charged"
                                ? { width: Sizes.dp52, height: Sizes.dp18 }
                                : {
                                      width: isAbbr ? Sizes.dp30 : Sizes.dp41,
                                      height: Sizes.dp18,
                                  },
                        ]}
                    />
                )}
            </View>
        );
    }
}

export class ExecuteChargeStatusView extends ChargeStatusView {
    statusIcon(): string {
        const { chargeStatus, showChargedStatus } = this.props;
        let statusIconName = "";
        if (chargeStatus == ChargeFormItemStatus.refunded) {
            statusIconName = "charge_item_status_refund";
        } else if (chargeStatus == ChargeFormItemStatus.chargeBack) {
            statusIconName = "charge_back";
        } else if (chargeStatus == ChargeFormItemStatus.partRefund) {
            statusIconName = "charge_item_status_part_refund";
        } else if (chargeStatus == ChargeFormItemStatus.partCharged) {
            statusIconName = "charge_item_status_part_charged";
        }

        if (showChargedStatus && chargeStatus == ChargeFormItemStatus.charged) {
            statusIconName = "charge_status_charged";
        } else if (showChargedStatus && chargeStatus == ChargeFormItemStatus.unCharged) {
            statusIconName = "charge_item_status_uncharge";
        }

        return statusIconName;
    }
}

export class ExecuteChargeStatusViewText extends React.Component<ChargeStatusViewProps> {
    statusInfo(): { text: string; textStyle: TextStyle; borderColor?: Color } {
        const { chargeStatus, showChargedStatus } = this.props;
        let statusText = { text: "", textStyle: TextStyles.t12NT1, borderColor: Colors.T3 };
        switch (chargeStatus) {
            case ChargeFormItemStatus.refunded:
                statusText = { text: "已退费", textStyle: TextStyles.t12NT1.copyWith({ color: Colors.T3 }), borderColor: Colors.T3 };
                break;
            case ChargeFormItemStatus.chargeBack:
                statusText = { text: "已退单", textStyle: TextStyles.t12NT1.copyWith({ color: Colors.T3 }), borderColor: Colors.T3 };
                break;
            case ChargeFormItemStatus.partRefund:
                statusText = {
                    text: "部分退费",
                    textStyle: TextStyles.t12NT1.copyWith({ color: Colors.mainColor }),
                    borderColor: Colors.mainColor,
                };
                break;
            case ChargeFormItemStatus.partCharged:
                statusText = { text: "部分收费", textStyle: TextStyles.t12NT1.copyWith({ color: Colors.Y2 }), borderColor: Colors.Y2 };
                break;
        }

        if (showChargedStatus && chargeStatus == ChargeFormItemStatus.charged) {
            statusText = {
                text: "已收费",
                textStyle: TextStyles.t12NT1.copyWith({ color: Colors.mainColor }),
                borderColor: Colors.mainColor,
            };
        } else if (showChargedStatus && chargeStatus == ChargeFormItemStatus.unCharged) {
            statusText = { text: "未收费", textStyle: TextStyles.t12NT1.copyWith({ color: Colors.Y2 }), borderColor: Colors.Y2 };
        }

        return statusText;
    }

    render(): JSX.Element {
        const { style, isRewriteMargin } = this.props;
        const statusText = this.statusInfo();
        return (
            <View
                style={[
                    style ?? {},
                    {
                        marginHorizontal: isRewriteMargin ? Sizes.dp0 : Sizes.dp8,
                        justifyContent: "center",
                        alignItems: "center",
                        height: Sizes.dp18,
                        borderColor: statusText.borderColor,
                        borderRadius: Sizes.dp4,
                        borderWidth: 1,
                    },
                ]}
            >
                {statusText && <Text style={[statusText.textStyle]}>{statusText.text}</Text>}
            </View>
        );
    }
}

export class ChargeStatusTextView extends React.Component<ChargeStatusViewProps> {
    render(): JSX.Element {
        const { style } = this.props;
        const { chargeStatus, showChargedStatus, isClosed = false, isOweList = false } = this.props;
        let statusText = "";
        let textColor: Color = Colors.transparent;
        if (chargeStatus === ChargeStatus.refunded) {
            statusText = "已退";
            textColor = Colors.R2;
        } else if (isClosed) {
            statusText = "已关闭";
            textColor = Colors.R2;
        }

        if (showChargedStatus && chargeStatus === ChargeStatus.charged) {
            statusText = "已收费";
            textColor = Colors.G2;
        }

        if (isOweList) {
            statusText = "待还";
            textColor = Colors.Y2;
        }

        return (
            <View style={style}>
                <Text style={{ ...TextStyles.t14NT2, color: textColor }}>{statusText}</Text>
            </View>
        );
    }
}

interface MedicineSellCountInputButtonProps {
    goodsInfo: GoodsInfo;
    count: number;
    dosageCount: number;
    unit: string;
    width?: number;
    autoFocus?: boolean;
    floatPrecisionLength: number;
    onChanged?: (value: number) => void;
    onFocusChanged?: (focus: boolean) => void;
}

export class MedicineSellCountInputButton extends BaseComponent<MedicineSellCountInputButtonProps> {
    static defaultProps = {
        width: 48,
    };
    private _textInput: CustomInput | null = null;

    constructor(props: MedicineSellCountInputButtonProps) {
        super(props);
    }

    focus(): void {
        this._textInput?.focus();
    }

    render(): JSX.Element {
        const { goodsInfo, count, dosageCount, unit, width, autoFocus, floatPrecisionLength } = this.props;

        let breakStock = false;
        if (count != null && dosageCount != null) {
            const totalCount = count * (dosageCount ?? 1);
            const maxStock = goodsInfo.stockCountWithUnit(unit) ?? 0.0;
            breakStock = totalCount > maxStock;
        }
        return (
            <CustomInput
                style={{
                    width: width,
                    height: Sizes.dp28,
                    borderColor: breakStock ? Colors.Y2 : undefined,
                }}
                ref={(ref) => (this._textInput = ref)}
                type={"input"}
                value={count}
                autoFocus={autoFocus}
                formatter={PrecisionLimitFormatter(floatPrecisionLength)}
                onChange={(value) => this.props.onChanged?.(parseFloat(value))}
            />
        );
    }
}

interface MedicineSellUnitSelectButtonProps {
    unit?: string; //当前选择的unit
    units?: string[]; //可选unit列表

    bottomSheetTitle?: string; //default: '选择单位'

    onUnitSelected?: (unit: string) => void;
    /**
     * 弹窗关闭回调
     * 默认执行
     */
    onPopClose?: () => void;
}

//药品单位选择按钮
export class MedicineSellUnitSelectButton extends BaseComponent<MedicineSellUnitSelectButtonProps> {
    static defaultProps = {
        bottomSheetTitle: "选择单位",
    };

    render(): JSX.Element {
        const { unit, units } = this.props;
        const selectEnable = (units?.length ?? 0) > 1;
        const borderColor = !selectEnable ? Colors.D2 : Colors.P1;
        const backgroundColor = !selectEnable ? Colors.D2 : undefined;

        return (
            <View
                style={{
                    height: Sizes.dp28,
                    minWidth: Sizes.dp38,
                    borderRadius: Sizes.dp28 / 2,
                    borderWidth: 1,
                    borderColor: borderColor,
                    backgroundColor: backgroundColor,
                    justifyContent: "center",
                    alignItems: "center",
                }}
                onClick={() => this._onClick()}
            >
                <Text style={TextStyles.t14NT1}>{unit ?? ""}</Text>
            </View>
        );
    }

    protected async _onClick(): Promise<void> {
        const { bottomSheetTitle, unit, units } = this.props;
        const selectEnable = (units?.length ?? 0) > 1;
        if (!selectEnable) return;
        const selects = await showOptionsBottomSheet({
            title: bottomSheetTitle!,
            options: units,
            initialSelectIndexes: new Set([unit ? units!.indexOf(unit) : -1]),
        });

        this.props.onPopClose?.();

        if (_.isEmpty(selects)) return;

        const select = selects![0];
        this.props.onUnitSelected?.(units![select]);
    }
}

interface ChargeRegistrationListItemProps {
    chargeForm?: ChargeForm;
    chargeFormItem: ChargeFormItem;
    checked?: boolean;
    enableCheckBox?: boolean;
    showCheckBox?: boolean;
    doctorMutable?: boolean;
    departmentMutable?: boolean;

    onCountInputValueChanged?: (count: number) => void;
    onDoctorChanged?: (department: Department, doctor: Doctor) => void;

    clinicBasicSetting?: GetClinicBasicSetting;
    chargeSheetStatus?: ChargeStatus;
    isWholeSheetOperateEnabled?: boolean;
}

export class ChargeRegistrationListItem extends BaseComponent<ChargeRegistrationListItemProps> {
    static defaultProps = {
        enableCheckBox: false,
        showCheckBox: false,
        doctorMutable: false,
        departmentMutable: false,
    };

    constructor(props: ChargeRegistrationListItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            chargeFormItem,
            checked,
            enableCheckBox,
            showCheckBox,
            doctorMutable,
            departmentMutable,
            /*onCountInputValueChanged,*/ onDoctorChanged,
            clinicBasicSetting,
            chargeSheetStatus,
            isWholeSheetOperateEnabled,
        } = this.props;
        let _displayName = chargeFormItem.displayName;
        if (chargeFormItem.isRegistration) _displayName = clinicBasicSetting?.registrationFeeStr ?? "";

        let forceFontColor = false;
        if (chargeFormItem.status == ChargeFormItemStatus.refunded) {
            forceFontColor = true;
        } else if (chargeFormItem.status == ChargeFormItemStatus.chargeBack && chargeSheetStatus != ChargeStatus.unCharged) {
            forceFontColor = true;
        }
        // 在收费单一次都没有收过费的情况下，item如果存在已退单状态
        const isRefundItemOfEmptyCharge =
            chargeSheetStatus == ChargeStatus.unCharged && chargeFormItem.status == ChargeFormItemStatus.chargeBack;

        const info = chargeFormItem.productInfo as ChargeRegistrationInfo;
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    {showCheckBox && (
                        <View style={{ paddingRight: Sizes.dp8 }}>
                            {checked ? (
                                <IconFontView
                                    name={"Chosen"}
                                    size={Sizes.dp16}
                                    color={isWholeSheetOperateEnabled ? Colors.P1 : Colors.mainColor}
                                />
                            ) : (
                                <View
                                    style={{
                                        width: Sizes.dp16,
                                        height: Sizes.dp16,
                                        borderWidth: Sizes.dp1,
                                        borderRadius: Sizes.dp8,
                                        borderColor: Colors.P1,
                                        backgroundColor: enableCheckBox ? Colors.whiteSmoke : undefined,
                                    }}
                                />
                            )}
                        </View>
                    )}
                    <Text style={forceFontColor ? TextStyles.t16MT4 : TextStyles.t16NT1.copyWith({ color: Colors.t2 })}>
                        {_displayName}
                    </Text>
                    <ChargeStatusView chargeStatus={isRefundItemOfEmptyCharge ? ChargeFormItemStatus.unCharged : chargeFormItem.status} />
                </View>
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <DoctorDepartmentSelectView
                        doctorName={info.doctorName}
                        departmentName={info.departmentName}
                        doctorMutable={doctorMutable}
                        departmentMutable={departmentMutable}
                        onChanged={onDoctorChanged}
                    />
                    <Text
                        style={{
                            ...(forceFontColor ? TextStyles.t16MT4 : TextStyles.t16NT1),
                        }}
                    >
                        {` | ${ABCUtils.formatPrice(chargeFormItem.unitCount! * chargeFormItem.unitPrice!)}`}
                    </Text>
                </View>
            </View>
        );
        return (
            <View style={[ABCStyles.rowAlignCenter]}>
                {/*{showCheckBox && (*/}
                {/*    <AbcCheckbox*/}
                {/*        check={checked}*/}
                {/*        enable={enableCheckBox}*/}
                {/*        size={Sizes.dp16}*/}
                {/*        style={{ marginRight: Sizes.dp6 }}*/}
                {/*        checkIconBgColor={Colors.white}*/}
                {/*    />*/}
                {/*)}*/}
                {showCheckBox && (
                    <View style={{ paddingRight: Sizes.dp2 }}>
                        {checked ? (
                            <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                        ) : (
                            <View
                                style={{
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    borderWidth: Sizes.dp1,
                                    borderRadius: Sizes.dp8,
                                    borderColor: Colors.P1,
                                    backgroundColor: enableCheckBox ? Colors.whiteSmoke : undefined,
                                }}
                            />
                        )}
                    </View>
                )}
                <View style={{ flex: 1, ...ABCStyles.rowAlignCenter }}>
                    <Text style={forceFontColor ? TextStyles.t16MT4 : TextStyles.t16MB}>{chargeFormItem.displayName ?? ""}</Text>
                    <ChargeStatusView chargeStatus={chargeFormItem.status} />
                    <SizedBox width={Sizes.dp8} />
                    <View style={{ flexShrink: 1 }}>
                        <DoctorDepartmentSelectView
                            doctorName={info.doctorName}
                            departmentName={info.departmentName}
                            doctorMutable={doctorMutable}
                            departmentMutable={departmentMutable}
                            onChanged={onDoctorChanged}
                        />
                    </View>
                </View>
                <View style={{ width: pxToDp(60) }}>
                    <Text
                        style={{
                            ...(forceFontColor ? TextStyles.t16MT4 : TextStyles.t16MB),
                            textAlign: "right",
                        }}
                    >
                        {ABCUtils.formatPrice(chargeFormItem.unitCount! * chargeFormItem.unitPrice!)}
                    </Text>
                </View>
            </View>
        );
    }
}

interface SingleBargainPrescriptionPriceFooterProps {
    totalPrice?: number;
    count?: number | string;
    unit?: string;
    customTitle?: string;
}

export class SingleBargainPrescriptionPriceFooter extends BaseComponent<SingleBargainPrescriptionPriceFooterProps> {
    static defaultProps = {
        unit: "种",
    };

    constructor(props: SingleBargainPrescriptionPriceFooterProps) {
        super(props);
    }

    render(): JSX.Element {
        const { totalPrice, count, unit } = this.props;
        return (
            <View
                style={{
                    ...ABCStyles.rowAlignCenter,
                    height: Sizes.dp36,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.priceStatistics,
                }}
            >
                <Spacer />

                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2 })}>{`${count}${unit ?? ""}`}</Text>
                    <Text style={TextStyles.t14NT1}> {`  ${abcI18Next.t("¥")}${ABCUtils.formatPrice(totalPrice ?? 0)}`}</Text>
                </View>
            </View>
        );
    }
}

interface SingleBargainAirPharmacyPrescriptionPriceFooterProps {
    totalPrice?: number;
    count: number;
    dosageCount: number;
    vendorName?: string;
    editable?: boolean; //default true
    isTotalPriceChanged?: boolean;

    onChangeTotalPrice?(price?: number): void;
}

export class SingleBargainAirPharmacyPrescriptionPriceFooter extends BaseComponent<SingleBargainAirPharmacyPrescriptionPriceFooterProps> {
    constructor(props: SingleBargainAirPharmacyPrescriptionPriceFooterProps) {
        super(props);
    }

    render(): JSX.Element {
        const { totalPrice, count, dosageCount, editable, isTotalPriceChanged } = this.props;
        return (
            <View
                style={{
                    ...ABCStyles.rowAlignCenter,
                    height: Sizes.dp36,
                    paddingLeft: Sizes.dp12,
                    paddingRight: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.priceStatistics,
                }}
            >
                <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2 })}> {`${dosageCount}剂${count}味`}</Text>
                <Spacer />

                {!editable && (
                    <Text style={[TextStyles.t14MT1, { width: Sizes.dp70, textAlign: "right" }]}>
                        {ABCUtils.formatPrice(totalPrice ?? 0)}
                    </Text>
                )}
                {editable && (
                    <CustomInput
                        borderType={"bottom"}
                        style={{ width: Sizes.dp70, textAlign: "right" }}
                        value={ABCUtils.formatPrice(totalPrice ?? 0)}
                        onBlur={(value) => {
                            if (!value) return;
                            this.props.onChangeTotalPrice?.(StringUtils.parseFloat(value, undefined));
                        }}
                        textStyle={{ color: isTotalPriceChanged ? Colors.Y2 : Colors.T1, fontWight: FontWeight.bold }}
                        disableStyle={{ fontSize: Sizes.dp14 }}
                    />
                )}
            </View>
        );
    }
}

interface DeliveryFeeItemProps {
    editable: boolean;
    addressEditable?: boolean; //default true
    deliveryInfo?: DeliveryInfo;
    showErrorHint?: boolean; //false
    checkDeliveryCompany?: boolean; //default false
    chargeData?: ChargeInvoiceDetailData;
    chargeForm?: ChargeForm;
    patient?: Patient;
    feeMutable?: boolean;
    showFee?: boolean; //default true
    charged?: boolean;
    chargeStatus?: number;
    bottomLine?: boolean;
    onChanged?: (deliveryInfo?: DeliveryInfo, chargeForm?: ChargeForm) => void;
    style?: Style;
    companyEditable?: boolean; // default true
    isCanCheckDetail?: boolean; // 收费处已收费时，快递信息不可点击(在原来基础逻辑上，用于禁用快递信息的编辑)

    showDialog?: boolean; // 以弹窗形式显示
}

export class DeliveryFeeItem extends BaseComponent<DeliveryFeeItemProps> {
    static defaultProps = {
        feeMutable: true,
        showDialog: false, // 默认显示页面
    };

    protected bottomLine: boolean;

    constructor(props: DeliveryFeeItemProps) {
        super(props);
        this.bottomLine = props.bottomLine ?? true;
    }

    protected get deliveryInfo(): DeliveryInfo | undefined {
        return this.props.deliveryInfo ?? this.props.chargeForm?.deliveryInfo ?? new DeliveryInfo();
    }

    protected get addressLocked(): boolean {
        const { chargeStatus, chargeForm, editable } = this.props;
        const status = chargeForm?.status ?? ChargeStatus.unCharged;
        return (
            !editable ||
            chargeStatus == ChargeFormItemStatus.refunded ||
            ((chargeForm?.isAirPharmacy ?? false) && status != ChargeStatus.unCharged && status != ChargeStatus.draft)
        );
    }

    async _onDeliveryInfoEditTap(): Promise<void> {
        const {
            chargeData,
            chargeForm,
            feeMutable = false,
            charged = false,
            editable,
            addressEditable = true,
            companyEditable = true,
            isCanCheckDetail = false,
            showDialog,
        } = this.props;
        if (this.addressLocked) return;

        if (this.deliveryInfo) this.deliveryInfo.deliveryFee__ = this.deliveryInfo.expectDeliverFee__ = chargeData?.deliveryFee;

        //空中药房快递地址编辑
        const isAirPharmacy = chargeForm?.isAirPharmacy ?? false;
        if (isAirPharmacy) {
            const airPharmacyPrescription = chargeData?.chargeForms?.filter((t) => t.isAirPharmacy);
            const airPharmacyList = airPharmacyPrescription?.map((item, index) => {
                item.sort = index;
                return item;
            });
            const otherAirPharmacyForms = airPharmacyList
                ?.filter((t) => t?.keyId != chargeForm?.keyId)
                ?.map((formItem) => {
                    const isPiece = (() => {
                        if (!!formItem?.specification) {
                            return ChineseMedicineSpecType.chinesePiece == ChineseMedicineSpecType.typeFromName(formItem.specification);
                        }
                        return formItem.medicineStateScopeId != MedicineScopeId.keLi;
                    })();
                    return {
                        deliveryInfo: formItem?.deliveryInfo,
                        keyId: formItem?.keyId,
                        usageScopeId: formItem?.usageScopeId,
                        vendorId: formItem?.vendorId,
                        usageInfo: formItem.usageInfo,
                        goodsTypeId: isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule,
                        items: formItem?.chargeFormItems
                            ?.filter((item) => !item.isDelivery && !item.isDecoction)
                            ?.map((sub) => {
                                return {
                                    doseCount: formItem?.doseCount,
                                    name: sub?.displayName,
                                    productId: sub?.productId,
                                    unit: sub?.unit,
                                    unitCount: sub?.unitCount,
                                    unitPrice: sub?.unitPrice,
                                };
                            }),
                        sort: formItem.sort,
                    };
                });
            const result = await AirPharmacyDeliveryInfoEditPage.show({
                chargeForm: chargeForm!,
                patient: this.props.patient!,
                payTypeMutable: !charged,
                addressMutable: true,
                deliverFeeEnable: feeMutable,
                otherAirPharmacyCalculateForm: chargeData?.status == ChargeStatus.draft ? undefined : otherAirPharmacyForms, //零售收费的空中药房只有一个
                airPharmacySort: chargeForm?.sort,
            });
            if (result && result.deliverInfo) {
                this.props.onChanged?.(result.deliverInfo, chargeForm);
            }
            return;
        }

        const commonOptions = {
            patient: this.props.patient!,
            deliveryInfo: this.deliveryInfo!,
            payTypeMutable: !charged,
            addressMutable: editable && addressEditable && !isCanCheckDetail,
            deliverFeeEnable: feeMutable,
            chargeData: chargeData,
            chargeForm: chargeForm,
            companyEditable: editable && companyEditable && !isCanCheckDetail,
            editCourierNumber: !isCanCheckDetail,
            isCallDeliveryRule: false,
        };

        const result: DeliveryInfoEditResult | undefined = !!showDialog
            ? await DeliveryInfoEditPage.showBottomPanel({
                  ...commonOptions,
                  showDialog: true,
              })
            : await DeliveryInfoEditPage.show(commonOptions);
        if (result && result.deliverInfo) {
            this.props.onChanged?.(result.deliverInfo);
        }
    }

    _OnCheckExpressInfo(): void {
        LogUtils.d("查看物流信息");
    }

    render(): JSX.Element {
        const {
            style,
            chargeStatus,
            chargeData,
            chargeForm,
            showFee = true,
            showErrorHint = false,
            bottomLine = false,
            checkDeliveryCompany = false,
        } = this.props;
        const deliveryInfo = this.deliveryInfo;
        const address = deliveryInfo?.displayAddress() ?? "";
        const addressEmpty = _.isEmpty(address);

        let deliveryFee: number | undefined = undefined;
        if (chargeForm) {
            deliveryFee = chargeForm.getFormItem(GoodsType.deliveryFee)?.totalPrice;
        } else if (deliveryInfo?.deliveryPayType === DeliveryPayType.cashNow) {
            deliveryFee = chargeData?.deliveryFee ?? 0;
        }

        const error = showErrorHint && (addressEmpty || (checkDeliveryCompany && _.isEmpty(deliveryInfo?.deliveryCompany?.id)));

        return (
            <ChargeListSettingItem
                title={"配送费"}
                iconName={"charge_delivery"}
                showErrorBorder={error}
                rightType={`${this.deliveryInfo?.deliveryPayType == DeliveryPayType.freightCollect ? "到付" : "寄付"} · ${
                    this.deliveryInfo?.deliveryCompany?.name ?? ""
                }`}
                rightCont={ABCUtils.formatPrice(deliveryFee ?? 0)}
                onClick={() => this._onDeliveryInfoEditTap()}
                editable={!this.addressLocked}
            />
        );
        return (
            <View
                style={[
                    Sizes.listBorderPadding,
                    {
                        minHeight: Sizes.listItemHeight,
                        // justifyContent: "space-evenly"
                    },
                    bottomLine ? ABCStyles.bottomLine : {},
                    style ?? {},
                    error ? ABCStyles.errorBorder : {},
                ]}
                onClick={() => this._onDeliveryInfoEditTap()}
            >
                <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp44 }]}>
                    <Text style={[TextStyles.t16MT1]}>{`快递费  `}</Text>
                    {!addressEmpty && (
                        <Text style={[TextStyles.t14NT2]}>
                            {`${this.deliveryInfo?.deliveryName ?? ""} ${this.deliveryInfo?.deliveryMobile ?? ""}`}
                        </Text>
                    )}
                    {<ChargeStatusView chargeStatus={chargeStatus} />}
                    <Spacer />
                    {!addressEmpty && showFee && deliveryFee != undefined && (
                        <Text style={[TextStyles.t16NT1]}>{ABCUtils.formatPrice(deliveryFee ?? 0)}</Text>
                    )}
                    {addressEmpty && !this.addressLocked && <Text style={TextStyles.t14NT4}>选择配送地址</Text>}
                    {addressEmpty && !this.addressLocked && <RightArrowView />}
                </View>
                {!addressEmpty && (
                    <AbcView
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                flex: 1,
                                justifyContent: "space-between",
                                marginBottom: Sizes.dp12,
                            },
                        ]}
                    >
                        <View>
                            <Text numberOfLines={1} style={[TextStyles.t14NT2, { flexShrink: 1, lineHeight: Sizes.dp20 }]}>
                                {`${address}`}
                            </Text>
                            <Text style={[TextStyles.t14NT2, { flexShrink: 1, lineHeight: Sizes.dp20 }]}>
                                {`${this.deliveryInfo?.deliveryPayType == DeliveryPayType.freightCollect ? "到付" : "寄付"} · ${
                                    this.deliveryInfo?.deliveryCompany?.name ?? ""
                                } `}
                            </Text>
                        </View>
                        {!this.addressLocked && <RightArrowView />}
                    </AbcView>
                )}
                {!addressEmpty && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenterSpaceBetween,
                            Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8),
                            { backgroundColor: Colors.bg1, marginBottom: Sizes.dp16 },
                        ]}
                    >
                        <Text numberOfLines={1} style={[TextStyles.t14NT2, { flexShrink: 1 }]}>
                            您的快件由【成都新都分拣中心】准备发…
                        </Text>
                        <AbcText style={[TextStyles.t14NM]} onClick={() => this._OnCheckExpressInfo()}>
                            详情
                        </AbcText>
                    </View>
                )}
            </View>
        );
    }
}

interface DecoctionFeeItemProps extends AbcViewProps {
    fee?: number;
    editable: boolean;
    feeMutable?: boolean;
    showFee?: boolean;
    chargeStatus?: number;
    bottomLine?: boolean;
    chargeData?: ChargeInvoiceDetailData;
    chargeForm?: ChargeForm;
    isOpenTakeMedicine?: boolean;
    onChanged?: (contactMobile?: string, decoctionForms?: ChargeForm[]) => void;
}

export class DecoctionFeeItem extends BaseComponent<DecoctionFeeItemProps> {
    static defaultProps = {
        feeMutable: true,
        showFee: true,
        bottomLine: true,
    };

    constructor(props: DecoctionFeeItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeData, chargeForm, showFee, chargeStatus, bottomLine, onChanged, style, editable, isOpenTakeMedicine, ...otherProps } =
            this.props;
        const feeMutable = (this.props.feeMutable ?? true) && editable;

        let { fee } = this.props;
        if (fee === undefined) {
            if (chargeForm) {
                fee = chargeForm.getFormItem(GoodsType.decoctionFee)?.totalPrice;
            }
        }
        const takeMedicineTime = ChargeUtils.getProcessTime(chargeData, chargeForm);
        return (
            <ChargeListSettingItem
                title={"加工费"}
                iconName={"charge_process_fee"}
                onClick={async () => {
                    if (!chargeData || !editable || !chargeForm?.processInfo) return;
                    const data = await MedicineProcessCostPage.show(chargeData, feeMutable, isOpenTakeMedicine);
                    if (!data) {
                        return false;
                    }

                    chargeData.contactMobile = data.contactMobile;
                    const forms = data.chargeForms?.filter((form) => form.isDecoction);
                    onChanged?.(data.contactMobile, forms);
                }}
                editable={!(!chargeData || !editable || !chargeForm?.processInfo)}
                chargeStatus={chargeStatus}
                contentBuilder={() => {
                    return (
                        <View style={{ justifyContent: "flex-end", alignItems: "flex-end" }}>
                            <Text style={[TextStyles.t16NT1]}>
                                {(chargeForm?.getFormItem(GoodsType.decoctionFee)?.name ?? "") +
                                    (!!chargeForm?.getFormItem(GoodsType.decoctionFee)?.name
                                        ? "｜" + ABCUtils.formatPrice(fee ?? 0)
                                        : ABCUtils.formatPrice(fee ?? 0))}
                            </Text>
                            <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })}>
                                {chargeData ? ChargeUtils.getProcessInfoDescription(chargeData!, chargeForm!) : ""}
                            </Text>
                            {isOpenTakeMedicine && (
                                <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })}>
                                    {!!takeMedicineTime ? takeMedicineTime : "取药时间:"}
                                </Text>
                            )}
                        </View>
                    );
                }}
            />
        );

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.listBorderPadding,
                    bottomLine ? ABCStyles.bottomLine : {},
                    {
                        height: Sizes.listItemHeight,
                    },
                    flattenStyles(style),
                ]}
                {...otherProps}
                onClick={async () => {
                    if (!chargeData || !editable || !chargeForm?.processInfo) return;
                    const data = await MedicineProcessCostPage.show(chargeData, feeMutable);
                    if (!data) {
                        return false;
                    }

                    chargeData.contactMobile = data.contactMobile;
                    const forms = data.chargeForms?.filter((form) => form.isDecoction);
                    onChanged?.(data.contactMobile, forms);
                }}
            >
                <Text style={TextStyles.t16MT1}>{chargeForm?.getFormItem(GoodsType.decoctionFee)?.name ?? "加工费"}</Text>
                <ChargeStatusView chargeStatus={chargeStatus} />
                <Text numberOfLines={1} style={[{ flex: 1 }, TextStyles.t14NT2]}>
                    {chargeData ? ChargeUtils.getProcessInfoDescription(chargeData!, chargeForm!) : ""}
                </Text>

                {showFee && <Text style={[TextStyles.t16NT1, { marginRight: Sizes.dp4 }]}>{ABCUtils.formatPrice(fee ?? 0)}</Text>}
                {showFee && feeMutable && <RightArrowView />}
            </View>
        );
    }
}

interface AirPharmacyFeeItemProps extends AbcViewProps {
    chargeForm: ChargeForm;
    bottomLine?: boolean;

    onChange?(arg1: ChargeFormItem): void;
}

export class AirPharmacyDecoctionFeeItem extends BaseComponent<AirPharmacyFeeItemProps> {
    static defaultProps = {
        feeMutable: true,
        showFee: true,
        bottomLine: true,
    };

    constructor(props: AirPharmacyFeeItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForm, bottomLine, style, ...otherProps } = this.props;
        const decoctionFeeItem = chargeForm.getFormItem(GoodsType.decoctionFee) ?? new ChargeFormItem();
        const fee = decoctionFeeItem?.totalPrice;
        const chargeStatus = decoctionFeeItem?.status;

        return (
            <ChargeListSettingItem
                title={"加工费"}
                iconName={"charge_process_fee"}
                chargeStatus={chargeStatus}
                contentBuilder={() => {
                    return chargeForm.isVirtualPharmacy ? (
                        <AbcTextInput
                            style={{ flex: 1, height: Sizes.dp20, ...TextStyles.t16NT1, textAlign: "right" }}
                            numberOfLines={1}
                            multiline={false}
                            defaultValue={ABCUtils.formatPrice(fee ?? 0)}
                            formatter={PrecisionLimitFormatter(2)}
                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                            onChangeText={(text) => {
                                // decoctionFeeItem.totalPrice = StringUtils.parseFloat(text);
                                // decoctionFeeItem.unitPrice = StringUtils.parseFloat(text);
                                decoctionFeeItem.expectedTotalPrice = StringUtils.parseFloat(text);
                                this.props.onChange?.(decoctionFeeItem);
                                this.forceUpdate();
                            }}
                        />
                    ) : (
                        <Text style={[TextStyles.t16NT1, { textAlign: "right" }]}>{ABCUtils.formatPrice(fee ?? 0)}</Text>
                    );
                }}
            />
        );

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.listBorderPadding,
                    bottomLine ? ABCStyles.bottomLine : {},
                    {
                        height: Sizes.listItemHeight,
                    },
                    flattenStyles(style),
                ]}
                {...otherProps}
            >
                <Text style={TextStyles.t16MT1}>加工费</Text>
                <ChargeStatusView chargeStatus={chargeStatus} />
                <Text numberOfLines={1} style={[{ flex: 1 }, TextStyles.t14NT2]}>
                    {(decoctionFeeItem?.productInfo as DecoctionProductInfo)?.description ?? ""}
                </Text>
                {chargeForm.isVirtualPharmacy ? (
                    <AbcTextInput
                        style={{ flex: 1, height: Sizes.dp20, ...TextStyles.t16NT1, textAlign: "right" }}
                        numberOfLines={1}
                        multiline={false}
                        defaultValue={ABCUtils.formatPrice(fee ?? 0)}
                        formatter={PrecisionLimitFormatter(2)}
                        customKeyboardBuilder={new NumberKeyboardBuilder({})}
                        onChangeText={(text) => {
                            // decoctionFeeItem.totalPrice = StringUtils.parseFloat(text);
                            // decoctionFeeItem.unitPrice = StringUtils.parseFloat(text);
                            decoctionFeeItem.expectedTotalPrice = StringUtils.parseFloat(text);
                            this.props.onChange?.(decoctionFeeItem);
                            this.forceUpdate();
                        }}
                    />
                ) : (
                    <Text style={TextStyles.t16NT1}>{ABCUtils.formatPrice(fee ?? 0)}</Text>
                )}
            </View>
        );
    }
}

interface AirPharmacyIngredientFeeItemProps extends AbcViewProps {
    chargeForm: ChargeForm;
    bottomLine?: boolean;
}

/**
 * 空中药方辅料费
 */
export class AirPharmacyIngredientFeeItem extends BaseComponent<AirPharmacyIngredientFeeItemProps> {
    static defaultProps = {
        feeMutable: true,
        showFee: true,
        bottomLine: true,
    };

    constructor(props: AirPharmacyFeeItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForm, bottomLine, style, ...otherProps } = this.props;
        const ingredientFeeItem = chargeForm.getFormItem(GoodsType.ingredient);
        const fee = ingredientFeeItem?.totalPrice;
        const chargeStatus = ingredientFeeItem?.status;

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    Sizes.listBorderPadding,
                    bottomLine ? ABCStyles.bottomLine : {},
                    {
                        height: Sizes.listItemHeight,
                    },
                    flattenStyles(style),
                ]}
                {...otherProps}
            >
                <Text style={TextStyles.t16MT1}>辅料费</Text>
                <ChargeStatusView chargeStatus={chargeStatus} />
                <Text numberOfLines={1} style={[{ flex: 1 }, TextStyles.t14NT2]}>
                    {(ingredientFeeItem?.productInfo as IngredientProductInfo)?.description ?? ""}
                </Text>
                <Text style={TextStyles.t16NT1}>{ABCUtils.formatPrice(fee ?? 0)}</Text>
            </View>
        );
    }
}

interface ChargeFormItemAmountInputViewProps extends AbcViewProps {
    autoFocus?: boolean;
    chargeFormItem: ChargeFormItem;
    select?: boolean;
    onChanged?: (count?: number, unit?: string) => void;
    onClick?: () => void;
    onCloseTap?: () => void;
    onEditFocusChanged?: (focus: boolean) => void;

    height?: number;
    showCheckBox?: boolean;
    checked?: boolean;
    style?: Style;
}

export class ChargeFormItemAmountInputView extends BaseComponent<ChargeFormItemAmountInputViewProps> {
    private _chargeInvoiceInput?: AbcTextInput | null;
    constructor(props: ChargeFormItemAmountInputViewProps) {
        super(props);
    }

    private _createActionBtn(): ActionBtn[] {
        const list: ActionBtn[] = [];
        list.push({
            text: "删除",
            handleClick: () => {
                this.props?.onCloseTap?.();
            },
        });

        return list;
    }

    render(): JSX.Element {
        const { onCloseTap, select, chargeFormItem, showCheckBox = false, checked = false, style, onChanged, ...otherProps } = this.props;
        return (
            <View
                style={[ABCStyles.rowAlignCenterSpaceBetween, { flex: 1, paddingBottom: Sizes.dp16, paddingTop: Sizes.dp10 }]}
                {...otherProps}
            >
                <View style={{ flex: 1, flexDirection: "row", paddingTop: Sizes.dp6 }}>
                    {showCheckBox && (
                        <View style={{ paddingRight: Sizes.dp8 }}>
                            {checked ? (
                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={Colors.mainColor} />
                            ) : (
                                <View
                                    style={{
                                        width: Sizes.dp16,
                                        height: Sizes.dp16,
                                        borderWidth: Sizes.dp1,
                                        borderRadius: Sizes.dp8,
                                        borderColor: Colors.P1,
                                    }}
                                />
                            )}
                        </View>
                    )}
                    <View style={{ flexShrink: 1 }}>
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <View style={{ flexShrink: 1 }}>
                                <StockNotEnoughTextView
                                    goodsStock={chargeFormItem.stockInfo()}
                                    text={chargeFormItem.name}
                                    textStyle={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })}
                                    tipContent={this._createActionBtn()}
                                    hasOtherOperate={select}
                                    bgColor={"rgba(51, 51, 51, 0.95)"}
                                />
                            </View>
                            {chargeFormItem.isPackage && <Text style={TextStyles.t16NT4}>【套餐】</Text>}
                        </View>
                        <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })}>
                            {chargeFormItem.goodsInfo.priceSpec ?? ""}
                        </Text>
                    </View>
                </View>
                <View style={{ paddingLeft: Sizes.dp4 }}>
                    <CustomInput
                        autoFocus={this.props.autoFocus}
                        ref={(ref) => (this._chargeInvoiceInput = ref?._textInput)}
                        style={{ width: Sizes.dp70 }}
                        borderType={"boxBorder"}
                        type={"input"}
                        value={chargeFormItem.unitCount ? chargeFormItem.unitCount : undefined}
                        formatter={PrecisionLimitFormatter(0)}
                        error={!Boolean(chargeFormItem.unitCount)}
                        onChange={(value) => onChanged?.(Number(value), undefined)}
                        alwaysShowUtil={true}
                        unit={chargeFormItem.unit}
                        unitList={chargeFormItem.goodsInfo.sellUnits}
                        containerStyle={{ height: Sizes.dp32 }}
                        textStyle={TextStyles.t13NT9.copyWith({ color: Colors.t1 })}
                        onChangeUnit={(unit) => onChanged?.(undefined, unit)}
                    />
                </View>
            </View>
        );

        return (
            <View style={[ABCStyles.rowAlignCenter, { height: Sizes.dp80 }, style ?? {}]}>
                {showCheckBox && <AbcCheckbox check={checked} style={{ paddingRight: 6 }} checkIconBgColor={Colors.white} />}

                <View style={{ justifyContent: "center", flex: 1 }}>
                    <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp18 }]}>
                        <View style={{ flex: 1, flexDirection: "row" }}>
                            <StockNotEnoughTextView
                                goodsStock={chargeFormItem.stockInfo()}
                                text={chargeFormItem.name}
                                textStyle={TextStyles.mainTitleStyle}
                            />
                            {chargeFormItem.isPackage && <Text style={TextStyles.t14NT4}>【套餐】</Text>}
                            <Text style={TextStyles.subTitleStyle}>{chargeFormItem.goodsInfo.packageSpec ?? ""}</Text>
                        </View>

                        <Text style={TextStyles.t16MB}>{ABCUtils.formatPrice(chargeFormItem.totalPrice!)}</Text>
                        {select && <CloseBtn onClick={onCloseTap} />}
                    </View>
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t14NT1}>{chargeFormItem.goodsInfo.priceSpec ?? ""}</Text>
                        <Spacer />

                        <MedicineSellCountInputButton
                            autoFocus={select}
                            count={chargeFormItem.unitCount!}
                            unit={chargeFormItem.unit ?? ""}
                            dosageCount={chargeFormItem.doseCount ?? 0}
                            goodsInfo={chargeFormItem.goodsInfo}
                            floatPrecisionLength={0}
                            onChanged={(value) => onChanged?.(value, undefined)}
                        />

                        <SizedBox width={Sizes.dp7} />

                        <MedicineSellUnitSelectButton
                            unit={chargeFormItem.unit}
                            units={chargeFormItem.goodsInfo.sellUnits}
                            onUnitSelected={(unit) => onChanged?.(undefined, unit)}
                        />
                    </View>
                </View>
            </View>
        );
    }
}

interface ChargeFormHeaderProps {
    decoction: boolean;
    delivery: boolean;
    mutable: boolean;
    showDoctorDiagnosis: boolean;
    doctorDiagnosis: boolean;
    onChanged?: (decoction?: boolean, delivery?: boolean) => void;
    onDoctorDiagnosisCheckChanged?: (check: boolean) => void;
    notShowBottomLine?: boolean; //不需要显示底部边线
    isOpenProcess?: boolean; //是否开启加工
}

export class ChargeFormNewHeader extends BaseComponent<ChargeFormHeaderProps> {
    static defaultProps = {
        decoction: false,
        delivery: false,
        mutable: true,
    };

    constructor(props: ChargeFormHeaderProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            delivery,
            mutable,
            onChanged,
            decoction,
            doctorDiagnosis,
            showDoctorDiagnosis,
            onDoctorDiagnosisCheckChanged,
            notShowBottomLine,
            isOpenProcess,
        } = this.props;
        const showBottomLine = (showDoctorDiagnosis && doctorDiagnosis) || (!userCenter.clinic?.isDentistryClinic && decoction) || delivery;
        return (
            <View>
                <Text style={[TextStyles.t18MT1, { lineHeight: Sizes.dp26 }]}>{"收费项"}</Text>
                <View style={[ABCStyles.rowAlignCenter, showBottomLine ? { paddingTop: Sizes.dp16 } : { paddingVertical: Sizes.dp16 }]}>
                    {showDoctorDiagnosis && (
                        <View
                            style={[
                                ABCStyles.rowAlignCenter,
                                Sizes.paddingLTRB(Sizes.dp14, Sizes.dp8),

                                {
                                    marginRight: Sizes.dp8,
                                    backgroundColor: doctorDiagnosis && mutable ? Colors.mainColor_008 : Colors.bg1,
                                    borderRadius: Sizes.dp4,
                                    borderWidth: doctorDiagnosis && mutable ? Sizes.dpHalf : 0,
                                    borderColor: doctorDiagnosis && mutable ? Colors.mainColor : undefined,
                                },
                            ]}
                            onClick={() => mutable && onDoctorDiagnosisCheckChanged?.(!doctorDiagnosis)}
                        >
                            {doctorDiagnosis ? (
                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={mutable ? Colors.mainColor : Colors.P1} />
                            ) : (
                                <View
                                    style={{
                                        width: Sizes.dp16,
                                        height: Sizes.dp16,
                                        borderWidth: Sizes.dp1,
                                        borderRadius: Sizes.dp8,
                                        borderColor: Colors.P1,
                                        backgroundColor: !mutable ? Colors.dividerLineColor : undefined,
                                    }}
                                />
                            )}
                            <SizedBox width={Sizes.dp4} />
                            <Text style={TextStyles.t14NT1.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>{"诊断"}</Text>
                        </View>
                    )}
                    {!userCenter.clinic?.isDentistryClinic && isOpenProcess && (
                        <View
                            style={[
                                ABCStyles.rowAlignCenter,
                                Sizes.paddingLTRB(Sizes.dp14, Sizes.dp8),
                                {
                                    marginRight: Sizes.dp8,
                                    backgroundColor: decoction && mutable ? Colors.mainColor_008 : Colors.bg1,
                                    borderRadius: Sizes.dp4,
                                    borderWidth: decoction && mutable ? Sizes.dpHalf : 0,
                                    borderColor: decoction && mutable ? Colors.mainColor : undefined,
                                },
                            ]}
                            onClick={() => mutable && onChanged?.(!decoction, undefined)}
                        >
                            {decoction ? (
                                <IconFontView name={"Chosen"} size={Sizes.dp16} color={mutable ? Colors.mainColor : Colors.P1} />
                            ) : (
                                <View
                                    style={{
                                        width: Sizes.dp16,
                                        height: Sizes.dp16,
                                        borderWidth: Sizes.dp1,
                                        borderRadius: Sizes.dp8,
                                        borderColor: Colors.P1,
                                        backgroundColor: !mutable ? Colors.dividerLineColor : undefined,
                                    }}
                                />
                            )}
                            <SizedBox width={Sizes.dp4} />
                            <Text style={TextStyles.t14NT1.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>{"加工"}</Text>
                        </View>
                    )}
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            Sizes.paddingLTRB(Sizes.dp14, Sizes.dp8),
                            {
                                marginRight: Sizes.dp8,
                                backgroundColor: delivery && mutable ? Colors.mainColor_008 : Colors.bg1,
                                borderRadius: Sizes.dp4,
                                borderWidth: delivery && mutable ? Sizes.dpHalf : 0,
                                borderColor: delivery && mutable ? Colors.mainColor : undefined,
                            },
                        ]}
                        onClick={() => mutable && onChanged?.(undefined, !delivery)}
                    >
                        {delivery ? (
                            <IconFontView name={"Chosen"} size={Sizes.dp16} color={mutable ? Colors.mainColor : Colors.P1} />
                        ) : (
                            <View
                                style={{
                                    width: Sizes.dp16,
                                    height: Sizes.dp16,
                                    borderWidth: Sizes.dp1,
                                    borderRadius: Sizes.dp8,
                                    borderColor: Colors.P1,
                                    backgroundColor: !mutable ? Colors.dividerLineColor : undefined,
                                }}
                            />
                        )}
                        <SizedBox width={Sizes.dp4} />
                        <Text style={TextStyles.t14NT1.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>{"配送"}</Text>
                    </View>
                </View>
                {!showBottomLine && !notShowBottomLine && <DividerLine />}
            </View>
        );
    }
}

interface DoctorDepartmentSelectViewProps {
    departmentName?: string;
    doctorName?: string;

    doctorMutable?: boolean;
    departmentMutable?: boolean;
    onChanged?: (department: Department, doctor: Doctor) => void;
}

export class DoctorDepartmentSelectView extends BaseComponent<DoctorDepartmentSelectViewProps> {
    _doctorName?: string;
    _departmentName?: string;
    _departments?: Department[];

    constructor(props: DoctorDepartmentSelectViewProps) {
        super(props);

        const { doctorName, departmentName } = this.props;
        this._doctorName = doctorName;
        this._departmentName = departmentName;
    }

    render(): JSX.Element {
        const { doctorMutable, departmentMutable } = this.props;

        let title: string | undefined;
        if (!_.isEmpty(this._departmentName)) {
            title = this._departmentName;
        }

        if (!_.isEmpty(this._doctorName)) {
            title = !_.isEmpty(title) ? `${title}-${this._doctorName}` : this._doctorName;
        }

        const mutable = doctorMutable || departmentMutable;
        if (_.isEmpty(title) && mutable) {
            title = "选择医生";
        }
        return (
            <View
                style={{
                    backgroundColor: mutable ? undefined : Colors.white,
                    borderRadius: Sizes.dp12,
                    justifyContent: "center",
                    flex: 1,
                }}
                onClick={this._onTap.bind(this)}
            >
                <Text numberOfLines={1} style={[mutable ? TextStyles.t16NB : TextStyles.t16NT1, { flexShrink: 1 }]}>
                    {title ?? ""}
                </Text>
            </View>
        );
    }

    async _onTap(): Promise<void> {
        const { doctorMutable, departmentMutable } = this.props;
        const mutable = doctorMutable || departmentMutable;
        if (!mutable) return;

        this._departments = this._departments ?? (await RegistrationDataProvider.getDoctorList());
        const select = await DoctorDepartmentSelectDialog.show(this._departmentName, this._doctorName, this._departments);

        if (!select) return;

        this._departmentName = select.first.departmentName;
        this._doctorName = select.second.doctorName;
        this.setState({});

        this.props.onChanged?.(select.first, select.second);
    }
}

interface OnlineOutpatientDisableProps {
    text?: string;
}

export class OnlineOutpatientDisableView extends BaseComponent<OnlineOutpatientDisableProps> {
    constructor(props: OnlineOutpatientDisableProps) {
        super(props);
    }

    render(): JSX.Element {
        return (
            <View style={[ABCStyles.centerChild]}>
                <AssetImageView name={"online_doctor_hint"} style={{ width: Sizes.dp80, height: Sizes.dp80 }} />
                <SizedBox height={Sizes.dp16} />
                <Text style={[TextStyles.t14NT3]}>{this.props.text ?? `${runtimeConstants.PRODUCT_NAME_THUMB}未开通在线问诊`}</Text>
            </View>
        );
    }
}

interface SelectableContainerProps {
    select: boolean;
    style?: Style;
    enable?: boolean; //default true
    onClick?: () => void;
    method?: number;
    showAbcFlag?: boolean;
    showTips?: boolean; //是否显示提示
}

export class SelectableContainer extends BaseComponent<SelectableContainerProps> {
    render(): JSX.Element {
        const { select, style = {}, onClick, enable = true, children, method, showAbcFlag = false, showTips = false } = this.props;
        let isShowSelected = false; // 判断是否有选中效果(ABC支付在未开通的情况下，点击不添加选中效果)
        if (method == PayMethod.payABCPay) {
            isShowSelected = select && showAbcFlag;
        } else {
            isShowSelected = select;
        }
        return (
            <AbcView
                style={[
                    style,
                    ABCStyles.rowAlignCenter,
                    {
                        height: Sizes.dp56,
                        justifyContent: "center",
                        borderRadius: Sizes.dp4,
                        borderColor: isShowSelected ? Colors.mainColor : enable ? Colors.P1 : Colors.bottomBtnDisable,
                        borderWidth: isShowSelected ? pxToDp(1.5) : Sizes.dp1,
                    },
                ]}
                onClick={enable || showTips ? onClick : undefined}
            >
                {children}
                {isShowSelected && (
                    <View
                        style={{ overflow: "hidden", position: "absolute", top: 0, right: 0, width: Sizes.dp40, height: Sizes.dp40 }}
                        collapsable={false}
                    >
                        <View
                            style={{
                                position: "absolute",
                                top: 0,
                                right: 0,
                                borderRadius: Sizes.dp4,
                                overflow: DeviceUtils.isIOS() ? "hidden" : undefined, //Android上hidden后，在激活输入法后，再打开空中药房收费界面上，顶部部份元素显示不出来，应该是系统的bug
                            }}
                        >
                            <View
                                style={{
                                    width: Sizes.dp40,
                                    height: Sizes.dp40,
                                    backgroundColor: Colors.mainColor,
                                    transform: [
                                        DeviceUtils.isAndroid() ? { rotateZ: "-45deg" } : {},
                                        {
                                            translateX: Sizes.dp22,
                                            translateY: -Sizes.dp22,
                                        },
                                        DeviceUtils.isIOS() ? { rotateZ: "-45deg" } : {},
                                    ],
                                }}
                            />
                            <View style={{ position: "absolute", top: Sizes.dp2, right: Sizes.dp2 }}>
                                <IconFontView name={"Positive_Selected"} size={Sizes.dp10} color={Colors.white} />
                            </View>
                        </View>
                    </View>
                )}
            </AbcView>
        );
    }
}

interface SelectableButtonProps {
    select: boolean;
    icon?: string;
    text?: string;
    textStyle?: Style;
    style?: Style;
    balance?: number; //帐户余额
    enable?: boolean; //default true
    onClick?: () => void;
}

export class SelectableButton extends BaseComponent<SelectableButtonProps> {
    render(): JSX.Element {
        const { select, icon, text, style = {}, onClick, balance, enable = true, textStyle } = this.props;
        return (
            <SelectableContainer
                style={{
                    backgroundColor: !enable ? Colors.bottomBtnDisable : Colors.white,
                    ...style,
                }}
                onClick={enable ? onClick : undefined}
                select={select}
            >
                {icon && (
                    <AssetImageView
                        name={icon}
                        style={{
                            width: Sizes.dp16,
                            height: Sizes.dp16,
                            marginRight: Sizes.dp4,
                        }}
                    />
                )}
                <Text style={textStyle ?? TextStyles.t16NT1}>{text ?? ""}</Text>

                {balance != undefined && (
                    <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp16 }]}>
                        余额：{abcI18Next.t("¥")}
                        {ABCUtils.formatPrice(balance)}
                    </Text>
                )}
            </SelectableContainer>
        );
    }
}

export class SelectableButtonV2 extends SelectableButton {
    render(): JSX.Element {
        const { select, icon, text, style = {}, onClick, balance, enable = true, textStyle } = this.props;
        return (
            <SelectableContainer
                style={{
                    backgroundColor: !enable ? Colors.bottomBtnDisable : Colors.white,
                    paddingHorizontal: Sizes.dp20,
                    ...style,
                }}
                onClick={enable ? onClick : undefined}
                select={select}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-start" }]}>
                    {icon && (
                        <AssetImageView
                            name={icon}
                            style={{
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                                marginRight: Sizes.dp5,
                            }}
                        />
                    )}
                    <Text style={textStyle ?? TextStyles.t14NB}>{text ?? ""}</Text>
                </View>

                {balance != undefined && (
                    <Text style={[TextStyles.t14Nt2, { marginLeft: Sizes.dp16 }]}>余额：{ABCUtils.formatPrice(balance)}</Text>
                )}
            </SelectableContainer>
        );
    }
}

interface ChargeMedicineListItemProps extends AbcViewProps {
    chargeForm: ChargeForm;
    chargeItem: ChargeFormItem;
    onCountInputValueChanged?: (count: number) => void;
    checked: boolean;
    enableCheckBox: boolean;
    showCheckBox: boolean;
    isOpenMultiplePharmacy?: boolean; //是否开启多药房
    isHasPharmacyOperate?: boolean; //是否可以操作多药房按钮
    onChangePharmacyType?: () => void; //切换药房
    onChangeRemark?: () => void; //更新备注
    onDeleteRemark?: () => void; //取消备注
    defaultPharmacyNo?: number;
    onDiscountChange?: (discount: number) => void; //折扣议价
    onTotalPriceChange?: (totalPrice: number) => void; //单项议总价
    chargeSheetStatus?: ChargeStatus; // 收费单状态
    isWholeSheetOperateEnabled?: boolean; // 是否开启整单收退费
}

export class ChargeMedicineListItem extends BaseComponent<ChargeMedicineListItemProps> {
    static defaultProps = {
        enableCheckBox: true,
        showCheckBox: false,
    };

    constructor(props: ChargeMedicineListItemProps) {
        super(props);
    }
    private _getCheckboxColor(): string {
        const { chargeSheetStatus, isWholeSheetOperateEnabled, chargeItem } = this.props;

        // 如果开启了整单收退费且不是已收费状态，显示灰色（不可编辑状态）
        if (chargeSheetStatus !== ChargeStatus.charged && isWholeSheetOperateEnabled) {
            return Colors.P1;
        }

        // 如果不是自备药品且未执行，显示主色调
        if (!chargeItem.isSelfProvided && !chargeItem?.itemIsExcuted) {
            return Colors.mainColor;
        }

        // 默认颜色
        return Colors.P1;
    }

    private _createActionBtn(): ActionBtn[] {
        const list: ActionBtn[] = [];
        const { isOpenMultiplePharmacy, chargeItem, chargeForm } = this.props;
        const prescriptionRemark = !!chargeItem?.remark ? chargeItem.remark : chargeItem?.specialRequirement;
        if (chargeForm.isChinesePrescription) {
            if (isOpenMultiplePharmacy && chargeItem.isCanSpecifyPharmacy) {
                list.push({
                    text: "药品来源",
                    handleClick: () => {
                        this.props?.onChangePharmacyType?.();
                    },
                });
            }
            return list;
        }
        if (isOpenMultiplePharmacy && chargeItem.isCanSpecifyPharmacy) {
            list.push({
                text: "药品来源",
                handleClick: () => {
                    this.props?.onChangePharmacyType?.();
                },
            });
        }

        if (!!prescriptionRemark) {
            list.push({
                text: "修改备注",
                handleClick: () => {
                    this.props?.onChangeRemark?.();
                },
            });
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.props?.onDeleteRemark?.();
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this.props?.onChangeRemark?.();
                },
            });
        }

        return list;
    }

    render(): JSX.Element {
        const {
            chargeForm,
            chargeItem,
            // onCountInputValueChanged,
            checked,
            // enableCheckBox,
            showCheckBox,
            style,
            isHasPharmacyOperate,
            isOpenMultiplePharmacy,
            defaultPharmacyNo,
            onDiscountChange,
            onTotalPriceChange,
            chargeSheetStatus,
            ...otherProps
        } = this.props;

        const stockInfo = chargeItem.stockInfo(chargeForm.pharmacyType);

        let finalChecked = checked;

        const forceFontColor =
            chargeItem.status == ChargeFormItemStatus.refunded ||
            (chargeSheetStatus != ChargeStatus.unCharged && chargeItem.status == ChargeFormItemStatus.chargeBack);

        let productInfo = "";
        let priceInfo = "";
        if (!chargeItem.isRegistration && chargeItem.productInfo != null) {
            productInfo = (chargeItem.productInfo as GoodsInfo).packageSpec;
            priceInfo = chargeItem.priceSpec;
        }

        //已退单的强制不check(但是如果收费单一次都没有收费form上的status为0，对于已退单的收费项是可以操作)
        if (finalChecked && chargeItem.status == ChargeFormItemStatus.chargeBack && chargeForm.status != 0) {
            finalChecked = false;
        }

        //自备药品强制check
        if (chargeItem.isSelfProvided) {
            finalChecked = true;
        }

        let unit = !_.isEmpty(chargeItem.unit) ? chargeItem.unit : chargeItem.goodsInfo.unit;
        if ((chargeForm.isTreatment || chargeForm.isExamination) && GoodsUtils.unitIsCustom(unit)) {
            unit = `*${unit}`;
        }

        let totalPrice = 0;
        if (chargeItem.status == 1) {
            totalPrice = chargeItem.totalPrice!;
        } else {
            totalPrice = chargeItem.unitCount! * chargeItem.unitPrice! * (chargeItem.doseCount ?? 1);
        }

        //备注(诊疗项目的备注字段为remark，处方的备注字段为specialRequirement)
        const prescriptionRemark = !!chargeItem?.remark ? chargeItem?.remark : chargeItem.specialRequirement;

        const charged = chargeItem.status == ChargeFormItemStatus.charged;
        const externalUsage = `${!!chargeItem.usageInfo?.dosage ? "共" + chargeItem.usageInfo?.dosage + "次" : ""}${
            !!chargeItem.usageInfo?.freq ? "，" + chargeItem.usageInfo?.freq : ""
        }${!!prescriptionRemark ? "，" : ""}`;

        // 诊疗项目才显示牙位信息
        const showToothInfo =
            chargeForm.isConsultation ||
            chargeForm.isTreatment ||
            chargeForm.isExamination ||
            chargeForm.isAdditional ||
            chargeForm.isMaterial ||
            chargeForm.isPackage ||
            chargeForm.isGlasses ||
            chargeForm.isNurseProductFee ||
            chargeForm.isOthersFee;

        const toothDetail = new ToothNosDetailInfo();
        const isShowToothStructure =
            !!userCenter.clinic?.isDentistryClinic &&
            showToothInfo &&
            !(toothDetail.isAll || toothDetail.isTopAll || toothDetail.isBottomAll);
        // 是否是一次都没有收费过的收费单下，退单项
        const isRefundItem = chargeSheetStatus == ChargeStatus.unCharged && chargeItem.status == ChargeFormItemStatus.chargeBack;

        return (
            <View style={[{ justifyContent: "center" }, flattenStyles(style)]} {...otherProps}>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, paddingRight: Sizes.dp4 }]}>
                        {showCheckBox && (
                            <View style={{ paddingRight: Sizes.dp8 }}>
                                {finalChecked ? (
                                    <IconFontView name={"Chosen"} size={Sizes.dp16} color={this._getCheckboxColor()} />
                                ) : (
                                    <View
                                        style={{
                                            width: Sizes.dp16,
                                            height: Sizes.dp16,
                                            borderWidth: Sizes.dp1,
                                            borderRadius: Sizes.dp8,
                                            borderColor: Colors.P1,
                                            backgroundColor: chargeItem.isSelfProvided ? Colors.whiteSmoke : undefined,
                                        }}
                                    />
                                )}
                            </View>
                        )}
                        <View style={[ABCStyles.rowAlignCenter, { flexShrink: 1, justifyContent: "flex-start" }]}>
                            {!!userCenter.clinic?.isDentistryClinic && showToothInfo && (
                                <View style={{ alignSelf: "stretch" }}>
                                    <AbcToothView
                                        key={chargeItem.toothNos?.join("")}
                                        toothNos={chargeItem.toothNos}
                                        disable={true}
                                        style={{ marginRight: Sizes.dp4 }}
                                    />
                                </View>
                            )}
                            <StockNotEnoughTextView
                                style={{ flexShrink: 1 }}
                                goodsStock={(chargeItem.status ?? 0) == ChargeFormItemStatus.unCharged ? stockInfo : undefined}
                                text={chargeItem.displayName ?? ""}
                                textStyle={{
                                    ...TextStyles.t16NT1.copyWith({
                                        color: forceFontColor ? Colors.T4 : Colors.T1,
                                        lineHeight: Sizes.dp22,
                                    }),
                                }}
                                tipContent={this._createActionBtn()}
                                hasOtherOperate={showCheckBox}
                                bgColor={"rgba(51, 51, 51, 0.95)"}
                            />
                        </View>
                        {chargeItem.isPackage && <Text style={TextStyles.t16NT4}>【套餐】</Text>}
                    </View>
                    {!chargeForm.isRegistration && ((chargeItem.isChineseMedicine && !charged) || !chargeItem.isChineseMedicine) && (
                        <Text style={[forceFontColor ? TextStyles.t16NT4 : TextStyles.t16NT1]}>
                            {`${
                                chargeItem.isSelfProvided
                                    ? "自备"
                                    : chargeForm.isAirPharmacy
                                    ? (chargeItem?.unitCount ?? 0) + (chargeItem?.unit ?? "")
                                    : "×" + (chargeItem?.unitCount ?? 0)
                            }`}
                        </Text>
                    )}
                    {charged && chargeItem.isChineseMedicine && (
                        <Text style={[forceFontColor ? TextStyles.t16NT4 : TextStyles.t16NT1]}>{ABCUtils.formatPrice(totalPrice)}</Text>
                    )}
                </View>
                <View
                    style={[
                        ABCStyles.rowAlignCenterSpaceBetween,
                        {
                            paddingLeft: showCheckBox ? Sizes.dp24 : undefined,
                            marginTop: isShowToothStructure ? Sizes.dp8 : Sizes.dp4,
                        },
                    ]}
                >
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        {isOpenMultiplePharmacy && chargeForm.isChinesePrescription && !!chargeItem.goodsInfo?.cMSpec && (
                            <Text style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })]}>
                                {chargeItem.goodsInfo?.cMSpec?.slice(-2) + "，" ?? ""}
                            </Text>
                        )}
                        {(chargeForm.isWesternPrescription || chargeForm.isInfusionPrescription || chargeForm.isGoods) &&
                            (!!productInfo || !!priceInfo) && (
                                <Text
                                    style={[
                                        forceFontColor ? TextStyles.t14NT4 : TextStyles.t14NT6,
                                        {
                                            lineHeight: Sizes.dp20,
                                            flexShrink: 1,
                                        },
                                    ]}
                                    numberOfLines={1}
                                >
                                    {`${productInfo}${!!priceInfo ? "，" + priceInfo : ""}`}
                                </Text>
                            )}
                        {!(chargeForm.isWesternPrescription || chargeForm.isInfusionPrescription || chargeForm.isGoods) && (
                            <Text
                                style={[
                                    forceFontColor ? TextStyles.t14NT4 : TextStyles.t14NT6,
                                    {
                                        lineHeight: Sizes.dp20,
                                    },
                                ]}
                            >
                                {`${abcI18Next.t("¥")}${ABCUtils.inventoryPriceWithRMB({
                                    price: chargeItem?.sourceUnitPrice ?? 0,
                                    showSymbols: false,
                                })}/${chargeItem?.unit ?? (chargeForm.isAirPharmacy ? "g" : "次")}`}
                            </Text>
                        )}
                        {isHasPharmacyOperate && <SizedBox width={Sizes.dp4} />}
                        {isHasPharmacyOperate && (
                            <PharmacyTagView pharmacyNo={chargeItem?.pharmacyNo} defaultPharmacyNo={defaultPharmacyNo} />
                        )}
                        <Spacer />
                        {userCenter.clinic?.isDentistryClinic && !chargeForm.isChinesePrescription && (
                            <View style={[ABCStyles.rowAlignCenter, { marginLeft: Sizes.dp4 }]}>
                                <CustomInput
                                    style={{ width: Sizes.dp60 }}
                                    disable={!!chargeItem?.status}
                                    borderType={"boxBorder"}
                                    type={"input"}
                                    value={chargeItem.totalPriceRatioPercent > 100 ? "-" : chargeItem.totalPriceRatioPercent}
                                    placeholder={"折扣"}
                                    placeholderColor={Colors.T4}
                                    formatter={MinMaxLimitFormat(0, 100)}
                                    textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                    alwaysShowUtil={true}
                                    unit={"%"}
                                    selectTextOnFocus={false}
                                    containerStyle={{ height: Sizes.dp24 }}
                                    onBlur={(value) => {
                                        if (!value) return;
                                        const discount = Number(value);
                                        onDiscountChange?.(discount);
                                    }}
                                />
                                <SizedBox width={Sizes.dp4} />
                                <CustomInput
                                    style={{ width: Sizes.dp80 }}
                                    startView={() => {
                                        return <Text style={[TextStyles.t13NT1]}>{abcI18Next.t("¥")}</Text>;
                                    }}
                                    disable={!!chargeItem?.status}
                                    borderType={"boxBorder"}
                                    type={"input"}
                                    value={ABCUtils.formatPrice(chargeItem?._displayTotalPrice)}
                                    placeholder={"总价"}
                                    placeholderColor={Colors.T4}
                                    formatter={PrecisionLimitFormatter(2)}
                                    textStyle={TextStyles.t13NT2.copyWith({ color: Colors.T1 })}
                                    alwaysShowUtil={true}
                                    selectTextOnFocus={false}
                                    containerStyle={{ height: Sizes.dp24 }}
                                    onBlur={(value) => {
                                        if (!value) return;
                                        const totalPrice = Number(value);
                                        onTotalPriceChange?.(totalPrice);
                                    }}
                                />
                            </View>
                        )}
                    </View>
                    <View style={[ABCStyles.rowAlignCenter]}>
                        {!chargeForm.isRegistration && charged && chargeItem.isChineseMedicine && (
                            <Text style={[forceFontColor ? TextStyles.t16NT4 : TextStyles.t14NT6]}>
                                {`${chargeItem.isSelfProvided ? "自备" : "×" + (chargeItem?.unitCount ?? 0)}`}
                            </Text>
                        )}
                        <ChargeStatusView
                            chargeStatus={isRefundItem ? ChargeFormItemStatus.unCharged : chargeItem.status}
                            style={{ marginLeft: Sizes.dp4, marginRight: 0 }}
                        />
                    </View>
                </View>
                {(!!prescriptionRemark || (chargeForm.isExternal && !!externalUsage)) && (
                    <View style={[ABCStyles.rowAlignCenter, { paddingLeft: showCheckBox ? Sizes.dp24 : undefined }]}>
                        {chargeForm.isExternal && (
                            <Text style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]}>{externalUsage}</Text>
                        )}
                        <Text numberOfLines={1} style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]}>
                            {prescriptionRemark ?? ""}
                        </Text>
                    </View>
                )}
            </View>
        );
        return (
            <View style={[{ justifyContent: "center" }, flattenStyles(style)]} {...otherProps}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    {showCheckBox && (
                        <AbcCheckbox
                            enable={false}
                            check={finalChecked}
                            size={Sizes.dp16}
                            style={{ marginRight: Sizes.dp6 }}
                            checkIconBgColor={Colors.white}
                            checkColor={chargeItem.isSelfProvided ? Colors.T4 : undefined}
                        />
                    )}
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <StockNotEnoughTextView
                            style={{ flexShrink: 1 }}
                            goodsStock={chargeItem.status == ChargeFormItemStatus.unCharged ? stockInfo : undefined}
                            text={chargeItem.displayName ?? ""}
                            textStyle={forceFontColor ? TextStyles.t16NT4 : TextStyles.t16NT1}
                            tipContent={this._createActionBtn()}
                            hasOtherOperate={showCheckBox}
                            bgColor={chargeForm.isChinesePrescription ? "transparent" : "rgba(51, 51, 51, 0.95)"}
                        />

                        {chargeItem.isPackage && <Text style={TextStyles.t14NT4}>【套餐】</Text>}
                        <ChargeStatusView chargeStatus={chargeItem.status} />
                        {isOpenMultiplePharmacy && chargeForm.isChinesePrescription && !!chargeItem.goodsInfo?.cMSpec && (
                            <Text style={[TextStyles.t12NT4]}>{chargeItem.goodsInfo?.cMSpec?.slice(-2) ?? ""}</Text>
                        )}
                    </View>

                    {!chargeForm.isRegistration && (
                        <Text style={TextStyles.subTitleStyle}>
                            {`${chargeItem.isSelfProvided ? "自备" : chargeItem.unitCount} ${unit}`}
                        </Text>
                    )}

                    <Text
                        style={[
                            forceFontColor ? TextStyles.t16MT4 : TextStyles.t16MB,
                            {
                                minWidth: Sizes.dp79,
                                textAlign: "right",
                            },
                        ]}
                    >
                        {ABCUtils.formatPrice(totalPrice)}
                    </Text>
                </View>

                {(chargeForm.isMedicine || chargeForm.isGoods) && (!!productInfo || !!priceInfo) && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                marginTop: Sizes.dp8,
                                marginLeft: showCheckBox ? Sizes.dp23 : undefined,
                            },
                        ]}
                    >
                        <Text style={[TextStyles.subTitleStyle2, { marginRight: Sizes.dp8 }]}>{productInfo}</Text>
                        <Text style={TextStyles.subTitleStyle2}>{priceInfo}</Text>
                    </View>
                )}
                {chargeForm.isExternal && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                marginTop: Sizes.dp8,
                                marginLeft: showCheckBox ? Sizes.dp23 : undefined,
                            },
                        ]}
                    >
                        <Text style={TextStyles.subTitleStyle2}>
                            {`共${chargeItem.usageInfo?.dosage}次，${chargeItem.usageInfo?.freq}`}
                        </Text>
                    </View>
                )}
                {(isHasPharmacyOperate || !!prescriptionRemark) && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                marginTop: Sizes.dp4,
                                marginLeft: showCheckBox ? Sizes.dp23 : undefined,
                            },
                        ]}
                    >
                        {isHasPharmacyOperate && (
                            <PharmacyTagView pharmacyNo={chargeItem?.pharmacyNo} defaultPharmacyNo={defaultPharmacyNo} />
                        )}
                        {isHasPharmacyOperate && !!prescriptionRemark && <SizedBox width={Sizes.dp8} />}
                        <Text numberOfLines={1} style={[TextStyles.t12NT4, { flexShrink: 1 }]}>
                            {prescriptionRemark ?? ""}
                        </Text>
                    </View>
                )}
            </View>
        );
    }
}
