/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/17
 *
 * @description
 */
import { DividerLine, Spacer, ToolBar, ToolBarButtonStyle1 } from "../base-ui";
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { ChargeInvoiceDetailData, ChargeOweSheets, ChargePayData, PayMethod } from "./data/charge-beans";
import { ChargePayPageBloc } from "./charge-pay-page-bloc";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { BaseComponent } from "../base-ui/base-component";
import { ABCStyles, ABCStyleSheet, Colors, Sizes, TextStyles } from "../theme";
import { ABCUtils } from "../base-ui/utils/utils";
import { GroupDivider } from "../base-ui/divider-line";
import { PayMethodGridView } from "./view/paymethod";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { SwitchView } from "../base-ui/views/switch-view";
import { StringUtils } from "../base-ui/utils/string-utils";
import { AbcFlexTextInput } from "../base-ui/views/abc-flex-text-input";
import { Subject } from "rxjs";
import abcI18Next from "../language/config";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import WillPopListener from "../base-ui/views/will-pop-listener";
import { ChargeCommentView } from "./view/charge-comment-view";

interface ChargePayPageProps {
    chargePayData: ChargePayData;
    chargeDetailData?: ChargeInvoiceDetailData; // 收费详情（需要在收费时，拼凑相应参数）
    hasPartedPaid?: boolean;

    successChangeCallback?(): void;

    registrationTrigger?: Subject<string>;

    requestDetailCallback?(): void;

    isRepayment?: boolean; //是否是还款页面
    repaymentList?: ChargeOweSheets[]; //还款列表
    isSingleOweCharge?: boolean; //是否是单个欠费还款
    isHideOweMethod?: boolean; //是否隐藏欠费支付方式
}

const styles = ABCStyleSheet.create({
    priceInput: {
        minWidth: Sizes.dp28,
        height: 30,
        textAlign: "right",
        ...TextStyles.t18MB,
    },
});

export class ChargePayPage extends BaseBlocNetworkPage<ChargePayPageProps, ChargePayPageBloc> {
    constructor(props: ChargePayPageProps) {
        super(props);
        this.bloc = new ChargePayPageBloc({
            chargePayData: this.props.chargePayData,
            chargeDetailData: this.props.chargeDetailData,
            hasPartedPaid: this.props.hasPartedPaid,
            isRepayment: this.props.isRepayment,
            ChargeOweSheets: this.props.repaymentList,
            isSingleOweCharge: this.props.isSingleOweCharge,
            isHideOweMethod: this.props.isHideOweMethod,
        });
        this.addDisposable(this.bloc);
    }

    static defaultProps = {
        isRepayment: false,
    };

    getAppBarTitle(): string {
        return `${this.props.isRepayment ? "还款" : "收费"}`;
    }

    onBackClick(): void {
        ABCNavigator.pop();
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.initing) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.initError) {
                    status = ABCNetworkPageContentStatus.error;
                }

                this.setContentStatus(status, state.initError);
            })
            .addToDisposableBag(this);
    }

    private buildChargeCommentView(): JSX.Element {
        const { chargeComment } = this.bloc.currentState;
        return (
            <ChargeCommentView
                chargeComment={chargeComment}
                onChangeText={(value: string) => this.bloc.requestUpdateChargeComment(value)}
            />
        );
    }

    public renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { selectPayMethod, isOpenAllinpay, chargePayData } = state;
        let isScanCodeCharge = false;
        if (selectPayMethod?.method == PayMethod.payABCPay && isOpenAllinpay) {
            isScanCodeCharge = true;
        }
        if (!state.init) return <View />;
        //匿名患者不支持欠费收费
        const isAnonymousPatient = !chargePayData?.chargeInvoiceDetailData?.patient?.name;
        const { isRepayment } = this.props;

        return (
            <View style={{ flex: 1 }}>
                <WillPopListener
                    onWillPop={() => {
                        this.onBackClick();
                    }}
                />
                <ScrollView showsVerticalScrollIndicator={false}>
                    <_PriceSummary />
                    <GroupDivider borderLine={true} />
                    <_PayMethodView />
                    <GroupDivider />
                    {this.buildChargeCommentView()}
                    {!isRepayment && <_SendDrug />}
                </ScrollView>

                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1
                        showIndicator={state.calculatingCharge}
                        text={`${isScanCodeCharge ? `扫码${isRepayment ? "还款" : "收费"}` : `${isRepayment ? "还款" : "收费"}`}`}
                        onClick={
                            (selectPayMethod?.method == PayMethod.payABCPay && !isOpenAllinpay) ||
                            ((selectPayMethod?.method == PayMethod.payABCPay || selectPayMethod?.method == PayMethod.payOweWay) &&
                                state.cashPrice == 0) ||
                            (isAnonymousPatient && selectPayMethod?.method == PayMethod.payOweWay)
                                ? undefined
                                : () => this.bloc.requestCharge(this.props.successChangeCallback, this.props.requestDetailCallback)
                        }
                    />
                </ToolBar>
            </View>
        );
    }
}

class _PriceSummary extends BaseComponent {
    static contextType = ChargePayPageBloc.Context;

    constructor(props: any) {
        super(props);
    }

    render() {
        const state = ChargePayPageBloc.fromContext(this.context).currentState;
        const title = "应收";
        //当前选择是否为欠费收费方式
        const payOweMethod = state.selectPayMethod?.method === PayMethod.payOweWay;

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        padding: Sizes.dp16,
                        backgroundColor: Colors.white,
                        justifyContent: "space-between",
                    },
                ]}
            >
                {!payOweMethod && (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>{title}</Text>
                        <Text style={[TextStyles.t18MY2, { marginLeft: Sizes.dp16 }]}>
                            {abcI18Next.t("￥")}
                            {ABCUtils.formatPrice(state.receivablePrice!)}
                        </Text>
                    </View>
                )}
                {payOweMethod && (
                    <View>
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>{title}</Text>
                            <Text style={[TextStyles.t18MY2, { marginLeft: Sizes.dp16 }]}>
                                {abcI18Next.t("￥")}
                                {ABCUtils.formatPrice(state.receivablePrice!)}
                            </Text>
                        </View>
                        {!!state.totalOweFee && (
                            <Text style={[TextStyles.t11NT2.copyWith({ color: Colors.R2 })]}>
                                {`当前欠费： ${abcI18Next.t("¥")}${ABCUtils.formatPrice(state.totalOweFee!)}`}
                            </Text>
                        )}
                    </View>
                )}
                <Spacer />

                {!!state.systemAutoOddText && (
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={TextStyles.t14NT3.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>
                            {state.systemAutoOddText}
                        </Text>
                    </View>
                )}
            </View>
        );
    }
}

class _PayMethodView extends BaseComponent {
    static contextType = ChargePayPageBloc.Context;

    constructor(props: any) {
        super(props);
    }

    render() {
        const state = ChargePayPageBloc.fromContext(this.context).currentState;
        const payCash = state.selectPayMethod?.method === PayMethod.payCash;
        //  支付方式为欠费支付时，支付金额默认为待收总金额，不支持修改
        const isOweWay = state.selectPayMethod?.method === PayMethod.payOweWay;
        //还款时，多个批量还款单，不可以修改金额
        const isSingleOweCharge = state.isRepayment && !state.isSingleOweCharge;
        return (
            <View style={{ backgroundColor: Colors.white, overflow: "scroll" }} overflow={"visible"}>
                <PayMethodGridView
                    showAbcFlag={state.isOpenAllinpay}
                    defaultMethod={state.selectPayMethod?.method}
                    payMethods={state.payMethods}
                    disableMethods={state.disableMethods}
                    methodBalance={state.methodBalance}
                    onChanged={(method) => ChargePayPageBloc.fromContext(this.context).requestUpdatePayMethod(method)}
                    isPaymentMode={isSingleOweCharge}
                    onOnlyTips={(method) => ChargePayPageBloc.fromContext(this.context).requestTipsNotPaymentReason(method)}
                />

                <DividerLine style={{ marginLeft: Sizes.dp16 }} />
                {state.selectPayMethod?.method != undefined && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                padding: Sizes.dp16,
                            },
                        ]}
                    >
                        <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>收现</Text>
                        <Spacer />
                        <Text style={TextStyles.t18MB}>{abcI18Next.t("¥")}</Text>
                        <AbcFlexTextInput
                            style={{
                                ...styles.priceInput,
                            }}
                            multiline={false}
                            defaultValue={ABCUtils.formatPrice(state.cashPrice ?? 0)}
                            formatter={[PrecisionLimitFormatter(2)]}
                            keyboardType={"numeric"}
                            syncTextOnBlur={true}
                            keyboardDistanceFromTextField={Sizes.dp80}
                            onChangeText={(value) => this._onChangeText(value)}
                            editable={!isOweWay && !isSingleOweCharge}
                        />
                    </View>
                )}

                {payCash && <DividerLine style={{ marginLeft: Sizes.dp16 }} />}

                {payCash && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                padding: Sizes.dp16,
                                justifyContent: "space-between",
                            },
                        ]}
                    >
                        <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>找零</Text>
                        <Text style={[TextStyles.t18MB]}>
                            {abcI18Next.t("¥") +
                                ABCUtils.formatPrice(
                                    state.cashPrice! - state.receivablePrice! > 0 ? state.cashPrice! - state.receivablePrice! : 0
                                )}
                        </Text>
                    </View>
                )}
                {state.receivablePrice! > state.cashPrice! && <DividerLine style={{ marginLeft: Sizes.dp16 }} />}
                {state.receivablePrice! > state.cashPrice! && (
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                height: Sizes.dp50,
                                paddingLeft: Sizes.dp16,
                                paddingRight: Sizes.dp12,
                                justifyContent: "space-between",
                            },
                        ]}
                    >
                        <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>欠收</Text>
                        <Text style={[TextStyles.t18MB.copyWith({ lineHeight: Sizes.dp18 })]}>
                            {abcI18Next.t("¥") +
                                ABCUtils.formatPrice(
                                    state.receivablePrice! - state.cashPrice! > 0 ? state.receivablePrice! - state.cashPrice! : 0
                                )}
                        </Text>
                    </View>
                )}
            </View>
        );
    }

    private _onChangeText(value: string) {
        const state = ChargePayPageBloc.fromContext(this.context).currentState;
        const cashPrice = StringUtils.parseFloat(value)!;
        if (cashPrice != state.cashPrice) {
            ChargePayPageBloc.fromContext(this.context).updateCashPrice(cashPrice);
        }
    }
}

class _SendDrug extends BaseComponent {
    static contextType = ChargePayPageBloc.Context;

    constructor(props: any) {
        super(props);
    }

    render() {
        const state = ChargePayPageBloc.fromContext(this.context).currentState;

        if (!state.canSendDrug) return <View />;

        //钱没有收全之前也不能发药
        if ((state.cashPrice ?? 0.0) < state.receivablePrice!) return <View />;

        // const chargeStatus = state.chargePayData?.chargeInvoiceDetailData?.status!;

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        padding: Sizes.dp16,
                        backgroundColor: Colors.white,
                        justifyContent: "space-between",
                    },
                ]}
            >
                <Text style={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}>同时发药</Text>
                <SwitchView
                    style={{ width: Sizes.dp48, height: Sizes.dp24 }}
                    value={state.enableSendDrug}
                    onChanged={(on) => ChargePayPageBloc.fromContext(this.context).requestToggleSendDragSwitch(on)}
                />
            </View>
        );
    }
}
