import { PatientSourceTypeSelectDialogBloc, SourceType } from "./patient-source-type-select-dialog-bloc";
import { BottomSheetHelper } from "../../base-ui/abc-app-library";
import React from "react";
import { Dimensions, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { BlocHelper } from "../../bloc/bloc-helper";
import { BaseBlocNetworkView } from "../../base-ui/base-page";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";
import { AbcView } from "../../base-ui/views/abc-view";
import { Patient, PatientSource, PatientsSourceTypesItem } from "../../base-business/data/beans";
import { SearchInput, UniqueKey } from "../../base-ui";
import { SafeAreaBottomView } from "../../base-ui/safe_area_view";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import FontWeight from "../../theme/font-weights";

interface PatientSourceTypeSelectDialogProps {
    source?: PatientSource;
}

export class PatientSourceTypeSelectDialog extends BaseBlocNetworkView<
    PatientSourceTypeSelectDialogProps,
    PatientSourceTypeSelectDialogBloc
> {
    public static show(props: PatientSourceTypeSelectDialogProps): Promise<PatientSource | undefined> {
        return showBottomPanel(<PatientSourceTypeSelectDialog {...props} />, { topMaskHeight: pxToDp(200) });
    }

    constructor(props: PatientSourceTypeSelectDialogProps) {
        super(props);
        this.bloc = new PatientSourceTypeSelectDialogBloc(props.source);
    }

    _searchInput?: AbcTextInput | null;

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {BottomSheetHelper.createTitleBar("来源推荐")}
                <View style={{ flex: 1, backgroundColor: Colors.white }}>
                    <View
                        style={{
                            flex: 1,
                            flexDirection: "row",
                            height: Math.min(Dimensions.get("window").height, pxToDp(548)) - 48, // 解决顶部BottomSheetHelper将内部列表挤下 导致显示不全的问题
                            backgroundColor: Colors.white,
                        }}
                    >
                        <View style={{ flex: 1 }}>{this._renderPrimaryType()}</View>
                        <View
                            style={{
                                backgroundColor: Colors.dividerLineColor,
                                alignSelf: "stretch",
                                width: Sizes.dpHalf,
                            }}
                        />
                        <View style={{ flex: 1 }}>{this._renderSubType()}</View>
                    </View>
                    <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
                </View>
            </View>
        );
    }

    /**
     * 就诊推荐一级类型
     * @private
     */
    private _renderPrimaryType(): JSX.Element {
        const { sourceTypes } = this.bloc.currentState;

        return (
            <AbcListView
                scrollEventThrottle={300}
                numberOfRows={sourceTypes.length}
                dataSource={sourceTypes}
                getRowKey={(index) => index.toString()}
                renderRow={(data) => {
                    return this._renderPrimaryItemRow(data);
                }}
            />
        );
    }

    private _renderPrimaryItemRow(data: PatientsSourceTypesItem): JSX.Element {
        const state = this.bloc.currentState;
        const { currentSource } = state;

        return (
            <AbcView
                style={[
                    ABCStyles.bottomLine,
                    ABCStyles.rowAlignCenter,
                    Sizes.paddingLTRB(Sizes.dp12, Sizes.dp14, Sizes.dp12, Sizes.dp14),
                    {
                        backgroundColor: data.id === currentSource?.id ? Colors.bg1 : undefined,
                        height: Sizes.listItemHeight,
                    },
                ]}
                onClick={() => {
                    this.bloc.requestSelectPrimaryType(data);
                    this._searchInput?.clear();
                }}
            >
                <View style={{ flex: 1 }}>
                    <Text
                        style={TextStyles.t14NT1.copyWith({
                            fontWeight: data.id === currentSource?.id ? FontWeight.medium : FontWeight.normal,
                        })}
                    >
                        {data.name!}
                    </Text>
                </View>
                {state.hasSubType(data) && <RightArrowView />}
            </AbcView>
        );
    }

    /**
     * 就诊推荐二级类型列表
     * @private
     */
    private _renderSubType(): JSX.Element {
        const { subTypes, currentSource } = this.bloc.currentState;
        if (currentSource?.name == SourceType.CUSTOMER_ADVISE) {
            return this._renderSearchCustomerView();
        } else if (
            currentSource?.name == SourceType.DOCTOR_ADVISE ||
            currentSource?.name == SourceType.DOCTOR_GUIDE_ADVISE ||
            currentSource?.name == SourceType.REFERRAL_DOCTOR
        ) {
            return this._renderSearchDoctorsView();
        } else {
            return (
                <AbcListView
                    scrollEventThrottle={300}
                    numberOfRows={subTypes.length}
                    dataSource={subTypes}
                    getRowKey={(index) => index.toString()}
                    renderRow={(data) => {
                        return this._renderSubTypeItemRow(data);
                    }}
                />
            );
        }
    }

    private _renderSubTypeItemRow(data: PatientsSourceTypesItem): JSX.Element {
        const { currentSource } = this.bloc.currentState;
        return (
            <AbcView>
                {data.children?.length && (
                    /**
                     * 自定义来源子选项
                     * @private
                     */
                    <AbcView>
                        {data.children?.map((children, index) => {
                            return (
                                <AbcView
                                    key={index}
                                    style={[
                                        ABCStyles.bottomLine,
                                        ABCStyles.rowAlignCenter,
                                        Sizes.paddingLTRB(Sizes.dp12, Sizes.dp14, Sizes.dp12, Sizes.dp14),
                                        {
                                            backgroundColor: currentSource?.sourceFrom === data.id ? Colors.D2 : undefined,
                                            height: Sizes.listItemHeight,
                                        },
                                    ]}
                                    onClick={() => this.bloc.requestSelectSubType(children)}
                                >
                                    <Text style={TextStyles.t16NT1}>{children.name!}</Text>
                                </AbcView>
                            );
                        })}
                    </AbcView>
                )}

                {data.children?.length == null && (
                    /**
                     * 默认来源子选项
                     * @private
                     */
                    <AbcView
                        style={[
                            ABCStyles.bottomLine,
                            ABCStyles.rowAlignCenter,
                            Sizes.paddingLTRB(Sizes.dp12, Sizes.dp14, Sizes.dp12, Sizes.dp14),
                            {
                                backgroundColor: currentSource?.sourceFrom === data.id ? Colors.bg1 : undefined,
                                height: Sizes.listItemHeight,
                            },
                        ]}
                        onClick={() => this.bloc.requestSelectSubType(data)}
                    >
                        <Text style={TextStyles.t14NT1}>{data.name!}</Text>
                    </AbcView>
                )}
            </AbcView>
        );
    }

    /**
     * 二级分类 - 医生、导医推荐
     * @private
     */
    private _renderSearchDoctorsView(): JSX.Element {
        const { subTypes, matchingPersonnel } = this.bloc.currentState;

        return (
            <AbcView style={{ flex: 1 }}>
                <AbcView
                    style={[
                        Sizes.marginLTRB(Sizes.dp12, Sizes.dp12, Sizes.dp12, 0),
                        {
                            backgroundColor: Colors.bg1,
                            borderRadius: Sizes.dp6,
                        },
                    ]}
                >
                    <View
                        style={[
                            {
                                flex: 1,
                                flexDirection: "row",
                            },
                        ]}
                    >
                        <SearchInput
                            style={{ height: Sizes.dp36, backgroundColor: Colors.bg1 }}
                            inputStyle={{ backgroundColor: Colors.bg1 }}
                            placeholder={"请输入"}
                            // autoFocus={true}
                            ref={(ref) => (this._searchInput = ref?._textInput)}
                            value={this._searchInput?.value ?? ""}
                            onChange={(name: string) => this.bloc.requestSearchDoctorName(name)}
                        />
                    </View>
                </AbcView>

                {!matchingPersonnel?.length && this._searchInput?.value !== "" ? (
                    <View style={{ flex: 1, justifyContent: "center" }}>
                        <Text style={[TextStyles.t14NT4, { alignSelf: "center" }]}>暂无内容</Text>
                    </View>
                ) : matchingPersonnel?.length && this._searchInput?.value !== "" ? (
                    <AbcListView
                        initialListSize={20}
                        style={{ flex: 1 }}
                        scrollEventThrottle={300}
                        numberOfRows={matchingPersonnel?.length ?? 0}
                        dataSource={matchingPersonnel ?? []}
                        getRowKey={(index) => matchingPersonnel?.[index].id ?? UniqueKey()}
                        renderRow={(data) => <PatientInfoItem info={data} />}
                    />
                ) : (
                    <AbcListView
                        initialListSize={20}
                        style={{ flex: 1 }}
                        scrollEventThrottle={300}
                        numberOfRows={subTypes?.length ?? 0}
                        dataSource={subTypes ?? []}
                        getRowKey={(index) => subTypes?.[index].id ?? UniqueKey()}
                        renderRow={(data) => <PatientInfoItem info={data} />}
                    />
                )}
            </AbcView>
        );
    }

    /**
     * 二级分类 - 顾客推荐
     * @private
     */
    private _renderSearchCustomerView(): JSX.Element {
        const { detailData, keyword } = this.bloc.currentState;

        return (
            <AbcView style={{ flex: 1 }}>
                <AbcView
                    style={[
                        Sizes.marginLTRB(Sizes.dp12, Sizes.dp12, Sizes.dp12, 0),
                        {
                            backgroundColor: Colors.bg1,
                            borderRadius: Sizes.dp6,
                        },
                    ]}
                >
                    <View
                        style={[
                            {
                                flex: 1,
                                flexDirection: "row",
                            },
                        ]}
                    >
                        <SearchInput
                            style={{ height: Sizes.dp36, backgroundColor: Colors.bg1 }}
                            inputStyle={{ backgroundColor: Colors.bg1 }}
                            placeholder={"搜索患者"}
                            // autoFocus={true}
                            onChange={(name: string) => this.bloc.requestSearchCustomerName(name)}
                        />
                    </View>
                </AbcView>
                {detailData?.length && keyword !== "" ? (
                    <AbcListView
                        initialListSize={20}
                        style={{ flex: 1 }}
                        scrollEventThrottle={300}
                        numberOfRows={detailData?.length ?? 0}
                        dataSource={detailData ?? []}
                        getRowKey={(index) => detailData?.[index].id ?? UniqueKey()}
                        renderRow={(data) => <PatientInfoItem info={data} />}
                    />
                ) : (
                    <View style={{ flex: 1, justifyContent: "center" }}>
                        <Text style={[TextStyles.t14NT4, { alignSelf: "center" }]}>暂无内容</Text>
                    </View>
                )}
            </AbcView>
        );
    }
}

interface PatientInfoItemProps {
    info: Patient;

    handleClick?(arg1: Patient): void;
}

class PatientInfoItem extends React.Component<PatientInfoItemProps> {
    static contextType = PatientSourceTypeSelectDialogBloc.Context;

    constructor(props: PatientInfoItemProps) {
        super(props);
    }

    render() {
        const bloc = PatientSourceTypeSelectDialogBloc.fromContext(this.context);
        const { id, name, mobile, age, sex } = this.props.info;
        const sourceInfo = {
            id: id!,
            name: name!,
        };
        return (
            <AbcView
                style={[ABCStyles.bottomLine, { flex: 1, paddingHorizontal: Sizes.dp12, paddingVertical: Sizes.dp14 }]}
                onClick={() => bloc.requestSelectSubType(sourceInfo)}
            >
                <AbcView style={ABCStyles.rowAlignCenter}>
                    {name && (
                        <Text
                            style={[TextStyles.t14NT1, { paddingRight: Sizes.dp16, lineHeight: Sizes.dp20 }]}
                            numberOfLines={1}
                            ellipsizeMode={"tail"}
                        >
                            {name}
                        </Text>
                    )}
                    {age?.year && (
                        <Text style={[TextStyles.t14NT1, { paddingRight: Sizes.dp16, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {`${age?.year}岁`}
                        </Text>
                    )}
                    {sex && (
                        <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1} ellipsizeMode={"tail"}>
                            {sex}
                        </Text>
                    )}
                </AbcView>

                {mobile && (
                    <Text style={[TextStyles.t14NT6, { marginTop: Sizes.dp4, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                        {mobile ?? ""}
                    </Text>
                )}
            </AbcView>
        );
    }
}
