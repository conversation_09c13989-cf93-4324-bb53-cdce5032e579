/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/4/30
 */

import { Bloc, BlocEvent } from "../../bloc";
import { Pat<PERSON>, <PERSON><PERSON>Address, PatientSource } from "../../base-business/data/beans";
import { fromJsonToDate, JsonMapper } from "../../common-base-module/json-mapper/json-mapper";

import React from "react";
import { PatientSearchPage } from "./patient-search-page";
import { AgePicker } from "../../base-ui/picker/age-picker";
import _ from "lodash";
import { CityInfo, DistrictInfo, ProvinceInfo } from "../data/patients";
import { Toast } from "../../base-ui/dialog/toast";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { errorToStr, TimeUtils } from "../../common-base-module/utils";
import { ValidatorUtils } from "../../base-ui/utils/validator-utils";
import { PatientAgent } from "../../base-business/data/patient-agent";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { PatientInfoEditComponentsConfig } from "./patient-edit-data";
import { ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";
import { BirthdayPicker } from "../../base-ui/picker/birthday-picker";
import { GetClinicMedicalRecordConfig, OnlinePropertyConfigProvider } from "../../data/online-property-config-provder";

export interface PatientDetailEditPageProps extends PatientInfoEditComponentsConfig {
    mode?: "create" | "edit";
    __switchPatient?: boolean;
}

class State {
    patientInfo?: Patient;
    hasChanged?: boolean = false;
    isEditing?: boolean = false;

    clinicFieldConfig?: GetClinicMedicalRecordConfig;
    employeesMeConfig?: EmployeesMeConfig;
    // 修改首诊来源
    get canEditFirstFromAway(): boolean {
        // 新增患者需要排除在外
        if (!this.patientInfo?.id) return true;
        // 有患者权限可编辑
        if (!!this.employeesMeConfig?.employeeDataPermission?.crm?.isCanModifyFirstFromAway) {
            return true;
        }
        return false;
    }

    // 修改患者姓名
    get canEditPatientName(): boolean {
        // 新增患者需要排除在外
        if (!this.patientInfo?.id) return true;
        // 有权限可编辑
        if (!!this.employeesMeConfig?.employeeDataPermission?.crm?.isCanModifyName) {
            return true;
        }
        return false;
    }

    // 修改患者身份证号
    get canEditIdCard(): boolean {
        // 新增患者需要排除在外
        if (!this.patientInfo?.id) return true;
        // 有权限可编辑
        if (!!this.employeesMeConfig?.employeeDataPermission?.crm?.isCanModifyIdCard) {
            return true;
        }
        return false;
    }

    // 修改患者档案号
    get canEditSn(): boolean {
        // 新增患者需要排除在外
        if (!this.patientInfo?.id) return true;
        // 有患者权限可编辑
        if (!!this.employeesMeConfig?.employeeDataPermission?.crm?.isCanModifySn) {
            return true;
        }
        return false;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    constructor() {
        super();
    }
}

class _EventUpdate extends _Event {}

class _EventChangePatientName extends _Event {
    name: string;

    constructor(name: string) {
        super();
        this.name = name;
    }
}

class _EventUpdatePatientSex extends _Event {
    sex: string;

    constructor(sex: string) {
        super();
        this.sex = sex;
    }
}

class _EventChangePatientTel extends _Event {
    tel: string;

    constructor(tel: string) {
        super();
        this.tel = tel;
    }
}

class _EventChangePatientAge extends _Event {}

class _EventChangePatientBirthday extends _Event {}

class _EventUpdateAddress extends _Event {
    provinceInfo: ProvinceInfo;
    cityInfo: CityInfo;
    districtInfo: DistrictInfo;

    constructor(provinceInfo: ProvinceInfo, cityInfo: CityInfo, districtInfo: DistrictInfo) {
        super();
        this.provinceInfo = provinceInfo;
        this.cityInfo = cityInfo;
        this.districtInfo = districtInfo;
    }
}

class _EventChangePatientAreaDetail extends _Event {
    areaDetail: string;

    constructor(areaDetail: string) {
        super();
        this.areaDetail = areaDetail;
    }
}

class _EventUpdateIdCard extends _Event {
    idCard: string;

    constructor(idCard: string) {
        super();
        this.idCard = idCard;
    }
}

class _EventUpdatePatientSource extends _Event {
    source: PatientSource;

    constructor(source: PatientSource) {
        super();
        this.source = source;
    }
}

class _EventChangeEditState {}

class _EventChangePatient extends _Event {}

class _EventSaveEditInfo extends _Event {}

class _EventBackPage extends _Event {}

class _EventUpdateSN extends _Event {
    sn: string;

    constructor(sn: string) {
        super();
        this.sn = sn;
    }
}

class _EventUpdateRemark extends _Event {
    remark: string;

    constructor(remark: string) {
        super();
        this.remark = remark;
    }
}

class _EventUpdatePatientProfession extends _Event {
    profession: string;

    constructor(profession: string) {
        super();
        this.profession = profession;
    }
}

class _EventModifyPatientCountryCode extends _Event {
    code: string;
    constructor(code: string) {
        super();
        this.code = code;
    }
}

class _EventUpdatePatientIdCardType extends _Event {
    idCardType: string;
    constructor(idCardType: string) {
        super();
        this.idCardType = idCardType;
    }
}

class _EventUpdatePatientCompany extends _Event {
    str: string;
    constructor(code: string) {
        super();
        this.str = code;
    }
}
class _EventUpdatePatientVisitReason extends _Event {
    str: string;
    constructor(code: string) {
        super();
        this.str = code;
    }
}
class _EventUpdatePatientEthnicity extends _Event {
    str: string;
    constructor(code: string) {
        super();
        this.str = code;
    }
}
class _EventUpdatePatientMarital extends _Event {
    str: number;
    constructor(code: number) {
        super();
        this.str = code;
    }
}

class _EventUpdatePatientWeight extends _Event {
    str: number;
    constructor(code: number) {
        super();
        this.str = code;
    }
}

class PatientEditPageBloc extends Bloc<_Event, State> {
    private readonly mode?: string;
    private readonly _pastHistory?: string;
    private clonePatient?: Patient;

    private readonly props: PatientDetailEditPageProps;

    constructor(props: PatientDetailEditPageProps) {
        super();
        this.props = props;
        const { mode, pastHistory } = props;
        if (mode == "create") {
            this.innerState.isEditing = true;
            this.innerState.hasChanged = true;
            this.mode = mode;
        }
        this._pastHistory = pastHistory;

        props && this.dispatch(new _EventInit());
    }

    static fromContext(context: PatientEditPageBloc): PatientEditPageBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<string, Function> {
        const map = new Map<string, Function>();
        map.set(_EventInit.name, this._mapEventInit);
        map.set(_EventUpdate.name, this._mapEventUpdate);
        map.set(_EventChangeEditState.name, this._mapEventChangeEditState); // 编辑患者信息改为直接可编辑这里用不上了
        map.set(_EventSaveEditInfo.name, this._mapEventSaveEditInfo); // 保存信息
        map.set(_EventChangePatient.name, this._mapChangePatient);
        map.set(_EventChangePatientName.name, this._mapChangePatientName);
        map.set(_EventUpdatePatientSex.name, this._mapEventUpdatePatientSex);
        map.set(_EventChangePatientTel.name, this._mapChangePatientTel);
        map.set(_EventChangePatientAge.name, this._mapChangePatientAge); // 修改患者年龄
        map.set(_EventChangePatientBirthday.name, this._mapChangePatientBirthday); // 修改患者生日
        map.set(_EventUpdateAddress.name, this._mapEventUpdateAddress);
        map.set(_EventChangePatientAreaDetail.name, this._mapChangePatientAreaDetail);
        map.set(_EventUpdateIdCard.name, this._mapEventUpdateIdCard);
        map.set(_EventBackPage.name, this._mapEventBackPage);
        map.set(_EventUpdatePatientSource.name, this._mapEventUpdatePatientSource); // 更新所选就诊推荐
        map.set(_EventUpdateSN.name, this._mapEventUpdateSN);
        map.set(_EventUpdatePatientProfession.name, this._mapEventUpdatePatientProfession);
        map.set(_EventUpdateRemark.name, this._mapEventUpdateRemark); // 更新备注
        map.set(_EventModifyPatientCountryCode.name, this._mapEventModifyPatientCountryCode); // 更新手机号地区号
        map.set(_EventUpdatePatientIdCardType.name, this._mapEventUpdatePatientIdCardType); // 更新手机号地区号
        map.set(_EventUpdatePatientCompany.name, this._mapEventUpdatePatientCompany); // 更新工作单位
        map.set(_EventUpdatePatientWeight.name, this._mapEventUpdatePatientWeight); // 更新体重
        map.set(_EventUpdatePatientVisitReason.name, this._mapEventUpdatePatientVisitReason); // 更新到店原因
        map.set(_EventUpdatePatientMarital.name, this._mapEventUpdatePatientMarital); // 更新到店原因
        map.set(_EventUpdatePatientEthnicity.name, this._mapEventUpdatePatientEthnicity); // 更新民族

        return map;
    }

    async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.clinicFieldConfig = await OnlinePropertyConfigProvider.instance.getClinicMedicalRecordConfig().catchIgnore();
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore(); // 数据权限
        this.innerState.isEditing = true;
        this.innerState.patientInfo = JsonMapper.deserialize(Patient, this.props.patient);

        const patientSourceId = this.innerState.patientInfo.patientSource?.id;
        const patientSourceFromId = this.innerState.patientInfo.patientSource?.sourceFrom;

        if (_.isEmpty(this.innerState.patientInfo.patientSource?.sourceFromName)) {
            // 自定义就诊推荐类型无主类型 这里在初始化中单独处理
            ClinicAgent.getPatientRelativeAdvise().then((rsp) => {
                rsp.rows?.map((source) => {
                    // 自定义推荐
                    if (source.children?.length) {
                        source.children.map((children) => {
                            if (children.id == patientSourceId) {
                                this.innerState.patientInfo!.patientSource!.name = source.name;
                                this.innerState.patientInfo!.patientSource!.sourceFromName = children.name;
                                this.innerState.hasChanged = true;
                            }
                            if (children.id == patientSourceFromId) {
                                this.innerState.patientInfo!.patientSource!.name = source.name;
                                this.innerState.patientInfo!.patientSource!.sourceFromName = children.name;
                                this.innerState.hasChanged = true;
                            }
                        });
                    }
                });
            });
        }
        this.innerState.patientInfo.name = this.innerState.patientInfo.name?.trim();
        if (!this.innerState.patientInfo.address) this.innerState.patientInfo.address = new PatientAddress();
        this.innerState.patientInfo.pastHistory = this.innerState.patientInfo.pastHistory ?? this._pastHistory;
        this.innerState.patientInfo.idCardType = this.innerState.patientInfo.idCardType ?? "身份证";
        this.clonePatient = _.cloneDeep(this.innerState.patientInfo);
        yield this.innerState.clone();
    }

    async *_mapEventUpdate(/*ignore: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    async *_mapEventChangeEditState(/*ignore: _EventChangeEditState*/): AsyncGenerator<State> {
        this.innerState.isEditing = true;
        this.update();
    }

    async *_mapChangePatient(): AsyncGenerator<State> {
        const patient = await ABCNavigator.navigateToPage(<PatientSearchPage {...this.props} patient={this.innerState.patientInfo} />);
        if (patient) {
            ABCNavigator.pop(patient, false);
        }
    }

    async *_mapEventSaveEditInfo(): AsyncGenerator<State> {
        if (!this.innerState.hasChanged) return;
        const { showPatientSource = false, requirePatientSource = false } = this.props;

        const clinicFieldConfig = this.innerState.clinicFieldConfig;
        const requireName = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "name" })?.required,
            requireAddress = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "address" })?.required,
            requireAge = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "age" })?.required,
            requireBirthday = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "birthday",
            })?.required,
            requireCertificates = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "certificates",
            })?.required,
            requireCompany = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "company" })?.required,
            requireEthnicity = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "ethnicity",
            })?.required,
            requireMarital = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "marital" })?.required,
            requireMobile = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "mobile" })?.required,
            requireProfession = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "profession",
            })?.required,
            requireRemark = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "remark" })?.required,
            requireSex = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sex" })?.required,
            requireSn = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sn" })?.required,
            requireSourceInfo = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "sourceInfo",
            })?.required,
            requireVisitReason = clinicFieldConfig?.getFieldConfigDetail({
                sourceKey: "patient",
                type: "create",
                field: "visitReason",
            })?.required,
            requireWeight = clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "weight" })?.required;
        if (!!requireName && _.isEmpty(this.innerState.patientInfo?.name?.trim())) {
            await Toast.show("请输入患者姓名", { warning: true });
            return;
        }

        if (StringUtils.containsEmoji(this.innerState.patientInfo?.name ?? "")) {
            await Toast.show("姓名不能包含表情符号", { warning: true });
            return;
        }
        if (!!requireSex && !this.innerState.patientInfo?.sex) {
            await Toast.show("请输入患者性别", { warning: true });
        }

        if (
            (requireMobile || this.innerState.patientInfo?.mobile) &&
            !StringUtils.validateMobile(this.innerState.patientInfo?.mobile ?? "", this.innerState.patientInfo?.countryCode)
        ) {
            await Toast.show("请输入正确的手机号", { warning: true });
            return;
        }
        if (!!requireAge && !this.innerState.patientInfo?.age) {
            await Toast.show("输入患者年龄", { warning: true });
            return;
        }
        if (!!requireBirthday && !this.innerState.patientInfo?.birthday) {
            await Toast.show("请输入患者生日", { warning: true });
            return;
        }

        if (
            (requireCertificates || this.innerState.patientInfo?.idCard) &&
            !ValidatorUtils.validateIdCard({ idCard: this.innerState.patientInfo?.idCard, type: this.innerState.patientInfo?.idCardType })
        ) {
            return;
        }

        if (showPatientSource && requirePatientSource && _.isEmpty(this.innerState.patientInfo?.patientSource?.id)) {
            await Toast.show("请选择患者来源", { warning: true });
            return;
        }
        if (!!requireSourceInfo && !this.innerState.patientInfo?.patientSource?.sourceDisplay) {
            await Toast.show("请输入患者来源信息", { warning: true });
            return;
        }

        if (!!requireMarital && !this.innerState.patientInfo?.maritalStr) {
            await Toast.show("请输入患者婚姻状况", { warning: true });
            return;
        }
        if (!!requireWeight && !this.innerState.patientInfo?.weight) {
            await Toast.show("请输入患者体重", { warning: true });
            return;
        }
        if (
            !!requireAddress &&
            (!this.innerState.patientInfo?.address?.addressDisplayWithGeo || !this.innerState.patientInfo?.address?.addressDetail)
        ) {
            await Toast.show("请输入患者地址", { warning: true });
            return;
        }
        if (!!requireProfession && _.isEmpty(this.innerState.patientInfo?.profession)) {
            await Toast.show("请输入患者职业", { warning: true });
            return;
        }
        if (!!requireSn && _.isEmpty(this.innerState.patientInfo?.sn)) {
            await Toast.show("请输入患者档案号", { warning: true });
            return;
        }
        if (!!requireCompany && _.isEmpty(this.innerState.patientInfo?.company)) {
            await Toast.show("请输入患者工作单位", { warning: true });
            return;
        }
        if (!!requireEthnicity && _.isEmpty(this.innerState.patientInfo?.ethnicity)) {
            await Toast.show("请输入患者民族", { warning: true });
            return;
        }
        if (!!requireRemark && _.isEmpty(this.innerState.patientInfo?.remark)) {
            await Toast.show("请输入患者备注", { warning: true });
            return;
        }
        if (!!requireVisitReason && _.isEmpty(this.innerState.patientInfo?.visitReason)) {
            await Toast.show("请输入到店原因", { warning: true });
            return;
        }

        //对已有患者进行修改，直接保存到服务端
        const patient = this.innerState.patientInfo;
        if (this.innerState.hasChanged) {
            const loadingDialog = new LoadingDialog("正在保存");
            loadingDialog.show();
            if (!!patient?.id) {
                try {
                    const result = await PatientAgent.updatePatientById(patient.id, patient);
                    if (result) {
                        this.innerState.patientInfo = Object.assign(this.innerState.patientInfo ?? {}, result);
                    }
                } catch (e) {
                    await loadingDialog.fail(`保存失败：${errorToStr(e)}`);
                    return;
                }
            } else {
                try {
                    const result = await PatientAgent.createPatient(patient!);
                    // 需要更新一下当前患者信息，避免创建成功了，但是id没有更新
                    if (result) {
                        this.innerState.patientInfo = Object.assign(this.innerState.patientInfo ?? {}, result);
                    }
                } catch (e) {
                    await loadingDialog.fail(`保存失败：${errorToStr(e)}`);
                    return;
                }
            }
            await loadingDialog.hide();
        }

        ABCNavigator.pop(this.innerState.patientInfo);
    }

    async *_mapChangePatientName(event: _EventChangePatientName): AsyncGenerator<State> {
        this.innerState.patientInfo!.name = event.name;
        this.innerState.hasChanged = true;
        this.update();
    }

    async *_mapEventUpdatePatientSex(event: _EventUpdatePatientSex): AsyncGenerator<State> {
        if (event.sex != this.innerState.patientInfo?.sex) {
            this.innerState.patientInfo!.sex = event.sex;
            this.innerState.hasChanged = true;
            this.update();
        }
    }

    async *_mapChangePatientTel(event: _EventChangePatientTel): AsyncGenerator<State> {
        this.innerState.patientInfo!.mobile = event.tel;
        this.innerState.hasChanged = true;
        this.update();
    }

    async *_mapChangePatientAge(): AsyncGenerator<State> {
        debugger;
        const selectInfo = await AgePicker.show(this.innerState.patientInfo!.age);
        if (selectInfo) {
            this.innerState.patientInfo!.age = selectInfo;
            this.innerState.patientInfo!.birthday = TimeUtils.age2birthday(selectInfo).format("yyyy-MM-dd");
            this.innerState.hasChanged = true;
            this.update();
        }
    }

    async *_mapChangePatientBirthday(): AsyncGenerator<State> {
        const _minDateMouthCount = 120 * 12; // 120（年）* 12（月） = 120年转换成月数
        const _defDateMouthCount = 40 * 12; // 40（年）* 12（月） = 40年转换成月数
        const _birthday = fromJsonToDate(this.innerState.patientInfo?.birthday);
        const selectInfo = await BirthdayPicker.show(
            _birthday ?? TimeUtils.getRecentMonthsFirstDay(_defDateMouthCount), // 无生日信息 默认至今40年前
            {
                minDate: TimeUtils.getRecentMonthsFirstDay(_minDateMouthCount, 1), // 限制最小可选范围 至今120年前
                maxDate: new Date(),
            }
        );

        if (selectInfo) {
            this.innerState.patientInfo!.age = TimeUtils.birthday2age(selectInfo);
            this.innerState.patientInfo!.birthday = selectInfo.format("yyyy-MM-dd");
            this.innerState.hasChanged = true;
            this.update();
        }
    }

    async *_mapEventUpdateAddress(event: _EventUpdateAddress): AsyncGenerator<State> {
        this.innerState.patientInfo!.address = JsonMapper.deserialize(PatientAddress, {
            ...this.innerState.patientInfo?.address,
            addressProvinceId: event.provinceInfo?.id,
            addressProvinceName: event.provinceInfo?.name,
            addressCityId: event.cityInfo?.id,
            addressCityName: event.cityInfo?.name,
            addressDistrictId: event.districtInfo?.id,
            addressDistrictName: event.districtInfo?.name,
        });
        this.innerState.hasChanged = true;
        this.update();
    }

    async *_mapChangePatientAreaDetail(event: _EventChangePatientAreaDetail): AsyncGenerator<State> {
        this.innerState.hasChanged = true;
        this.innerState.patientInfo!.address!.addressDetail = event.areaDetail;
        this.update();
    }

    async *_mapEventUpdateIdCard(event: _EventUpdateIdCard): AsyncGenerator<State> {
        this.innerState.hasChanged = true;
        this.innerState.patientInfo!.idCard = event.idCard;

        const birthTime = ValidatorUtils.getBirthWithIdCard(event.idCard);
        if (!!birthTime) {
            //身份证号生成Age
            const ageInfo = TimeUtils.birthday2age(birthTime);
            this.innerState.patientInfo!.age = ageInfo;
            if (!!ageInfo) this.innerState.patientInfo!.birthday = TimeUtils.age2birthday(ageInfo).format("yyyy-MM-dd");
        }
        this.update();
    }

    async *_mapEventBackPage(/*event: _EventBackPage*/): AsyncGenerator<State> {
        if (this.innerState.isEditing) ABCNavigator.pop();
        this.innerState.isEditing = false;
        this.innerState.patientInfo = _.cloneDeep(this.clonePatient);
        this.update();
    }

    async *_mapEventUpdatePatientSource(event: _EventUpdatePatientSource): AsyncGenerator<State> {
        this.innerState.patientInfo!.patientSource = event.source;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    async *_mapEventUpdateSN(event: _EventUpdateSN): AsyncGenerator<State> {
        const sn = StringUtils.parseInt(event.sn);
        this.innerState.patientInfo!.sn = sn > 0 ? event.sn : undefined;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    async *_mapEventUpdateRemark(event: _EventUpdateRemark): AsyncGenerator<State> {
        this.innerState.patientInfo!.remark = event.remark;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    async *_mapEventUpdatePatientProfession(event: _EventUpdatePatientProfession): AsyncGenerator<State> {
        this.innerState.patientInfo!.profession = event.profession;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    async *_mapEventModifyPatientCountryCode(event: _EventModifyPatientCountryCode): AsyncGenerator<State> {
        this.innerState.patientInfo!.countryCode = event.code;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    async *_mapEventUpdatePatientIdCardType(event: _EventUpdatePatientIdCardType): AsyncGenerator<State> {
        this.innerState.patientInfo!.idCardType = event.idCardType;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    async *_mapEventUpdatePatientCompany(event: _EventUpdatePatientCompany): AsyncGenerator<State> {
        this.innerState.patientInfo!.company = event.str;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    async *_mapEventUpdatePatientWeight(event: _EventUpdatePatientWeight): AsyncGenerator<State> {
        this.innerState.patientInfo!.weight = event.str;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    async *_mapEventUpdatePatientVisitReason(event: _EventUpdatePatientVisitReason): AsyncGenerator<State> {
        this.innerState.patientInfo!.visitReason = event.str;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    async *_mapEventUpdatePatientMarital(event: _EventUpdatePatientMarital): AsyncGenerator<State> {
        this.innerState.patientInfo!.marital = event.str;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    async *_mapEventUpdatePatientEthnicity(event: _EventUpdatePatientEthnicity): AsyncGenerator<State> {
        this.innerState.patientInfo!.ethnicity = event.str;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }
    update(): void {
        this.dispatch(new _EventUpdate());
    }

    requestChangePatient(): void {
        this.dispatch(new _EventChangePatient());
    }

    requestChangeEditState(): void {
        this.dispatch(new _EventChangeEditState());
    }

    requestChangePatientName(name: string): void {
        this.dispatch(new _EventChangePatientName(name));
    }

    requestUpdatePatientSex(sex: string): void {
        this.dispatch(new _EventUpdatePatientSex(sex));
    }

    requestChangePatientTel(tel: string): void {
        this.dispatch(new _EventChangePatientTel(tel));
    }

    requestChangePatientAge(): void {
        this.dispatch(new _EventChangePatientAge());
    }

    requestChangePatientBirthday(): void {
        this.dispatch(new _EventChangePatientBirthday());
    }

    requestUpdateAddress(provinceInfo: ProvinceInfo, cityInfo: CityInfo, districtInfo: DistrictInfo): void {
        this.dispatch(new _EventUpdateAddress(provinceInfo, cityInfo, districtInfo));
    }

    requestChangePatientAreaDetail(areaDetail: string): void {
        this.dispatch(new _EventChangePatientAreaDetail(areaDetail));
    }

    requestUpdateIdCard(idCard: string): void {
        this.dispatch(new _EventUpdateIdCard(idCard));
    }

    requestUpdatePatientSource(source: PatientSource): void {
        this.dispatch(new _EventUpdatePatientSource(source));
    }

    requestUpdatePatientProfession(profession: string): void {
        this.dispatch(new _EventUpdatePatientProfession(profession));
    }

    requestSaveEditInfo(): void {
        this.dispatch(new _EventSaveEditInfo());
    }

    requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }

    requestUpdateSN(sn: string): void {
        this.dispatch(new _EventUpdateSN(sn));
    }

    requestUpdateRemark(remark: string): void {
        this.dispatch(new _EventUpdateRemark(remark));
    }

    requestModifyPatientCountryCode(code: string): void {
        this.dispatch(new _EventModifyPatientCountryCode(code));
    }

    requestUpdatePatientIdCardType(type: string): void {
        this.dispatch(new _EventUpdatePatientIdCardType(type));
    }
    requestUpdatePatientCompany(company: string): void {
        this.dispatch(new _EventUpdatePatientCompany(company));
    }
    requestUpdatePatientWeight(weight: number): void {
        this.dispatch(new _EventUpdatePatientWeight(weight));
    }
    requestUpdatePatientVisitReason(visitReason: string): void {
        this.dispatch(new _EventUpdatePatientVisitReason(visitReason));
    }
    requestUpdatePatientMarital(marital: number): void {
        this.dispatch(new _EventUpdatePatientMarital(marital));
    }
    requestUpdatePatientEthnicity(ethnicity: string): void {
        this.dispatch(new _EventUpdatePatientEthnicity(ethnicity));
    }
}

export { PatientEditPageBloc, State };
