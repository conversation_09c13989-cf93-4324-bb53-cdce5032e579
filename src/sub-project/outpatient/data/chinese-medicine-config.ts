/**
 * create by dengjie
 * desc:
 * create date 2020/4/28
 */

import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import ChineseMedicine from "../../../assets/medicine_usage/chinese-medicine-config";
import { employeeSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { ChineseMedicineSpecType } from "../../base-business/data/beans";
import _ from "lodash";
import { ChargeAgent } from "../../charge/data/charge-agent";
import { MedicineScopeId, UsageScopeId } from "../../charge/data/charge-bean-air-pharmacy";

const GET_PR_USAGE_DEFAULT = "getPRUsageDefault";

export class ChineseMedicineConfig {
    freq?: Array<ChineseUsageItemInfo>;
    dailyDosage!: Array<ChineseUsageItemInfo>;
    specialRequirement?: Array<ChineseUsageItemInfo>;
    usages?: Array<ChineseUsageItemInfo>;
    usageLevel?: Array<ChineseUsageItemInfo>;
    requirement?: Array<ChineseUsageItemInfo>;
    requirements?: Array<ChineseUsageItemInfo>;
    grade?: Array<Grade>;

    /// 中药规格，饮片等
    specification?: Array<string>;

    ///处方初始默认配置值
    prescriptionDefaultValues?: PrescriptionDefaultValue;

    chinesePRUsageDefault?: Map<string, PrescriptionDefaultUsage>;
}

class PrescriptionDefaultValue {
    dailyDosage?: string;
    usage?: string;
    freq?: string;
    usageLevel?: string;
    specification?: string;
}

export class PrescriptionDefaultUsage {
    dailyDosage?: string;
    usage?: string;
    freq?: string;
    usageLevel?: string;
    usageDays?: string;
}

export class ChineseUsageItemInfo {
    id!: number;
    name!: string;
    namePY?: string;
    namePYFirst?: string;
    value?: number;
    time?: number;
    isCustom?: boolean;
}

class Grade {
    name?: string;
    value?: string;
}

export class ChineseMedicineConfigProvider {
    static _config: ChineseMedicineConfig;

    /**
     * 获取中药用法
     * @return ChineseMedicineConfig ChineseMedicineConfig
     */
    static getConfig(): ChineseMedicineConfig {
        if (this._config) return this._config;
        this._config = JsonMapper.deserialize(ChineseMedicineConfig, ChineseMedicine);
        const userPRUsageDefault = employeeSharedPreferences.getObject(GET_PR_USAGE_DEFAULT)
            ? JSON.parse(employeeSharedPreferences.getObject(GET_PR_USAGE_DEFAULT))
            : undefined;
        if (userPRUsageDefault && !_.isEmpty(userPRUsageDefault)) {
            const newDefault = new Map<string, PrescriptionDefaultUsage>(userPRUsageDefault);
            if (newDefault.has("颗粒剂"))
                this._config.chinesePRUsageDefault = new Map<string, PrescriptionDefaultUsage>(userPRUsageDefault);
        }
        return this._config;
    }

    static getChinesePRUsageDefault(): Map<string, PrescriptionDefaultUsage> {
        return this.getConfig().chinesePRUsageDefault!;
    }

    static getChinesePRWithUsage(usage: string): PrescriptionDefaultUsage | undefined {
        return this.getChinesePRUsageDefault().get(usage);
    }

    static async getChinesePRWithScopeId(id: string): Promise<PrescriptionDefaultUsage | undefined> {
        const usageList = await ChargeAgent.getUsageScopes(true);
        return this.getChinesePRWithUsage(usageList.find((item) => item.id == id)?.name ?? "");
    }

    static getChinesePRWithSpecification(specificationType: number): PrescriptionDefaultUsage | undefined {
        if (specificationType == ChineseMedicineSpecType.chinesePiece) {
            return this.getChinesePRWithUsage("煎服");
        }
        if (specificationType == ChineseMedicineSpecType.chineseGranule) {
            return this.getChinesePRWithUsage("冲服");
        }
    }

    static setChinesePRWithUsage(usages: PrescriptionDefaultUsage[]): void {
        usages.forEach((item) => {
            if (item.usage) this.getConfig().chinesePRUsageDefault?.set(item.usage, item);
        });
        const chinesePRUsageDefault = JSON.stringify(Array.from(this.getConfig().chinesePRUsageDefault ?? []));
        employeeSharedPreferences.setObject(GET_PR_USAGE_DEFAULT, chinesePRUsageDefault);
    }
}

export class ChineseMedicineUsageInfo {
    static GenerateUsageInfo(label?: string, start = 0, max?: number, step = 1, unit?: string): ChineseUsageItemInfo[] | undefined {
        if (!max) return;
        const _arr = [];
        for (let i = start; i <= max; i += step) {
            _arr.push({
                id: i,
                name: `${label}${i}${unit}`,
                value: i,
            });
        }
        return _arr;
    }

    static GetChineseUsageParam(usage?: string):
        | {
              defaultFreq?: string;
              defaultUsageLevel?: string;
              freq?: ChineseUsageItemInfo[];
              usageLevel?: ChineseUsageItemInfo[];
          }
        | undefined {
        if (!usage) return undefined;
        const usageInfoMap = new Map();
        usageInfoMap.set(UsageScopeId.zhiWan, {
            defaultFreq: "1日2次",
            defaultUsageLevel: "每次9g",
            freq: ChineseMedicineUsageInfo.GenerateUsageInfo("1日", 1, 5, 1, "次"),

            usageLevel: ChineseMedicineUsageInfo.GenerateUsageInfo("每次", 8, 12, 1, "g"),
        });
        usageInfoMap.set(UsageScopeId.daFen, {
            defaultFreq: "1日2次",
            defaultUsageLevel: "每次10g",
            freq: ChineseMedicineUsageInfo.GenerateUsageInfo("1日", 1, 5, 1, "次"),

            usageLevel: ChineseMedicineUsageInfo.GenerateUsageInfo("每次", 9, 15, 1, "g"),
        });
        usageInfoMap.set(MedicineScopeId.pingZhuang, {
            defaultFreq: "1日2次",
            defaultUsageLevel: "每次20g",
            freq: ChineseMedicineUsageInfo.GenerateUsageInfo("1日", 1, 5, 1, "次"),
            usageLevel: ChineseMedicineUsageInfo.GenerateUsageInfo("每次", 5, 40, 5, "g"),
        });
        usageInfoMap.set(MedicineScopeId.daiZhuang, {
            defaultFreq: "1日2次",
            defaultUsageLevel: "每次20g",
            freq: ChineseMedicineUsageInfo.GenerateUsageInfo("1日", 1, 5, 1, "次"),
            usageLevel: [
                {
                    name: "每次20g",
                    value: 20,
                },
            ],
        });
        usageInfoMap.set(UsageScopeId.keLi, {
            defaultFreq: "1日3次",
            defaultUsageLevel: "每次1袋",
            usageLevel: [
                {
                    name: "每次半袋",
                    value: 0.5,
                },
                {
                    name: "每次1袋",
                    value: 1,
                },
                {
                    name: "每次2袋",
                    value: 2,
                },
                {
                    name: "每次3袋",
                    value: 3,
                },
            ],
        });
        return usageInfoMap.get(usage);
    }
}
