/**
 * create by dengjie
 * desc: 关于智能处方的接口
 * create date 2020/5/7
 */
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { ABCApiNetwork } from "../../net";
import { OutpatientInvoiceDetail, PrescriptionChineseForm, PrescriptionFormItem } from "./outpatient-beans";
import _ from "lodash";
import { AcupunctureData, AcupunctureItem } from "../acupuncture/data/acupuncture-bean";
import { PrescriptionTemplate, PrescriptionTemplateInfo } from "./prescription-template-bean";
import { IngredientEnum, PsychotropicNarcoticTypeEnum } from "../../base-business/data/beans";

export class CDSSMedicineSuggestUsage {
    itemId?: string;
    goodsId?: string;
    medicineCadn?: string;
    name?: string;
    specification?: string;
    manufacturer?: string;
    ast?: boolean;
    usage?: string;
    ivgtt?: number;
    ivgttUnit?: string;
    freq?: string;
    dosage?: string;
    dosageUnit?: string;
    specialRequirement?: string;
    useDismounting?: number;
    unitCount?: number;
    unit?: string;
    created?: string;
    doctorId?: string;
    clinicId?: string;
    patientId?: string;
    copyWriterId?: string;
    birthday?: string;
    age?: number;
}

export class PhysicalExam {
    symptomName?: string;
    selected?: boolean;
    common?: boolean;
}

export class ConcomitantSymptom {
    symptomName?: string;
    selected?: boolean;
    common?: boolean;
}

export class BiochemicalExam {
    symptomName?: string;
    selected?: boolean;
    common?: boolean;
}

export class TherapyExam {
    symptomName?: string;
    selected?: boolean;
    common?: boolean;
}

export class PrescriptionExam {
    common?: boolean;
    selected?: boolean;
    symptomName?: string;
    @JsonProperty({ type: Array, clazz: PrescriptionFormItem })
    prescriptionFormItems?: PrescriptionFormItem[];

    tansExam2TemplateDetail(): PrescriptionTemplate {
        const prTemp: PrescriptionTemplate = JsonMapper.deserialize(PrescriptionTemplate, {});

        prTemp.detail = JsonMapper.deserialize(PrescriptionTemplateInfo, {
            prescriptionChineseForms: [],
        });
        const chineseForm = JsonMapper.deserialize(PrescriptionChineseForm, { doseCount: 1 });
        chineseForm.prescriptionFormItems = this.prescriptionFormItems;

        prTemp.detail.prescriptionChineseForms?.push(chineseForm);
        prTemp.prInfo = prTemp.detail;
        return prTemp;
    }
}

export class VerifyLevel {
    static pass = 1; //通过
    static moderately = 2; //轻度
    static middle = 3; //中度
    static danger = 4; //严重
    static avoid = 5; //禁忌
    static nameMaps?: Map<number, string> = new Map([
        [VerifyLevel.moderately, "轻度"],
        [VerifyLevel.middle, "中度"],
        [VerifyLevel.danger, "严重"],
        [VerifyLevel.avoid, "禁忌"],
        [VerifyLevel.pass, "通过"],
    ]);
}

export class GetDiagnosisRspData {
    diseaseGuid?: string;
    diseaseName?: string;
    isSerious?: boolean;
    level?: number;

    @JsonProperty({
        name: "concomitant_symptom",
        type: Array,
        clazz: ConcomitantSymptom,
    })
    concomitantSymptom?: Array<ConcomitantSymptom>;

    @JsonProperty({ name: "physical_exam", type: Array, clazz: PhysicalExam })
    physicalExam?: Array<PhysicalExam>;

    @JsonProperty({
        name: "biochemical_exam",
        type: Array,
        clazz: BiochemicalExam,
    })
    biochemicalExam?: Array<BiochemicalExam>;

    @JsonProperty({
        name: "therapy_exam",
        type: Array,
        clazz: TherapyExam,
    })
    therapyExam?: Array<TherapyExam>;

    @JsonProperty({
        name: "prescription_exam",
        type: Array,
        clazz: PrescriptionExam,
    })
    prescriptionExam?: Array<PrescriptionExam>;
}

export class GetDiagnosisRspRequest {
    q?: string;
    sex?: string;
    age?: string;
}

export class GetDiagnosisRspStatus {
    code?: number;
    message?: string;
}

export class GetDiagnosisRsp {
    @JsonProperty({ type: GetDiagnosisRspRequest })
    request?: GetDiagnosisRspRequest;

    @JsonProperty({ type: GetDiagnosisRspStatus })
    status?: GetDiagnosisRspStatus;

    @JsonProperty({ type: Array, clazz: GetDiagnosisRspData })
    data!: Array<GetDiagnosisRspData>;
}

export class ChinesePhysical {
    lichen?: Array<string>;
    observe?: Array<string>;
    pulse?: Array<string>;
    tongue?: Array<string>;
}

export class GetAiDiagnosisRspDate {
    @JsonProperty({ type: Array, clazz: GetDiagnosisRspData })
    list?: Array<GetDiagnosisRspData>;

    @JsonProperty({ type: ChinesePhysical })
    physical?: ChinesePhysical;
}

export class VerifyItemDetailWestern {
    medicineA?: string;
    medicineB?: string;
    innComponentA?: string;
    innComponentB?: string;

    influence?: string;

    level?: number; //VerifyLevel

    mechanism?: string; // 风险详情

    measures?: string; //建议措施

    //  int literature_level;

    //  int action_type

    get displayErrorInfo(): string {
        if (this.influence && this.medicineA && this.medicineB) {
            const errorInfo = this.influence?.replace(/{A}/g, this.medicineA).replace(/{B}/g, this.medicineB);
            return `${errorInfo}（风险等级：${VerifyLevel.nameMaps?.get(this.level!)}）`;
        }
        return "";
    }
}

export class VerifyItemDetail {
    @JsonProperty({ type: Array, clazz: VerifyItemDetailWestern })
    western?: Array<VerifyItemDetailWestern>;

    cadn?: string;
    goodsId?: string;
    keyId?: string;
    reason?: string;
    prescriptionType?: IngredientEnum[];
    prescriptionName?: string;
    index?: number; //处方索引
    /**
     * prescriptionType与PsychotropicNarcoticType隐射关系
     */
    get psychotropicNarcoticType(): PsychotropicNarcoticTypeEnum {
        switch (this.prescriptionType?.[0]) {
            case IngredientEnum.JING_1:
                return PsychotropicNarcoticTypeEnum.JING1;
            case IngredientEnum.JING_2:
                return PsychotropicNarcoticTypeEnum.JING2;
            case IngredientEnum.MA:
                return PsychotropicNarcoticTypeEnum.MAZUI;
            case IngredientEnum.DU:
                return PsychotropicNarcoticTypeEnum.DU;
            default:
                return PsychotropicNarcoticTypeEnum.NONE;
        }
    }
}

export class DiseaseSymptom {
    @JsonProperty({ name: "symptom_type" })
    symptomType?: number;

    @JsonProperty({ name: "symptom_name" })
    symptomName?: string;

    @JsonProperty({ name: "symptom_boost" })
    symptomBoost?: number;

    @JsonProperty({ name: "symptom_common" })
    symptomCommon?: boolean;
}

export class SuggestDiseaseSource {
    @JsonProperty({ name: "disease_name" })
    diseaseName?: string;

    @JsonProperty({ name: "disease_id" })
    diseaseId?: string;

    @JsonProperty({ name: "disease_type" })
    diseaseType?: number;

    @JsonProperty({ name: "disease_common" })
    diseaseCommon?: boolean;

    @JsonProperty({ name: "disease_serious" })
    diseaseSerious?: boolean;

    @JsonProperty({ name: "disease_epidemic" })
    diseaseEpidemic?: boolean;

    // disease_season: any[];

    @JsonProperty({ name: "disease_sex" })
    diseaseSex?: number;
    // disease_age_level: any[];

    @JsonProperty({ name: "disease_symptoms", type: Array, clazz: DiseaseSymptom })
    diseaseSymptoms?: DiseaseSymptom[];
}

export class SuggestDiseaseItem {
    @JsonProperty({ name: "_index" })
    index?: string;

    @JsonProperty({ name: "_type" })
    type?: string;

    @JsonProperty({ name: "_id" })
    id?: string;

    @JsonProperty({ name: "_score" })
    score?: number;

    @JsonProperty({ name: "_source", type: SuggestDiseaseSource })
    source?: SuggestDiseaseSource;
}

class VerifyFITNESSCheckItemDetail {
    medicineCadn?: string;
    goodsId?: string;
    keyId?: string;
}
export class VerifyFITNESSCheckItem {
    categary?: string;
    checkItems?: [];
    cnName?: string;
    @JsonProperty({
        fromJson: (detail) => {
            try {
                return JSON.parse(detail);
            } catch (e) {
                return detail;
            }
        },
    })
    detail?: VerifyFITNESSCheckItemDetail;
    level?: string;
    name?: string;
    pharmacyType?: string;
}

export class VerifyItem {
    categary?: string;
    level?: string;
    name?: string;

    @JsonProperty({
        fromJson: (detail) => {
            try {
                const _detailJson = JSON.parse(detail);
                if (_.has(_detailJson, "chinese")) {
                    return _detailJson.chinese
                        .map((item: any) => {
                            return `${item.reason}`;
                        })
                        .join("\n");
                }
                return JsonMapper.deserialize(VerifyItemDetail, _detailJson);
            } catch (e) {
                return detail;
            }
        },
    })
    detail?: VerifyItemDetail | string;
    cnName?: string;
    pharmacyType?: number; // 药房类型 0 本地药房 1 空中药房 2 虚拟药房
    static verifyItemLevelMaps: Map<string, number> = new Map<string, number>([
        ["PASS", VerifyLevel.pass],
        ["WARN", VerifyLevel.middle],
        ["DANGER", VerifyLevel.danger],
    ]);

    mapToVerifyLevel(): number {
        return VerifyItem.verifyItemLevelMaps.get(<string>this.level) ?? VerifyLevel.pass;
    }

    //保存后台返回的原始数据+里面多个药品使用`goodsId{num}`去存储，暂时没有进行keyId和goodsId分组
    //使用原始数据进行字符串比对
    @JsonProperty({ name: "detail" })
    __originDetail?: string;
    @JsonProperty({ type: Array, clazz: VerifyFITNESSCheckItem })
    checkItems?: VerifyFITNESSCheckItem[];
}

export class VerifyItems {
    @JsonProperty({ type: Array, clazz: VerifyItem })
    REGULATION?: Array<VerifyItem>;

    @JsonProperty({ type: Array, clazz: VerifyItem })
    FITNESS?: Array<VerifyItem>;
}

export class OutpatientInvoiceVerifyRsp {
    @JsonProperty({ type: VerifyItems })
    verifyItems?: VerifyItems;

    @JsonProperty({ type: Array, clazz: VerifyItem })
    checkItems?: VerifyItem[]; // 已签字签字药品

    private _level?: number; //VerifyLevel

    get level(): number {
        if (this._level != null) return this._level;

        this._level = VerifyLevel.pass;
        let _list: VerifyItem[] = [];
        if (!_.isEmpty(this.verifyItems?.REGULATION)) _list = _list.concat(this.verifyItems?.REGULATION ?? []);
        if (!_.isEmpty(this.verifyItems?.FITNESS)) _list = _list.concat(this.verifyItems?.FITNESS ?? []);
        for (const item of [..._list]) {
            const itemLevel = item.mapToVerifyLevel();
            this._level = Math.max(this._level, itemLevel); // 取level优先级最大展示
        }

        return this._level;
    }

    get hasControlledSubstancesRule(): VerifyFITNESSCheckItem[] | undefined {
        const ControlledSubstancesRuleItem = this?.verifyItems?.FITNESS?.find((f_item) => f_item.name == "ControlledSubstancesRule");

        return ControlledSubstancesRuleItem?.checkItems;
    }
}

export class CDSSAgent {
    /**
     * 搜索药品的用法
     * @param medicineCadn
     * @param goodsId
     * @param type
     * @param age
     * @param weight
     * @param sex
     * @param usageFrom
     */
    static async getMedicineUsage({
        medicineCadn,
        goodsId,
        type,
        age,
        weight,
        sex,
        usageFrom = 0,
    }: {
        medicineCadn?: string;
        goodsId?: string;
        type?: number;
        age?: number;
        weight?: number;
        sex?: string;
        usageFrom?: number; // 0-诊所；10-医院
    }): Promise<CDSSMedicineSuggestUsage> {
        return ABCApiNetwork.get("cdss/medicine/usage", {
            useBody: true,
            queryParameters: {
                medicineCadn: medicineCadn ?? "",
                goodsId: goodsId ?? "",
                type: type?.toString() ?? "",
                weight: weight?.toString() ?? "",
                sex: sex ?? "",
                age: age?.toString() ?? "",
                usageFrom: usageFrom,
            },
        });
    }

    /**
     * 获取智能诊断推荐 西医
     * @param params chiefComplaint主诉
     */
    static async getDiagnosis(params: { q: string; sex: string; age: number }): Promise<GetAiDiagnosisRspDate> {
        const { q, age } = params;
        let { sex } = params;
        if (sex == "男") {
            sex = "male";
        } else if (sex == "女") {
            sex = "female";
        }
        const rsp: GetDiagnosisRsp = await ABCApiNetwork.get("cdss/diagnosis", {
            queryParameters: { q: q ?? "", sex: sex, age },
            clazz: GetDiagnosisRsp,
            useBody: true,
        });
        const returnV = new GetAiDiagnosisRspDate();
        returnV.list = rsp.data;
        return returnV;
    }

    /**
     * 智能审方
     * @param outpatientInvoice outpatientInvoice
     * @param isControlledSubstances
     */
    static async outpatientVerify(
        outpatientInvoice: OutpatientInvoiceDetail,
        isControlledSubstances: boolean
    ): Promise<OutpatientInvoiceVerifyRsp> {
        return await ABCApiNetwork.post("cdss/outpatient/verify", {
            useBody: true,
            clazz: OutpatientInvoiceVerifyRsp,
            body: { isControlledSubstances: isControlledSubstances, ...outpatientInvoice },
        });
    }

    /**
     * 获取智能诊断推荐 中医
     * @param params
     */
    static async getChineseDiagnosis(params: {
        q: string;
        sex: string;
        age: number;
        dn: string;
        hpi: string;
        ob: string;
        sn: string;
        tn: string;
        dictDiseaseType?: string;
    }): Promise<GetAiDiagnosisRspDate> {
        let { sex } = params;
        if (sex == "男") {
            sex = "male";
        } else if (sex == "女") {
            sex = "female";
        }
        return await ABCApiNetwork.get("cdss/prescription", {
            queryParameters: { ...params, sex },
            clazz: GetAiDiagnosisRspDate,
            clearUndefined: true,
        });
    }

    /**
     *
     * @param key
     */
    static getSuggestDisease(key: string): Promise<SuggestDiseaseItem[]> {
        return ABCApiNetwork.get<SuggestDiseaseItem[]>("cdss/suggests/disease", {
            queryParameters: { q: key ?? "" },
        }).then((rsp) => {
            return rsp.map((item) => JsonMapper.deserialize(SuggestDiseaseItem, item));
        });
    }

    static getAcupuncture(): Promise<AcupunctureItem[]> {
        return ABCApiNetwork.get<AcupunctureData>("cdss/acupuncture", {
            clazz: AcupunctureData,
            useBody: true,
        }).then((rsp) => {
            /**
             * 处理流派选穴和身体部位 穴位问题
             */
            const list: AcupunctureItem[] = rsp.data?.acupuncture ?? [];
            if (!!rsp.data?.schoolAcupuncture) {
                let childrenList: AcupunctureItem[] = [];
                rsp.data?.schoolAcupuncture.map((it) => {
                    childrenList.push({
                        id: "",
                        name: it.name,
                        children: [],
                        type: 0,
                        __isSecondary: true,
                    });
                    //对董氏奇穴名字进行覆写
                    it.children.map((c_it) => {
                        const l_i = c_it.name.indexOf("("),
                            r_i = c_it.name.indexOf(")");
                        if (l_i >= 0) {
                            c_it.name = c_it.name.substring(l_i + 1, r_i);
                        }
                    });
                    childrenList = childrenList.concat(it.children);
                });
                list.push({
                    id: "",
                    name: "流派选穴", // 三主穴位名
                    children: childrenList,
                    type: 0,
                });
            }
            if (!!rsp.data?.bodyPosition) {
                rsp.data.bodyPosition.__isBodyPosition = true;
                rsp.data.bodyPosition.children.map((it) => {
                    it.__isBodyPosition = true;
                    it.children.map((itt) => (itt.__isBodyPosition = true));
                });
                list.push(rsp.data.bodyPosition);
            }
            return list;
        });
    }
}
