/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/4/20
 */
import PathUtils from "../../base-business/file/path-utils";
import { LogUtils } from "../../common-base-module/log";
import { ABCTrace } from "../../common-base-module/trace/abc-trace";
import FileUtils from "../../common-base-module/file/file-utils";
import { ChineseMedicineSpecType, PsychotropicNarcoticTypeEnum } from "../../base-business/data/beans";

export class OutpatientConst {
    static kMaxPrescriptionPictures = 3; //拍照抓方,图片最大张数

    static async getDraftDir(clinicScope: boolean): Promise<string | null> {
        const dir = await PathUtils.getOutpatientDir(true, clinicScope);
        const ret = await FileUtils.createDir(`${dir}/draft`);

        if (!ret) {
            LogUtils.d("failed to create outpatient draft dir");
            return null;
        }

        return dir;
    }

    static async getDraftFile(draftId: string, clinicScope?: boolean): Promise<string> {
        const draftDir = await this.getDraftDir(clinicScope ?? true);

        return `${draftDir}/${draftId}.dat`;
    }

    //获取本地搜索存储路径
    static async getSearchHistoryFile(sceneId: string): Promise<string> {
        let dir = await PathUtils.getOutpatientDir();
        dir = `${dir}/history`;
        const dirExist = await FileUtils.fileExists(dir);
        if (!dirExist) await FileUtils.createDir(dir);

        return `${dir}/history_${sceneId}.dat`;
    }

    //门诊相关本地数据存储位置
    static async getOutpatientDir(): Promise<string> {
        return await PathUtils.getOutpatientDir();
    }

    static dosageUsageProperty = "适量";

    ////聊天相关目录
    /// chat/outpatientId/
    ///                 /res/audio
    /**
     * 获取在线网诊聊天相关资源存储目录（如语音）
     */

    static async getChatDir(): Promise<string> {
        let dir = await ABCTrace(PathUtils.getOutpatientDir(true, false), "获取门诊目录");
        dir = `${dir}/chat`;
        const dirExist = await FileUtils.fileExists(dir);
        if (!dirExist) await FileUtils.createDir(dir);

        return dir;
    }

    static async getChatCacheDir(outpatientId: string): Promise<string> {
        let dir = await ABCTrace(OutpatientConst.getChatDir(), "获取门诊目录");
        dir = `${dir}/${outpatientId}`;
        const dirExist = await FileUtils.fileExists(dir);
        if (!dirExist) await FileUtils.createDir(dir);

        return dir;
    }

    static async getChatConversionDetailDir(): Promise<string> {
        let dir = await ABCTrace(OutpatientConst.getChatDir(), "获取门诊目录");
        dir = `${dir}/conversionDetail`;
        const dirExist = await FileUtils.fileExists(dir);
        if (!dirExist) await FileUtils.createDir(dir);

        return dir;
    }

    /**
     * 获取在线网诊聊天相关资源存储目录（如语音）
     */
    static async getChatAudioResCacheDir(outpatientId: string): Promise<string> {
        let dir = await OutpatientConst.getChatCacheDir(outpatientId);
        dir = `${dir}/res/audio`;
        const dirExist = await FileUtils.fileExists(dir);
        if (!dirExist) await FileUtils.createDir(dir);
        return dir;
    }
}

export enum MedicineAddType {
    western, // 成药
    chinese,
    infusion, // 输注
    examination,
    treatment,
    goods,
    medicalMaterial,
    others,
    mixProduct,
    package,
    external,
    nurse, // 护理项目
    examinationTest,
    surgery, // 手术
}

export enum OutpatientContentRefKey {
    patient = "patientRef",

    //----------病历相关----------//
    chiefComplaint = "chiefComplaintRef", // 主诉
    pastHistory = "pastHistoryRef", // 既往史
    presentHistory = "presentHistoryRef", // 现病史
    symptomTime = "symptomTimeRef", // 发病时间
    allergicHistory = "allergicHistoryRef", // 过敏史
    personalHistory = "personalHistoryRef", // 个人史
    birthHistory = "birthHistoryRef", // 出生史
    diagnosis = "diagnosisRef", // 诊断
    familyHistory = "familyHistoryRef", // 流行病史
    epidemiologicalHistory = "epidemiologicalHistoryRef",
    physicalExamination = "physicalExaminationRef", // 体格检查
    chineseExamination = "chineseExaminationRef", // 望闻问切
    tongue = "tongueRef", // 舌象
    pulse = "pulseRef", // 脉象
    auxiliaryExaminations = "auxiliaryExaminationsRef", // 辅助检查
    syndrome = "syndromeRef", // 辨证
    therapy = "therapyRef", // 治法
    chinesePrescription = "chinesePrescriptionRef", // 方药
    dentistryDiagnosisInfos = "dentistryDiagnosisInfosRef", // 牙科
    obstetricalHistory = "obstetricalHistoryRef", // 月经婚育史
    attachment = "attachmentRef", // 附件
    wearGlassesHistory = "wearGlassesHistoryRef", //戴镜史
    eyeExamination = "eyeExaminationRef", //眼部检查
    inspectionReport = "inspectionReportRef", //检查报告
    treatmentPlans = "treatmentPlansRef", //治疗计划
    disposals = "disposalsRef", //处置
    dentistryExaminations = "dentistryExaminationsRef", //口腔检查
    oralExamination = "oralExaminationRef", //诊所管家---口腔检查
    syndromeTreatment = "syndromeTreatmentRef", //诊所管家---辨证论治
    doctorAdvice = "doctorAdviceRef", //诊所管家---医嘱建议
    prognosis = "prognosisRef", //诊所管家---预后
}

export const SelfProvidedText = "【自备】";
export const PsychotropicNarcoticTypeList = [
    { label: "无", value: PsychotropicNarcoticTypeEnum.NONE },
    { label: "麻醉", value: PsychotropicNarcoticTypeEnum.MAZUI },
    { label: "精一", value: PsychotropicNarcoticTypeEnum.JING1 },
    { label: "精二", value: PsychotropicNarcoticTypeEnum.JING2 },
    { label: "毒", value: PsychotropicNarcoticTypeEnum.DU },
    { label: "普通", value: PsychotropicNarcoticTypeEnum.PUTONG },
    { label: "儿科", value: PsychotropicNarcoticTypeEnum.ERKE },
    { label: "慢病", value: PsychotropicNarcoticTypeEnum.MANBING },
    { label: "老年病", value: PsychotropicNarcoticTypeEnum.LAONIANBING },
    { label: "急诊", value: PsychotropicNarcoticTypeEnum.JIZHEN },
    { label: "长期", value: PsychotropicNarcoticTypeEnum.CHANGQI },
];

export const conversionTypeList = [
    { label: "按等效饮片量开方", value: ChineseMedicineSpecType.chinesePiece },
    { label: "按实际颗粒量开方", value: ChineseMedicineSpecType.chineseGranule },
];
