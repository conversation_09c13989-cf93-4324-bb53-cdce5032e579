/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-23
 *
 * @description
 */
import { ChatUser } from "./chat-user";
import { DisposableTracker } from "../../../../../common-base-module/cleanup/disposable";
import { IMMsg } from "../../../../data/im-beans";

export enum ChatMessageStatus {
    sending,
    unread,
    read,
    revoked = 3,
}

export class ChatMessage extends DisposableTracker {
    /// Id of the message if no id is supplied a new id is assigned
    /// using a [UUID v4] this behaviour could be overriden by provind
    /// and [optional] paramter called [messageIdGenerator].
    /// [messageIdGenerator] take a function with this
    /// signature [String Function()]
    id?: string;

    /// Actual text message.
    text?: string;

    /// It's a [non-optional] pararmter which specifies the time the
    /// message was delivered takes a [DateTime] object.
    createdAt?: Date;

    /// Takes a [ChatUser] object which is used to distinguish between
    /// users and also provide avaatar URLs and name.
    user?: ChatUser;

    /// A [non-optional] parameter which is used to display images
    /// takes a [Sring] as a audioUrl
    image?: string;

    imageSize?: { width?: number; height?: number };

    /// A [non-optional] parameter which is used to display vedio
    /// takes a [Sring] as a audioUrl
    video?: string;

    audio?: string; //音频地址
    duration?: number; //视频或音频时长

    ///
    status?: ChatMessageStatus;

    get isSystemMsg(): boolean {
        return false;
    }

    ///chat_view不使用此值，由业务自己决定如何使用
    obj: any;

    updateObj(obj: IMMsg): void {
        this.obj = obj;
    }

    dispose(): void {
        this.closeDisposables();
    }

    get isText(): boolean {
        return !!this.text;
    }

    get isAudio(): boolean {
        return !!this.audio;
    }

    constructor(options: {
        id?: string;
        text?: string;
        user?: ChatUser;
        image?: string;
        imageSize?: { width?: number; height?: number };
        video?: string;
        audio?: string;
        duration?: number;
        createdAt?: Date;
    }) {
        super();
        this.id = options.id;
        this.text = options.text;
        this.user = options.user;
        this.image = options.image;
        this.imageSize = options.imageSize;
        this.video = options.video;
        this.audio = options.audio;
        this.duration = options.duration;
        this.createdAt = options.createdAt || new Date();
    }

    render(): JSX.Element | undefined {
        return undefined;
    }
}
