/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../../../bloc";
import { ChatUser } from "../chat-view/model/chat-user";
import { BaseLoadingState } from "../../../../bloc/bloc-helper";
import { ChatMessage, ChatMessageStatus } from "../chat-view/model/chat-message";
import {
    IMConversationDetail,
    IMMsg,
    IMMsgAudioBody,
    IMMsgImageBody,
    IMMsgSystemCustomTextBody,
    IMMsgSystemTextBody,
    IMMsgTextBody,
    IMMsgType,
    IMSceneType,
    UserType,
} from "../../../data/im-beans";
import { userCenter } from "../../../../user-center";
import imMessageLoader, { GetMessageRsp } from "../../../data/im-message-loader";
import { LogUtils } from "../../../../common-base-module/log";
import _ from "lodash";
import { EMPTY, Subject } from "rxjs";
import { EventName } from "../../../../bloc/bloc";
import { errorToStr } from "../../../../common-base-module/utils";
import { urlDispatcher } from "../../../../url-dispatcher/url-dispatcher";
import FileUtils from "../../../../common-base-module/file/file-utils";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../../../common-base-module/common-error";
import { IMCtrlMessageType, imService } from "../../../data/im";
import { OnlineMessageSubject } from "../../../../base-business/msg/online-message-manager";
import { fromPromise } from "rxjs/internal-compatibility";
import { ImAgent } from "../../../data/im-agent";
import imCacheManager from "../../../data/im-cache-manager";
import { OutpatientConst } from "../../../data/outpatient-const";
import {
    DoctorTimeMessage,
    RegCardCancelMessage,
    RegCardRequestMessage,
    RegCardSuccessMessage,
    RegDoctorMessage,
    RegMessage,
    RegProjectMessage,
    RegRecordMessage,
    SystemCustomTextMessage,
    SystemTextMessage,
    TreatmentMessage,
} from "../../../chat-page/outpatient-im-message";
import { JsonMapper } from "../../../../common-base-module/json-mapper/json-mapper";
import React from "react";
import { delayed } from "../../../../common-base-module/rxjs-ext/rxjs-ext";

class _UnknownChatUser extends ChatUser {
    employeeId: string;

    constructor(employeeId: string, otherSide: boolean, avatarPlaceHolder?: string) {
        super({
            otherSide: otherSide,
            avatarPlaceHolder: avatarPlaceHolder,
        });

        this.employeeId = employeeId;
    }
}

class State extends BaseLoadingState {
    messages: ChatMessage[] = [];
    messagesMap = new Map<string, ChatMessage>(); //用于快速判断消息是否重复
    loadingMsg = true;
    loadMsgError: any;

    lastGetMessageRsp?: GetMessageRsp;
    rawMessageList: IMMsg[] = [];

    get currentChatUser(): ChatUser {
        return this.chatUserMap.get(userCenter.employee!.id!)!;
    }

    chatMessageCacheDir!: string;
    currentConsultationId!: string; //当前会话id

    historyConversationIds?: string[];

    chatUserMap: Map<string, ChatUser> = new Map();

    hasPreConsultation(): boolean {
        return (
            !_.isEmpty(this.historyConversationIds) &&
            this.historyConversationIds!.findIndex((item) => this.currentConsultationId == item) < this.historyConversationIds!.length - 1
        );
    }

    get hasMoreMessage(): boolean {
        return !this.lastGetMessageRsp || this.lastGetMessageRsp.limit == this.lastGetMessageRsp.messageList.length;
    }

    isEmpty(): boolean {
        return _.isEmpty(this.messages);
    }

    clone(): State {
        const copy = new State();
        Object.assign(copy, this);
        return copy;
    }

    copyTo<T extends State>(target: T): T {
        Object.assign(target, this);
        return target;
    }
}

//获取到数据事件
export class FetchDataState extends State {
    previousMessageCount?: number;

    static cloneFrom(state: State): FetchDataState {
        const copy = new FetchDataState();
        return state.copyTo(copy);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    historyConversationIds?: string[];
    constructor(historyConversationIds?: string[]) {
        super();
        this.historyConversationIds = historyConversationIds;
    }
}

class _EventUpdate extends _Event {}

class _EventRequestPreConversionMsg extends _Event {}

class _EventRevokeMessage extends _Event {
    message: ChatMessage;

    constructor(message: ChatMessage) {
        super();
        this.message = message;
    }
}

class _EventToState extends _Event {
    state: State;

    constructor(state: State) {
        super();
        this.state = state;
    }
}

class _EventSendMessage extends _Event {
    message: ChatMessage;

    constructor(message: ChatMessage) {
        super();
        this.message = message;
    }
}

class ConversationChatViewBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ConversationChatViewBloc | undefined>(undefined);
    _loadMoreMessage = new Subject<number>();
    _loadDataTrigger = new Subject<number>();
    private _urlHandler?: (url: string) => boolean;

    //如果记录已经发起的拉取会话详情的请求，防止重复请求
    private readonly _hasFiredGetConversationDetail = new Set<string>();

    private readonly _conversationId: string;
    private readonly _selfChatUser: ChatUser; //聊天人-自己
    private readonly _peerChatUser: ChatUser; //聊天人-对方
    private readonly _msgConverter?: (msg: IMMsg) => ChatMessage | undefined;
    private _imSceneType: IMSceneType;

    constructor(options: {
        conversationId: string;
        selfChatUser: ChatUser;
        peerChatUser: ChatUser;
        imSceneType: IMSceneType;
        msgConverter?: (msg: IMMsg) => ChatMessage | undefined;
        historyConversationIds?: string[];
    }) {
        super();
        this._conversationId = options.conversationId;
        this._selfChatUser = options.selfChatUser;
        this._peerChatUser = options.peerChatUser;
        this._msgConverter = options.msgConverter;
        this._imSceneType = options.imSceneType;

        this.dispatch(new _EventInit(options.historyConversationIds));
    }

    static fromContext(context: ConversationChatViewBloc): ConversationChatViewBloc {
        return context;
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSendMessage, this._mapEventSendMessage);
        map.set(_EventRequestPreConversionMsg, this._mapEventRequestPreConversionMsg);
        map.set(_EventToState, this._mapEventToState);
        map.set(_EventRevokeMessage, this._mapEventRevokeMessage);
        return map;
    }

    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState.clone();
    }

    private async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        this.innerState.historyConversationIds = event.historyConversationIds;
        this.addDisposable(this._loadDataTrigger);
        this._loadDataTrigger
            .subscribe(() => {
                this._initForChatView();
            })
            .addToDisposableBag(this);

        this._loadDataTrigger.next(0);
    }

    /**
     * 初始化消息列表拉取相关
     * @private
     */
    private async _initForChatView(): Promise<void> {
        imCacheManager.init().then();

        //读取会话详情,构建参与着信息
        const conversionId = this._conversationId;
        if (conversionId) {
            imCacheManager.readConversationDetail(conversionId).then((detail) => {
                if (detail) {
                    this._setupChatUserInfo(detail);
                    this.update();
                }
            });
        }

        LogUtils.d("ConversationChatViewBloc._initForChatView");
        const chatRootDir = await OutpatientConst.getChatCacheDir(conversionId);
        const chatResDir = `${chatRootDir}/${conversionId}`;
        if (!(await FileUtils.fileExists(chatResDir))) {
            await FileUtils.createDir(chatResDir);
        }

        this.innerState.chatMessageCacheDir = chatResDir;
        this.innerState.currentConsultationId = this._conversationId;

        this.innerState.chatUserMap.set(this._selfChatUser.uid!, this._selfChatUser);
        // this.innerState.chatUserMap.set(this._peerChatUser.uid!, this._peerChatUser);

        this.addDisposable(this._loadMoreMessage);

        this._loadMoreMessage
            .pipe(
                switchMap(() => {
                    if (!this.innerState.hasMoreMessage) {
                        return EMPTY;
                    }

                    this.innerState.loadingMsg = true;
                    this.innerState.loadMsgError = null;
                    this.update();
                    return imMessageLoader
                        .loadHistoryMessages(this.innerState.currentConsultationId, this.innerState.lastGetMessageRsp)
                        .catch((err) => new ABCError(err))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.loadingMsg = false;
                if (rsp instanceof ABCError) {
                    this.innerState.loadMsgError = rsp.detailError;
                    this.update();
                } else {
                    this.innerState.loading = false;

                    let firstLoad = false;
                    const oldMessageCount = this.innerState.messages.length;
                    //会话首次加载（或是下拉显示历史咨询）
                    if (!this.innerState.lastGetMessageRsp) {
                        firstLoad = true;
                    }

                    this.innerState.lastGetMessageRsp = rsp;

                    const newMessages = this._convertToChatMessages(rsp.messageList);

                    this.innerState.rawMessageList.push(...rsp.messageList); //保存原始信息

                    //首次进入时,将当前会话的所有消息标记为已读
                    if (this.innerState.messages.length == 0) {
                        //延时标记为已读，标记为已读会触发首页更新，导致当前界面卡
                        delayed(500)
                            .subscribe(() => {
                                imService.markAllAsRead(this._conversationId, this._selfChatUser.uid!, this._imSceneType);
                            })
                            .addToDisposableBag(this);
                    }

                    this.innerState.messages.push(...newMessages);
                    LogUtils.d("_mapEventInit this.innerState.messages = " + this.innerState.messages.length);

                    const state = FetchDataState.cloneFrom(this.innerState);
                    if (firstLoad) {
                        state.previousMessageCount = oldMessageCount;

                        //这里防止消息太少了，不够一屏不能触发加载更多事件，导致不能加载历史消息（因为会话可能收到部份消息缓存了）
                        if (this.innerState.hasMoreMessage && this.innerState.messages.length < 15) this._loadMoreMessage.next(0);
                    }

                    this.emitState(state);
                    if (rsp.fromCache && rsp.conversionId == this._conversationId) {
                        this._refreshLatestMessage();
                    }
                }
            })
            .addToDisposableBag(this);

        this._loadMoreMessage.next(0);

        const newMessageSubscription = imService.allNewMessageObserver.subscribe((newMsg) => {
            LogUtils.d("PatientConversationPageBloc._mapEventInit receive new msg");
            if (newMsg.conversationId != this._conversationId) return;
            //将收到消息标记为已读
            if (newMsg.fromUserId !== userCenter.employee?.id) {
                imService.markAsRead(newMsg.conversationId!, userCenter.employee!.id!, newMsg.id!, this._imSceneType);
            }

            if (this.innerState.messagesMap.has(newMsg.id!)) {
                const message = this.innerState.messagesMap.get(newMsg.id!);
                if (message) {
                    message.updateObj(newMsg);
                    this.update();
                }
                return;
            }

            const message = this._toChatMessage(newMsg);
            if (message == null) return;
            for (const index in this.innerState.messages) {
                const msg = this.innerState.messages[index];
                if (newMsg.equalIgnoreMsgId(msg.obj)) return;
            }

            this.innerState.messages.splice(0, 0, message);

            this.innerState.messagesMap.set(newMsg.id!, message);

            //收到以下消息说明门诊单状态变化,需要后台刷新
            // const msgType = newMsg.msgType;

            this.update();
        });

        this.addDisposable(newMessageSubscription);

        imService.ctrlMsgObserver
            .subscribe((ctrlMsg) => {
                if (ctrlMsg.conversationId != this.innerState.currentConsultationId) return;
                for (const msg of this.innerState.messages) {
                    if (
                        msg.id == ctrlMsg.sequenceId ||
                        ((msg.id == ctrlMsg.msgId || msg.id == ctrlMsg.sequenceId) && ctrlMsg.type == IMCtrlMessageType.revoke) ||
                        msg.obj.msgId == ctrlMsg.msgId
                    ) {
                        //由于我们的发送的消息还没有生成后台的id,由ctrl消息返回的消息里带有对应的id,这里将后台id也加到到列表用于后续
                        const message = this.innerState.messagesMap.get(msg!.id!);
                        if (message) this.innerState.messagesMap.set(ctrlMsg!.msgId!, message);

                        //服务端收到消息了,将此条消息写到缓存中去
                        if (ctrlMsg.type == IMCtrlMessageType.received && message && message.obj instanceof IMMsg) {
                            const rawMsg = message.obj;
                            rawMsg.created = ctrlMsg.created;
                            rawMsg.msgId = ctrlMsg.msgId;
                            rawMsg.id = ctrlMsg.msgId;

                            imMessageLoader.onNewMessageReceived(rawMsg);
                        } else if (ctrlMsg.type == IMCtrlMessageType.revoke && message && message.obj instanceof IMMsg) {
                            const rawMsg = message.obj;
                            rawMsg.msgId = ctrlMsg.msgId;
                            rawMsg.id = ctrlMsg.msgId;
                            rawMsg.status = ChatMessageStatus.revoked;

                            imMessageLoader.onNewMessageReceived(rawMsg);
                        }

                        switch (ctrlMsg.type) {
                            case IMCtrlMessageType.received:
                                msg.status = ChatMessageStatus.unread;
                                break;

                            case IMCtrlMessageType.allRead:
                            case IMCtrlMessageType.read:
                                msg.status = ChatMessageStatus.read;
                                break;
                            case IMCtrlMessageType.revoke:
                                msg.status = ChatMessageStatus.revoked;
                                break;
                        }

                        this.update();
                        break;
                    }
                }
            })
            .addToDisposableBag(this);

        ///对于从缓存读取消息,部份消息卡片的状态更新,将通过个回调来通知
        imMessageLoader.messageUpdateObserver
            .subscribe((update) => {
                if (update.clearAndReplace) {
                    this._clearConversionMsg(update.conversionId);
                    const newMessages = this._convertToChatMessages(update.messageList);
                    this.innerState.rawMessageList.splice(0, 0, ...update.messageList); //保存原始信息
                    this.innerState.messages.splice(0, 0, ...newMessages);
                    this.update();
                } else {
                    update.messageList.forEach((item) => {
                        const message = this.innerState.messagesMap.get(item.id!);
                        if (message) {
                            message.updateObj(item);
                        }
                    });
                }
            })
            .addToDisposableBag(this);

        //监听websocket连接状态,如果重新连接上,尝试拉取当前会话最新消息
        const connectObserver = new OnlineMessageSubject("connect");
        this.addDisposable(connectObserver);
        connectObserver
            .subscribe(() => {
                LogUtils.d("_initForChatView connect");
                this._refreshLatestMessage();
            })
            .addToDisposableBag(this);
    }

    //清理一个会话中消息相
    private _clearConversionMsg(conversionId: string) {
        _.remove(this.innerState.rawMessageList, (msg) => msg.conversationId == conversionId);
        _.remove(this.innerState.messages, (msg) => (msg.obj as IMMsg).conversationId == conversionId);

        this.innerState.messagesMap.forEach((value, key, map) => {
            if (value.obj instanceof IMMsg) {
                if (value.obj.conversationId == conversionId) map.delete(key);
            }
        });
    }

    private async *_mapEventSendMessage(event: _EventSendMessage): AsyncGenerator<State> {
        const { message } = event;
        event.message.id = new Date().getTime().toString();
        event.message.status = ChatMessageStatus.sending;

        this.innerState.messages.splice(0, 0, message);

        const msg = new IMMsg(null);
        msg.fromUserId = userCenter.employee!.id;
        msg.fromUserType = UserType.doctor;
        msg.toUserId = this._peerChatUser.uid!;
        msg.conversationId = this._conversationId;
        msg.toUserType = UserType.patient;
        msg.sequenceId = message.id;
        msg.created = new Date();
        msg.sceneType = this._imSceneType;

        if (!_.isEmpty(message.text)) {
            msg.msgType = IMMsgType.text;
            const msgBody = new IMMsgTextBody();
            msgBody.text = message.text;
            msg.body = msgBody;
        } else if (!_.isEmpty(message.image)) {
            msg.msgType = IMMsgType.image;
            const msgBody = new IMMsgImageBody();
            msgBody.imageUrl = message.image;
            msgBody.height = message.imageSize?.height;
            msgBody.width = message.imageSize?.width;
            msg.body = msgBody;
        } else if (!_.isEmpty(message.audio)) {
            msg.msgType = IMMsgType.audio;
            const msgBody = new IMMsgAudioBody();
            msgBody.audioUrl = message.audio;
            msgBody.duration = message.duration;
            msg.body = msgBody;
        }

        event.message.obj = msg;

        LogUtils.d("msg.body =" + msg.body + ", message.text = " + message.text);
        imService.sendMessage(msg);
        this.innerState.messagesMap.set(msg!.sequenceId!, message);
        yield this.innerState.clone();
    }

    private async *_mapEventRequestPreConversionMsg(/*ignored: _EventRequestPreConversionMsg*/): AsyncGenerator<State> {
        const historyConsultations = this.innerState.historyConversationIds;
        if (_.isEmpty(historyConsultations)) return;

        const currentConversionId = this.innerState.currentConsultationId!;
        if (currentConversionId == this._conversationId) {
            // this.innerState.currentConversionId = historyConsultations![historyConsultations!.length - 1].conversationId;
            this.innerState.currentConsultationId = historyConsultations![0];
            this.innerState.lastGetMessageRsp = undefined;
            this._loadMoreMessage.next(0);
        } else {
            //已经在查看历史记录了
            const index = historyConsultations?.findIndex((item) => currentConversionId === item);

            //如果已经是最后一个了
            if (index == historyConsultations!.length - 1) return;

            this.innerState.currentConsultationId = historyConsultations![index! + 1];

            this.innerState.lastGetMessageRsp = undefined;
            this._loadMoreMessage.next(0);
        }
    }

    private async *_mapEventToState(event: _EventToState): AsyncGenerator<State> {
        yield event.state;
    }

    private _convertToChatMessages(rawMessages: IMMsg[]) {
        const newMessages: ChatMessage[] = [];
        for (const msg of rawMessages) {
            //判断消息是否重复,防止重复显示
            if (this.innerState.messagesMap.has(msg.id!)) continue;

            const message = this._toChatMessage(msg);
            if (message) {
                newMessages.push(message);
                this.innerState.messagesMap.set(msg.id!, message);
            }

            //出现了不能识别的聊天者,尝试一次刷新会话中用户信息
            if (message?.user instanceof _UnknownChatUser) {
                this._refreshChatUserInfo();
            }
        }
        return newMessages;
    }

    public requestRevokeMessage(message: ChatMessage): void {
        this.dispatch(new _EventRevokeMessage(message));
    }

    /*
    更新最新消息
     */
    private _refreshLatestMessage(): void {
        LogUtils.d("_refreshLatestMessage");
        const conversionId = this._conversationId;
        //如果是从缓存加载的当前咨询单的消息列表,需要查当前是否有新消息
        fromPromise(imMessageLoader.loadNewestMessages(conversionId))
            .subscribe(
                (rsp) => {
                    const newMessages = this._convertToChatMessages(rsp.messageList);
                    this.innerState.messages.splice(0, 0, ...newMessages);

                    this.update();
                },
                (error) => {
                    LogUtils.e("_refreshLatestMessage error = " + errorToStr(error));
                }
            )
            .addToDisposableBag(this);
    }

    private _setupChatUserInfo(detail: IMConversationDetail): boolean {
        detail.groupParticipants?.forEach((item) => {
            const patient = item.participantType === UserType.patient;
            let user = this.innerState.chatUserMap.get(item.participantId!);
            if (!user) {
                user = new ChatUser({});
                this.innerState.chatUserMap.set(item.participantId!, user);
            }
            user.uid = item.participantId!;
            user.name = item.name ?? item.wxNickName ?? "";
            user.avatar = item.headImgUrl;
            user.avatarPlaceHolder = patient ? "user_avatar_male" : "avatar_doctor";
            user.otherSide = patient;
        });

        let hasUpdate = true;
        this.innerState.messages.forEach((message) => {
            if (message.user instanceof _UnknownChatUser) {
                const user = this.innerState.chatUserMap.get(message.user.employeeId);
                if (user) {
                    message.user = user;
                    hasUpdate = true;
                }
            }
        });

        return hasUpdate;
    }

    private _refreshChatUserInfo(): void {
        //判断是否已经发送相应的请求，并记录，防止重复请求
        const conversionId = this.innerState.currentConsultationId;
        if (this._hasFiredGetConversationDetail.has(conversionId)) return;
        this._hasFiredGetConversationDetail.add(conversionId);

        fromPromise(ImAgent.getConversationDetail(conversionId))
            .subscribe((detail) => {
                const hasUpdate = this._setupChatUserInfo(detail);
                if (hasUpdate) this.update();

                if (detail) imCacheManager.saveConversionDetail(detail);
            })
            .addToDisposableBag(this);
    }

    _toChatMessage(msg: IMMsg): ChatMessage | undefined {
        let message: ChatMessage | undefined;
        const patient = msg.fromUserId === this._peerChatUser.uid;
        const user =
            this.innerState.chatUserMap.get(msg.fromUserId!) ??
            new _UnknownChatUser(msg.fromUserId!, patient, patient ? "user_avatar_male" : "avatar_doctor");

        const msgType = msg.msgType;
        message = this._msgConverter?.(msg); //优先使用外部设置的消息转换函数，处理一些自定义消息卡片
        if (message) {
        } else if (msgType == IMMsgType.text) {
            message = new ChatMessage({
                text: msg.body?.text ?? "",
            });
        } else if (msgType == IMMsgType.image) {
            const imageBody: IMMsgImageBody = msg.body;
            message = new ChatMessage({
                text: "",
                image: imageBody.imageUrl,
                imageSize: { width: imageBody.width, height: imageBody.height },
                createdAt: msg.created,
                id: msg.sequenceId,
                user: user,
            });
        } else if (msgType == IMMsgType.audio) {
            const audioBody: IMMsgAudioBody = msg.body;
            message = new ChatMessage({
                audio: audioBody.audioUrl,
                duration: audioBody.duration,
            });
        } else if (msgType == IMMsgType.systemText) {
            const body: IMMsgSystemTextBody = msg.body;
            message = new SystemTextMessage(body.text!);
        } else if (msgType == IMMsgType.systemCustomText) {
            const body: IMMsgSystemCustomTextBody = msg.body;
            message = JsonMapper.deserialize(SystemCustomTextMessage, body);
        } else if (msgType == IMMsgType.treatmentReport) {
            message = new TreatmentMessage({});
        } else if (msgType == IMMsgType.projectMessage) {
            message = new RegProjectMessage({});
        } else if (msgType == IMMsgType.doctorTimeMessage) {
            message = new DoctorTimeMessage({});
        } else if (msgType == IMMsgType.regMessage) {
            message = new RegMessage({});
        } else if (msgType == IMMsgType.regRecordMessage) {
            message = new RegRecordMessage({});
        } else if (msgType == IMMsgType.regCardRequestMessage) {
            message = new RegCardRequestMessage({});
        } else if (msgType == IMMsgType.regCardSuccessMessage) {
            message = new RegCardSuccessMessage({});
        } else if (msgType == IMMsgType.regCardCancelMessage) {
            message = new RegCardCancelMessage({});
        } else if (msgType == IMMsgType.regDoctorMessage) {
            message = new RegDoctorMessage({});
        } else {
            LogUtils.e("PatientConversationPageBloc._toChatMessage unknownMessage = " + JSON.stringify(msg));
        }

        if (message) {
            message.obj = msg;
            message.id = msg.id ?? msg.sequenceId;
            message.user = user;
            message.createdAt = msg.created;
            message.status = msg.status;
        }

        return message;
    }

    private emitState(state: State): void {
        this.dispatch(new _EventToState(state));
    }

    //重新加载数据
    public requestReloadData(): void {
        this._loadDataTrigger.next(123);
    }

    public requestSendMessage(message: ChatMessage): void {
        LogUtils.d("requestSendMessage");
        this.dispatch(new _EventSendMessage(message));
    }

    ///加载更多消息
    public requestLoadMore(): void {
        LogUtils.d("requestLoadMore");
        if (!this.innerState.hasMoreMessage || this.innerState.loadingMsg) return;
        this._loadMoreMessage.next(0);
    }

    ///加载上一次咨询会话消息
    public requestPreConversionMgs(): void {
        if (this.innerState.hasMoreMessage) this._loadMoreMessage.next(0);

        this.dispatch(new _EventRequestPreConversionMsg());
    }

    private async *_mapEventRevokeMessage(event: _EventRevokeMessage): AsyncGenerator<State> {
        const message = event.message.obj as IMMsg;
        const { id, fromUserId, conversationId, sequenceId } = message;
        imService.revokeMessage(conversationId ?? this._conversationId, fromUserId ?? "", id ?? "", sequenceId);
    }

    dispose(): void {
        super.dispose();
        this._urlHandler && urlDispatcher.removeListener(this._urlHandler);

        this.innerState.messages.forEach((item23) => item23.dispose());
    }
}

export { ConversationChatViewBloc, State };
