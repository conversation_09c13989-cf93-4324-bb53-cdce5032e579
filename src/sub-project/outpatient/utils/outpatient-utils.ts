/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/4/29
 */
import {
    AntibioticEnum,
    ChineseMedicineSpecType,
    GoodsInfo,
    GoodsType,
    GoodsTypeId,
    MedicalRecord,
    SurgeryRequest,
} from "../../base-business/data/beans";
import { MedicineUsageInput } from "../medicine-add-page/medicine-add-page";
import {
    OutpatientInvoiceDetail,
    PrescriptionChineseForm,
    PrescriptionExternalForm,
    PrescriptionFormItem,
    PrescriptionInfusionForm,
    PrescriptionProductForm,
    PrescriptionWesternForm,
} from "../data/outpatient-beans";
import { ABCUtils } from "../../base-ui/utils/utils";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { UUIDGen } from "../../common-base-module/utils";
import _, { isNil } from "lodash";
import { GoodsAgent, SearchGoodsBySpecItem } from "../../data/goods/goods-agent";
import { LogUtils } from "../../common-base-module/log";
import { OutpatientConst } from "../data/outpatient-const";
import { ChargeInvoiceDetailData, ChargeSourceFormType, ChargeStatus } from "../../charge/data/charge-beans";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { MedicineUsage } from "../medicine-add-page/medicine-add-page-bean";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { Subject } from "rxjs";
import { ToothNos } from "../../data/tooth-bean";
import { PrescriptionTemplate } from "../data/prescription-template-bean";
import { userCenter } from "../../user-center";
import { PharmacyListConfig } from "../../inventory/data/inventory-bean";

export class OutpatientUtils {
    static asyncOutpatientMedicineRecordToothStatus: Map<number, boolean> = new Map<number, boolean>();
    /**
     * 同步牙位事件处理
     */
    static asyncOutpatientMedicineRecordTooth = new Subject<{ tooth: ToothNos; index: number; status: boolean }>();

    /**
     * 同步添加方药事件处理
     */
    static asyncOutpatientMedicineAddPrescription = new Subject<{ prescriptionTemplate: PrescriptionTemplate }>();

    static canEditWithChargeStatus(chargeStatus?: number): boolean {
        return chargeStatus == undefined || chargeStatus == ChargeStatus.unCharged;
    }

    static fillItemFromPrescription(formItem: PrescriptionFormItem): PrescriptionFormItem {
        formItem.inputUsages = JsonMapper.deserialize(MedicineUsage, {
            unit: formItem.unit,
            unitCount: formItem.unitCount,
            dosage: {
                dosageUnit: formItem.dosageUnit,
                count: Number(formItem.dosage ?? 0),
            },
            ast: formItem.ast,
            usage: { name: formItem.usage },
            freq: { en: formItem.freq },
            days: formItem.days,
            specialRequirement: { name: formItem.specialRequirement ?? "", id: -1 },
        });
        return formItem;
    }

    static fillPrescriptionFromItem(formItem: PrescriptionFormItem, medicine: GoodsInfo, usage?: MedicineUsage): PrescriptionFormItem {
        formItem.goodsId = medicine.id;
        formItem.productId = medicine.id;
        formItem.type = medicine.type;
        formItem.subType = medicine.subType;
        formItem.name = medicine.displayName;
        if (medicine.isChineseMedicine) {
            formItem.medicineCadn = medicine.displayName ?? medicine.medicineCadn;
        } else {
            formItem.medicineCadn = medicine._medicineCadn ?? medicine.medicineCadn;
        }
        formItem.productInfo = medicine;
        formItem.inputUsages = usage;
        formItem.unit = usage?.unit;
        formItem.unitCount = (usage?.unitCount ?? formItem.unitCount ?? 0) * (usage?.dosage?.externalUnitCount ?? 1);
        formItem.unitPrice = (function () {
            if (!isNil(usage?.unitPrice)) return usage?.unitPrice;
            if (!!usage?.unit) return medicine.unitPriceWithUnit(usage.unit);
            return undefined;
        })();
        formItem.useDismounting = usage?.unit ? (medicine.useDismounting(usage.unit) ? 1 : 0) : undefined;
        formItem.dosageUnit = usage?.dosage?.dosageUnit;
        formItem.dosage = usage?.dosage?.count?.toString();
        formItem.ast = usage?.ast;
        formItem.astResult = usage?.astResult;
        formItem.usage = usage?.usage?.name;
        formItem.freq =
            medicine.isTreatment || medicine.isPhysiotherapy ? usage?.supportInputDays?.freq : usage?.freq?.en ?? usage?.freq?.name;
        formItem.days = medicine.isTreatment || medicine.isPhysiotherapy ? usage?.supportInputDays?.days : usage?.days;
        formItem.specialRequirement = usage?.specialRequirement?.name;
        formItem.acupoints = usage?.acuPoints;
        formItem.remark = medicine.remark ?? undefined;
        formItem.externalGoodsItems = usage?.externalGoodsItems ?? undefined;
        formItem.doctorId = usage?.doctorId;
        formItem.doctorName = usage?.doctorName;
        formItem.departmentId = usage?.departmentId;
        formItem.departmentName = usage?.departmentName;
        formItem.nurseId = usage?.nurseId;
        formItem.nurseName = usage?.nurseName;
        formItem.pharmacyType = usage?.pharmacyInfo?.type;
        formItem.pharmacyNo = usage?.pharmacyInfo?.no;
        formItem.chargeType = usage?.selfProvidedStatus;
        formItem.toothNos = usage?.toothNos;
        formItem.dailyDosage = usage?.supportInputDays?.dailyDosage;
        formItem.externalUnitCount = usage?.dosage?.externalUnitCount;
        formItem.executeStatus = medicine.__executeStatus;
        formItem.executeStatusName = medicine.__executeStatusName;
        formItem.examinationResult = medicine.__examinationResult;
        formItem.needExecutive = medicine.needExecutive;
        formItem.batchInfos = medicine._batchInfos;
        formItem.expectedTotalPriceRatio = usage?.expectedTotalPriceRatio;
        formItem.expectedTotalPrice = usage?.expectedTotalPrice;
        formItem.expectedUnitPrice = usage?.expectedUnitPrice;
        formItem.totalPrice = usage?.totalPrice;
        formItem.totalPriceRatio = usage?.totalPriceRatio;
        formItem.fractionPrice = usage?.fractionPrice;
        formItem.children = medicine.children;
        formItem.surgeryDetail = medicine.surgeryDetail;
        formItem.composeChildren = medicine.composeChildren;

        return formItem;
    }

    static createProductFormWithSourceFormType(options: {
        sourceFormType: number;
        medicines: Array<GoodsInfo>;
        inputUsages: AbcMap<GoodsInfo, MedicineUsage>;
    }): PrescriptionProductForm {
        const { sourceFormType, medicines, inputUsages } = options;
        const oldForm = JsonMapper.deserialize(PrescriptionProductForm, {
            sourceFormType: sourceFormType,
            keyId: UUIDGen.generate(),
        });
        oldForm.productFormItems = oldForm.productFormItems ?? [];

        const newProductFormItems = medicines.map((goods) => {
            let formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                keyId: UUIDGen.generate(),
            });
            const usage = inputUsages.get(goods);
            let _remark = usage?.specialRequirement?.name;
            const acupunturePointsStr = usage?.acuPoints?.map((item) => item.name).join();
            _remark = !!acupunturePointsStr ? `${!!_remark ? `${_remark};` : ""}[穴位]${acupunturePointsStr};` : _remark;
            goods.remark = !!goods.remark ? goods.remark : _remark;
            formItem = OutpatientUtils.fillPrescriptionFromItem(formItem, goods, inputUsages.get(goods));
            return formItem;
        });

        //重新添加未收费项
        oldForm.productFormItems = oldForm.productFormItems.concat(newProductFormItems);

        return oldForm;
    }

    //生成诊疗项目处方
    static createPrescriptionProductForm(input: MedicineUsageInput): PrescriptionProductForm[] | null {
        if (input === null || ABCUtils.isEmpty(input.medicines) || !input.medicines?.[0].selectedMedicines.length) return null;
        const group = input.medicines![0];
        const examination: GoodsInfo[] = [];
        const treatment: GoodsInfo[] = [];
        const goodsMedicines: GoodsInfo[] = [];
        const materialMedicines: GoodsInfo[] = [];
        const packages: GoodsInfo[] = [];
        const otherFees: GoodsInfo[] = [];
        const nurseFees: GoodsInfo[] = [];
        const surgeryFees: GoodsInfo[] = [];
        group?.selectedMedicines?.forEach((item: GoodsInfo) => {
            if (item.type == GoodsType.examination) examination.push(item);
            if (item.type == GoodsType.treatment) treatment.push(item);
            if (item.type == GoodsType.package) packages.push(item);
            if (item.isGoods) goodsMedicines.push(item);
            if (item.isMedicalMaterial) materialMedicines.push(item);
            if (item.isOtherFee) otherFees.push(item);
            if (item.isNurseFee) nurseFees.push(item);
            if (item.isSurgery) surgeryFees.push(item);
        });
        const productFormsList: PrescriptionProductForm[] = [];
        const _examination = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.examination,
            medicines: examination,
            inputUsages: group.inputUsages,
        });
        const _treatment = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.treatment,
            medicines: treatment,
            inputUsages: group.inputUsages,
        });
        const _packages = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.package,
            medicines: packages,
            inputUsages: group.inputUsages,
        });
        const _goods = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.goods,
            medicines: goodsMedicines,
            inputUsages: group.inputUsages,
        });
        const _material = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.material,
            medicines: materialMedicines,
            inputUsages: group.inputUsages,
        });
        const _otherFee = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.otherFee,
            medicines: otherFees,
            inputUsages: group.inputUsages,
        });
        const _nurseFee = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.nurseProductFee,
            medicines: nurseFees,
            inputUsages: group.inputUsages,
        });
        const _surgeryFee = OutpatientUtils.createProductFormWithSourceFormType({
            sourceFormType: ChargeSourceFormType.surgeryFee,
            medicines: surgeryFees,
            inputUsages: group.inputUsages,
        });
        if (!!examination.length) {
            productFormsList.push(_examination);
        }
        if (!!treatment.length) {
            productFormsList.push(_treatment);
        }
        if (!!packages.length) {
            productFormsList.push(_packages);
        }
        if (!!goodsMedicines.length) {
            productFormsList.push(_goods);
        }
        if (!!materialMedicines.length) {
            productFormsList.push(_material);
        }
        if (!!otherFees.length) {
            productFormsList.push(_otherFee);
        }
        if (!!nurseFees.length) {
            productFormsList.push(_nurseFee);
        }
        if (!!surgeryFees.length) {
            productFormsList.push(_surgeryFee);
        }
        return productFormsList;
    }

    //生成输液处方
    static createPrescriptionInfusionForm(input: MedicineUsageInput, oldForm?: PrescriptionInfusionForm): PrescriptionInfusionForm | null {
        if (input == null || ABCUtils.isEmpty(input.medicines)) return null;
        const form = JsonMapper.deserialize(PrescriptionInfusionForm, {
            ...oldForm,
            prescriptionFormItems: [],
            keyId: UUIDGen.generate(),
        });

        let groupId = 1;
        input.medicines?.map((group) => {
            const formItems = group.selectedMedicines.map((medicine) => {
                const oldFormItem = oldForm?.prescriptionFormItems?.find(
                    (pItem) => pItem.goodsInfo.id == medicine.id && pItem.groupId == groupId
                );
                const formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    ...oldFormItem,
                    groupId: oldFormItem?.groupId ?? groupId,
                    keyId: medicine.keyId ?? UUIDGen.generate(),
                });
                group.infusionPrescriptionUsage?.fillPrescriptionInfusionFormItem(
                    this.fillPrescriptionFromItem(formItem, medicine, group.inputUsages.get(medicine)!)
                );
                return formItem;
            });
            form.prescriptionFormItems = form.prescriptionFormItems!.concat(formItems);

            ++groupId;
        });

        form.psychotropicNarcoticType = input.psychotropicNarcoticType;
        form.pharmacyType = input.pharmacyInfo?.type;
        form.pharmacyNo = input.pharmacyInfo?.no;
        form.pharmacyName = input.pharmacyInfo?.name;
        return form;
    }

    //生成成药处方
    static createPrescriptionWesternForm(input: MedicineUsageInput, oldForm?: PrescriptionWesternForm): PrescriptionWesternForm | null {
        if (input == null || ABCUtils.isEmpty(input.medicines)) return null;
        const form = JsonMapper.deserialize(PrescriptionWesternForm, {
            ...oldForm,
            prescriptionFormItems: [],
            keyId: oldForm?.keyId ?? UUIDGen.generate(),
        });

        // let groupId = 1;
        input.medicines?.map((group) => {
            const formItems = group.selectedMedicines.map((medicine) => {
                // PrescriptionFormItem
                const oldFormItem = oldForm?.prescriptionFormItems?.find((pItem) => pItem.goodsInfo.id == medicine.id);
                const formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    ...oldFormItem,
                    groupId: group.groupId == ChargeUtils.maxGroupId ? undefined : group.groupId,
                    keyId: medicine.keyId ?? UUIDGen.generate(),
                });
                return this.fillPrescriptionFromItem(formItem, medicine, group.inputUsages.get(medicine)!);
            });

            form.prescriptionFormItems = form.prescriptionFormItems?.concat(formItems);

            // ++groupId;
        });

        //精麻类型
        form.psychotropicNarcoticType = input.psychotropicNarcoticType;

        form.pharmacyType = input.pharmacyInfo?.type;
        form.pharmacyNo = input.pharmacyInfo?.no;
        form.pharmacyName = input.pharmacyInfo?.name;

        return form;
    }

    //生成中药处方
    static createPrescriptionChineseForm(input: MedicineUsageInput, form?: PrescriptionChineseForm): PrescriptionChineseForm | undefined {
        if (input == null || ABCUtils.isEmpty(input.medicines)) return undefined;

        form =
            form ??
            JsonMapper.deserialize(PrescriptionChineseForm, {
                keyId: UUIDGen.generate(),
                prescriptionFormItems: [],
            });
        const group = input.medicines![0];
        const prescriptionFormItems = [];
        group.chinesePrescriptionUsage?.fillPrescriptionChineseForm(form!);
        if (
            form.vendorId != group.vendor?.vendorId ||
            form.vendorName != group.vendor?.vendorName ||
            form.pharmacyName != group.vendor?.pharmacyName
        ) {
            form.vendor = group.vendor;
            form.vendorId = group.vendor?.vendorId;
            form.vendorName = group.vendor?.vendorName;
            form.pharmacyName = group.vendor?.pharmacyName;
            form.processPrice = group.vendor?.processPrice;
            form.psychotropicNarcoticType = input.psychotropicNarcoticType; // 赋值处方类型到(空中药房)
        }
        form.usageScopeId = group.chinesePrescriptionUsage?.usageScopeId;
        form.medicineStateScopeId = group.chinesePrescriptionUsage?.medicineStateScopeId;
        for (const medicine of group.selectedMedicines) {
            let formItem =
                form.prescriptionFormItems?.find((it) => it.displayName == medicine.displayName) ??
                JsonMapper.deserialize(PrescriptionFormItem, {
                    keyId: medicine.keyId ?? UUIDGen.generate(),
                });
            formItem = this.fillPrescriptionFromItem(formItem, medicine, group?.inputUsages?.get(medicine) ?? new MedicineUsage());

            prescriptionFormItems?.push(formItem);
        }
        form!.prescriptionFormItems = prescriptionFormItems;

        //赋值加工信息(本地药房)
        const process = input.process;
        if (process && (!group?.vendor || !group.vendor?.pharmacyType)) {
            form.isDecoction = process.isDecoction;
            form.usageType = process.usageType;
            form.usageSubType = process.usageSubType;
            form.processBagUnitCount = process.processBagUnitCount;
            form.contactMobile = process.contactMobile;
            form.totalProcessCount = process.totalProcessCount;
            form.processRemark = process.processRemark;
            form.psychotropicNarcoticType = input.psychotropicNarcoticType;
        }

        form.deliveryInfo = group.chinesePrescriptionUsage?.prescriptionFormDelivery;

        // 创建处方时，清除后台返回的价格信息
        form.medicinePriceInfo = undefined;

        return form!;
    }

    //生成外治处方
    static createPrescriptionExternalForm(input: MedicineUsageInput): PrescriptionExternalForm | undefined {
        if (input == null || ABCUtils.isEmpty(input.medicines)) return undefined;
        const form = JsonMapper.deserialize(PrescriptionExternalForm, {
            prescriptionFormItems: [],
            keyId: UUIDGen.generate(),
        });
        input.medicines?.map((group) => {
            const formItems = group.selectedMedicines.map((medicine) => {
                const formItem = JsonMapper.deserialize(PrescriptionFormItem, {
                    groupId: group.groupId,
                    keyId: medicine.keyId ?? UUIDGen.generate(),
                });
                return this.fillPrescriptionFromItem(formItem, medicine, group.inputUsages.get(medicine)!);
            });

            form.prescriptionFormItems = form.prescriptionFormItems?.concat(formItems);
            form.usageType = group.usageType;
            form.usageSubType = group.usageSubType;
            form.specification = group.specification;
            form.psychotropicNarcoticType = input.psychotropicNarcoticType;
        });

        return form;
    }

    static tripChargedItemForOutpatientPriceCalculate(detailData: OutpatientInvoiceDetail): OutpatientInvoiceDetail {
        detailData = JsonMapper.deserialize(OutpatientInvoiceDetail, detailData);
        const finalDetailData = JsonMapper.deserialize(OutpatientInvoiceDetail, {
            patient: detailData.patient,
            registrationFee: detailData.registrationFee ?? 0,
            expectedTotalPrice: detailData.expectedTotalPrice,
            sourceTotalPrice: detailData.sourceTotalPrice,
            isTotalPriceChanged: detailData.isTotalPriceChanged,
            totalPrice: detailData.totalPrice,
            adjustmentFee: detailData.adjustmentFee,
            registrationFeeStatus: detailData.registrationFeeStatus,
            prescriptionInfusionForms: detailData.prescriptionInfusionForms ?? [],
            prescriptionChineseForms: detailData.prescriptionChineseForms ?? [],
            prescriptionWesternForms: detailData.prescriptionWesternForms ?? [],
            prescriptionExternalForms: detailData.prescriptionExternalForms ?? [],
            isOnline: detailData.isOnline,
            productForms: detailData.productForms ?? [],
            psychotropicNarcoticEmployee: detailData?.psychotropicNarcoticEmployee,
        });

        if (!OutpatientUtils.canEditWithChargeStatus(finalDetailData.registrationFeeStatus)) {
            finalDetailData.registrationFee = 0.0;
        }

        finalDetailData.productForms?.forEach((form: PrescriptionProductForm) => {
            _.remove(form.productFormItems!, (item) => !OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        });

        finalDetailData.prescriptionWesternForms?.forEach((form: PrescriptionWesternForm) => {
            _.remove(
                form.prescriptionFormItems!,
                (item) => !OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!) || item.isSelfProvided
            );
        });

        finalDetailData.prescriptionChineseForms?.forEach((form: PrescriptionChineseForm) => {
            delete form.vendor;
            _.remove(
                form.prescriptionFormItems!,
                (item) => !OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!) || item.isSelfProvided
            );
        });

        finalDetailData.prescriptionInfusionForms?.forEach((form: PrescriptionInfusionForm) => {
            _.remove(
                form.prescriptionFormItems!,
                (item) => !OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!) || item.isSelfProvided
            );
        });

        finalDetailData.prescriptionExternalForms?.forEach((form: PrescriptionExternalForm) => {
            _.remove(form.prescriptionFormItems!, (item) => !OutpatientUtils.canEditWithChargeStatus(item.chargeStatus!));
        });

        return finalDetailData;
    }

    static clearPrescriptionFlags(
        forms: Array<PrescriptionProductForm | PrescriptionChineseForm | PrescriptionInfusionForm | PrescriptionWesternForm>
    ): void {
        if (ABCUtils.isEmpty(forms)) return;

        for (const form of forms) {
            if (form instanceof PrescriptionProductForm) {
                //        form.contactMobile = undefined;
                form.chargeStatus = undefined;
                form.chargeStatusName = undefined;
                form.totalPrice = undefined;
                form.sort = undefined;
                form.id = undefined;
                //        form.qrid = undefined;
                form.patientOrderId = undefined;
                form.outpatientSheetId = undefined;
                form.patientId = undefined;
                form.clinicId = undefined;
                form.departmentId = undefined;
                form.doctorId = undefined;
                form.created = undefined;
                form.created = undefined;
                form.keyId = undefined;
            } else {
                form.contactMobile = undefined;
                form!.chargeStatus = undefined;
                form.chargeStatusName = undefined;
                if (form instanceof PrescriptionChineseForm && form.pharmacyType == PharmacyType.air) {
                } else {
                    form.totalPrice = undefined;
                }
                form.sort = undefined;
                form.id = undefined;
                form.qrid = undefined;
                form.patientOrderId = undefined;
                form.outpatientSheetId = undefined;
                form.patientId = undefined;
                form.clinicId = undefined;
                form.departmentId = undefined;
                form.doctorId = undefined;
                form.created = undefined;
                form.created = undefined;
                form.keyId = undefined;
            }
            if (form instanceof PrescriptionChineseForm) {
                //中药处方(本地药房)中的代加工信息清除，空中药房不清除
                if (!form.pharmacyType) {
                    form.isDecoction = undefined;
                    form.usageType = undefined;
                    form.usageSubType = undefined;
                    form.processBagUnitCount = undefined;
                }
            }

            let formItems: any[] | undefined = [];

            if (form instanceof PrescriptionProductForm) {
                formItems = form.productFormItems;
            } else {
                formItems = form.prescriptionFormItems;
            }

            if (ABCUtils.isEmpty(formItems)) break;

            for (const formItem of formItems!) {
                formItem.chargeStatus = undefined;
                // 外治处方的排序遵循历史处方中的排序
                if (!(form instanceof PrescriptionExternalForm)) {
                    formItem.sort = undefined;
                }
                formItem.totalPrice = undefined;
                formItem.expectedUnitPrice = undefined;
                formItem.expectedTotalPrice = undefined;
                formItem.keyId = undefined;
                formItem.astResult = undefined;
                formItem.id = undefined;
                formItem.patientOrderId = undefined;
                formItem.clinicId = undefined;
                formItem.outpatientSheetId = undefined;
                formItem.domainMedicineId = undefined;
                formItem.examinationResult = undefined;
                if (formItem.groupId == ChargeUtils.maxGroupId) {
                    formItem.groupId = undefined;
                }
            }
        }
    }

    /**
     * 获取各个处方当前选中的药房信息，如果没有，则设置为处方对应的默认药房
     * @param params
     */
    static initPrescriptionPharmacyInfo(params: {
        initGoodsTypeId?: number;
        pharmacyNo?: number;
        departmentId?: string;
    }): PharmacyListConfig | undefined {
        const { initGoodsTypeId, pharmacyNo, departmentId } = params;
        const pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        const pharmacyInfo = pharmacyInfoConfig?.pharmacyList?.filter((pharmacy) => pharmacy.no == pharmacyNo);
        const pharmacyList = !!pharmacyInfo?.length
            ? pharmacyInfo
            : pharmacyInfoConfig?.filterPharmacyListWithGoodsType(initGoodsTypeId, undefined, departmentId)?.filter((t) => !isNil(t?.no));
        const selectPharmacyInfo = !!pharmacyList?.length
            ? pharmacyList[0]
            : pharmacyInfoConfig?.pharmacyList?.find((k) => k.no == PharmacyType.normal);
        return selectPharmacyInfo;
    }

    /// 刷新 form中各项目的goods info
    static async refreshGoodsInfo(forms: Array<any>, clinicId?: string, isNeedReturnVal?: boolean, no?: number): Promise<any[] | void> {
        const pharmacyNo = no;
        let formItems: any[] = [];
        forms.forEach((form) => {
            if (form instanceof PrescriptionProductForm) {
                if (ABCUtils.isNotEmpty(form.productFormItems!)) formItems = formItems?.concat(form.productFormItems);
            } else if (ABCUtils.isNotEmpty(form.prescriptionFormItems) && !(form instanceof PrescriptionChineseForm))
                formItems = formItems.concat(form.prescriptionFormItems);
        });

        const goodsIds = new Set<string>();
        formItems?.forEach((formItem) => {
            const formItemId = formItem.goodsInfo?.id;
            if (formItemId) {
                goodsIds.add(formItemId);
            }
        });

        if (goodsIds.size) {
            let newGoodsInfo: Map<string, GoodsInfo>;

            try {
                const goodsInfo = await GoodsAgent.searchGoodsByIds({
                    goodsIds: [...goodsIds],
                    pharmacyNo: 0,
                    clinicId,
                });

                newGoodsInfo = new Map();
                goodsInfo?.forEach((item) => {
                    newGoodsInfo.set(item.id!, item);
                });
            } catch (e) {
                LogUtils.e("_mapEventPreviewTemplate error = " + JSON.stringify(e));
                return e;
            }

            if (newGoodsInfo != null) {
                formItems.forEach((formItem) => {
                    const formItemId = formItem.goodsInfo?.id;
                    if (formItemId) {
                        if (!newGoodsInfo.has(formItemId)) {
                            formItem.goodsInfo!.id = undefined;
                            formItem.goodsId = undefined;
                            formItem.unitPrice = 0;
                            formItem.piecePrice = 0;
                            if (formItem.productInfo) {
                                formItem.productInfo.id = undefined;
                                formItem.productInfo.packagePrice = 0;
                                formItem.productInfo.piecePrice = 0;
                            }
                            return;
                        }

                        formItem.productInfo = newGoodsInfo.get(formItemId);
                        const goodsInfo = (formItem.productInfo = newGoodsInfo.get(formItemId));
                        if (goodsInfo != null) {
                            //如果剂量单位有变化，清掉之前模板里的信息
                            if (
                                goodsInfo.usageUnits.indexOf(formItem.dosageUnit) == -1 &&
                                formItem.dosageUnit != OutpatientConst.dosageUsageProperty
                            ) {
                                formItem.dosageUnit = null;
                            }
                            if (
                                (!(
                                    (formItem.productInfo.isTreatment ||
                                        formItem.productInfo.isPhysiotherapy ||
                                        formItem.productInfo.isTreatmentOther) &&
                                    formItem.unit == "贴"
                                ) &&
                                    formItem.productInfo.sellUnits.indexOf(formItem.unit) == -1) ||
                                (formItem.useDismounting == 1 && !formItem.productInfo.canDismounting)
                            ) {
                                formItem.useDismounting = 0;
                                formItem.unit = null;
                                formItem.unitPrice = null;
                            } else {
                                formItem.unitPrice = formItem.productInfo.unitPriceWithUnit(formItem.unit);
                            }
                        }
                    }
                });
            }
        }

        // //searchChineseMedicineGoodsBySpec接口为1.5新加，需要判断
        // if (!isServerApiMatch150()) return;

        //对于没有药品信息的中药单独走searchChineseMedicineGoodsBySpec接口查询
        for (const form of forms) {
            if (form instanceof PrescriptionChineseForm) {
                // const spec = !!form.specification ? form.specification : "中药饮片";
                const cadns = new Array<{
                    medicineCadn: string;
                    goodsId?: string;
                    cMSpec?: string;
                    manufacturer?: string;
                    keyId?: string;
                    pieceUnit?: string;
                    extendSpec?: string;
                }>();
                form.prescriptionFormItems?.forEach((item) => {
                    const cMSpec = item.cMSpec ?? item.productInfo?.cMSpec ?? form?.specification;
                    item.keyId = item.keyId ?? UUIDGen.generate();
                    cadns.push({
                        medicineCadn: item.displayName!,
                        goodsId: item.goodsId ?? "",
                        cMSpec: !!cMSpec ? cMSpec : "中药饮片",
                        manufacturer: item.manufacturer ?? item.productInfo?.manufacturer ?? "",
                        keyId: item.keyId,
                        pieceUnit: item.pieceUnit ?? item.productInfo?.pieceUnit ?? "",
                        extendSpec: item.productInfo?.extendSpec ?? "",
                    });
                });

                if (ABCUtils.isNotEmpty(cadns)) {
                    let infoLists = new Array<SearchGoodsBySpecItem>();
                    // 解决：复制历史处方时查询的pharmacyNo不是默认初始的pharmacyNo，导致库存信息读取错误问题
                    const initGoodsTypeId =
                        form?.specification == ChineseMedicineSpecType.fullNames()[1]
                            ? GoodsTypeId.medicineChineseGranule
                            : GoodsTypeId.medicineChinesePiece;
                    const initPharmacyNo = this.initPrescriptionPharmacyInfo({ initGoodsTypeId })?.no;
                    try {
                        infoLists = await GoodsAgent.searchChineseMedicineGoodsBySpec({
                            cMSpec: "",
                            list: cadns,
                            pharmacyNo: pharmacyNo ?? initPharmacyNo ?? 0,
                        });
                    } catch (e) {}

                    const infoMap = new Map<string, GoodsInfo>();
                    infoLists.forEach((item) => {
                        const _key = item.keyId ?? item.goods?.displayName ?? "";
                        if (!infoMap.get(_key)) {
                            infoMap.set(_key, item.goods!);
                        }
                    });

                    form.prescriptionFormItems?.forEach((item) => {
                        const newInfo = infoMap.get(item.keyId!);
                        if (newInfo != null) {
                            newInfo.pieceUnit = newInfo?.pieceUnit ?? item?.pieceUnit;
                            const {
                                cMSpec,
                                id,
                                manufacturer,
                                materialSpec,
                                medicineCadn,
                                packageCostPrice,
                                packagePrice,
                                packageUnit,
                                pieceNum,
                                piecePrice,
                                pieceUnit,
                                stockPackageCount,
                                stockPieceCount,
                                type,
                                subType,
                                extendSpec,
                            } = newInfo;
                            //将goodsinfo的价格信息赋到formItem上
                            const priceInfo = {
                                cMSpec,
                                goodsId: id,
                                manufacturer,
                                materialSpec,
                                medicineCadn,
                                packageCostPrice,
                                packagePrice,
                                packageUnit,
                                pieceNum,
                                piecePrice,
                                pieceUnit,
                                stockPackageCount,
                                stockPieceCount,
                                type,
                                subType,
                                unitPrice: piecePrice,
                                extendSpec,
                            };
                            if (newInfo.pharmacyGoodsStockList?.every((i) => i.pharmacyNo != item.pharmacyNo)) {
                                Object.assign(priceInfo, {
                                    ...newInfo.pharmacyGoodsStockList[0],
                                });
                            }
                            _.assign(item, priceInfo);
                            item.productInfo = newInfo;
                        }
                    });
                    form.pharmacyNo = form.prescriptionFormItems?.[0].pharmacyNo ?? form.pharmacyNo;
                    form.pharmacyName = form.prescriptionFormItems?.[0].pharmacyName ?? form.pharmacyName;
                    form.pharmacyType = form.prescriptionFormItems?.[0].pharmacyType ?? form.pharmacyType;
                }
            }
        }
        if (isNeedReturnVal) return forms;
    }

    /// 当前门诊单中是否有开具处方（包含商品之类)
    static hasPrescription(outpatientSheet: OutpatientInvoiceDetail): boolean {
        return (
            ABCUtils.isNotEmpty(outpatientSheet.productForms ?? []) ||
            ABCUtils.isNotEmpty(outpatientSheet.prescriptionWesternForms ?? []) ||
            ABCUtils.isNotEmpty(outpatientSheet.prescriptionChineseForms ?? []) ||
            ABCUtils.isNotEmpty(outpatientSheet.prescriptionInfusionForms ?? []) ||
            ABCUtils.isNotEmpty(outpatientSheet.prescriptionExternalForms ?? [])
        );
    }

    static copyPriceAttrToPrescription(fromForms: any[], toForms: any[], shouldNotCopyExpectedPrice?: boolean): void {
        if (ABCUtils.isEmpty(fromForms) || ABCUtils.isEmpty(toForms)) return;

        for (const form of fromForms) {
            const originalForm = toForms.find((item) => item.keyId == form.keyId);
            if (originalForm) {
                Object.assign(originalForm, {
                    totalPrice: form.totalPrice,
                    isTotalPriceChanged: form.isTotalPriceChanged,
                    sheetFlatPrice: form.sheetFlatPrice,
                    sourceTotalPrice: form.sourceTotalPrice,
                    expectedTotalPrice: form.expectedTotalPrice,
                });
            }
            let fromFormItems: Array<PrescriptionFormItem>;
            let toFormItems: Array<PrescriptionFormItem>;

            const { eqConversionRule, pharmacyType } = form;
            const isLocalPharmacy = pharmacyType === PharmacyType.normal
            const isChinesePiece = eqConversionRule === ChineseMedicineSpecType.chinesePiece;

            if (form instanceof PrescriptionProductForm) {
                fromFormItems = form.productFormItems!;
                toFormItems = originalForm?.productFormItems;
            } else {
                if (form.pharmacyType == PharmacyType.air) {
                    originalForm.expectedTotalPrice = form.expectedTotalPrice;
                }
                fromFormItems = form.prescriptionFormItems;
                toFormItems = originalForm.prescriptionFormItems;
            }

            for (const formItem of fromFormItems) {
                const originalFormItem = toFormItems.find((item) => item.keyId == formItem.keyId);
                originalFormItem!.totalPrice = formItem.totalPrice;
                originalFormItem!.sourceUnitPrice = formItem.sourceUnitPrice;
                originalFormItem!.sourceTotalPrice = formItem.sourceTotalPrice;
                originalFormItem!.unitPrice = formItem.unitPrice;
                originalFormItem!.formFlatPrice = formItem.formFlatPrice;
                originalFormItem!.sheetFlatPrice = formItem.sheetFlatPrice;
                originalFormItem!.fractionPrice = formItem.fractionPrice;
                originalFormItem!.isUnitPriceChanged = formItem.isUnitPriceChanged;
                originalFormItem!.isTotalPriceChanged = formItem.isTotalPriceChanged;
                originalFormItem!.totalPriceRatio = formItem.totalPriceRatio;

                const { goodsInfo } = formItem;
                const { typeId, pieceUnit } = goodsInfo || {};
                const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;

                if (
                    userCenter.enableEqCoefficient &&
                    isLocalPharmacy &&
                    isChinesePiece &&
                    isMedicineChineseGranule &&
                    (pieceUnit === "g" || pieceUnit === "克")
                ) {
                    originalFormItem!.unitCount = formItem.unitCount;
                }
                if (!shouldNotCopyExpectedPrice) {
                    if (!_.isNil(formItem!.expectedTotalPrice)) {
                        originalFormItem!.expectedTotalPrice = formItem.expectedTotalPrice;
                    }
                    originalFormItem!.expectedUnitPrice = formItem.expectedUnitPrice;
                    originalFormItem!.expectedTotalPriceRatio = formItem.expectedTotalPriceRatio;
                }
            }
        }
    }

    /**
     * 从门诊单里提取治疗方案信息
     * e.g:中药10味，西药3种，治疗5项，理疗1项
     * @param outpatient
     */
    static treatmentOptions(outpatient: OutpatientInvoiceDetail): string {
        const goods: GoodsInfo[] = [];
        [
            ...(outpatient.prescriptionWesternForms ?? []),
            ...(outpatient.prescriptionInfusionForms ?? []),
            ...(outpatient.prescriptionChineseForms ?? []),
            ...(outpatient.prescriptionExternalForms ?? []),
        ].forEach((form) => {
            const formItems = form.prescriptionFormItems;
            formItems?.forEach((item) => {
                goods.push(item.goodsInfo);
            });
        });

        outpatient.productForms?.forEach((form) => {
            const formItems = form.productFormItems;
            formItems?.forEach((item) => {
                goods.push(item.goodsInfo);
            });
        });

        return this.treatmentOptionsFromGoodsInfo(goods);
    }

    static treatmentOptionsFromGoodsInfo(goods: GoodsInfo[]): string {
        let westernCount = 0;
        let chineseCount = 0;
        let treatment = 0;
        let examination = 0;
        let materialGoods = 0;
        let other = 0;

        function visitMedicineItem(goodsInfo: GoodsInfo) {
            if ((goodsInfo.isWesternMedicine ?? false) || (goodsInfo?.isChineseWesternMedicine ?? false)) {
                westernCount++;
            } else if (goodsInfo?.isChineseMedicine ?? false) {
                chineseCount++;
            } else if (goodsInfo.type == GoodsType.treatment) {
                treatment++;
            } else if (goodsInfo.type == GoodsType.examination) {
                examination++;
            } else if (goodsInfo.isGoods || goodsInfo.type == GoodsType.material) {
                materialGoods++;
            } else {
                other++;
            }
        }

        goods.forEach((item) => visitMedicineItem(item));

        let summary = "";
        if (westernCount > 0) {
            summary = `西药${westernCount}种`;
        }

        if (chineseCount > 0) {
            summary += (summary.length ? "，" : "") + `中药${chineseCount}味`;
        }

        if (examination > 0) {
            summary += (summary.length ? "，" : "") + `检查检验${examination}项`;
        }
        if (treatment > 0) {
            summary += (summary.length ? "，" : "") + `治疗理疗${treatment}项`;
        }
        if (materialGoods > 0) {
            summary += (summary.length ? "，" : "") + `材料商品${materialGoods}项`;
        }

        if (other > 0) {
            summary += (summary.length ? "，" : "") + `其它${other}项`;
        }

        return summary;
    }

    static treatmentOptionsFromChargeData(chargeData: ChargeInvoiceDetailData): string {
        const goods: GoodsInfo[] = [];
        chargeData.chargeForms?.forEach((form) => {
            form.chargeFormItems?.forEach((formItem) => {
                if (formItem.goodsInfo) goods.push(formItem.goodsInfo);
            });
        });

        return this.treatmentOptionsFromGoodsInfo(goods);
    }

    ///检查门诊单里是否包含未收费项
    static hasUnChargedItem(detailData: OutpatientInvoiceDetail): boolean {
        if (detailData == null) return false;

        if (OutpatientUtils.canEditWithChargeStatus(detailData.registrationFeeStatus!) && detailData.isOnline != 1) {
            return true;
        }

        if (ABCUtils.isNotEmpty(detailData.productForms ?? [])) {
            for (const form of detailData.productForms!) {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) return true;
            }
        }

        if (ABCUtils.isNotEmpty(detailData.prescriptionWesternForms ?? [])) {
            for (const form of detailData.prescriptionWesternForms!) {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) return true;
            }
        }

        if (ABCUtils.isNotEmpty(detailData.prescriptionChineseForms ?? [])) {
            for (const form of detailData.prescriptionChineseForms!) {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) return true;
            }
        }

        if (ABCUtils.isNotEmpty(detailData.prescriptionInfusionForms ?? [])) {
            for (const form of detailData.prescriptionInfusionForms!) {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) return true;
            }
        }

        if (ABCUtils.isNotEmpty(detailData.prescriptionExternalForms ?? [])) {
            for (const form of detailData.prescriptionExternalForms!) {
                if (OutpatientUtils.canEditWithChargeStatus(form.chargeStatus!)) return true;
            }
        }

        return false;
    }

    static OutpatientPrescriptionFormKey: (keyof Pick<
        OutpatientInvoiceDetail,
        "prescriptionWesternForms" | "prescriptionChineseForms" | "prescriptionInfusionForms" | "prescriptionExternalForms"
    >)[] = ["prescriptionWesternForms", "prescriptionChineseForms", "prescriptionInfusionForms", "prescriptionExternalForms"];

    /**
     * 清空门诊单上所有的议价相关信息
     * @param detail
     * @param isUpdateNegotiator  -- 更新议价人
     */
    static clearOutpatientDetailAllExpectedPrice(
        detail?: OutpatientInvoiceDetail,
        isUpdateNegotiator?: boolean
    ): OutpatientInvoiceDetail | undefined {
        //清空sheet的议价信息
        if (!detail) return;
        detail.expectedTotalPrice = undefined;
        //清空form上的议价信息
        this.OutpatientPrescriptionFormKey.forEach((formKey) => {
            for (const form of detail[formKey] ?? []) {
                form.expectedTotalPrice = undefined;
                for (const item of form.prescriptionFormItems ?? []) {
                    // item.expectedTotalPrice = undefined;
                    // item.expectedUnitPrice = undefined;
                    item.isTotalPriceChanged = 0;
                    item.isUnitPriceChanged = 0;
                    if (!!isUpdateNegotiator)
                        item.unitAdjustmentFeeLastModifiedBy = userCenter.employee?.id ?? userCenter.employee?.employeeId;
                }
            }
        });

        return detail;
    }

    /**
     * 清空处方上的议价信息+还原初始价格
     */
    static clearPrescriptionFormExpected(
        form: PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionExternalForm
    ): void {
        form.expectedTotalPrice = undefined;
        form.isTotalPriceChanged = undefined;
        form.sheetFlatPrice = undefined;
        for (const item of form.prescriptionFormItems ?? []) {
            item.currentUnitPrice = undefined;
            item.expectedTotalPrice = undefined;
            item.expectedUnitPrice = undefined;
            item.fractionPrice = undefined;
            item.formFlatPrice = undefined;
            item.sheetFlatPrice = undefined;
            item.isUnitPriceChanged = 0;
            item.isTotalPriceChanged = 0;

            //还原初始价格
            item.unitPrice = item.sourceUnitPrice ?? item.unitPrice;
            item.totalPrice = item.totalPrice ?? item.totalPrice;
        }
    }

    /**
     * 判断处方是否含有议价信息
     */
    static checkPrescriptionFormHasExpected(
        form: PrescriptionChineseForm | PrescriptionWesternForm | PrescriptionInfusionForm | PrescriptionExternalForm
    ): boolean {
        for (const item of form.prescriptionFormItems ?? []) {
            if (this.checkPrescriptionFormItemHasExpected(item)) {
                return true;
            }
        }
        return !_.isNil(form.expectedTotalPrice) || !!form.isTotalPriceChanged;
    }

    /**
     * 判断项目是否含有议价信息
     */
    static checkPrescriptionFormItemHasExpected(item: PrescriptionFormItem): boolean {
        return !_.isNil(item.expectedTotalPrice) || !_.isNil(item.expectedUnitPrice) || !_.isNil(item.expectedTotalPriceRatio);
    }

    /**
     * 病历口腔相关
     */
    static DentistryMedicalRecordKey: (keyof Pick<
        MedicalRecord,
        "auxiliaryExaminations" | "disposals" | "extendDiagnosisInfos" | "treatmentPlans" | "dentistryExaminations"
    >)[] = ["auxiliaryExaminations", "disposals", "extendDiagnosisInfos", "treatmentPlans", "dentistryExaminations"];

    static regex1 = /^否认.*/;
    static regex2 = /.*不过敏$/;
    static regex3 = /.*未过敏$/;
    static regex4 = /^无.*过敏$/;
    /**
     * val 就是实际过敏史的字段
     * displayValue 就是展示用的
     * @param {*} val
     * @returns
     */
    static allergicHistoryWaring(val: string): boolean {
        if (!val) {
            return false;
        }
        if (
            this.regex1.test(val) ||
            this.regex2.test(val) ||
            this.regex3.test(val) ||
            this.regex4.test(val) ||
            val === "否认药物过敏史" ||
            val === "否认食物过敏史"
        ) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 门诊单各个处方添加keyId
     */
    static prescriptionInitKeyId(detailData: OutpatientInvoiceDetail): void {
        detailData?.prescriptionInfusionForms?.forEach((formItem) => {
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem?.prescriptionFormItems?.forEach((item) => {
                item.keyId = item.keyId || UUIDGen.generate();
            });
        });
        detailData?.prescriptionWesternForms?.forEach((formItem) => {
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem?.prescriptionFormItems?.forEach((item) => {
                item.keyId = item.keyId || UUIDGen.generate();
            });
        });
        detailData?.prescriptionChineseForms?.forEach((formItem) => {
            formItem.medicinePriceInfo = undefined;
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem?.prescriptionFormItems?.forEach((item) => {
                item.keyId = item.keyId || UUIDGen.generate();
            });
        });
        detailData?.prescriptionExternalForms?.forEach((formItem) => {
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem?.prescriptionFormItems?.forEach((item) => {
                item.keyId = item.keyId || UUIDGen.generate();
            });
        });
        detailData?.productForms?.forEach((formItem) => {
            formItem.keyId = formItem.keyId || UUIDGen.generate();
            formItem?.productFormItems?.forEach((item) => {
                item.keyId = item.keyId || UUIDGen.generate();
            });
        });
    }

    /**
     * 根据医生职称限制开出药品限制剔除处方内容
     */
    static removePrescriptionByPracticeInfo(
        form:
            | PrescriptionChineseForm
            | PrescriptionWesternForm
            | PrescriptionInfusionForm
            | PrescriptionExternalForm
            | PrescriptionProductForm,
        antimicrobialTypes?: AntibioticEnum[]
    ): PrescriptionFormItem[] | undefined {
        // antimicrobialTypes == undefined 此时未开启配置
        if (!antimicrobialTypes) return;
        const antimicrobialDrug: PrescriptionFormItem[] = [];
        if (form instanceof PrescriptionProductForm) {
            form.productFormItems = form.productFormItems?.filter((item) => {
                if (item.goodsInfo.isPackage) {
                    const isAntimicrobialDrug = item.goodsInfo.composeChildren?.every((subGoods) => {
                        // 没有设置抗菌药类型的不做改变
                        if (isNil(subGoods.antibiotic)) return true;
                        antimicrobialTypes.includes(subGoods.antibiotic ?? 0);
                    });
                    if (!isAntimicrobialDrug) {
                        antimicrobialDrug.push(item);
                    }
                    return isAntimicrobialDrug;
                } else {
                    // 没有设置抗菌药类型的不做改变
                    if (isNil(item.goodsInfo.antibiotic)) return true;
                    if (!antimicrobialTypes.includes(item.goodsInfo.antibiotic ?? 0)) {
                        antimicrobialDrug.push(item);
                    }
                    return antimicrobialTypes.includes(item.goodsInfo.antibiotic ?? 0);
                }
            });
        } else {
            form.prescriptionFormItems = form.prescriptionFormItems?.filter((item) => {
                // 没有设置抗菌药类型的不做改变
                if (isNil(item.goodsInfo.antibiotic)) return true;
                if (!antimicrobialTypes.includes(item.goodsInfo.antibiotic ?? 0)) {
                    antimicrobialDrug.push(item);
                }
                return antimicrobialTypes.includes(item.goodsInfo.antibiotic ?? 0);
            });
        }
        return antimicrobialDrug;
    }
    static removePrescriptionByPracticeInfoWithForms(
        forms?: (
            | PrescriptionChineseForm
            | PrescriptionWesternForm
            | PrescriptionInfusionForm
            | PrescriptionExternalForm
            | PrescriptionProductForm
        )[],
        antimicrobialTypes?: AntibioticEnum[]
    ): void {
        if (!forms || !forms.length) return;
        forms.forEach((form) => {
            this.removePrescriptionByPracticeInfo(form, antimicrobialTypes);
        });
    }

    /**
     * 获取手术申请单默认值
     */
    static getInitSurgeryReq(surgeryDate?: Date): SurgeryRequest {
        return {
            isInit: true,
            name: "",
            surgeryDepartmentId: "", // 手术科室id
            surgeryDoctorId: "", // 手术医生id
            assistantEmployeeIds: [], // 手术助手id列表
            preoperativeDiagnosis: [], // 术前诊断
            intendedSurgeries: [], // 拟施手术
            type: "", // 手术类型
            isDaytimeSurgery: 0, // 是否日间手术(患者在一个工作日内完成入院、手术和出院的一种手术模式)
            surgeryPosture: "", // 手术体位
            estimateMinutes: "", // 预计时长分钟数
            surgicalRequirements: "", // 手术需求
            // 麻醉信息
            surgeryArrangement: {
                surgeryDate: new Date(surgeryDate ?? new Date()).format("yyyy-MM-dd"), // 手术日期
                operatingRoomId: "", // 手术室id
                operatingRoomName: "", // 手术室名称
                anesthesiaDoctorId: "", // 麻醉医生
                anesthesiaAssistantEmployeeIds: [], // 麻醉助手
                surgeryNurseEmployeeIds: [], // 手术护士
                circulatingNurseEmployeeIds: [], // 巡回护士
                anesthesiaMethod: "", // 麻醉方式
                asaLevel: "", // asa分级
                preoperativeFastingFlag: "", // 术前进食
            },
        };
    }
}
