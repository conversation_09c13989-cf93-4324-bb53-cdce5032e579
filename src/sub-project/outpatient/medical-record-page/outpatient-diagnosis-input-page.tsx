/**
 * create by deng<PERSON>e
 * desc: 诊断
 * create date 2020/4/29
 */
import _ from "lodash";
import React from "react";
import { of, Subject } from "rxjs";
import { Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { GetAiDiagnosisRspDate } from "../data/cdss";
import { NoIntelligentDiagnosticTip } from "../outpatient-views";
import { debounceTime, switchMap } from "rxjs/operators";
import { MedicalRecordConfigType } from "../../data/online-property-config-provder";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ShebaoAgent } from "../../base-business/mix-agent/shebao-agent";
import { DiseasesCode } from "../../base-business/mix-agent/data/shebao-bean";
import { ignore } from "../../common-base-module/global";
import { AbcTag } from "../../base-ui/abc-app-library/common/abc-tag";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { BaseMedicalRecordPage, BaseMedicalRecordPageProps } from "./base-medical-record-page";

interface OutpatientDiagnosisInputPageProps extends BaseMedicalRecordPageProps {
    diagnosis?: string;
    aiDiagnosis?: GetAiDiagnosisRspDate;
    diagnosisType?: MedicalRecordConfigType;
}

// UI重构 诊断
export class OutpatientDiagnosisInputPage extends BaseMedicalRecordPage<OutpatientDiagnosisInputPageProps> {
    private checked: boolean;
    pageDisplayName = "诊断";

    private _searchDiagnosisTrigger = new Subject();
    private _shebaoDiseaseList?: DiseasesCode[];
    constructor(props: OutpatientDiagnosisInputPageProps) {
        super(props);
        this.inputValue = this.props.diagnosis ?? "";
        this.checked = false;
    }

    componentDidMount(): void {
        this._searchDiagnosisTrigger
            .pipe(
                debounceTime(200),
                switchMap(() => {
                    if (!_.last(this._splitString(this.inputValue))) {
                        return of({ diagnosisInfos: [] });
                    }
                    return ShebaoAgent.getDiseasesSearchList(_.last(this._splitString(this.inputValue)))
                        .catchIgnore()
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp) {
                    this._shebaoDiseaseList = rsp.diagnosisInfos;
                    this.forceUpdate();
                }
            })
            .addToDisposableBag(this);
    }

    private _splitString(str: string): string[] {
        return str.split(StringUtils.specialComma).filter((item) => !!item.trim());
    }

    formatter = (oldStr: string, newStr: string): string => {
        ignore(oldStr);
        return newStr.replace("\n", "");
    };

    onFocus(): void {
        this._textInputRef?.setValue(`${!!this.inputValue ? `${this.inputValue}${StringUtils.specialComma}` : ""}`);
    }

    onBlur(): void {
        this.inputValue = this.inputValue
            .split(StringUtils.specialComma)
            .map((item) => item.replace(/^[,，、;.。<br>\n]*|[,，、;.。<br>\n]*$/g, ""))
            .filter((item) => !!item)
            .join(StringUtils.specialComma);
        this._textInputRef?.setValue(this.inputValue);
    }

    _renderAiDiagnosis(): JSX.Element {
        const aiDiagnosis = this.props.aiDiagnosis?.list;
        if (_.isEmpty(aiDiagnosis) && !this._shebaoDiseaseList?.length) return <NoIntelligentDiagnosticTip />;

        if (_.isArray(aiDiagnosis) && !_.isEmpty(aiDiagnosis) && _.isEmpty(this._shebaoDiseaseList)) {
            return (
                <View
                    style={[
                        Sizes.paddingLTRB(Sizes.dp16, /* DeviceUtils.isIOS() ? Sizes.dp20 : Sizes.dp10,*/ Sizes.dp20, Sizes.dp16),
                        {
                            backgroundColor: Colors.white,
                        },
                    ]}
                >
                    <Text style={[TextStyles.t14NM, { marginBottom: Sizes.dp8, lineHeight: Sizes.dp20 }]}>智能诊断辅助</Text>
                    <View
                        style={{
                            flexDirection: "row",
                            flexWrap: "wrap",
                        }}
                    >
                        {aiDiagnosis
                            ?.filter((t) => !!t.diseaseName)
                            .map((item, index) => (
                                <AbcTag
                                    key={index}
                                    text={item.diseaseName}
                                    textStyle={[
                                        TextStyles.t14NT1.copyWith({ color: this.checked ? Colors.mainColor : Colors.t2 }),
                                        { flexShrink: 1, lineHeight: Sizes.dp20 },
                                    ]}
                                    style={[
                                        ABCStyles.rowAlignCenter,
                                        Sizes.marginLTRB(0, Sizes.dp0, Sizes.dp8, Sizes.dp8),

                                        {
                                            backgroundColor: this.checked ? Colors.theme2_08 : Colors.bg1,
                                            flexShrink: 1,
                                            borderColor: undefined,
                                        },
                                    ]}
                                    textNumberOfLines={1}
                                    value={item}
                                    onClick={(value) => {
                                        this._checkChip(value.diseaseName ?? "");
                                    }}
                                />
                            ))}
                    </View>
                </View>
            );
        }

        return <View />;
    }

    _renderSheBaoDisease(): JSX.Element {
        if (
            _.isArray(this._shebaoDiseaseList) &&
            !_.isEmpty(this._shebaoDiseaseList) &&
            !_.isEmpty(_.last(StringUtils.splitStringWithReg(this.inputValue)))
        ) {
            return (
                <View
                    style={[
                        Sizes.paddingLTRB(Sizes.dp16, DeviceUtils.isIOS() ? Sizes.dp20 : Sizes.dp10, Sizes.dp16),
                        {
                            backgroundColor: Colors.white,
                        },
                    ]}
                >
                    {/*<Text style={[TextStyles.t14NM, { marginBottom: Sizes.dp8, lineHeight: Sizes.dp20 }]}>诊断提示</Text>*/}
                    <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                        {this._shebaoDiseaseList.map((item, index) => (
                            <AbcTag
                                key={index}
                                text={item.name}
                                textStyle={[
                                    TextStyles.t14NT1.copyWith({ color: this.checked ? Colors.mainColor : Colors.t2 }),
                                    { flexShrink: 1 },
                                ]}
                                style={[
                                    ABCStyles.rowAlignCenter,
                                    Sizes.marginLTRB(0, Sizes.dp0, Sizes.dp8, Sizes.dp8),
                                    {
                                        backgroundColor: this.checked ? Colors.theme2_08 : Colors.bg1,
                                        flexShrink: 1,
                                        borderColor: undefined,
                                    },
                                ]}
                                textNumberOfLines={1}
                                value={item}
                                // extendedLabel={() => this._renderAiDiagnosisExtensionLabel(item)}
                                onClick={(value) => {
                                    this._checkChip(value.name, true); // false 不可重复添加
                                    this.forceUpdate();
                                }}
                            />
                        ))}
                    </View>
                </View>
            );
        }
        return <View />;
    }

    _checkChip(diseaseName: string, hasShebaoCode = false): void {
        if (diseaseName != null) {
            const keyword = _.last(this._splitString(this.inputValue));
            let diagnosisValueArray = this.inputValue
                ? this.inputValue
                      .split(StringUtils.specialComma)
                      .map((item) => item.replace(/^[,，、;.。<br>\n]*|[,，、;.。<br>\n]*$/g, ""))
                      .filter((item) => !!item)
                : [];

            if (diagnosisValueArray.indexOf(diseaseName) > -1) return;
            if (hasShebaoCode) {
                diagnosisValueArray[diagnosisValueArray.length - 1] = diagnosisValueArray[diagnosisValueArray.length - 1]
                    .replace(keyword ?? "", "")
                    .replace(/[,，、;]$/g, "");
            }
            this._shebaoDiseaseList = [];
            this.setState({});
            diagnosisValueArray.push(diseaseName);
            diagnosisValueArray = diagnosisValueArray.filter((item) => item);
            this.inputValue = diagnosisValueArray.length ? diagnosisValueArray.map((item) => `${item}`).join(StringUtils.specialComma) : "";
            this._textInputRef?.setValue(`${this.inputValue}${StringUtils.specialComma}`);
        }
        this.forceUpdate();
    }

    handleChangeInputValue(text: string): void {
        super.handleChangeInputValue(text);
        this._searchDiagnosisTrigger.next();
    }

    protected renderMedicalRecordContent(): JSX.Element {
        return (
            <View>
                {this._renderAiDiagnosis()}
                {this._renderSheBaoDisease()}
            </View>
        );
    }
}
