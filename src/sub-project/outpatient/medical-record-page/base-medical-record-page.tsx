/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2022/9/21
 * @Copyright 成都字节流科技有限公司© 2022
 */
import React from "react";
import { View, ScrollView, Text } from "@hippy/react";
import { BasePage, DividerLine, SizedBox } from "../../base-ui";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AbcTextInput, TextFormatter } from "../../base-ui/views/abc-text-input";
import { AbcText } from "../../base-ui/views/abc-text";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { AbcIntelligenceChip } from "../outpatient-views";
import { AnyType } from "../../common-base-module/common-types";
import { ignore } from "../../common-base-module/global";
import { UIUtils } from "../../base-ui/utils";

export interface BaseMedicalRecordPageProps {}

export class BaseMedicalRecordPage<P extends BaseMedicalRecordPageProps, S = {}> extends BasePage<P, S> {
    /**
     * 输入框的渲染高度
     * @protected
     */
    protected inputHeight = DeviceUtils.isOhos() ? 20 : 0;
    /**
     * 页面中的输入框节点信息
     * @protected
     */
    protected _textInputRef?: AbcTextInput | null;
    protected inputValue: string;
    protected pageDisplayName = "";
    protected pageDisplayKey = "";
    protected maxLength = 500;

    constructor(props: P) {
        super(props);
        this.inputValue = "";
    }

    pageName(): string | undefined {
        return this.pageDisplayName;
    }

    static async show<P extends BaseMedicalRecordPageProps>(options?: P): Promise<string> {
        const topMaskHeight = UIUtils.getScreenHeight() * 0.2;
        return showBottomPanel(React.createElement(this, { ...options }), { topMaskHeight: topMaskHeight });
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    renderContent(): JSX.Element {
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: Colors.white,
                }}
            >
                {this.renderMedicalRecordInput()}
                <DividerLine
                    lineHeight={Sizes.dpHalf}
                    color={Colors.dividerLineColor}
                    style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                />
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    {this.renderMedicalRecordContent()}
                </ScrollView>
            </View>
        );
    }

    protected saveInputValue(): void {
        ABCNavigator.pop(this.inputValue.trim());
    }

    protected handleChangeInputValue(text: string): void {
        this.inputValue = text;
        this.setState({});
    }

    formatter?: TextFormatter | TextFormatter[];

    onFocus?(): void;

    onBlur?(): void;

    protected handleSelectChip(data: AnyType): void {
        ignore(data);
        return;
    }

    protected renderMedicalRecordInput(): JSX.Element {
        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp22, Sizes.dp16, Sizes.dp20)]}>
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "space-between" }]}>
                    <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp22 }]}>{this.pageDisplayName}</Text>
                    <AbcText style={[TextStyles.t14MM, { lineHeight: Sizes.dp22 }]} onClick={this.saveInputValue.bind(this)}>
                        {"确定"}
                    </AbcText>
                </View>
                <SizedBox height={Sizes.dp17} />
                <AbcTextInput
                    style={{
                        ...TextStyles.t16NB,
                        flexGrow: 1,
                        backgroundColor: Colors.transparent,
                        underlineColorAndroid: Colors.white,
                        height: this.inputHeight,
                        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                    }}
                    ref={(ref) => {
                        this._textInputRef = ref;
                    }}
                    enableDefaultToolBar={true}
                    defaultValue={this.inputValue}
                    multiline={true}
                    placeholder={`请输入${this.pageDisplayName}`}
                    returnKeyType={"done"}
                    selectTextOnFocus={false}
                    placeholderTextColor={Colors.t4}
                    onContentSizeChange={(event) => {
                        this.inputHeight = Math.min(event.contentSize.height, Sizes.dp95);
                        this.forceUpdate();
                    }}
                    onChangeText={this.handleChangeInputValue.bind(this)}
                    maxLength={this.maxLength}
                    formatter={this.formatter}
                    onFocus={this.onFocus?.bind(this)}
                    onBlur={this.onBlur?.bind(this)}
                />
            </View>
        );
    }

    protected renderMedicalRecordContent(): JSX.Element {
        return <View />;
    }

    protected renderMedicalRecordSubTitle(title: string): JSX.Element {
        return (
            <View>
                <Text style={[TextStyles.t14MB.copyWith({ lineHeight: Sizes.dp20 })]}>{title}</Text>
            </View>
        );
    }

    protected _createItem(list: Array<string>, type: string): JSX.Element {
        return (
            <View
                style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                }}
            >
                {list.map((item, index) => (
                    <AbcIntelligenceChip key={index} text={item} type={type} onClick={this.handleSelectChip.bind(this)} />
                ))}
            </View>
        );
    }
}
