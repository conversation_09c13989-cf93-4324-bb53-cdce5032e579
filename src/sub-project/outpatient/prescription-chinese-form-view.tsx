/**
 * create by deng<PERSON><PERSON>
 * desc: 中药处方
 * create date 2020/5/11
 */
import React from "react";
import { PrescriptionChineseForm, PrescriptionFormItem } from "./data/outpatient-beans";
import { ABCUtils } from "../base-ui/utils/utils";
import { Image, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { OutPatientInvoicePageBloc } from "./outpatient-invoice-page-bloc";
import { DividerLine, IconFontView, SizedBox, Spacer } from "../base-ui";
import { AbcView } from "../base-ui/views/abc-view";
import { ChargeFormItemStatus, ChargeFormStatus } from "../charge/data/charge-beans";
import { MedicineScopeId, PharmacyType } from "../charge/data/charge-bean-air-pharmacy";
import { ChineseMedicineSpecType, GoodsTypeId, StockInfo } from "../base-business/data/beans";
import { NumberUtils } from "../common-base-module/utils";
import { userCenter } from "../user-center";
import { AbcBasePanel } from "../base-ui/abc-app-library";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import _ from "lodash";
import { TextWithErrorHint } from "./outpatient-views";
import { ChargeStatusView } from "../charge/view/charge-views";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { PsychotropicNarcoticTypeView } from "./views/psychotropic-narcotic-type-view";
import abcI18Next from "../language/config";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";

interface PrescriptionChineseFormViewsProps {
    prescriptionChineseForms: Array<PrescriptionChineseForm>;
}

export const PrescriptionChineseFormViews = (props: PrescriptionChineseFormViewsProps): JSX.Element => {
    const prescriptionChineseForms = props.prescriptionChineseForms;
    if (ABCUtils.isEmpty(prescriptionChineseForms)) return <View />;
    const totalCount = prescriptionChineseForms.length;
    const groupItem = prescriptionChineseForms.map((item, index) => (
        <_PrescriptionChineseFormView key={index} prescriptionForm={item} index={index++} totalCount={totalCount} />
    ));
    return <View style={{ overflow: "hidden" }}>{groupItem}</View>;
};

interface _PrescriptionChineseFormViewProps {
    prescriptionForm: PrescriptionChineseForm;
    index: number;
    totalCount: number;
}

// UI重构的 中药表单面板 _PrescriptionChineseFormView
export class _PrescriptionChineseFormView extends React.Component<_PrescriptionChineseFormViewProps> {
    static contextType = OutPatientInvoicePageBloc.Context;

    constructor(props: _PrescriptionChineseFormViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { prescriptionForm, index, totalCount } = this.props;
        const state = OutPatientInvoicePageBloc.fromContext(this.context).currentState;
        let chineseMedicineSpecType = "";

        // 判断处方类型：哪个类目数量最多就展示哪个类型
        if (prescriptionForm.prescriptionFormItems && prescriptionForm.prescriptionFormItems.length > 0) {
            const allItems = prescriptionForm.prescriptionFormItems;
            let pieceCount = 0;
            let granuleCount = 0;

            // 统计各类型数量
            allItems.forEach((item) => {
                if (item.productInfo?.cMSpec?.includes("中药饮片")) {
                    pieceCount++;
                } else if (item.productInfo?.cMSpec?.includes("中药颗粒")) {
                    granuleCount++;
                }
            });

            // 根据数量决定类型，只显示饮片或颗粒
            if (pieceCount >= granuleCount && pieceCount > 0) {
                chineseMedicineSpecType = "饮片";
            } else if (granuleCount > 0) {
                chineseMedicineSpecType = "颗粒";
            }
        }

        const headerText = `${chineseMedicineSpecType}处方${totalCount > 1 ? ABCUtils.toChineseNum(index + 1) : ""}`;

        const chargedStatus = prescriptionForm.chargeStatus == ChargeFormStatus.charged && !prescriptionForm.isPartChargeFee; // 中药处方单（已收费）
        let priceCount = prescriptionForm.localTotalPrice;
        if ((prescriptionForm.chargeStatus ?? 0) <= ChargeFormStatus.charged) {
            priceCount +=
                (!!prescriptionForm?.isDecoction ? prescriptionForm.processPrice ?? 0 : 0) + (prescriptionForm.ingredientPrice ?? 0);
        }
        const price = state.showTotalPrice ? `${abcI18Next.t("¥")}${ABCUtils.formatPrice(priceCount)}` : "";

        let totalPrice = "";
        let hasSelfPay = false;
        if ((prescriptionForm.prescriptionFormItems ?? []).some((item) => item.isSelfProvided)) hasSelfPay = true;
        if (hasSelfPay) {
            totalPrice = `${abcI18Next.t("¥")}${ABCUtils.formatPrice(prescriptionForm.localTotalPrice!)}`;
        } else {
            totalPrice = price;
        }
        const editable = state.isEditing && state.isItemCanEdit(prescriptionForm.chargeStatus!);

        /**
         * 本地的单子totalPrice包含了加工费
         * 拉取的已诊单子不包含加工费
         */
        const priceStr: string[] = [];
        const processPrice = prescriptionForm.processPrice;
        const ingredientPrice = prescriptionForm.ingredientPrice;
        const { vendor } = prescriptionForm;
        if (!!prescriptionForm?.isDecoction && processPrice) {
            priceStr.push(`加工费${ABCUtils.formatPrice(processPrice)}`);
        }
        if (ingredientPrice) {
            priceStr.push(`辅料费${ABCUtils.formatPrice(ingredientPrice)}`);
        }

        if (prescriptionForm.isAirPharmacy && vendor?.finishedRate) {
            priceStr.push(`加工出品率约为${NumberUtils.percent((vendor?.finishedRate ?? 0) * 100)}，成品重量以实际为准`);
        }

        return (
            <View accessibilityLabel={headerText}>
                <AbcBasePanel
                    panelStyle={index + 1 > 1 ? { marginTop: Sizes.dp16 } : {}}
                    onClick={() => {
                        OutPatientInvoicePageBloc.fromContext(this.context).requestModifyChineseMedicine(prescriptionForm);
                    }}
                >
                    <AbcCardHeader
                        style={{
                            height: Sizes.dp46,
                            paddingTop: Sizes.dp16,
                            paddingBottom: Sizes.dp5,
                            marginBottom: Sizes.dp11,
                        }}
                        titleSuffix={() => {
                            return (
                                <View style={[ABCStyles.rowAlignCenter, { flex: 1, marginRight: Sizes.dp8 }]}>
                                    <Text style={TextStyles.t18MT1}>{headerText}</Text>
                                    <View
                                        style={[
                                            Sizes.paddingLTRB(Sizes.dp4, Sizes.dp2, Sizes.dp4, Sizes.dp2),
                                            {
                                                marginLeft: Sizes.dp4,
                                                borderRadius: Sizes.dp2,
                                                backgroundColor: Colors.bg5,
                                                flexShrink: 1,
                                            },
                                        ]}
                                    >
                                        <Text
                                            style={[TextStyles.t12NT2.copyWith({ color: "#E28F4C" }), { lineHeight: Sizes.dp17 }]}
                                            numberOfLines={1}
                                        >
                                            {!!prescriptionForm.pharmacyType
                                                ? prescriptionForm.vendorName ?? prescriptionForm.pharmacyName ?? ""
                                                : !!prescriptionForm.pharmacyName
                                                ? prescriptionForm.pharmacyName
                                                : "本地药房"}
                                        </Text>
                                    </View>
                                    <View style={{ marginLeft: Sizes.dp4 }}>
                                        <PsychotropicNarcoticTypeView
                                            key={prescriptionForm.psychotropicNarcoticType ?? 0}
                                            type={prescriptionForm.psychotropicNarcoticType}
                                            disable={true}
                                            tagStyle={Sizes.paddingLTRB(Sizes.dp4, Sizes.dp2)}
                                        />
                                    </View>
                                </View>
                            );
                        }}
                        disableSpace={true}
                        rightRender={() => {
                            return (
                                <View style={ABCStyles.rowAlignCenter}>
                                    {!!priceStr.length && (
                                        <AbcView
                                            onClick={() => {
                                                this._showIngredientPrice(priceStr.join("、")).then();
                                            }}
                                        >
                                            <IconFontView
                                                name={"info"}
                                                size={Sizes.dp18}
                                                color={Colors.P3}
                                                style={{ marginRight: Sizes.dp8 }}
                                            />
                                        </AbcView>
                                    )}

                                    <Text style={TextStyles.t18MT1.copyWith({ lineHeight: Sizes.dp22 })}>{totalPrice}</Text>
                                    {editable && state.showTotalPrice && (
                                        <View
                                            style={{
                                                width: Sizes.dp1,
                                                height: Sizes.dp19,
                                                backgroundColor: Colors.dividerLineColor,
                                                marginHorizontal: Sizes.dp12,
                                            }}
                                        />
                                    )}
                                    {editable && (
                                        <IconFontView
                                            color={Colors.mainColor}
                                            name={"trash"}
                                            size={Sizes.dp18}
                                            onClick={() => {
                                                OutPatientInvoicePageBloc.fromContext(this.context).requestDeletePrescription(
                                                    prescriptionForm
                                                );
                                            }}
                                        />
                                    )}
                                </View>
                            );
                        }}
                    />
                    {chargedStatus && (
                        <AssetImageView
                            name={"charge_seal"}
                            style={{
                                position: "absolute",
                                height: Sizes.dp46,
                                width: Sizes.dp53,
                                top: 0,
                                right: 0,
                            }}
                        />
                    )}
                    {editable && (
                        // 删除图标热区
                        <AbcView
                            style={{
                                position: "absolute",
                                height: Sizes.dp46,
                                width: Sizes.dp46,
                                top: 0,
                                right: 0,
                            }}
                            onClick={() => {
                                OutPatientInvoicePageBloc.fromContext(this.context).requestDeletePrescription(prescriptionForm);
                            }}
                        />
                    )}
                    <View ref={prescriptionForm.scrollKey} style={[{ backgroundColor: Colors.white }]} overflow={"visible"}>
                        {this._renderPrescriptionGroupItem()}
                        <SizedBox height={Sizes.dp4} />
                        <DividerLine style={{ marginHorizontal: Sizes.dp16 }} lineHeight={Sizes.dpHalf} color={Colors.window_bg} />
                        <View overflow={"visible"}>{this._renderPrescriptionGroupUsage()}</View>
                    </View>
                </AbcBasePanel>
            </View>
        );
    }

    // 医生签名视图
    _renderHandSignView(isShowMargin?: boolean): JSX.Element {
        const state = OutPatientInvoicePageBloc.fromContext(this.context).currentState;
        const allClinicAllDoctors = state.canUseDoctorList;
        const matchDoctorInfo = allClinicAllDoctors?.find((f) => f.doctorId == state.detailData?.doctorId);
        const doctorName = matchDoctorInfo?.doctorName;
        const doctorHandSign = matchDoctorInfo?.handSign;
        return (
            <View style={{ marginLeft: isShowMargin ? Sizes.dp4 : 0 }}>
                {!!doctorHandSign ? (
                    <Image
                        src={doctorHandSign}
                        style={{
                            width: Sizes.dp36,
                            height: Sizes.dp17,
                        }}
                    />
                ) : (
                    <Text style={TextStyles.t12NT3.copyWith({ color: Colors.t3 })}>{doctorName ?? ""}</Text>
                )}
            </View>
        );
    }

    _renderPrescriptionGroupItem(): JSX.Element {
        const _bloc = OutPatientInvoicePageBloc.fromContext(this.context).currentState;
        const showErrorHint = _bloc.showErrorHint;
        const medicinesView: JSX.Element[] = [];
        const prescriptionForm = this.props.prescriptionForm;
        const { prescriptionFormItems, eqConversionRule } = prescriptionForm;
        const isChinesePiece = eqConversionRule === ChineseMedicineSpecType.chinesePiece;
        const getUnitCountField = (formItem: PrescriptionFormItem) => {
            const { typeId, pieceUnit } = formItem.goodsInfo || {};
            const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;
            // 中药颗粒 & 单位为g/克
            const isChineseGranuleAndUnitG = isMedicineChineseGranule && (pieceUnit === "g" || pieceUnit === "克");
            if (userCenter.enableEqCoefficient && isChinesePiece && isChineseGranuleAndUnitG) {
                return "eqUnitCount";
            }
            return "unitCount";
        };

        prescriptionFormItems?.forEach((formItem, index) => {
            //unitCount有可能为字符串，所以需要先转一次
            const unitCountField = getUnitCountField(formItem);
            const currentUnitCount = formItem[unitCountField];
            formItem[unitCountField] = !!currentUnitCount
                ? (currentUnitCount.toString().length ?? 0) > 0
                    ? Number(currentUnitCount)
                    : currentUnitCount
                : currentUnitCount;
            let specialRequirement: string;

            //兼容"* 地龙"情况
            const displayNameList = formItem.displayName.split(" ");
            const _name = displayNameList.find((item) => !!item);
            let name = displayNameList.find((item) => !!item && /[\u4e00-\u9fa5]/.test(item));
            if (name != _name) {
                name = `${_name}${name}`;
            }

            const usageUnit = `${
                _.isNumber(formItem && formItem[getUnitCountField(formItem)]) ? formItem[getUnitCountField(formItem)] : "-"
            }${formItem.unit ? formItem.unit : "-"}`;
            if (_.isString(formItem?.specialRequirement) || _.isNil(formItem?.specialRequirement)) {
                // 所有药品都不需要加括号
                specialRequirement = formItem?.specialRequirement ?? "";
            } else {
                //@ts-ignore
                specialRequirement = formItem?.specialRequirement ? formItem?.specialRequirement.name : "";
            }

            const isSelfProvided = formItem.isSelfProvided; // 自备药品不需提示库存不足
            const stockInfo =
                !!prescriptionForm.chargeStatus || isSelfProvided
                    ? new StockInfo(true)
                    : formItem.goodsInfo.stockInfo(
                          formItem?.unit,
                          formItem[getUnitCountField(formItem)],
                          prescriptionForm.doseCount ?? 1,
                          undefined,
                          undefined,
                          undefined,
                          prescriptionForm.pharmacyType
                      );
            let unDiagnosis = false;
            if (!prescriptionForm.chargeStatus) {
                unDiagnosis = userCenter?.inventoryClinicConfig?.stockGoodsConfig?.disableNoStockGoods == 2;
            }
            // 已退费的文字需要弱化
            const isFadeText = formItem.chargeStatus == ChargeFormStatus.refunded;
            // 在一次都没有收费过的情况下，如果子项中存在已退单的，不需要显示已退单状态（收费未落地）
            const hasRefundItemOfEmptyCharge =
                prescriptionForm.chargeStatus == ChargeFormStatus.unCharged && formItem.chargeStatus == ChargeFormStatus.chargeBack;

            //多药房--需要显示当前药品的类型（饮片、颗粒）
            const showGoodsSpecTypeName = formItem.productInfo?.cMSpec?.slice(-2);

            // 有医生签名
            const hasAutograph = formItem?.verifySignatures?.some((item) => item.status == 1);

            const isControlledSubstances = _bloc.checkGoodsIsControlledSubstances(formItem.goodsInfo.id);
            medicinesView.push(
                <AbcView
                    key={index}
                    style={{ width: pxToDp(150), paddingBottom: Sizes.dp12 }}
                    onClick={() => {
                        OutPatientInvoicePageBloc.fromContext(this.context).requestModifyChineseMedicine(prescriptionForm, undefined);
                    }}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        {!!specialRequirement && (
                            <TextWithErrorHint
                                text={`${specialRequirement}`}
                                error={showErrorHint && specialRequirement == "--"}
                                textStyle={[
                                    stockInfo.stockEnough || unDiagnosis ? TextStyles.t12NT6 : TextStyles.t12NY2,
                                    { lineHeight: Sizes.dp16 },
                                    isFadeText ? TextStyles.t12NT4 : {},
                                ]}
                                style={{ flexShrink: 1 }}
                            />
                        )}
                        {!!showGoodsSpecTypeName && (
                            <View
                                style={{
                                    marginLeft: !!specialRequirement ? Sizes.dp4 : 0,
                                    height: Sizes.dp16,
                                }}
                            />
                        )}
                        {hasAutograph && this._renderHandSignView(!!specialRequirement || !!showGoodsSpecTypeName)}
                    </View>
                    <View style={ABCStyles.rowAlignCenter}>
                        <TextWithErrorHint
                            text={`${name}`}
                            error={showErrorHint && name == "--"}
                            textStyle={[
                                stockInfo.stockEnough || unDiagnosis ? TextStyles.t16NT1 : TextStyles.t16NY2,
                                isControlledSubstances ? TextStyles.t16NY2 : {},
                                { lineHeight: Sizes.dp22 },
                                isFadeText ? TextStyles.t16NT4 : {},
                            ]}
                            style={[{ marginRight: !!usageUnit ? Sizes.dp4 : 0 }, { flexShrink: 1 }]}
                        />
                        <TextWithErrorHint
                            text={`${usageUnit}`}
                            error={showErrorHint && usageUnit.includes("-")}
                            textStyle={[
                                stockInfo.stockEnough || unDiagnosis ? TextStyles.t14NT1 : TextStyles.t14NY2,
                                isControlledSubstances ? TextStyles.t16NY2 : {},
                                { lineHeight: Sizes.dp20, flexShrink: 1 },
                                isFadeText ? TextStyles.t14NT4 : {},
                            ]}
                            style={[
                                {
                                    marginRight: !!specialRequirement || _.isEmpty(specialRequirement) ? Sizes.dp4 : 0,
                                },
                            ]}
                        />
                        <Spacer />
                        <ChargeStatusView
                            chargeStatus={hasRefundItemOfEmptyCharge ? ChargeFormStatus.unCharged : formItem.chargeStatus}
                            style={{ marginRight: 0 }}
                            isAbbr={true}
                        />
                    </View>
                </AbcView>
            );
        });
        return (
            <View
                style={{
                    flexDirection: "row",
                    flexWrap: "wrap",
                    marginHorizontal: Sizes.dp16,
                    justifyContent: "space-between",
                }}
            >
                {medicinesView}
            </View>
        );
    }

    // UI 重构中药药品 加工 用法
    // 共30剂 煎服 1日3剂 1日3次 每次120ml 饭后服用
    // 加工：人工煎药（1剂3袋）
    _renderPrescriptionGroupUsage(): JSX.Element {
        const {
            doseCount,
            usage,
            dailyDosage,
            freq,
            usageLevel,
            requirement,
            displayProcess,
            pharmacyType,
            usageScopeId,
            medicineStateScopeId,
            vendor,
            isDecoction,
            usageDays,
            processBagUnitCount,
            totalProcessCount,
            processRemark,
            deliveryInfo,
            id,
            // specification,
        } = this.props.prescriptionForm;
        const _bloc = OutPatientInvoicePageBloc.fromContext(this.context),
            state = _bloc.currentState,
            { showErrorHint, isEditing } = state;
        const _unCharged = this.props.prescriptionForm?.chargeStatus != ChargeFormItemStatus.unCharged;
        const editable = state.isEditing && state.isItemCanEdit(this.props.prescriptionForm.chargeStatus!);
        const _showErrorHint = showErrorHint && _unCharged && isEditing;
        let _medicineStateList = vendor?.vendorAvailableMedicalStates ?? [];
        if (!vendor?.vendorAvailableMedicalStates?.length || !state.isEditing) {
            _medicineStateList = [];
            state.usageList.forEach((item1) => {
                if (item1.id == usageScopeId) {
                    item1.children?.forEach((item2) => {
                        if (item2.id == medicineStateScopeId) {
                            _medicineStateList.push({
                                medicalStatId: (item2.id as MedicineScopeId) ?? "",
                                medicalStatName: item2.cisDisplayName ?? "",
                            });
                        }
                    });
                }
            });
        }
        const remarkStyles = TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 });
        const titleStyle = TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 });
        const isExistMedicineState = _medicineStateList?.find((it) => it.medicalStatId == medicineStateScopeId)?.medicalStatName;

        const createText =
            `${!!doseCount ? "共" + doseCount + "剂" : "--剂"}` +
            // `${!!usage ? "，" + usage : "--"}` +
            `${!!dailyDosage ? "，" + dailyDosage : ""}` +
            `${!!freq ? "，" + freq : ""}` +
            `${!!usageLevel ? "，" + usageLevel : ""}` +
            `${!!usageDays ? "，" + usageDays : ""}` +
            `${!!requirement ? "，" + requirement : ""}`;
        const createErrorHint = (_showErrorHint && !doseCount) || (_showErrorHint && !usage);
        const airSliceUsage = `${!!processBagUnitCount ? "（1剂煎" + processBagUnitCount + "袋" : ""}${
            !!totalProcessCount
                ? "，共" + totalProcessCount + "袋" + (!!processBagUnitCount ? "）" : "")
                : !!processBagUnitCount
                ? "）"
                : ""
        }${!!processRemark ? "，" + processRemark : ""}`;
        //空中药房-颗粒剂-固定袋数
        let airParticles = `${!!processBagUnitCount ? "（1剂调" + processBagUnitCount + "袋）" : ""}${
            !!processRemark ? "，" + processRemark : ""
        }`;
        if (pharmacyType == PharmacyType.air && vendor?.calculateProcessBagType == 1) {
            airParticles = `${
                !!vendor?.processBagUnitCount ? "（1剂调" + vendor?.processBagUnitCount + vendor?.processBagUnit + "）" : ""
            }${!!processRemark ? "，" + processRemark : ""}`;
        }

        return (
            <View style={Sizes.paddingLTRB(Sizes.dp16)}>
                <AbcView
                    overflow={"visible"}
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            backgroundColor: Colors.white,
                        },
                    ]}
                >
                    <TextWithErrorHint
                        title={`用法${!!usage ? `(${usage})` : ""}`}
                        titleStyle={titleStyle}
                        textStyle={remarkStyles}
                        text={createText}
                        error={createErrorHint}
                        style={{ flexShrink: 1 }}
                        numberOfLines={2}
                    />
                </AbcView>
                {pharmacyType != PharmacyType.air &&
                    pharmacyType != PharmacyType.virtual &&
                    (editable || !_.isEmpty(displayProcess)) &&
                    isDecoction &&
                    !!displayProcess && (
                        <View style={{ paddingTop: Sizes.dp12, paddingRight: Sizes.dp8 }}>
                            <Text style={titleStyle}>加工</Text>
                            <Text numberOfLines={2} style={[remarkStyles, { flexShrink: 1 }]}>{`${displayProcess ?? ""}`}</Text>
                        </View>
                    )}
                {pharmacyType != PharmacyType.normal && !!_medicineStateList.length && !!isExistMedicineState && (
                    <View style={{ paddingTop: Sizes.dp12, paddingRight: Sizes.dp8 }}>
                        <Text style={titleStyle}>药态</Text>
                        <Text
                            numberOfLines={2}
                            style={[
                                remarkStyles,
                                {
                                    flexShrink: 1,
                                },
                            ]}
                        >
                            {`${isExistMedicineState}${
                                !!pharmacyType && medicineStateScopeId == MedicineScopeId.daiJian
                                    ? airSliceUsage
                                    : pharmacyType == PharmacyType.air && medicineStateScopeId == MedicineScopeId.keLi
                                    ? airParticles
                                    : ""
                            }`}
                        </Text>
                    </View>
                )}
                {pharmacyType != PharmacyType.normal && (!!deliveryInfo?.getAddressInfo || !!deliveryInfo?.combineAddress) && (
                    <View style={{ paddingTop: Sizes.dp12, paddingRight: Sizes.dp8 }}>
                        <Text style={titleStyle}>快递</Text>
                        <View style={{ flex: 1 }}>
                            {!!deliveryInfo?.getAddressInfo && (
                                <Text
                                    numberOfLines={1}
                                    style={[
                                        remarkStyles,
                                        {
                                            flexShrink: 1,
                                        },
                                    ]}
                                >
                                    {`${deliveryInfo?.getAddressInfo ?? ""}`}
                                </Text>
                            )}
                            {!!deliveryInfo?.expressInfo && (
                                <Text
                                    numberOfLines={1}
                                    style={[
                                        remarkStyles,
                                        {
                                            flexShrink: 1,
                                        },
                                    ]}
                                >
                                    {deliveryInfo?.expressInfo ?? ""}
                                </Text>
                            )}
                        </View>
                    </View>
                )}
                {pharmacyType == PharmacyType.air && !!deliveryInfo?.getLatestLogisticTrace && (
                    <AbcView
                        style={[{ paddingTop: Sizes.dp8, paddingRight: Sizes.dp8, flex: 1 }]}
                        onClick={() => _bloc.requestCheckExpressInfo(id ?? "", deliveryInfo?.getCompanyNameAndDeliveryNo)}
                    >
                        <View
                            style={[
                                Sizes.paddingLTRB(Sizes.dp12, Sizes.dp8),
                                {
                                    backgroundColor: Colors.bg1,
                                    flexDirection: "row",
                                    flex: 1,
                                    borderRadius: Sizes.dp3,
                                },
                            ]}
                        >
                            <Text numberOfLines={2} style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1 }]}>
                                {deliveryInfo?.getLatestLogisticTrace}
                            </Text>
                            <Text style={[TextStyles.t14NM.copyWith({ lineHeight: Sizes.dp20 }), { alignSelf: "flex-end" }]}>详情</Text>
                        </View>
                    </AbcView>
                )}
            </View>
        );
    }

    /**
     * 显示加工费
     * @private
     */
    private async _showIngredientPrice(str?: string): Promise<void> {
        if (!str) return;
        showConfirmDialog("", str).then();
    }
}
