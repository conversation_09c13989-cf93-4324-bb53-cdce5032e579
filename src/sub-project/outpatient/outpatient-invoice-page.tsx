/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020-03-31
 *
 * @description
 * 患者数据模块请求
 */

import React, { useContext } from "react";
import { ScrollView, Style, Text, View, Clipboard } from "@hippy/react";
import { BasePage, DividerLine, IconFontView, SizedBox } from "../base-ui";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../theme";
import { BlocBuilder } from "../bloc";
import { OutPatientInvoicePageBloc, ScrollToFocusItemState } from "./outpatient-invoice-page-bloc";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { OutpatientUtils } from "./utils/outpatient-utils";
import { ABCUtils } from "../base-ui/utils/utils";
import { OutpatientInvoiceDetail } from "./data/outpatient-beans";
import { PrescriptionWesternFormViews } from "./prescription-western-form-view";
import { PrescriptionInfusionFormViews } from "./prescription-infusion-form-view";
import { PrescriptionChineseFormViews } from "./prescription-chinese-form-view";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../base-ui/base-page";
import { AbcText } from "../base-ui/views/abc-text";
import { AbcView } from "../base-ui/views/abc-view";
import WillPopListener from "../base-ui/views/will-pop-listener";
import { AbcPopMenu, MenuItem } from "../base-ui/views/pop-menu";
import { NumberKeyboardBuilder } from "../base-ui/views/keyboards/number-keyboard";
import { TreeDotView } from "../base-ui/iconfont/iconfont-view";
import { PrescriptionExternalFormView } from "./prescription-external-form-view";
import { LogUtils } from "../common-base-module/log";
import { AbcScrollView } from "../base-ui/views/abc-scroll-view";
import { OutpatientContentRefKey } from "./data/outpatient-const";
import { AbcCardHeader } from "../base-ui/abc-app-library/common/abc-card-header";
import { AbcBasePanel, AbcCollapse, AbcDialog, BottomSheetHelper } from "../base-ui/abc-app-library";
import { PrescriptionTypeSelectView } from "./views/prescription-type-select-view";
import AbcPatientCardInfoView from "./views/new-patient-Info-view";
import { MedicalRecordGroup } from "./views/new-medical-record-group";
import { OutpatientHistoryDialogResult } from "./outpatient-history-dialog";
import { OutpatientSetting } from "./views/outpatient-setting";
import { ProductFormView } from "./views/product-form-view";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { PrecisionLimitFormatter } from "../base-ui/utils/formatter";
import { keyboardListener } from "../common-base-module/utils/keyboard-listener";
import { VerifyItem, VerifyItemDetail, VerifyLevel } from "./data/cdss";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { AbcButton } from "../base-ui/views/abc-button";
import PatientHospitalDetailView from "../views/patient-hospital-info/patient-hospital-detail-view";
import { PushScanSceneType, PushToPayDialog } from "../views/push-to-pay-dialog/push-to-pay-dialog";
import { userCenter } from "../user-center";
import { DialogIndex, showConfirmDialog } from "../base-ui/dialog/dialog-builder";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { Toast } from "../base-ui/dialog/toast";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { TimeUtils } from "../common-base-module/utils";
import { StringUtils } from "../base-ui/utils/string-utils";
import { RegistrationDetail } from "../registration/data/bean";
import { pxToDp } from "../base-ui/utils/ui-utils";
import { DentistryConfig } from "../registration/dentistry/data/bean";
import abcI18Next from "../language/config";
import { AirPharmacyOrderIdView } from "../views/air-pharmacy-order-id-view";
import { GuardianEditCard } from "../views/guardian-edit-dialog/guardian-edit-page";
import { HistoryPermissionModuleType } from "../base-business/data/beans";
import { AbcBannerTips } from "../base-ui/abc-banner-tips/abc-banner-tips";

const kOutpatientStart = OutpatientContentRefKey.patient;
const kOutpatientEnd = "kOutpatientEnd";

interface OutpatientInvoicePageProps {
    outpatientId?: string;
    draftId?: string;
    detailData?: OutpatientInvoiceDetail;
    registrationSheetId?: string;
}

enum OutpatientMenuItemValue {
    deleteSheet = 1, // 删除接诊
    setting = 2, // 门诊设置
    preview = 3, // 费用预览
    preDiagnosis = 4, // 完成预诊
    pushPayment = 5, // 推送支付
    preDiagnosisCancel = 6, // 取消预诊
    visit = 7, // 复诊预约
    addPrescription = 8, // 添加处方
}

export class OutpatientInvoicePage extends BaseBlocNetworkPage<OutpatientInvoicePageProps, OutPatientInvoicePageBloc> {
    private _pageContentRef?: AbcScrollView | null;
    private showPageMask = false;

    constructor(props: OutpatientInvoicePageProps) {
        super(props);
        this.bloc = new OutPatientInvoicePageBloc({ ...props });
        this.addDisposable(this.bloc);
    }

    componentDidMount(): void {
        super.componentDidMount();

        this.setContentStatus(ABCNetworkPageContentStatus.loading, null);
        const stateObserver = this.bloc.state.subscribe((state) => {
            let status = ABCNetworkPageContentStatus.show_data;
            let error: any;
            if (state.loading) {
                status = ABCNetworkPageContentStatus.loading;
            } else if (state.loadError) {
                status = ABCNetworkPageContentStatus.error;
                error = state.loadError;
            }

            this.setContentStatus(status, error);

            if (state instanceof ScrollToFocusItemState) {
                this.makeFocusItemVisible();
            }
        });

        keyboardListener
            .subscribe((visible) => {
                this.showPageMask = visible.visible;
                this.setState({});
            })
            .addToDisposableBag(this);
        this.addDisposable(stateObserver);
    }

    makeFocusItemVisible(): void {
        const state = this.bloc.currentState;
        const { focusItemKey } = state;
        this._pageContentRef?.scrollChildToVisible(focusItemKey ?? "", kOutpatientStart, kOutpatientEnd);
    }

    getAppBarTitle(): string {
        const state = this.bloc.currentState;
        if (state.detailData == undefined) return "";
        if (state.detailData?.isOnline == 1) return "网络接诊";
        if (!state.isEditing) return "门诊单";
        if (state.detailData.isLocalDraft || state.detailData.isWaitVisit) return "接诊";
        else return "编辑门诊单";
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.transparent;
    }

    /**
     * 门诊标题状态显示
     */
    getAPPBarCustomTitle(): JSX.Element {
        const { detailData, registrationDetail, registrationConfig } = this.bloc.currentState;

        const referralSource = registrationDetail?.referralSource;

        let subTitle = "";
        if (detailData?.isContinueDiagnose) {
            subTitle = "回诊";
        } else if (detailData?.isVisited) {
            subTitle = "已诊";
        }

        return (
            <BlocBuilder
                bloc={this.bloc}
                condition={() => true}
                build={() => (
                    <View style={{ flex: 1, justifyContent: "center" }}>
                        <View style={[ABCStyles.rowAlignCenter, { alignSelf: "center" }]}>
                            <SizedBox width={Sizes.dp40} />
                            <Text style={TextStyles.t18MT1}>{this.getAppBarTitle()}</Text>
                            {!!subTitle ? (
                                <Text
                                    style={[
                                        TextStyles.t14NM2,
                                        {
                                            width: Sizes.dp40,
                                            textAlign: "center",
                                        },
                                    ]}
                                >
                                    {subTitle}
                                </Text>
                            ) : (
                                <SizedBox width={Sizes.dp40} />
                            )}
                        </View>
                        {detailData && referralSource && (
                            <AbcView
                                style={{
                                    alignSelf: "center",
                                    paddingTop: referralSource ? Sizes.dp4 : undefined,
                                    paddingBottom: referralSource ? Sizes.dp8 : undefined,
                                }}
                                onClick={() => {
                                    return showBottomPanel(
                                        <AppointmentRegistrationInformationView
                                            detail={detailData}
                                            registrationDetail={registrationDetail}
                                            registrationConfig={registrationConfig}
                                        />,
                                        {
                                            topMaskHeight: pxToDp(437),
                                        }
                                    );
                                }}
                            >
                                <View style={ABCStyles.rowAlignCenter}>
                                    <Text style={TextStyles.t11NG2}>
                                        {`第${detailData?.diagnoseCount}次就诊[${
                                            registrationDetail?.registrationFormItem?.isReservedName ?? ""
                                        }：转诊自${registrationDetail?.referralSource?.doctorName ?? ""}]`}
                                    </Text>
                                    <IconFontView name={"Dropdown_Triangle"} size={Sizes.dp14} color={Colors.G2} />
                                </View>
                            </AbcView>
                        )}
                    </View>
                )}
            />
        );
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    onBackClick(): void {
        this.bloc.requestBack();
    }

    /**
     * 门诊设置
     */
    outpatientSettingIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        const _key = 0;
        if (state.detailData == undefined) return [<View key={_key} />];

        const menuItems: MenuItem<number>[] = this._createMenuItemsList();

        if (!menuItems.length || state.orderIsLocking) return [<View key={_key} />];

        return [
            <View
                key={_key}
                style={{ paddingHorizontal: Sizes.dp8 }}
                collapsable={false}
                onClick={async () => {
                    const select = await AbcPopMenu.show(
                        menuItems,
                        { x: Sizes.dp24, y: Sizes.dp42 },
                        { x: Sizes.dp82, y: Sizes.dp42 },
                        { fullGrid: true }
                    );
                    switch (select) {
                        case OutpatientMenuItemValue.setting: {
                            await ABCNavigator.navigateToPage(<OutpatientSetting bloc={this.bloc} />); // 门诊设置
                            break;
                        }
                        case OutpatientMenuItemValue.deleteSheet: {
                            this.bloc.requestDeleteOutpatient(); // 删除接诊
                            break;
                        }
                        case OutpatientMenuItemValue.preview: {
                            this.bloc.requestChargePreview(); // 费用预览
                            break;
                        }
                        case OutpatientMenuItemValue.preDiagnosis: {
                            this.bloc.requestPreOutpatient(); // 提交预诊
                            break;
                        }
                        case OutpatientMenuItemValue.pushPayment: {
                            this._onQuickScanPay(state.detailData?.patientOrderId); // 推送支付
                            break;
                        }
                        case OutpatientMenuItemValue.preDiagnosisCancel: {
                            this.bloc.requestFreshInit(true); // 取消预诊
                            break;
                        }
                        case OutpatientMenuItemValue.visit: {
                            this.bloc.requestRepeatAppointment({
                                patient: state.detailData?.patient,
                                doctorId: state.detailData?.doctorId,
                                departmentId: state.detailData?.departmentId,
                            }); // 新建复诊
                            break;
                        }
                        case OutpatientMenuItemValue.addPrescription: {
                            this._addNewPrescription().then();
                            break;
                        }
                    }
                }}
            >
                <TreeDotView color={Colors.T1} />
            </View>,
        ];
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            detailData = state.detailData;
        if (detailData == undefined) return <View />;
        const {
            isEditing,
            orderIsLocking,
            lockDetail,
            chargeLockDetail,
            isDentistryClinic,
            disableUpdateMedicalRecord,
            disableAddPrescription,
            tollCollectorCharges,
            microclinicsOrSelfServiceMachines,
        } = state;
        const isOnline = detailData?.isOnline == 1;
        const patient = detailData && detailData.patient;
        const showErrorHint = state.showErrorHint;
        const revisitStatusName = detailData.getRevisitStatusName;

        const diagnoseCount = detailData.diagnoseCount; // 就诊历史数量

        const { isHospitalizationSheet, hospitalPatientOrderId } = detailData;
        const { hospitalDetail } = state;
        const isShowHospital = isHospitalizationSheet || !!hospitalDetail?.hospitalSheetId; //是否显示长护详情

        let medicineRecordStr = "患者";
        if (isDentistryClinic) {
            medicineRecordStr = revisitStatusName;
        }
        let lockTips = "";
        // 门诊锁单不锁自己
        if (!!lockDetail && !!lockDetail?.status && lockDetail?.employeeId != userCenter.employee?.id) {
            lockTips = `医生${lockDetail?.employeeName}正在编辑，编辑结束前其他人不可同时编辑`;
        } else if (!!chargeLockDetail && !!chargeLockDetail?.status && chargeLockDetail?.value?.chargeInProgress) {
            if (tollCollectorCharges) {
                lockTips = `${
                    !!chargeLockDetail?.employeeName ? "收费员" + chargeLockDetail?.employeeName : "本单"
                }正在收费中，当前仅能修改病历`;
            } else if (microclinicsOrSelfServiceMachines) {
                lockTips = "患者正在自助支付，当前仅能修改病历";
            }
        }

        return (
            <View style={[DeviceUtils.isOhos() ? {} : { flexGrow: 1, backgroundColor: Colors.prescriptionBg }]}>
                <WillPopListener
                    onWillPop={() => {
                        this.bloc.requestBack();
                    }}
                />
                {this.showPageMask && (
                    <View style={[ABCStyles.absoluteFill, { zIndex: 10 }]} onTouchDown={() => AbcTextInput.focusInput?.blur()} />
                )}
                {orderIsLocking && <AbcBannerTips tips={lockTips} />}
                <AbcScrollView
                    style={{ height: DeviceUtils.isOhos() ? undefined : 1, flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._pageContentRef = ref;
                    }}
                    scrollEventThrottle={100}
                >
                    <View ref={OutpatientContentRefKey.patient} collapsable={false} />

                    <AbcPatientCardInfoView
                        isEditing={isEditing}
                        isOutpatient={true}
                        showErrorHint={showErrorHint}
                        // 不能修改患者的情况： 挂号过来的 || 已诊的 || 已收费的 || 网诊 || 口腔诊所
                        patientSwitchable={
                            !(
                                !!detailData.isOnline ||
                                (detailData.chargeStatus ?? 0) > 0 ||
                                detailData.source == 1 ||
                                detailData?.isVisited ||
                                state.isDentistryClinic
                            )
                        }
                        patient={patient}
                        diagnoseCount={diagnoseCount}
                        revisitStatusName={revisitStatusName}
                        onChange={(patient) => {
                            this.bloc.requestEditPatientInfo(patient);
                        }}
                        onClearPatient={() => {
                            this.bloc.requestClearPatient();
                        }}
                        onChangePatientHistory={(result: OutpatientHistoryDialogResult) => {
                            this.bloc.requestNavToHistoryDialog(result); // 复制病历或处方
                        }}
                        type={HistoryPermissionModuleType.outpatient}
                        canSeePatientMobileInOutpatient={state.canSeePatientPhone}
                        onCancel={() => this.bloc.requestDeleteDraft()}
                        disableAddPrescription={disableAddPrescription}
                        disableUpdateMedicalRecord={disableUpdateMedicalRecord}
                    />

                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        <InvoiceDoctorCard isOnline={isOnline} />
                    </AbcBasePanel>

                    {isShowHospital && (
                        <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                            <PatientHospitalDetailView
                                orderId={hospitalPatientOrderId}
                                hospitalDetail={hospitalDetail}
                                editable={detailData.isLocalDraft || detailData.isWaitVisit}
                                onChange={(hospital) => this.bloc.requestUpdateClinicType(hospital)}
                            />
                        </AbcBasePanel>
                    )}

                    {(state.isBeijingSupervise || !!detailData.patientGuardian) && patient?.age?.validLimitYear() && (
                        <GuardianEditCard
                            edit={isEditing}
                            guardianDetail={detailData.patientGuardian}
                            onChange={(value) => {
                                this.bloc.requestModifyPatientGuardianDetail(value);
                            }}
                        />
                    )}

                    <AbcBasePanel panelStyle={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}>
                        <AbcCardHeader
                            style={{ height: Sizes.dp26, marginTop: Sizes.dp20 }}
                            title={`${medicineRecordStr}病历`}
                            titleStyle={TextStyles.t18MT1}
                            rightRender={
                                (isEditing && !disableUpdateMedicalRecord) || state.canEditMedical
                                    ? () => (
                                          <View style={ABCStyles.rowAlignCenter}>
                                              <AbcView onClick={() => this.bloc.requestAddAttachment()}>
                                                  <IconFontView name={"add file"} color={Colors.mainColor} size={Sizes.dp18} />
                                              </AbcView>
                                              <SizedBox width={Sizes.dp14} />
                                              <AbcView
                                                  style={{ paddingLeft: Sizes.dp6 }}
                                                  onClick={() => this.bloc.requestAddMedicalRecordTemplate()}
                                              >
                                                  <IconFontView name={"template"} color={Colors.mainColor} size={Sizes.dp18} />
                                              </AbcView>
                                          </View>
                                      )
                                    : undefined
                            }
                        />
                        <MedicalRecordGroup />
                    </AbcBasePanel>

                    {state.isEditing && !detailData?.productForms?.length && (
                        <OutpatientAddButton
                            style={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}
                            title={"诊疗项目"}
                            titleStyle={[TextStyles.t18MT1, { lineHeight: Sizes.dp26 }]}
                            onClick={() => {
                                this.bloc.requestAddProduct({ fromAddBtn: true });
                            }}
                        />
                    )}

                    {!!detailData?.productForms?.length && (
                        <View style={[{ ...Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0) }, { overflow: "hidden" }]}>
                            <ProductFormView />
                        </View>
                    )}

                    <View style={{ marginHorizontal: Sizes.dp8 }}>
                        {!!detailData?.prescriptionWesternForms?.length && <SizedBox height={Sizes.dp18} />}
                        <PrescriptionWesternFormViews
                            prescriptionWesternForms={detailData?.prescriptionWesternForms ?? []}
                            departmentId={detailData?.departmentId}
                        />

                        {!!detailData?.prescriptionChineseForms?.length && <SizedBox height={Sizes.dp18} />}
                        <PrescriptionChineseFormViews prescriptionChineseForms={detailData?.prescriptionChineseForms ?? []} />

                        {!!detailData?.prescriptionInfusionForms?.length && <SizedBox height={Sizes.dp18} />}
                        <PrescriptionInfusionFormViews
                            prescriptionInfusionForms={detailData?.prescriptionInfusionForms ?? []}
                            departmentId={detailData?.departmentId}
                        />

                        {!!detailData?.prescriptionExternalForms?.length && <SizedBox height={Sizes.dp18} />}
                        <PrescriptionExternalFormView prescriptionForms={detailData?.prescriptionExternalForms ?? []} />
                    </View>

                    {state.isEditing && !userCenter.clinic?.isDentistryClinic && !state.disableAddPrescription && (
                        <View style={{ overflow: "hidden" }}>
                            <OutpatientAddButton
                                type={OutpatientAddButtonType.center}
                                style={Sizes.marginLTRB(Sizes.dp8, Sizes.dp18, Sizes.dp8, 0)}
                                title={"添加处方"}
                                titleStyle={[TextStyles.t16MM, { lineHeight: Sizes.dp22 }]}
                                onClick={() => {
                                    this._addNewPrescription().then();
                                }}
                            />
                        </View>
                    )}

                    <AirPharmacyOrderIdView orderId={detailData.airPharmacyOrderId} />

                    <View style={{ marginHorizontal: Sizes.dp8 }}>
                        <AIJudge />
                        <VisitTimeView />
                    </View>

                    <SizedBox height={Sizes.dp50} />
                    <View ref={kOutpatientEnd} collapsable={false} />
                </AbcScrollView>
            </View>
        );
    }

    async airPharmacyRiskDialog(): Promise<void> {
        const airPharmacyRiskDialog = await showConfirmDialog(
            "",
            <View style={{ alignItems: "center" }}>
                <AssetImageView name={"fail"} style={{ width: Sizes.dp42, height: Sizes.dp42 }} />
                <Text
                    style={[
                        TextStyles.t18MT1,
                        {
                            marginVertical: Sizes.dp16,
                            lineHeight: Sizes.dp28,
                        },
                    ]}
                >
                    {"风险提示"}
                </Text>
                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp24 })}>
                    {"智能审方发现存在用药风险，需完成风险药品双签才可完诊"}
                </Text>
            </View>,
            "确认"
        );
        if (airPharmacyRiskDialog == DialogIndex.positive) return;
    }

    /**
     * 门诊 完诊、发送/修改、保存
     */
    getRightAppBarIcons(): JSX.Element[] {
        const bloc = this.bloc,
            state = bloc.currentState,
            detailData = state.detailData;

        const needDoubleSign =
            (state.needDoubleSign && state.airPharmacyRisk) || (state.needDoubleSign && state.isDangerPrescriptionMustSign); // 选择空中药房，或开启门诊设置的风险处方强制双签 必须双签 才可提交门诊单
        const outpatientLocking = state.outpatientLocking && state.lockDetail?.employeeId != userCenter.employee?.id; // 只有门诊锁才控制按钮是否禁用，收费锁暂时不禁用，因为收费锁时允许病历修改
        if (detailData?.isVisited || detailData?.isReceived) {
            return [
                <View key={"all"} style={ABCStyles.rowAlignCenter}>
                    <View key={"setting"} style={{ marginRight: Sizes.dp8 }}>
                        {this.outpatientSettingIcons()}
                    </View>
                    {!state.notLockingIsEditing || outpatientLocking || state.needBackToEdit ? (
                        <AbcButton
                            style={[
                                {
                                    height: Sizes.dp30,
                                    backgroundColor: outpatientLocking ? Colors.bdColor : Colors.mainColor,
                                },
                                !!detailData?.isContinueDiagnose ? { minWidth: Sizes.dp52 } : { width: Sizes.dp52 },
                            ]}
                            key={"modify"}
                            onClick={() => {
                                this.bloc.requestModify();
                            }}
                            pressColor={outpatientLocking ? Colors.bdColor : Colors.mainColorPress}
                        >
                            <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                                {!!detailData?.isContinueDiagnose ? "继续接诊" : "修改"}
                            </Text>
                        </AbcButton>
                    ) : (
                        <AbcButton
                            style={{
                                height: Sizes.dp30,
                                width: Sizes.dp52,
                                backgroundColor: /* bloc.hasChanged ? Colors.theme2 : "#C6C6C6"*/ Colors.mainColor,
                            }}
                            key={"save"}
                            onClick={() => {
                                needDoubleSign ? this.airPharmacyRiskDialog() : bloc.submitChanges();
                            }}
                            pressColor={Colors.mainColorPress}
                        >
                            <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                                {"保存"}
                            </Text>
                        </AbcButton>
                    )}
                </View>,
            ];
        } else if (detailData?.isWaitVisit) {
            const send = state.chargeConfig?.autoSendOrderInfoSwitch == 1 && detailData?.isOnline == 1;
            return [
                <View key={"all"} style={ABCStyles.rowAlignCenter}>
                    <View key={"setting"} style={{ marginRight: Sizes.dp8 }}>
                        {this.outpatientSettingIcons()}
                    </View>
                    <AbcButton
                        style={{
                            height: Sizes.dp30,
                            width: Sizes.dp52,
                            backgroundColor: /* bloc.hasChanged ? Colors.theme2 : "#C6C6C6"*/ Colors.mainColor,
                        }}
                        key={"submit"}
                        onClick={() => {
                            needDoubleSign ? this.airPharmacyRiskDialog() : bloc.submitChanges();
                        }}
                        pressColor={Colors.mainColorPress}
                    >
                        <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                            {!send ? "完诊" : "发送"}
                        </Text>
                    </AbcButton>
                </View>,
            ];
        }
        return [];
    }

    onLongClick(): void {
        LogUtils.liveLog(JSON.stringify(this.bloc.currentState.detailData), "outpatient-detail");
    }

    async _onQuickScanPay(patientOrderId?: string): Promise<void> {
        const { canSendToPatient } = this.bloc.currentState;
        await PushToPayDialog.show({
            patientOrderId,
            type: PushScanSceneType.outpatient,
            isShowSendBtn: canSendToPatient,
            pushToPatient: (chargeSheetId) => {
                this.bloc.requestPushOrderToPatient(chargeSheetId ?? "");
            },
            onPaySuccess: () => {
                this.bloc.requestFreshInit();
            },
        });
    }

    /**
     * 处方医嘱
     */
    async _addNewPrescription(): Promise<void> {
        const _bloc = this.bloc;
        if (!_bloc.currentState.isEditing) return;

        const outpatientConfig = _bloc.currentState.outpatientConfig;
        const type = await AbcDialog.showBottomSheet(<PrescriptionTypeSelectView prescriptionConfig={outpatientConfig?.prescription} />);

        if (type) {
            switch (type) {
                case "western": {
                    _bloc.requestAddPrescriptionWestern(); // 成药处方
                    return;
                }
                case "chinese": {
                    _bloc.requestAddPrescriptionChinese(); // 中药处方
                    return;
                }
                case "infusion": {
                    _bloc.requestAddPrescriptionInfusion(); // 输液/注射处方
                    return;
                }
                case "external": {
                    _bloc.requestAddPrescriptionExternal(); // 外治处方
                    break;
                }
                case "template": {
                    _bloc.requestInvokeTemplate(); // 处方模板
                }
            }
        }
    }

    /**
     * 生成门诊操作列表
     * @private
     */
    private _createMenuItemsList(): MenuItem<number>[] {
        const state = this.bloc.currentState;
        const menuItems: MenuItem<number>[] = [];

        if (!state.detailData) return menuItems;

        const isOnline = state.detailData?.isOnline == 1; //网诊
        const hasUnChargedItem = OutpatientUtils.hasUnChargedItem(state.detailData); // 未收费
        const { isEditing, isMeetpushPayment, disableAddPrescription } = state;
        const { isVisited, statusName, isWaitVisit, chargeStatus } = state.detailData;
        const isAnonymousPatients = !!state.detailData?.patient?.name;
        const chargeConfig = state.chargeConfig;

        if (!isOnline && !isVisited && statusName != "待诊") {
            menuItems.push(
                new MenuItem({
                    value: OutpatientMenuItemValue.deleteSheet,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp22} style={{ bottom: -Sizes.dp1 }} />,
                    text: "删除接诊",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        if (isWaitVisit && isEditing && !!userCenter.clinic?.isDoctorAssist) {
            menuItems.push(
                new MenuItem({
                    value: OutpatientMenuItemValue.preDiagnosis,
                    icon: (
                        <IconFontView
                            name={"yuzhen"}
                            color={Colors.T1}
                            size={Sizes.dp19}
                            style={{
                                bottom: -Sizes.dp4,
                                paddingRight: Sizes.dp3,
                                width: Sizes.dp22,
                                height: Sizes.dp25,
                            }}
                        />
                    ),
                    text: "完成预诊",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
            if (statusName == "待诊") {
                menuItems.push(
                    new MenuItem({
                        value: OutpatientMenuItemValue.preDiagnosisCancel,
                        icon: (
                            <IconFontView
                                name={"cancel"}
                                color={Colors.T1}
                                size={Sizes.dp19}
                                style={{
                                    bottom: -Sizes.dp4,
                                    paddingRight: Sizes.dp3,
                                    width: Sizes.dp22,
                                    height: Sizes.dp25,
                                }}
                            />
                        ),
                        text: "取消",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        }

        if (isEditing) {
            if (userCenter.clinic?.isDentistryClinic && !disableAddPrescription) {
                menuItems.push(
                    new MenuItem({
                        value: OutpatientMenuItemValue.addPrescription,
                        icon: (
                            <IconFontView
                                name={"add"}
                                color={Colors.T1}
                                size={Sizes.dp18}
                                style={{ bottom: -Sizes.dp5, width: Sizes.dp22, height: Sizes.dp24 }}
                            />
                        ),
                        text: "添加处方",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        }

        if (hasUnChargedItem) {
            if (isEditing && !userCenter.clinic?.isDentistryClinic) {
                menuItems.push(
                    new MenuItem({
                        value: OutpatientMenuItemValue.preview,
                        icon: (
                            <IconFontView
                                name={"books"}
                                color={Colors.T1}
                                size={Sizes.dp20}
                                style={{ bottom: -Sizes.dp3, width: Sizes.dp22, height: Sizes.dp24 }}
                            />
                        ),
                        text: "费用预览",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        }

        if ((!isEditing || isVisited) && !isOnline) {
            if (
                isMeetpushPayment &&
                OutpatientUtils.canEditWithChargeStatus(chargeStatus) &&
                isAnonymousPatients &&
                chargeConfig?.doctorCanPushChargeSheetSwitch
            ) {
                menuItems.push(
                    new MenuItem({
                        value: OutpatientMenuItemValue.pushPayment,
                        icon: (
                            <IconFontView
                                name={"push"}
                                color={Colors.T1}
                                size={Sizes.dp18}
                                style={{ bottom: -Sizes.dp5, width: Sizes.dp22, height: Sizes.dp24 }}
                            />
                        ),
                        text: "推送支付",
                        textStyle: { fontSize: Sizes.dp14 },
                    })
                );
            }
        }

        if (statusName != "草稿" && !!userCenter.clinic?.isDentistryClinic) {
            menuItems.push(
                new MenuItem({
                    value: OutpatientMenuItemValue.visit,
                    icon: (
                        <IconFontView
                            name={"visit"}
                            color={Colors.T1}
                            size={Sizes.dp20}
                            style={{ bottom: -Sizes.dp3, width: Sizes.dp22, height: Sizes.dp24 }}
                        />
                    ),
                    text: "复诊预约",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }

        menuItems.push(
            new MenuItem({
                value: OutpatientMenuItemValue.setting,
                icon: <IconFontView name={"set"} color={Colors.T1} size={Sizes.dp22} style={{ bottom: -Sizes.dp1 }} />,
                text: "门诊设置",
                textStyle: { fontSize: Sizes.dp14 },
            })
        );

        return menuItems;
    }
}

/**
 * 医生信息修改
 */
const InvoiceDoctorCard = ({ isOnline }: { isOnline: boolean }) => {
    const _inputPriceWidth = Sizes.dp91;
    const Context = useContext<OutPatientInvoicePageBloc>(OutPatientInvoicePageBloc.Context);
    const bloc = OutPatientInvoicePageBloc.fromContext(Context);
    const state = bloc.currentState;
    let doctorName: string | undefined = state.detailData?.doctorName ?? "";
    let departmentName: string | undefined = "";
    if (!!state.detailData?.departmentName) {
        departmentName = `-${state.detailData?.departmentName}`;
    }
    //挂号过来的可能会有部门，但是没有医生
    if (!state.detailData?.doctorId) doctorName = "";

    const doctorMutable = state.isEditing && OutpatientUtils.canEditWithChargeStatus(state.detailData?.chargeStatus);
    const registrationFeeMutable =
        state.shouldRegisteredBargainSwitch &&
        state.isEditing &&
        OutpatientUtils.canEditWithChargeStatus(state.detailData?.registrationFeeStatus) &&
        !isOnline;
    const _registrationFeeInputStyle = registrationFeeMutable ? TextStyles.t16NT1 : TextStyles.t16NT2.copyWith({ color: Colors.t2 });

    const showBottomPadding = doctorMutable || registrationFeeMutable;
    return (
        <View
            style={[
                ABCStyles.rowAlignCenter,
                {
                    paddingTop: Sizes.dp16,
                    paddingBottom: doctorMutable ? Sizes.dp11 : Sizes.dp16,
                    paddingHorizontal: Sizes.dp16,
                    height: Sizes.dp58,
                },
            ]}
        >
            <AbcView
                style={[
                    doctorMutable ? ABCStyles.bottomLine : {},
                    ABCStyles.rowAlignCenter,
                    {
                        paddingBottom: showBottomPadding ? Sizes.dp9 : undefined,
                        borderColor: state.showErrorHint && !state.detailData?.doctorName ? Colors.errorBorder : Colors.dividerLineColor,
                        backgroundColor: state.showErrorHint && !state.detailData?.doctorName ? Colors.errorBorderBg : undefined,
                        flex: state.enableRegistrationCategories ? 40 : 51,
                    },
                ]}
                onClick={() => {
                    if (doctorMutable) {
                        bloc.requestSelectDoctor();
                    }
                }}
            >
                <Text
                    style={[
                        TextStyles.t16MT1,
                        {
                            lineHeight: Sizes.dp22,
                        },
                    ]}
                    numberOfLines={1}
                >
                    {doctorName || "选择医生"}
                </Text>
                <Text
                    style={[
                        TextStyles.t16MT1,
                        {
                            lineHeight: Sizes.dp22,
                            flexShrink: 1,
                        },
                    ]}
                    numberOfLines={1}
                >
                    {departmentName}
                </Text>
            </AbcView>

            {!isOnline && (
                <AbcView
                    style={[
                        doctorMutable ? ABCStyles.bottomLine : {},
                        ABCStyles.rowAlignCenter,
                        {
                            paddingBottom: showBottomPadding ? Sizes.dp9 : undefined,
                            marginHorizontal: Sizes.dp16,
                            flex: state.enableRegistrationCategories ? 21 : 10,
                        },
                    ]}
                    onClick={() => {
                        if (state.enableRegistrationCategories) {
                            if (doctorMutable) {
                                bloc.requestModifyRevisitStatusAndCategories();
                            }
                        } else {
                            if (state.canChangeRevisitedStatus && doctorMutable) {
                                bloc.requestModifyRevisitStatus();
                            }
                        }
                    }}
                >
                    {state.enableRegistrationCategories ? (
                        <AbcText style={[TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp22 })]}>
                            {[
                                state.detailData?.getRevisitStatusName,
                                state.enableRegistrationCategories ? state.detailData?.registrationCategoryDisplay : undefined,
                            ]
                                .filter((t) => !!t)
                                .join("/")}
                        </AbcText>
                    ) : (
                        <AbcText style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })]}>
                            {state.detailData?.getRevisitStatusName ?? ""}
                        </AbcText>
                    )}
                </AbcView>
            )}

            <View
                style={[
                    registrationFeeMutable ? ABCStyles.bottomLine : {},
                    ABCStyles.rowAlignCenter,
                    {
                        paddingBottom: showBottomPadding ? Sizes.dp9 : undefined,
                        flex: 23,
                    },
                    DeviceUtils.isAndroid() ? { width: _inputPriceWidth } : {},
                ]}
            >
                <Text style={_registrationFeeInputStyle}>{abcI18Next.t("¥")}</Text>
                <AbcTextInput
                    selectTextOnFocus={false}
                    editable={registrationFeeMutable}
                    returnKeyType={"done"}
                    multiline={false}
                    syncTextOnBlur={true} //解决更改值后切换医生props不改变问题
                    numberOfLines={1}
                    placeholder={"点击输入"}
                    defaultValue={ABCUtils.formatPrice(state.detailData?.registrationFee ?? 0)}
                    formatter={PrecisionLimitFormatter(2)}
                    customKeyboardBuilder={new NumberKeyboardBuilder({})}
                    style={{
                        flex: 1,
                        underlineColorAndroid: Colors.white,
                        backgroundColor: Colors.white,
                        height: Sizes.dp22,
                        maxWidth: _inputPriceWidth,
                        ..._registrationFeeInputStyle,
                        paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                    }}
                    textShareMenuVisible={false}
                    placeholderTextColor={Colors.T4}
                    onChangeText={(value) => {
                        if (registrationFeeMutable) {
                            bloc.requestUpdateRegistrationFee(Number(value));
                        }
                    }}
                />
            </View>
        </View>
    );
};

const AIJudge = () => {
    const Context = useContext<OutPatientInvoicePageBloc>(OutPatientInvoicePageBloc.Context);
    const bloc = OutPatientInvoicePageBloc.fromContext(Context);
    const state = bloc.currentState;
    if (state.aiVerify == null || (state.detailData?.isVisited && !state.isEditing)) return <View />;
    const level = state.aiVerify?.level;
    let _color, style, text: string | undefined;

    if (level == VerifyLevel.pass) {
        _color = Colors.freshGreen;
        style = TextStyles.t14NFG;
        text = " · 通过";
    } else if (level <= VerifyLevel.middle) {
        _color = Colors.Y2;
        style = TextStyles.t14NY2;
        text = " · 提醒";
    } else {
        _color = Colors.R2;
        style = TextStyles.t14NR2;
        text = " · 风险";
    }

    const createHandlePanel = ({
        view,
        handleText,
        disable,
        handleCallback,
    }: {
        view: JSX.Element[] | undefined;
        handleText: string;
        disable: boolean;
        handleCallback: () => void;
    }) => {
        if (!view || !view.length) return undefined;
        return (
            <View style={{ marginTop: Sizes.dp8, borderRadius: Sizes.dp6, backgroundColor: "rgba(170,171,179,0.10)" }}>
                <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20, Sizes.dp16, Sizes.dp20)]}>{view}</View>
                <DividerLine lineHeight={Sizes.dpHalf} color={Colors.bdColor} style={{ marginHorizontal: Sizes.dp16 }} />
                <AbcView
                    onClick={handleCallback}
                    style={[ABCStyles.rowAlignCenter, { justifyContent: "center", paddingVertical: Sizes.dp16 }]}
                >
                    <Text style={TextStyles.t16NM.copyWith({ color: !disable ? Colors.mainColor : Colors.t3 })}>{`${handleText}`}</Text>
                </AbcView>
            </View>
        );
    };

    const createAiVerifyCheckItems = (ai: VerifyItem, index: number, showLineThrough = false) => {
        const level = ai.mapToVerifyLevel();
        let _color: Color,
            iconName = "Attention1";
        if (level == VerifyLevel.pass) {
            _color = Colors.freshGreen;
            iconName = "prescription_verfiy_pass";
        } else if (level <= VerifyLevel.middle) {
            _color = Colors.Y2;
        } else {
            _color = Colors.R2;
        }

        if (showLineThrough) {
            _color = Colors.t4;
        }

        return (
            <View key={index} style={[{ flexDirection: "row", marginBottom: Sizes.dp12 }]}>
                <View style={{ top: 0, marginRight: Sizes.dp8 }}>
                    {iconName == "Attention1" ? (
                        <IconFontView size={Sizes.dp16} name={"Attention1"} color={_color} />
                    ) : (
                        <AssetImageView name={iconName} style={{ width: Sizes.dp16, height: Sizes.dp16 }} />
                    )}
                </View>
                <Text
                    style={[
                        TextStyles.t14NT1.copyWith({ color: _color }),
                        { flex: 1 },
                        showLineThrough ? { textDecorationLine: "line-through" } : {},
                    ]}
                >
                    {ai.detail instanceof VerifyItemDetail ? ai.detail?.reason ?? "" : ""}
                </Text>
            </View>
        );
    };

    const allHandlePanel: JSX.Element[] = [];
    // 处方双签
    const doubleSignStatus = state.needDoubleSign ? "确认" : "取消";
    const doubleSignView = createHandlePanel({
        view: state.aiVerify?.checkItems
            ?.filter((item) => !(item.detail as VerifyItemDetail)?.prescriptionName)
            .map((ai, index) => {
                return createAiVerifyCheckItems(ai, index);
            }),
        handleText: `${doubleSignStatus}签字`,
        disable: !state.needDoubleSign,
        handleCallback: () =>
            !state.detailData?.doctorId
                ? Toast.show("请选择医生", { warning: true })
                : bloc.requestViewAiVerifyDoubleSignResult(state.needDoubleSign),
    });
    !!doubleSignView && allHandlePanel.push(doubleSignView);

    state.aiVerify?.checkItems
        ?.filter((item) => item.name == "ControlledNarcoticAndToxicRule" && item.mapToVerifyLevel() != VerifyLevel.pass)
        .map((ai, index) => {
            const { prescriptionName, index: sortIndex = 0, psychotropicNarcoticType } = ai.detail as VerifyItemDetail;
            let disable = false;
            switch (prescriptionName) {
                case "中西成药处方": {
                    disable = state.detailData!.prescriptionWesternForms![sortIndex].psychotropicNarcoticType == psychotropicNarcoticType;
                    break;
                }
                case "外治处方": {
                    disable = state.detailData!.prescriptionExternalForms![sortIndex].psychotropicNarcoticType == psychotropicNarcoticType;
                    break;
                }
                case "中药处方": {
                    disable = state.detailData!.prescriptionChineseForms![sortIndex].psychotropicNarcoticType == psychotropicNarcoticType;
                    break;
                }
                case "输注处方": {
                    disable = state.detailData!.prescriptionInfusionForms![sortIndex].psychotropicNarcoticType == psychotropicNarcoticType;
                    break;
                }
            }
            allHandlePanel.push(
                createHandlePanel({
                    view: [createAiVerifyCheckItems(ai, index, disable)],
                    handleText: "快速设置",
                    disable: disable,
                    handleCallback: () => {
                        ai.detail instanceof VerifyItemDetail && bloc.requestChangePrescriptionType(ai.detail);
                    },
                })!
            );
        });

    return (
        <AbcView style={{ marginTop: Sizes.dp18 }}>
            <AbcText
                style={style.copyWith({ color: _color })}
                onClick={() => {
                    bloc.requestViewAiVerifyResult();
                }}
            >
                {`智能审方${text}`}
            </AbcText>
            {level !== VerifyLevel.pass && !!allHandlePanel?.length && <AbcCollapse showCount>{allHandlePanel}</AbcCollapse>}
        </AbcView>
    );
};
const formatPatientOrderNo = (value = "") => {
    const srcStr = "00000000";
    if (!value) {
        return srcStr;
    }
    return `${srcStr}${value}`.slice(-8);
};
const VisitTimeView = () => {
    const Context = useContext<OutPatientInvoicePageBloc>(OutPatientInvoicePageBloc.Context);
    const bloc = OutPatientInvoicePageBloc.fromContext(Context);
    const state = bloc.currentState;
    const patientOrderNo = formatPatientOrderNo(state.detailData?.patientOrderNo?.toString() ?? "");
    // 草稿不显示诊号
    const isDraft = state.detailData?.statusName == "草稿";
    if (isDraft && !state.detailData?.diagnosedDate) return <View />;
    return (
        <View style={{ marginTop: Sizes.dp16 }}>
            {!isDraft && (
                <AbcView style={{ paddingBottom: Sizes.dp8 }}>
                    <AbcText
                        style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp16 })}
                        onLongClick={async () => {
                            await Clipboard.setString(patientOrderNo);
                            await Toast.show(`已复制诊号到剪切板`, { success: true });
                        }}
                    >
                        {`诊号：${patientOrderNo}`}
                    </AbcText>
                </AbcView>
            )}

            {!!state.detailData?.diagnosedDate && (
                <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp16 })}>
                    {`就诊时间：${state.detailData?.diagnosedDate.format("yyyy-MM-dd HH:mm:ss")}`}
                </Text>
            )}
        </View>
    );
};

/**
 * 门诊页新增项目按钮
 */
enum OutpatientAddButtonType {
    center, // 居中显示
    flexStart, // 从左至右
}

interface OutpatientAddButtonProps {
    style?: Style | Style[];
    title: string; // 主标题
    titleStyle?: Style | Style[];
    onClick?: () => void;
    type?: number;
}

const OutpatientAddButton = (props: OutpatientAddButtonProps) => {
    return (
        <AbcView
            style={[
                {
                    flex: 1,
                    borderRadius: Sizes.dp6,
                    backgroundColor: Colors.white,
                    overflow: "hidden",
                },
                flattenStyles(props.style),
            ]}
            onClick={() => {
                props.onClick?.();
            }}
        >
            {props.type == OutpatientAddButtonType.center ? (
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            justifyContent: "center",
                            paddingVertical: Sizes.dp16,
                            overflow: "hidden",
                        },
                    ]}
                >
                    <IconFontView style={{ paddingTop: Sizes.dp3 }} name={"add"} color={Colors.mainColor} size={Sizes.dp14} />
                    <SizedBox width={Sizes.dp4} />
                    <AbcText style={[{ paddingTop: Sizes.dp2, color: Colors.mainColor }, flattenStyles(props.titleStyle)]}>
                        {props.title}
                    </AbcText>
                </View>
            ) : (
                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-start", padding: Sizes.dp16 }]}>
                    <AbcText style={props.titleStyle}>{props.title}</AbcText>
                </View>
            )}
        </AbcView>
    );
};

/**
 * 门诊 医生名称搜索弹窗
 *
 */

interface AppointmentRegistrationInformationViewProps {
    detail: OutpatientInvoiceDetail;
    registrationDetail?: RegistrationDetail;
    registrationConfig?: DentistryConfig;
}

class AppointmentRegistrationInformationView extends BasePage<AppointmentRegistrationInformationViewProps> {
    getShowStatusBar(): boolean {
        return false;
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    renderContent(): JSX.Element {
        const { created } = this.props.detail;
        const registrationConfig = this.props.registrationConfig;
        const registrationFormItem = this.props.registrationDetail?.registrationFormItem;
        const reserveTime = registrationFormItem?.reserveTime;
        const timeOfDay = registrationFormItem?.timeOfDay;
        const orderNo = registrationFormItem?.orderNo;

        const referralSource = this.props.registrationDetail?.referralSource;
        let reserveDateViewStr = `${TimeUtils.formatDate(created, "MM月dd日")} ${TimeUtils.getDayOfWeek(created, "周")} `;
        if (timeOfDay && !!registrationConfig?.isFixedMode) {
            reserveDateViewStr += `${timeOfDay}${orderNo ? `${orderNo}号 ` : ""}`;
        }
        if (reserveTime?.start && reserveTime.end && !!registrationConfig?.isFlexibleMode) {
            reserveDateViewStr += `${reserveTime?.start}~${reserveTime?.end}`;
        }

        const registrationProducts = this.props.registrationDetail?.registrationFormItem?.registrationProducts;
        const registrationProductsStr = registrationProducts?.map((it) => it.displayName)?.join(StringUtils.specialComma);

        const visitSourceRemark = this.props.registrationDetail?.visitSourceRemark;

        const { patient } = this.props.detail;

        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {BottomSheetHelper.createTitleBar("预约挂号信息")}
                <ScrollView contentContainerStyle={Sizes.paddingLTRB(Sizes.dp18, Sizes.dp16, Sizes.dp18, Sizes.dp16)}>
                    <View>
                        {this.renderARInformationItemView(
                            "turn",
                            `${referralSource?.doctorName}${referralSource?.departmentName ? `-${referralSource?.departmentName}` : ""}于 ${
                                referralSource?.referralTime ? new Date(referralSource?.referralTime).format("yyyy-MM-dd") : ""
                            } 转出`
                        )}
                    </View>
                    <View>
                        {this.renderARInformationItemView(
                            "doctor-2",
                            `${registrationFormItem?.doctorName}${
                                registrationFormItem?.departmentName ? `-${registrationFormItem?.departmentName}` : ""
                            } ${this.props.registrationDetail?.__revisitStatusName}`
                        )}
                    </View>
                    <View>{this.renderARInformationItemView("time_1", reserveDateViewStr)}</View>
                    {!!registrationProductsStr?.length && (
                        <View>{this.renderARInformationItemView("project", StringUtils.stringBr2N(registrationProductsStr))}</View>
                    )}
                    {!!visitSourceRemark?.length && <View>{this.renderARInformationItemView("remark", visitSourceRemark)}</View>}
                    {!!patient?.patientSource && (
                        <Text numberOfLines={1} style={[TextStyles.t16NT6, { flexShrink: 1, marginLeft: Sizes.dp27 }]}>
                            {`推荐：${patient.patientSource?.name ?? ""} - ${patient?.patientSource?.sourceFromName ?? ""}`}
                        </Text>
                    )}
                </ScrollView>
            </View>
        );
    }

    private renderARInformationItemView(icon: string, text = ""): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp16 }]}>
                <IconFontView name={icon} size={Sizes.dp14} color={Colors.T6} />
                <SizedBox width={Sizes.dp11} />
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })]}>{text}</Text>
            </View>
        );
    }
}
