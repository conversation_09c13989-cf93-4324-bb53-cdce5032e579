/**
 * create by deng<PERSON><PERSON>
 * desc:
 * create date 2020/6/30
 */
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IconFontView, Sized<PERSON>ox, Tab, Ta<PERSON> } from "../../base-ui";
import { ScrollView, Text, View } from "@hippy/react";
import { ProductAddPageBloc } from "./product-add-page-bloc";
import { NumberUtils } from "../../common-base-module/utils";
import { MedicineAddGroup } from "../medicine-add-page/medicine-add-page";
import { MedicineAddType } from "../data/outpatient-const";
import { AntibioticEnum, GoodsInfo, GoodsType, GoodsTypeId } from "../../base-business/data/beans";
import { BlocBuilder } from "../../bloc";
import _ from "lodash";
import WillPopListener from "../../base-ui/views/will-pop-listener";
import { ABCStyles, ABCStyleSheet, Color, Colors, Sizes, TextStyles } from "../../theme";
import { BaseComponent } from "../../base-ui/base-component";
import sizes from "../../theme/sizes";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { CustomInput } from "../../base-ui/input/custom-input";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { ABCNetworkPageContentStatus, BaseBlocPage, NetworkView } from "../../base-ui/base-page";
import { ValueHolder } from "../../base-ui/utils/value-holder";
import { AbcView } from "../../base-ui/views/abc-view";
import { MedicineSearchInput } from "../../base-ui/views/medicine-search-input";
import { StockNotEnoughTextView } from "../../base-ui/views/stock-not-enough-text-view";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { Completer } from "../../common-base-module/async/completer";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { AbcBasePanel } from "../../base-ui/abc-app-library";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { MedicineUsage } from "../medicine-add-page/medicine-add-page-bean";
import { ProductAddPageSearch } from "./product-add-page-search";
import { ProductAddCardPage } from "./product-add-card-page";
import { PrescriptionActiveGroupView } from "../medicine-add-page/views/prescription-active-group";
import { DiagnosisProjectSearchView } from "../views/medicine-search/medicine-search-view";
import { ProductMedicineUsageParams } from "./product-add-page-bean";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { AbcButton } from "../../base-ui/views/abc-button";
import { userCenter } from "../../user-center";
import abcI18Next from "../../language/config";
import { SolidIconFontView } from "../outpatient-views";
import { OutpatientInvoiceDetail } from "../data/outpatient-beans";
import { AbcPrescriptionItemView, PrescriptionAdditionalInfo } from "../../base-ui/abc-prescription-item-view/abc-prescription-item-view";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { DiagnosisTreatmentConfig } from "../../data/online-property-config-provder";

export class MedicineAddPageCategoryItem {
    isDentistry: boolean;
    isHospital: boolean;

    id: number;
    title: string;

    constructor(id: number, title: string, isDentistry?: boolean, isHospital = false) {
        this.id = id;
        this.title = title;
        this.isDentistry = isDentistry ?? false;
        this.isHospital = isHospital;
    }
}

export const kCategoryItems = [
    new MedicineAddPageCategoryItem(MedicineAddType.examination, "检查检验"),
    new MedicineAddPageCategoryItem(MedicineAddType.treatment, "治疗理疗", true),
    new MedicineAddPageCategoryItem(MedicineAddType.medicalMaterial, "医疗器械"),
    new MedicineAddPageCategoryItem(MedicineAddType.goods, "商品"),
    new MedicineAddPageCategoryItem(MedicineAddType.others, "其他费用"),
    new MedicineAddPageCategoryItem(MedicineAddType.package, "套餐"),
];

const kHospitalCategoryItems = [
    new MedicineAddPageCategoryItem(MedicineAddType.examination, "检查", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.examinationTest, "检验", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.treatment, "治疗", true, true),
    new MedicineAddPageCategoryItem(MedicineAddType.nurse, "护理", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.surgery, "手术", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.medicalMaterial, "耗材", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.goods, "商品", false, true),
    new MedicineAddPageCategoryItem(MedicineAddType.package, "套餐", false, true),
];

const styles = ABCStyleSheet.create({
    categoryItemContainer: {
        height: Sizes.listItemHeight,
        paddingLeft: Sizes.listHorizontalMargin,
        justifyContent: "center",
    },
    listItemContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        paddingVertical: Sizes.dp16,
    },
    listItemUnitText: { ...TextStyles.t12NT6, marginLeft: 8 },
    closeButtonContainer: { paddingRight: 11, paddingVertical: Sizes.dp6 },
});

export interface ProductAddPageProps {
    medicineUsageParams: ProductMedicineUsageParams;
    searchClinicId?: string;
    detailData?: OutpatientInvoiceDetail;
    allowAntibiotic?: AntibioticEnum[]; // 当前医生允许开出的限制药品类型
    disableAddPrescription?: boolean; // 是否禁止新增处方
    diagnosisTreatment?: DiagnosisTreatmentConfig; // 诊疗项目配置
}

export class ProductAddPage extends BaseBlocPage<ProductAddPageProps, ProductAddPageBloc> {
    _currentCategory: MedicineAddPageCategoryItem;
    private _scrollViewRef?: ScrollView | null;

    //键盘弹出时，scrollview可能还没有layout 完成，在触发滚动到底部时无法真正滚动到底部，通过设置这个
    //promise，在scrollview的最后一个child layout后触发
    private _pendingScrollLayoutCompleter?: Completer<void>;

    constructor(props: ProductAddPageProps) {
        super(props);
        const type = this.props.medicineUsageParams.type;
        let _kCategoryItems = kCategoryItems;
        if (userCenter.clinic?.isNormalHospital) {
            _kCategoryItems = kHospitalCategoryItems;
        }
        if (type != undefined) {
            this._currentCategory = _kCategoryItems.find((item) => item.id == type)!;
        } else {
            this._currentCategory = _kCategoryItems[0];
        }
        this.bloc = new ProductAddPageBloc({
            props: props?.medicineUsageParams,
            searchClinicId: props?.searchClinicId,
            allowAntibiotic: props?.allowAntibiotic,
        });
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        return `${this.props.medicineUsageParams.title}`;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const isInSearchMode = this.bloc.currentState.isInSearchMode;
        if (isInSearchMode) return [];
        return [
            <AbcView
                key={"finish"}
                onClick={() => {
                    this.bloc.requestSubmit();
                }}
                style={{ padding: 8 }}
            >
                <Text style={TextStyles.t16NW}>完成</Text>
            </AbcView>,
        ];
    }

    getAppBarBgColor(): Color {
        return Colors.mainColor;
    }

    getAppBarBtnColor(): Color {
        return Colors.white;
    }

    getStatusBarColor(): Color {
        return this.getAppBarBgColor();
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    getAppBarTitleColor(): string {
        return Colors.white;
    }

    getCountAndPrice(): JSX.Element {
        const _price = this.bloc.currentState.totalPrice;
        const _count = this.bloc.currentState.productCount;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    {
                        alignItems: "center",
                        backgroundColor: Colors.mainColor,
                        height: Sizes.dp30,
                    },
                ]}
            >
                <Text style={[TextStyles.t14NW]}>
                    {`${_count}种，${abcI18Next.t("¥")}${_price ? NumberUtils.formatMaxFixed(_price, 2) : "0.00"}`}
                </Text>
            </View>
        );
    }

    onBackClick(fromEdgeGesture?: boolean): void {
        //直接保存返回
        this.bloc.requestBackPage(fromEdgeGesture);
    }

    _renderSelectListView(): JSX.Element {
        const _bloc = this.bloc;
        return (
            <View style={{ flex: 1 }}>
                <ScrollView
                    style={{ flex: 1 }}
                    showsVerticalScrollIndicator={false}
                    ref={(ref) => {
                        this._scrollViewRef = ref;
                    }}
                    onScrollBeginDrag={() => {
                        AbcTextInput.focusInput?.blur();
                    }}
                >
                    {this._renderSelectList(_bloc.currentState.groups[0])}
                    <View
                        collapsable={false}
                        style={{ height: 2 }}
                        onLayout={
                            (/*evt*/) => {
                                this._pendingScrollLayoutCompleter?.resolve();
                            }
                        }
                    />
                </ScrollView>
            </View>
        );
    }

    _renderSelectList(medicineAddGroup: MedicineAddGroup): JSX.Element {
        const state = this.bloc.currentState;
        return (
            <View style={[{ flexGrow: 1, marginBottom: 10 }]}>
                {medicineAddGroup.selectedMedicines?.map((item, index) => {
                    const _select = state.isSelect(medicineAddGroup, item);
                    return (
                        <ProductInfoCell
                            key={index}
                            select={_select}
                            goodInfo={item}
                            goodUsage={medicineAddGroup.inputUsages.get(item)!}
                            group={medicineAddGroup}
                        />
                    );
                })}
            </View>
        );
    }

    _renderDefaultSearch(): JSX.Element {
        const { enableSwitchType = true, switchTypes } = this.props.medicineUsageParams;
        let categoryList = undefined;
        if (switchTypes?.length) {
            categoryList = [];
            switchTypes.forEach((item) => {
                switch (item) {
                    case MedicineAddType.examination:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.examination, "检查检验"));
                        break;
                    case MedicineAddType.treatment:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.treatment, "治疗理疗"));
                        break;
                    case MedicineAddType.medicalMaterial:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.medicalMaterial, "医疗器械"));
                        break;
                    case MedicineAddType.goods:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.goods, "商品"));
                        break;
                    case MedicineAddType.others:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.others, "其他费用"));
                        break;
                    case MedicineAddType.package:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.package, "套餐"));
                }
            });
        }
        return (
            <View style={{ flexDirection: "row", flex: 1 }}>
                {enableSwitchType && (
                    <_CategoryPicker
                        selectCategory={this._currentCategory.id}
                        categoryList={categoryList}
                        onChanged={(newCategory) => this._onCategoryChanged(newCategory)}
                    />
                )}
                <_GoodsSearchDisplayView
                    onClickItem={(goods) => {
                        this.bloc.requestAddSelectList(goods);
                    }}
                />
            </View>
        );
    }

    private _onCategoryChanged(category: MedicineAddPageCategoryItem) {
        if (this._currentCategory.id == category.id) return;
        this._currentCategory = category;
        this.bloc.requestUpdateSearchType(category.id);
    }

    async scrollToBottom(now = false): Promise<void> {
        if (now) {
            this._scrollViewRef?.scrollTo(0, Number.MAX_SAFE_INTEGER, true);
            return;
        }

        this._pendingScrollLayoutCompleter = new Completer<void>();
        //添加一个200ms超时，是为了防止调用scrollToBottom时，已经提成好了，不会再resolve_pendingScrollLayoutCompleter引起滚动失效的问题
        await Promise.race([delayed(200).toPromise(), this._pendingScrollLayoutCompleter.promise]).catchIgnore();
        this._scrollViewRef?.scrollTo(0, Number.MAX_SAFE_INTEGER, true);
    }

    private _searchInput?: AbcTextInput | null;

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            medicineType = state.medicineType;
        if (_.isNil(medicineType)) return <View>{medicineType}</View>;
        return (
            <View style={{ flex: 1 }}>
                <WillPopListener
                    onWillPop={(fromEdgeGesture) => {
                        this.onBackClick(fromEdgeGesture);
                    }}
                />
                {this.getCountAndPrice()}
                {state.isInSearchMode ? this._renderDefaultSearch() : this._renderSelectListView()}
                <MedicineSearchInput
                    autoFocus={!this.props.medicineUsageParams.selectMedicine}
                    placeholder={"输入项目拼音码"}
                    ref={(searchInput) => {
                        this._searchInput = searchInput?._textInput;
                        this._searchInput && this.bloc.requestCacheSearchInput(this._searchInput);
                    }}
                    onFocus={() => {
                        this.bloc.requestUpdateSearchFocus(true);
                        delayed(100).subscribe(() => {
                            this._scrollViewRef?.scrollTo(0, Number.MAX_SAFE_INTEGER, true);
                        });
                    }}
                    onBlur={() => {
                        this.bloc.requestUpdateSearchFocus(false);
                    }}
                    onChange={(text) => {
                        this.bloc.requestSearchGoods(text);
                    }}
                />
            </View>
        );
    }
}

export class NewProductAddPage extends BaseBlocPage<ProductAddPageProps, ProductAddPageBloc> {
    _currentCategory: MedicineAddPageCategoryItem;
    private _scrollViewRef?: ScrollView | null;
    private _searchInput?: AbcTextInput | null;

    //键盘弹出时，scrollview可能还没有layout 完成，在触发滚动到底部时无法真正滚动到底部，通过设置这个
    //promise，在scrollview的最后一个child layout后触发
    private _pendingScrollLayoutCompleter?: Completer<void>;

    constructor(props: ProductAddPageProps) {
        super(props);
        const type = this.props.medicineUsageParams.type;
        let _kCategoryItems = kCategoryItems;
        if (userCenter.clinic?.isNormalHospital) {
            _kCategoryItems = kHospitalCategoryItems;
        }
        if (type != undefined) {
            this._currentCategory = _kCategoryItems.find((item) => item.id == type)!;
        } else {
            this._currentCategory = _kCategoryItems[userCenter.clinic?.isDentistryClinic ? 1 : 0];
        }
        this.bloc = new ProductAddPageBloc({
            props: props?.medicineUsageParams,
            detailData: props?.detailData,
            allowAntibiotic: props?.allowAntibiotic,
        });
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    // getAppBarTitle(): string {
    //     return `${this.props.medicineUsageParams.title}`;
    // }

    pageName(): string | undefined {
        return this.props.medicineUsageParams.title ?? "诊疗项目";
    }

    getAppBarTitleColor(): string {
        return Colors.black;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return (
            <View style={{ alignItems: "center", marginBottom: Sizes.dp2 }}>
                <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp22 }]}>{this.props.medicineUsageParams.title ?? ""}</Text>
            </View>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const isInSearchMode = this.bloc.currentState.isInSearchMode;
        if (isInSearchMode) return [];
        return [
            <AbcView
                key={"finish"}
                onClick={() => {
                    this.bloc.requestSubmit();
                }}
                style={{
                    paddingHorizontal: Sizes.dp12,
                    paddingVertical: Sizes.dp5,
                    backgroundColor: Colors.mainColor,
                    borderRadius: Sizes.dp4,
                }}
            >
                <Text style={[TextStyles.t14MM.copyWith({ color: Colors.white }), { lineHeight: Sizes.dp20 }]}>{`完成`} </Text>
            </AbcView>,
        ];
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBtnColor(): Color {
        return Colors.black;
    }

    getStatusBarColor(): Color {
        return this.getAppBarBgColor();
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    getBottomSafeAreaColor(): string {
        return Colors.prescriptionBg;
    }

    getAppBarBackIcon(): JSX.Element {
        return (
            <AbcView
                style={[ABCStyles.rowAlignCenter, { paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 }]}
                onClick={() => this.onBackClick()}
            >
                <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 })]}>取消</Text>
            </AbcView>
        );
    }

    onBackClick(): void {
        //直接保存返回
        this.bloc.requestBackPage();
    }

    getTouchEmptyBlurTextInput(): boolean {
        return true;
    }

    _renderTopButton(): JSX.Element {
        const productOperateBtn = [
            {
                icon: "template",
                text: "诊疗模板",
                onClick: () => {
                    this.bloc.requestCopyTemplate();
                },
                iconFontSize: Sizes.dp14,
            },
            {
                icon: "save",
                text: "保存模板",
                onClick: () => {
                    this.bloc.requestSaveTemplate();
                },
            },
        ];
        const { disableAddPrescription } = this.props;
        //    如果disableAddPrescription为true, 则不显示诊疗模板按钮
        if (!!disableAddPrescription) {
            productOperateBtn.splice(0, 1);
        }
        return <PrescriptionActiveGroupView list={productOperateBtn} />;
    }

    _renderAddItemPanel(): JSX.Element {
        const medicineUsageParams = Object.assign(
            this.props.medicineUsageParams,
            {
                medicines: [new MedicineAddGroup()],
                selectMedicine: undefined,
                selectGroup: new MedicineAddGroup(),
            },
            userCenter.clinic?.isDentistryClinic ? { switchTypes: [MedicineAddType.examination] } : {}
        );
        const { disableAddPrescription } = this.props;
        if (!!disableAddPrescription) return <View />;
        return (
            <AbcButton
                pressColor={Colors.dialogBtnPress}
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        backgroundColor: Colors.white,
                        height: Sizes.dp54,
                        paddingLeft: Sizes.dp14,
                        justifyContent: "flex-start",
                    },
                ]}
                onClick={async () => {
                    const result: GoodsInfo = await showBottomPanel(
                        <ProductAddPageSearch medicineUsageParams={medicineUsageParams} allowAntibiotic={this.props.allowAntibiotic} />,
                        {
                            topMaskHeight: pxToDp(44 + 44),
                        }
                    );
                    if (!result) return;
                    this.bloc.requestAddSelectList(result);
                }}
            >
                <IconFontView name={"add"} color={Colors.mainColor} size={Sizes.dp12} />
                <Text style={[TextStyles.t16NM, { marginLeft: Sizes.dp12 }]}>{"添加项目"}</Text>
            </AbcButton>
        );
    }

    private _renderAddDrugPanel(): JSX.Element {
        const medicineUsageParams = Object.assign(this.props.medicineUsageParams, {
            medicines: [new MedicineAddGroup()],
            selectMedicine: undefined,
            selectGroup: new MedicineAddGroup(),
        });
        return (
            <DiagnosisProjectSearchView
                placeholder={"添加项目"}
                diagnosisUsage={medicineUsageParams}
                onChange={(goodsInfo) => {
                    if (!goodsInfo) return;
                    this.bloc.requestAddSelectList(goodsInfo);
                }}
                // ref={(searchInput) => {
                //     this._searchInput = searchInput?.searchInput;
                //     this._searchInput && this.bloc.requestCacheSearchInput(this._searchInput);
                // }}
            />
        );
    }

    _renderDoctorInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            { doctorDisplayStr, nurseDisplayStr } = state;
        if (!state.isDentistry) return <View />;

        if (!state.productCount) return <View />;

        return (
            <AbcBasePanel panelStyle={{ marginHorizontal: Sizes.dp8, marginBottom: Sizes.dp18 }}>
                <AbcView
                    style={[ABCStyles.rowAlignCenter, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, Sizes.dp11)]}
                    onClick={() => {
                        this.bloc.requestChangeProductDoctorInfo();
                    }}
                >
                    <View style={{ flex: 1, alignSelf: "stretch" }}>
                        <View style={[{ paddingBottom: Sizes.dp9 }]}>
                            <Text style={[!!doctorDisplayStr ? TextStyles.t16NT1 : TextStyles.t16NT4]} numberOfLines={1}>
                                {!!doctorDisplayStr ? doctorDisplayStr : "医生"}
                            </Text>
                        </View>
                        <DividerLine color={Colors.dividerLineColor} />
                    </View>

                    <SizedBox width={Sizes.dp8} />
                    <View style={{ flex: 1, alignSelf: "stretch" }}>
                        <View style={[{ paddingBottom: Sizes.dp9 }]}>
                            <Text style={[!!nurseDisplayStr ? TextStyles.t16NT1 : TextStyles.t16NT4]} numberOfLines={1}>
                                {!!nurseDisplayStr ? nurseDisplayStr : "护士"}
                            </Text>
                        </View>
                        <DividerLine color={Colors.dividerLineColor} />
                    </View>
                </AbcView>
            </AbcBasePanel>
        );
    }

    _renderProjectList(): JSX.Element {
        const state = this.bloc.currentState,
            { showErrorHint } = state;
        const selectGoodsList = state.groups[0]?.selectedMedicines;
        const _price = userCenter.clinic?.isDentistryClinic ? state.totalPriceAfterBargain : state.totalPrice;
        const _count = state.productCount;
        const isExistBatch = selectGoodsList?.some((t) => !!t?._batchInfos?.length);
        return (
            <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingTop: Sizes.dp18 }}>
                {this._renderDoctorInfoView()}
                <AbcBasePanel panelStyle={{ marginHorizontal: Sizes.dp8 }}>
                    {!_.isEmpty(selectGoodsList) &&
                        selectGoodsList?.map((goods, index) => {
                            const goodUasge = state.groups[0].inputUsages.get(goods!);
                            if (!goodUasge) return <View />;
                            return (
                                <ProductAddCardPage
                                    key={goods.compareKey() + index}
                                    medicine={goods}
                                    usage={goodUasge!}
                                    showErrorHint={showErrorHint}
                                    departmentId={this.props?.medicineUsageParams?.outpatientSheetDoctorInfo?.departmentId}
                                    isOpenSupportInputDays={this.props?.medicineUsageParams?.isOpenSupportInputDays}
                                    disableAddPrescription={this.props?.disableAddPrescription}
                                    diagnosisTreatment={this.props?.diagnosisTreatment}
                                />
                            );
                        })}
                    {/*{this._renderAddDrugPanel()}*/}
                    {this._renderAddItemPanel()}
                </AbcBasePanel>
                {!!selectGoodsList?.length && (
                    <AbcView
                        style={[
                            ABCStyles.rowAlignCenter,
                            { marginTop: Sizes.dp18, paddingRight: Sizes.dp24, justifyContent: "flex-end", flex: 1 },
                        ]}
                        onClick={() => this.bloc.requestCheckGoodsBatchInfo()}
                    >
                        <Text
                            style={[
                                TextStyles.t16NT2.copyWith({
                                    color: Colors.t2,
                                    lineHeight: Sizes.dp22,
                                }),
                                { textAlign: "right" },
                            ]}
                        >
                            {`共 ${_count} 种，${abcI18Next.t("¥")} ${_price ? NumberUtils.formatMaxFixed(_price, 2) : "0.00"}`}
                        </Text>
                        {isExistBatch && (
                            <View style={ABCStyles.rowAlignCenter}>
                                <SizedBox width={Sizes.dp4} />
                                <IconFontView name={"arrow_down"} color={Colors.T6} size={Sizes.dp16} />
                            </View>
                        )}
                    </AbcView>
                )}
            </ScrollView>
        );
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState,
            medicineType = state.medicineType;
        if (_.isNil(medicineType)) return <View>{medicineType}</View>;
        return (
            <View style={[DeviceUtils.isOhos() ? undefined : { flex: 1, backgroundColor: Colors.prescriptionBg }]}>
                <WillPopListener
                    onWillPop={() => {
                        this.onBackClick();
                    }}
                />
                {this._renderTopButton()}
                {this._renderProjectList()}
            </View>
        );
    }
}

interface ProductInfoCellProps {
    select?: boolean;
    columnCount?: number;
    goodInfo: GoodsInfo;
    goodUsage: MedicineUsage;
    group: MedicineAddGroup;
    onClick?: (arg0: any, arg1: any) => void;
}

export class ProductInfoCell extends BaseComponent<ProductInfoCellProps> {
    static contextType = ProductAddPageBloc.Context;

    constructor(props: ProductInfoCellProps) {
        super(props);
    }

    static defaultProps = {
        select: false,
        columnCount: 1,
    };

    deleteItem(): void {
        const { goodInfo, group } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestDeleteMedicine(group, goodInfo);
        if (AbcTextInput.focusInput) AbcTextInput.focusInput.blur();
    }

    _renderTreatment(): JSX.Element {
        const { goodInfo, goodUsage, select } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, { flexGrow: 1 }]}>
                <AbcView
                    style={styles.closeButtonContainer}
                    onClick={() => {
                        this.deleteItem();
                    }}
                >
                    <SolidIconFontView fillColor={Colors.P5} iconColor={Colors.T2} size={14} iconName={"cross_small"} />
                </AbcView>
                <View style={{ flex: 1 }}>
                    <_TreatmentUsageItemView select={select} medicine={goodInfo} usage={goodUsage} />
                </View>
            </View>
        );
    }

    _renderGood(): JSX.Element {
        const { goodInfo, goodUsage, select } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                <AbcView
                    style={styles.closeButtonContainer}
                    onClick={() => {
                        this.deleteItem();
                    }}
                >
                    <SolidIconFontView fillColor={Colors.P5} iconColor={Colors.T2} size={14} iconName={"cross_small"} />
                </AbcView>
                <View style={{ flex: 1 }}>
                    <_GoodsUsageItemView select={select} medicine={goodInfo} usage={goodUsage} />
                </View>
            </View>
        );
    }

    _renderContent(): JSX.Element {
        const { goodInfo } = this.props;
        switch (goodInfo.type) {
            case GoodsType.treatment:
            case GoodsType.examination:
            case GoodsType.package: {
                return this._renderTreatment();
            }
            case GoodsType.goods: {
                return this._renderGood();
            }
            default: {
                return this._renderGood();
            }
        }
    }

    render(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    sizes.paddingLTRB(Sizes.dp12, 0, 0, 0),
                    {
                        borderWidth: 0.5,
                        borderColor: Colors.dividerLineColor,
                        backgroundColor: Colors.white,
                    },
                ]}
            >
                <View style={{ flexGrow: 1 }}>{this._renderContent()}</View>
            </View>
        );
    }
}

interface _TreatmentUsageItemViewProps {
    medicine: GoodsInfo;
    usage: MedicineUsage;
    select?: boolean;
    showErrorHint?: boolean;
}

class _TreatmentUsageItemView extends React.Component<_TreatmentUsageItemViewProps, any> {
    static contextType = ProductAddPageBloc.Context;

    constructor(props: _TreatmentUsageItemViewProps) {
        super(props);
    }

    render() {
        const { medicine, usage, select } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        const showErrorHint = _bloc.currentState.showErrorHint;
        const _count = usage.unitCount;
        const _unit = usage?.unit;
        return (
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    height: Sizes.listItemHeight,
                }}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <StockNotEnoughTextView
                        goodsStock={medicine?.stockInfo(_unit, _count, 1)}
                        textStyle={TextStyles.t16MB}
                        text={`${medicine.name ?? ""}${medicine.isPackage ? "【套餐】" : ""}`}
                        style={{ flexShrink: 1 }}
                    />
                </View>
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            marginRight: sizes.listHorizontalMargin,
                        },
                    ]}
                >
                    <CustomInput
                        borderType={"bottom"}
                        type={"input"}
                        placeholder={"数量"}
                        border={true}
                        value={_count}
                        error={showErrorHint && !Boolean(_count)}
                        formatter={PrecisionLimitFormatter(0)}
                        onChange={this._onCountChange.bind(this)}
                        autoFocus={select}
                        onEndEditing={() => {
                            _bloc.requestUpdateSearchbarStatus(true);
                        }}
                    />
                    <View style={{ width: 6 }} />
                    <CustomInput
                        borderType={"bottom"}
                        type={"sheet"}
                        disable={true}
                        value={_unit}
                        onChange={this._onChangeMedicineUnit.bind(this)}
                    />
                </View>
            </View>
        );
    }

    _onCountChange(value: string) {
        const { medicine } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateTreatmentAmount(medicine, Number(value));
    }

    _onChangeMedicineUnit() {
        const { medicine } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestSelectMedicineUnit(medicine);
    }
}

interface _GoodsUsageItemViewProps {
    medicine: GoodsInfo;
    usage: MedicineUsage;
    select?: boolean;
    showErrorHint?: boolean;
}

class _GoodsUsageItemView extends React.Component<_GoodsUsageItemViewProps> {
    static contextType = ProductAddPageBloc.Context;

    constructor(props: _TreatmentUsageItemViewProps) {
        super(props);
    }

    render() {
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        const { medicine, usage, select } = this.props;
        const showErrorHint = _bloc.currentState.showErrorHint;
        const stockInfo = medicine.stockInfo(usage?.unit, usage?.unitCount, 1);
        const _count = usage.unitCount;
        const _unit = usage?.unit;
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        flex: 1,
                        justifyContent: "space-between",
                        height: Sizes.listItemHeight,
                    },
                ]}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <StockNotEnoughTextView
                        style={{ flexShrink: 1 }}
                        goodsStock={stockInfo}
                        text={medicine.displayName}
                        textStyle={TextStyles.t16MB}
                    />
                    <SizedBox width={6} />
                    <Text style={TextStyles.t14NT4}>{`${medicine.packageSpec}`}</Text>
                </View>
                <SizedBox width={6} />
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            marginRight: sizes.listHorizontalMargin,
                        },
                    ]}
                >
                    <CustomInput
                        borderType={"bottom"}
                        type={"input"}
                        placeholder={"数量"}
                        border={true}
                        value={_count}
                        error={showErrorHint && !Boolean(_count)}
                        autoFocus={select}
                        formatter={PrecisionLimitFormatter(medicine.isGoods ? 2 : 0)}
                        onChange={this._onCountChange.bind(this)}
                        onEndEditing={() => {
                            _bloc.requestUpdateSearchbarStatus(true);
                        }}
                    />
                    <SizedBox width={6} />
                    <CustomInput
                        borderType={"bottom"}
                        type={"sheet"}
                        disable={false}
                        value={_unit}
                        error={showErrorHint && !Boolean(_unit)}
                        onChange={this._onChangeMedicineUnit.bind(this)}
                    />
                </View>
            </View>
        );
    }

    _onCountChange(value: string) {
        const { medicine } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestUpdateTreatmentAmount(medicine, Number(value));
    }

    _onChangeMedicineUnit() {
        const { medicine } = this.props;
        const _bloc = ProductAddPageBloc.fromContext(this.context);
        _bloc.requestSelectMedicineUnit(medicine);
    }
}

interface _CategoryPickerProps {
    selectCategory: number;
    categoryList?: MedicineAddPageCategoryItem[];
    onChanged: (newCategory: MedicineAddPageCategoryItem) => void;
}

class _CategoryPicker extends BaseComponent<_CategoryPickerProps> {
    static contextType = ProductAddPageBloc.Context;

    render() {
        const { selectCategory, categoryList } = this.props;
        let _kCategoryItems = kCategoryItems;
        if (userCenter.clinic?.isNormalHospital) {
            _kCategoryItems = kHospitalCategoryItems;
        }
        const category = categoryList ?? _kCategoryItems;

        let __kCategoryItems = [...category];
        if (userCenter.clinic?.isDentistryClinic) {
            const state = ProductAddPageBloc.fromContext(this.context).currentState;
            __kCategoryItems.splice(
                4,
                0,
                ...state.kCustomCategoryItems.map((item) => new MedicineAddPageCategoryItem(item.id, item.title, true))
            );
            __kCategoryItems = __kCategoryItems.filter((item) => item.isDentistry);
        }

        return (
            <View style={{ backgroundColor: Colors.D2, width: Sizes.dp80 }}>
                {__kCategoryItems.map((item) => {
                    const select = selectCategory === item.id;
                    return (
                        <AbcView
                            key={item.id.toString()}
                            style={{
                                height: Sizes.listItemHeight,
                                paddingLeft: Sizes.listHorizontalMargin,
                                justifyContent: "center",
                                backgroundColor: select ? Colors.white : undefined,
                            }}
                            onClick={() => this.props.onChanged(item)}
                        >
                            <Text style={select ? TextStyles.t14NT1 : TextStyles.t14NT4}>{item.title}</Text>
                        </AbcView>
                    );
                })}
            </View>
        );
    }
}

export class _NewCategoryPicker extends BaseComponent<_CategoryPickerProps> {
    static contextType = ProductAddPageBloc.Context;

    render(): JSX.Element {
        const { selectCategory, categoryList } = this.props;
        let _kCategoryItems = kCategoryItems;
        if (userCenter.clinic?.isNormalHospital) {
            _kCategoryItems = kHospitalCategoryItems;
        }
        const category = categoryList ?? _kCategoryItems;

        const __kCategoryItems = [...category];
        if (userCenter.clinic?.isDentistryClinic) {
            const state = ProductAddPageBloc.fromContext(this.context).currentState;
            __kCategoryItems.push(...state.kCustomCategoryItems.map((item) => new MedicineAddPageCategoryItem(item.id, item.title, true)));
        }
        return (
            <AbcView style={{ flex: 1, paddingHorizontal: Sizes.dp12 }}>
                <Tabs
                    scrollBeginDragInputBlur={false}
                    lineMargin={0}
                    initialPage={__kCategoryItems.findIndex((t) => t.id == selectCategory) ?? 0}
                    onChange={(idx) => this.props.onChanged(__kCategoryItems[idx])}
                    tabsStyle={{
                        justifyContent: "center",
                        paddingHorizontal: Sizes.dp4,
                        flexDirection: "row",
                        alignItems: "center",
                        flex: undefined,
                    }}
                    lineColor={Colors.mainColor}
                    tabStyle={{
                        marginRight: Sizes.dp24,
                        ...TextStyles.t16NT3.copyWith({ color: Colors.t2 }),
                    }}
                    currentStyle={{
                        ...TextStyles.t16MT1,
                    }}
                >
                    {__kCategoryItems.map((item) => {
                        return <Tab key={item.id.toString()} title={item.title} />;
                    })}
                </Tabs>
            </AbcView>
        );
    }
}

interface _GoodsSearchDisplayViewProps {
    onClickItem?: (goods: GoodsInfo) => void;
    hintContent?: JSX.Element;
}

export class _GoodsSearchDisplayView extends NetworkView<_GoodsSearchDisplayViewProps> {
    static contextType = ProductAddPageBloc.Context;

    constructor(props: _GoodsSearchDisplayViewProps) {
        super(props);
    }

    componentDidMount(): void {
        this.setContentStatus(ABCNetworkPageContentStatus.loading);
        ProductAddPageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.searching && !state.lastSearchRsp) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.searchError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (_.isEmpty(state.medicines)) {
                    status = ABCNetworkPageContentStatus.empty;
                }

                this.setContentStatus(status, state.searchError);
            })
            .addToDisposableBag(this);
    }

    renderContent(): JSX.Element {
        const state = ProductAddPageBloc.fromContext(this.context).currentState;
        const { medicines } = state;
        const length = medicines.length;
        return (
            <AbcListView
                style={{ flex: 1 }}
                initialListSize={30}
                renderRow={(data, ignored, index) => this._renderRow(data, index!, length)}
                dataSource={medicines}
                getRowKey={(index) => medicines[index].scrollKey}
                numberOfRows={length}
                scrollEventThrottle={300}
                showScrollIndicator={false}
                onEndReached={() => {
                    ProductAddPageBloc.fromContext(this.context).requestLoadMore();
                }}
            />
        );
    }

    emptyContent(): JSX.Element {
        const state = ProductAddPageBloc.fromContext(this.context).currentState;
        if (_.isEmpty(state.medicines) && _.isEmpty(state.keyword)) {
            let content = this.props.hintContent;
            if (content) {
                content = (
                    <View
                        style={{
                            flex: 1,
                            alignItems: "stretch",
                            justifyContent: "stretch",
                            backgroundColor: Colors.red,
                        }}
                    >
                        {content}
                    </View>
                );
            }
            if (content) return content;
        }

        if (_.isEmpty(state.keyword)) return <View />;
        return super.emptyContent();
    }

    reloadData(): void {
        ProductAddPageBloc.fromContext(this.context).requestReloadData();
    }

    private _renderRow(data: GoodsInfo, index: number, totalCount: number) {
        const valueHolder = new ValueHolder<AbcView | null>();
        return (
            <AbcView
                collapsable={false}
                ref={(ref) => (valueHolder.value = ref)}
                style={{
                    backgroundColor: Colors.white,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                }}
                onClick={() => this.props.onClickItem?.(data)}
            >
                <_ListItem goodsInfo={data} />
                {index != totalCount - 1 && <DividerLine />}
            </AbcView>
        );
    }
}

interface _ListItemProps {
    goodsInfo: GoodsInfo;
}

export const _ListItem: React.FC<_ListItemProps> = ({ goodsInfo }) => {
    const drugClassification =
        goodsInfo.isChineseMedicine ||
        goodsInfo.isWesternMedicine ||
        goodsInfo.isChineseWesternMedicine ||
        goodsInfo.isGoods ||
        goodsInfo.isMedicalMaterial;
    const projectClassification =
        goodsInfo.type == GoodsType.treatment ||
        goodsInfo.type == GoodsType.examination ||
        goodsInfo.type == GoodsType.nurseProduct ||
        goodsInfo.type == GoodsType.otherGoods49 ||
        goodsInfo.type == GoodsType.surgery;
    const specification = goodsInfo.packageSpec ?? `${goodsInfo.cMSpec} ${goodsInfo.pieceUnit}`;
    let stock = "";
    const packageCount = goodsInfo.getStockPackageCount() ?? 0;
    const pieceCount = goodsInfo.getStockPieceCount() ?? 0;

    if (packageCount > 0) {
        stock = `${goodsInfo.getStockPackageCount()}${goodsInfo.packageUnit}`;
    }

    if (pieceCount > 0) {
        stock = `${stock} ${goodsInfo.getStockPieceCount()} ${goodsInfo.pieceUnit}`;
    }

    //没有库存
    if (_.isEmpty(stock)) {
        stock = `0${goodsInfo.unitPreferPackage}`;
    }

    const hasStock = goodsInfo.hasStock;
    if (!drugClassification && !projectClassification && !goodsInfo.isPackage) return <View />;
    return (
        <AbcPrescriptionItemView
            displayName={goodsInfo.displayName ?? ""}
            unit={projectClassification ? goodsInfo.unit : undefined}
            typeName={goodsInfo.isPackage ? "套餐" : goodsInfo.displayTypeName}
            price={drugClassification ? goodsInfo.unitPricePreferPackage : goodsInfo.unitPrice}
            numberOfLines={2}
            expandContent={() => {
                if (!(projectClassification && goodsInfo.isCoItemPharmacy)) return <View />;
                return (
                    <View
                        style={{
                            ...ABCStyles.rowAlignCenter,
                            padding: Sizes.dp2,
                            backgroundColor: Colors.dividerLineColor,
                            borderRadius: Sizes.dp2,
                            marginLeft: Sizes.dp2,
                            alignSelf: "flex-start",
                        }}
                    >
                        <Text style={TextStyles.t12NT6}>{`${goodsInfo.typeId == GoodsTypeId.examine ? "检验送检" : "检查外包"}`}</Text>
                    </View>
                );
            }}
        >
            {drugClassification && (
                <PrescriptionAdditionalInfo
                    specification={specification}
                    manufacturer={goodsInfo.manufacturer}
                    hasStock={hasStock}
                    remainStockTextFlag={"余"}
                    surplusStockDisplay={stock}
                />
            )}
            {goodsInfo.isPackage && !!goodsInfo.packageChildrenDisplay && (
                <View style={{ marginTop: Sizes.dp8 }}>
                    <Text numberOfLines={1} style={[TextStyles.t12NT6, { marginLeft: Sizes.dp8 }]}>
                        {goodsInfo.packageChildrenDisplay}
                    </Text>
                </View>
            )}
        </AbcPrescriptionItemView>
    );
};
