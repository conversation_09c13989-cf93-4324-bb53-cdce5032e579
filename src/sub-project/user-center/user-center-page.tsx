/**
 * create by deng<PERSON>e
 * desc: 个人中心主页
 * create date 2020/7/20
 */
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { SizedBox, Spacer, UniqueKey } from "../base-ui";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { ClinicEmployee, userCenter } from "./user-center";
import { LogUtils } from "../common-base-module/log";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import { BaseComponent } from "../base-ui/base-component";
import { AbcView } from "../base-ui/views/abc-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { SettingPage } from "./setting-page";
import { AboutMe } from "./about-me";
import { UserCenterPageBloc } from "./user-center-page-bloc";
import { Badge } from "../base-ui/badge/badge";
import { upgradeDataManager, UpgradeInfo } from "../upgrade/upgrade";
import { BaseBlocPage } from "../base-ui/base-page";
import { MessageSettings } from "./message-settings";

interface UserCenterPageProps {}

const iconSize = Sizes.dp56;

export class UserCenterPage extends BaseBlocPage<UserCenterPageProps, UserCenterPageBloc> {
    private _employee?: ClinicEmployee;
    private _upgradeInfo?: UpgradeInfo | null;

    constructor(props: UserCenterPageProps) {
        super(props);
        this._employee = userCenter.employee;
        this.bloc = new UserCenterPageBloc();
    }

    public componentDidMount(): void {
        super.componentDidMount();
        this._employee = userCenter.employee;
        LogUtils.d("UserCenterPage componentDidMount this._employee = " + JSON.stringify(this._employee));

        const prepareListener = this._prepare()
            .toObservable()
            .subscribe(() => {
                this.setState({});
            });
        this.addDisposable(prepareListener);

        const upgradeInfo = upgradeDataManager.upgradeObserver.subscribe((upgrade) => {
            this._upgradeInfo = upgrade;
            this.setState({});
        });
        this.addDisposable(upgradeInfo);
    }

    private async _prepare(): Promise<boolean> {
        this._employee = await userCenter.employee;
        this._upgradeInfo = await upgradeDataManager.getUpgradeInfo().catch(() => null);
        return true;
    }

    private _renderUserInfo(): JSX.Element {
        const bgHeight = Sizes.dp160;
        return (
            <View>
                <AssetImageView
                    name={"self_user_bg"}
                    ignoreTheme={false}
                    style={{
                        flex: 1,
                        height: bgHeight,
                    }}
                />
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            position: "absolute",
                            bottom: 0,
                            left: 0,
                            right: 0,
                            padding: Sizes.listHorizontalMargin,
                        },
                    ]}
                >
                    <View
                        style={[
                            {
                                borderWidth: 1,
                                borderColor: Colors.white,
                                borderRadius: iconSize,
                                overflow: "hidden",
                                backgroundColor: Colors.white,
                            },
                        ]}
                    >
                        <AssetImageView
                            name={"avatar_doctor"}
                            style={{
                                position: "absolute",
                                flex: 1,
                                height: iconSize,
                                width: iconSize,
                            }}
                        />
                        <AssetImageView style={{ flex: 1, height: iconSize, width: iconSize }} src={this._employee?.headImgUrl} />
                    </View>
                    <SizedBox width={16} />
                    <View style={{ flex: 1 }}>
                        <Text style={[TextStyles.t18MW, { flexShrink: 1 }]} numberOfLines={1}>
                            {this._employee?.name ?? ""}
                        </Text>
                    </View>
                </View>
            </View>
        );
    }

    getAppBar(): any {
        return <View />;
    }

    public renderContent(): JSX.Element {
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler; // 药店管家不展示设置
        return (
            <View style={{ backgroundColor: Colors.window_bg, flex: 1 }}>
                {this._renderUserInfo()}

                <View style={{ backgroundColor: Colors.white }}>
                    <SettingItemView
                        key={"message_settings"}
                        imageName={"message_set"}
                        title={"消息设置"}
                        onClick={() => {
                            ABCNavigator.navigateToPage(<MessageSettings />);
                        }}
                    />
                    <SettingItemView
                        key={"about_us"}
                        imageName={"about_us"}
                        title={"关于我们"}
                        redPoint={this._upgradeInfo != null}
                        onClick={() => {
                            ABCNavigator.navigateToPage(<AboutMe />);
                        }}
                    />
                    {!isDrugstoreButler && (
                        <SettingItemView
                            key={"setting"}
                            imageName={"setting"}
                            title={"设置"}
                            redPoint={false}
                            onClick={() => {
                                ABCNavigator.navigateToPage(<SettingPage />);
                            }}
                        />
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />

                <AbcView
                    style={{ backgroundColor: Colors.white }}
                    onClick={() => {
                        userCenter.logout();
                    }}
                >
                    <Text style={[TextStyles.t16NR2, { lineHeight: iconSize, textAlign: "center" }]}>{"退出登录"}</Text>
                </AbcView>
            </View>
        );
    }

    render(): any {
        return this.renderContent();
    }
}

interface SettingItemViewProps {
    style?: Style;
    showBottomLine?: boolean;
    imageName?: string;
    title?: string;
    redPoint?: boolean;
    onClick?: () => void;
}

export class SettingItemView extends BaseComponent<SettingItemViewProps> {
    constructor(props: SettingItemViewProps) {
        super(props);
    }

    static defaultProps = {
        showBottomLine: true,
    };

    public render(): JSX.Element {
        const { showBottomLine, imageName, title, redPoint, onClick, style } = this.props;
        const rowViews: JSX.Element[] = [];
        if (imageName) {
            rowViews.push(<AssetImageView key={UniqueKey()} name={imageName} ignoreTheme={false} />);
            rowViews.push(<SizedBox key={UniqueKey()} width={Sizes.dp16} />);
        }
        if (title) {
            rowViews.push(
                <Badge key={UniqueKey()} dot={redPoint}>
                    <Text style={[TextStyles.t16NB]}>{title}</Text>
                </Badge>
            );
        }
        rowViews.push(<Spacer key={UniqueKey()} />);
        rowViews.push(<IconFontView key={UniqueKey()} name={"Arrow_Right"} size={16} color={Colors.P1} />);
        return (
            <AbcView
                style={[
                    {
                        paddingLeft: Sizes.listHorizontalMargin,
                        height: Sizes.dp56,
                        ...style,
                    },
                ]}
                onClick={() => {
                    onClick?.();
                }}
            >
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            flex: 1,
                            paddingRight: Sizes.listHorizontalMargin,
                        },
                        showBottomLine ? ABCStyles.bottomLine : {},
                    ]}
                >
                    {rowViews}
                </View>
            </AbcView>
        );
    }
}
