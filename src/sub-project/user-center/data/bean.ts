/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/6/3
 */

import { fromJsonToDate, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";

export enum EditionType {
    none = 0, //无版本
    basic = 10, //基础版
    professional = 20, // 专业版
    ultimate = 30, //旗舰版
    vip = 40, //大客户版
}

export class RequiredMinEdition {
    id?: EditionType;
    name?: string;
    key?: string;
    price?: number;
    pricePeriod?: number;
    maxEmployeeCount?: number;
}

export class PurchaseItem {
    name?: string;
    key?: string;
    isPurchased?: number;
    isValid?: number;
    isSupportIndependentPurchase?: number;
    requiredMinEdition?: RequiredMinEdition;

    //  当前状态是否可用
    get isAvailableStatus(): boolean {
        return !!this.isPurchased && !!this.isValid;
    }
}

export class IndependentPurchaseItem {
    name?: string;
    key?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    beginDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    endDate?: Date;
    beginDateStr?: string;
    endDateStr?: string;
}

export class Edition extends RequiredMinEdition {
    currentEmployeeCount?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    beginDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    endDate?: Date;
    leftDays?: number; //剩余天数
    beginDateStr?: string;
    endDateStr?: string; //到期日期
    isExpired?: number; //是否已经到期（1--到期）
    effectiveDays?: number; //距离生效天数
    isTrial?: number; //是否试用门店
}

export class ClinicEditionPromotion {
    isHavePromotion?: number; // 是否有优惠券
    totalPromotionAmount?: number; // 优惠券金额
}

export class ClinicEdition {
    @JsonProperty({ type: Edition })
    edition?: Edition;
    @JsonProperty({ type: Array, clazz: IndependentPurchaseItem })
    independentPurchaseItems?: IndependentPurchaseItem[];
    @JsonProperty({ type: Array, clazz: PurchaseItem })
    purchaseItems?: PurchaseItem[];
    expireReminder?: {
        leftDays: number;
    }; //是否显示到期提醒
    availableEditionList?: any[]; // app 不会使用到
    @JsonProperty({ type: ClinicEditionPromotion })
    promotion?: ClinicEditionPromotion;

    /**
     * 功能版本查询
     * @param feature
     */
    checkFeaturePurcheased(feature: string): boolean {
        return !!this.purchaseItems?.find((item) => item.key == feature)?.isPurchased;
    }

    /**
     * 门店已经到期
     * 1: 未生效 0: 正常 1: 已到期
     */
    get isExpired(): boolean {
        const { isExpired = 0 } = this.edition ?? {};
        return isExpired == 1;
    }

    // 剩余天数
    get expireDays(): number {
        const { leftDays } = this.edition ?? {};
        return leftDays || 0;
    }
    // 到期提醒剩余天数
    get expireAlertDays(): number {
        const { leftDays } = this.expireReminder || {};
        return leftDays || 0;
    }
    // 是否试用门店
    get isTrial(): boolean {
        const { isTrial = 0 } = this.edition || {};
        return isTrial === 1;
    }
    // 是否未生效
    get isIneffective(): boolean {
        const { isExpired = 0 } = this.edition || {};
        return isExpired === -1;
    }
    // 距离生效天数
    get effectiveDays(): number {
        const { effectiveDays = 0 } = this.edition || {};
        return effectiveDays;
    }
    // 是否即将到期
    get isExpireSoon(): boolean {
        // 试用
        if (this.isTrial) {
            return false;
        }
        // 未生效
        if (this.isIneffective) {
            return false;
        }
        // 已到期
        if (this.isExpired) {
            return false;
        }
        const { leftDays = Number.MAX_SAFE_INTEGER } = this.edition || {};
        return leftDays <= 5;
    }

    /**
     * 门店是否有优惠券
     */
    get isHavePromotion(): boolean {
        return !!this.promotion && this.promotion.isHavePromotion == 1;
    }

    /**
     * 当前状态是否可用
     */
    checkCurrentStateAvailable(feature: string): boolean {
        let canUse = true;
        const matchingData = this.purchaseItems?.find((t) => t.key === feature);
        if (!!matchingData) {
            canUse = matchingData.isAvailableStatus;
        }
        return canUse;
    }
}

export enum HisType {
    normal = 0,
    dentistry = 1,
    ophthalmology = 2, //眼科诊所
    drugstoreButler = 10, //药店管家
    normalHospital = 100, //医院管家
}

export class MallOrderDetailRsp {
    afterSaleStatus?: number;
    afterSaleStatusName?: string;
    afterSales?: any[];
    agreementInfo?: {};
    attachments?: any[];
    @JsonProperty({ fromJson: fromJsonToDate })
    autoReceiveTime?: Date;
    buyerId?: number;
    buyerMobile?: string;
    buyerName?: string;
    canStartAfterSale?: number;
    comment?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    dealDeliveryPrice?: number;
    dealDiscountPrice?: number;
    dealGoodsTotalPrice?: number;
    dealTotalPrice?: number;
    deliveryPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    deliveryTime?: Date;
    discountPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    expireTime?: Date;
    firstOrderDiscountTotalPrice?: number;
    firstOrderDiscounts?: any[];
    goodsReductionTotalPrice?: number;
    goodsReductions?: any[];
    goodsTotalPrice?: number;
    id?: number;
    invoiceApply?: {};
    isPublicTransferAccount?: number;
    itemCount?: number;
    items?: [];
    logistics?: {};
    outStockAfterSaleStatus?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    payTime?: Date;
    payTransactionId?: string;
    payType?: number;
    payTypeName?: string;
    paymentDeliveryPrice?: number;
    paymentGoodsTotalPrice?: number;
    paymentTotalPrice?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    preSendOrderTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    receivedTime?: Date;
    redPacketTotalPrice?: number;
    redPackets?: any[];
    seckillDiscountTotalPrice?: number;
    servicePrice?: number;
    status?: number;
    statusLogs?: any[];
    statusName?: string;
    stockInRecords?: any[];
    stockInStatus?: number;
    totalOutOfStockPrice?: number;
    totalPrice?: number;
    totalWeight?: number;
    unitDiscountTotalPrice?: number;
    unitDiscounts?: any[];
    vendorId?: number;
    vendorName?: string;
}
