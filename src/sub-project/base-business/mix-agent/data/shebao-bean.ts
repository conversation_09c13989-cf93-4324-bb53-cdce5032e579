/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/12/10
 *
 * @description
 */
import { JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";

export enum RegionConst {
    CHENG_DU = "sichuan_chengdu", // 成都
    BEI_JING = "beijing", // 北京
    LU_ZHOU = "luzhou", // 泸州
    CHONG_QING = "chongqing", // 重庆
    CHONG_QING_GB = "chongqing_gb", // 重庆国标
    SHEN_ZHEN = "shenzhen", // 深圳
    SHEN_ZHEN_GB = "shenzhen_gb", // 深圳国标
    WU_HAN = "wuhan", // 武汉
    NAN_CHANG = "nanchang", // 南昌
    HANG_ZHOU = "hangzhou", // 杭州
    QU_ZHOU = "quzhou", // 衢州
    GUANG_ZHOU = "guangzhou", // 广州
    GUANG_ZHOU_GB = "guangzhou_gb", // 广州国标
    MAO_MING = "maoming", // 茂名
    ZHAN_JIANG = "zhanjiang", // 湛江
    FO_SHAN = "foshan", // 佛山
    DONG_GUAN = "dongguan", // 东莞
    QING_DAO = "qingdao", // 青岛
    XI_AN = "xian", // 西安
    SHEN_YANG = "shenyang", //沈阳
    NAN_JING = "nanjing", // 南京
    HE_ZHOU = "hezhou", // 贺州
    NING_BO = "ningbo", // 宁波
    MIAN_YANG = "mianyang", // 绵阳
    HAN_ZHONG = "hanzhong", // 汉中
    NATIONAL_DEFAULT = "national-default", // 未开通社保地区的默认值
    DA_LIAN = "dalian", // 大连
}

export interface DiseasesCode {
    code: string;
    name: string;
    type: number;
    hint: string;
    diseaseType?: number;
}

export class ShebaoConfigBasicInfo {
    cityHospitalCode?: string;
    cityNationalCode?: string;
    hospitalCode?: string;
    hospitalName?: string;
    provHospitalCode?: string;
    provNationalCode?: string;
    switchSpecialDisease?: number;
    hospitalType?: string;
    isOpenSameDayMedicalSettleLimit?: number; // 是否开启同日结算提醒：0 不开启 1 开启
    isOpenElecSetlCertUpload?: number; // 电子结算凭证是否开通 0 未开启 1 已开启
    //  开启电子结算凭证
    get openElecSetlCertUpload(): boolean {
        return this.isOpenElecSetlCertUpload === 1;
    }
}

export class ClinicShebaoConfig {
    clinicId?: number;
    chainId?: number;
    region?: "hangzhou" | string; //区域
    status?: number; //status === 10  视为开通社保，不走记账
    apiVersion?: number; //对接服务商
    @JsonProperty({ type: ShebaoConfigBasicInfo })
    basicInfo?: ShebaoConfigBasicInfo; //医疗机构信息

    /* 成都银海需要以下字段，其他不需要 */
    agencyNo?: number; // 市医保经办机构码
    provinceAgencyNo?: number; // 省医保经办机构码
    matchingCodeType?: number; // 智能对码模式

    warningSwitch?: number; // 医保异动提醒 0 不告警 1 告警
    warningShebaoNotMatched?: number; // 药品项目未对码 0 不告警 1 告警
    warningDictExpired?: number; // 医保目录失效 0 不告警 1 告警
    warningPriceExceed?: number; // 定价高于限价 0 不告警 1 告警
    warningDaysInAdvance?: number; // 提前多少天告警，默认 15 天
    limitPriceType?: number; // 刷卡限价策略 0 宽松 1 严格
    warningDictExpiredProvince?: number; // 失效提醒 省目录
    warningShebaoNotMatchedProvince?: number; // 对码提醒 省目录
    dictDiseaseType?: string; //门诊诊断推荐   default--老版本,v2--21版本
    _isEnableShebaoSettle?: number; // 终端自定义字段（判断当前诊所是否开启显示医保结算）

    // 是否国标
    get isNational(): boolean {
        return this.isChongqingGb || this.isShenzhenGb || this.isGuangzhouGb;
    }

    get supportSocialMark(): boolean {
        return this.isChengdu || this.isChongqing || this.isShenzhen || this.isWuhan || this.isHangzhou || this.isQingdao || this.isNanjing;
    }

    // 是否显示医保刷卡标志
    get isDisplaySocialMark(): boolean {
        return this.supportSocialMark && this.isOpenSocial;
    }

    // 显示两定报表tab，支持地区：成都、重庆
    get isDisplayLiangDingReport(): boolean {
        return this.isChengdu || this.isChongqing;
    }

    // 显示医保设置tab，支持地区：除武汉、南昌以外的地区
    get isDisplayMatchCode(): boolean {
        return !(this.isWuhan || this.isNanchang);
    }

    // 显示医保限价tab，支持地区：成都、重庆、深圳、武汉
    get isDisplayLimitReport(): boolean {
        return this.isChengdu || this.isChongqing || this.isShenzhen || this.isWuhan;
    }

    // 是否需要必填病种编码
    get isRequiredDiagnosis(): boolean {
        return this.isOpenSocial && (this.isHangzhou || this.isQingdao || this.isNanjing);
    }

    // 是否需要诊断必须与医保目录对应
    get isSaveDiseaseCode(): boolean {
        return this.isOpenSocial && !this.isChengdu;
    }

    // 是否可以医保刷卡
    get isOpenSocial(): boolean {
        return this.status === 10;
    }

    get shebaoPayMethodEnable(): boolean {
        return this.status != 10;
    }

    // 是否杭州
    get isRegionHanZhou(): boolean {
        return this.region === RegionConst.HANG_ZHOU;
    }

    // 是否成都
    get isChengdu(): boolean {
        return this.region === RegionConst.CHENG_DU;
    }

    // 是否重庆
    get isChongqing(): boolean {
        return this.region === RegionConst.CHONG_QING;
    }

    // 是否重庆国标
    get isChongqingGb(): boolean {
        return this.region === RegionConst.CHONG_QING_GB;
    }

    // 是否深圳
    get isShenzhen(): boolean {
        return this.region === RegionConst.SHEN_ZHEN;
    }

    // 是否深圳国标
    get isShenzhenGb(): boolean {
        return this.region === RegionConst.SHEN_ZHEN_GB;
    }

    // 是否广州
    get isGuangzhou(): boolean {
        return this.region === RegionConst.GUANG_ZHOU;
    }

    // 是否广州国标
    get isGuangzhouGb(): boolean {
        return this.region === RegionConst.GUANG_ZHOU_GB;
    }

    // 是否武汉
    get isWuhan(): boolean {
        return this.region === RegionConst.WU_HAN;
    }

    // 是否南昌
    get isNanchang(): boolean {
        return this.region === RegionConst.NAN_CHANG;
    }

    // 是否杭州
    get isHangzhou(): boolean {
        return this.region === RegionConst.HANG_ZHOU;
    }

    // 是否青岛
    get isQingdao(): boolean {
        return this.region === RegionConst.QING_DAO;
    }

    // 是否南京
    get isNanjing(): boolean {
        return this.region === RegionConst.NAN_JING;
    }

    // 是否北京
    get isBeijing(): boolean {
        return this.region === RegionConst.BEI_JING;
    }
}
