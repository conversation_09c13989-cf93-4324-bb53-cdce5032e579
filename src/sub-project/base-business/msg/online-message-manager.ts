/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-25
 *
 * @description
 */
import { LogUtils } from "../../common-base-module/log";
import { Subject } from "rxjs";
import { SocketIo } from "../../common-base-module/socket-io/socket-io";
import { environment } from "../config/environment";
import { LoginEvent, ModuleIds, userCenter } from "../../user-center/user-center";
import { EventEmitter } from "../../common-base-module/socket-io/event-emitter";
import { DisposableTracker } from "../../common-base-module/cleanup/disposable";
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Pair } from "../../base-ui/utils/value-holder";
import { eventCenter } from "../event-center/event-center";
import { abcNetDelegate, DeviceLoginSuccess } from "../../net/abc-net-delegate";
import { PatientOrderLockDetail } from "../data/patient-order/patient-order-bean";
import { SystemMessageNotifyMsg } from "../../data/notification";
import { UUIDGen } from "../../common-base-module/utils";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { EditionRenewalMsg } from "../../data/edition-renewal-bean";

export type Listener = (...args: any[]) => void;

export class WSOutpatientMessage {
    patientOrderId?: string;
    operatorId?: string;
    @JsonProperty({ name: "todo_consultation" })
    todoConsultation?: number;
}

export class WSChargeMessage {
    patientOrderId?: string;
    operatorId?: string;
    done?: number;
}

export class WSOutpatientLockMessage {
    patientOrderId?: string;
    chainId?: string;
    operatorId?: string;
    clinicId?: string;
    operatorName?: string;
    lock?: boolean;
}

const __systemMessageNotify = "message.notify.sys";
const __editionRenewalFee = "sc-clinic.edition_renewal_remind";
const __patientOrderSheetLock = "patientOrder.sheet_lock"; // 所有socket锁单信息的集合（包含了门诊、收费、退费锁单）
const __patientOrderSheetUnLock = "patientOrder.sheet_unlock"; // 所有socket锁单解锁信息的集合（包含了门诊、收费、退费锁单）

class OnlineMessageManager extends DisposableTracker {
    outpatientMsgObserver = new Subject<WSOutpatientMessage>();

    // ignore: close_sinks
    chargeMsgObserver = new Subject<WSChargeMessage>();

    // 所有锁单信息的消息通知（包括门诊、收费、退费等）
    patientOrderSheetLockMsgObserver = new Subject<PatientOrderLockDetail>();

    //系统消息通知
    systemMessageNotifyObserver = new Subject<SystemMessageNotifyMsg>();

    //版本续费提醒
    editionRenewalFeeObserver = new Subject<any>();

    eventEmitter = new EventEmitter();
    private webSocket?: SocketIo;

    constructor() {
        super();
        eventCenter.subscribe((event) => {
            if (event instanceof DeviceLoginSuccess) {
                try {
                    this.init();
                } catch (e) {}
            }
        });

        userCenter.loginObserver.subscribe((event) => {
            if (event.type == LoginEvent.loginOut) {
                this.disconnect();
            }
        });
    }

    init() {
        this.disconnect();
        const socketUrl = `${environment.serverHostScheme}://${environment.serverHostName}`;
        const appToken = userCenter.appToken();
        const grayFlag = abcNetDelegate.grayFlag;

        LogUtils.d(`OnlineMessageManager.init socketUrl = ${socketUrl}, grayFlag = ${grayFlag}`);
        const extraHeaders: any = {};
        extraHeaders["grayflag"] = grayFlag ?? "";
        extraHeaders["APP_UA"] = environment.appUA() || "";
        extraHeaders["Origin"] = socketUrl;

        // [cookieAbcYunTokenKey + "=" + currentToken, "domain=" + host, "path=/"
        const _params = {
            namespace: `/app${DeviceUtils.isAndroid() ? "?sceneType=2" : ""}`,
            // log: true,
            // compress: true,
            forceWebsockets: true,
            cookies: "_abcyun_token_=" + appToken + "; domain=" + environment.serverHostName + "; path=/",
            extraHeaders: extraHeaders,
        };
        if (DeviceUtils.isIOS()) {
            Object.assign(_params, { connectParams: { sceneType: 2 } });
        }
        // @ts-ignore
        this.webSocket = new SocketIo(socketUrl, _params);

        const moduleEventObservers = new Map<string, Pair<Subject<any>, Function>>([
            ["m" + ModuleIds.MODULE_ID_OUTPATIENT, new Pair(this.outpatientMsgObserver, this._toOutpatientMessage.bind(this))],
            ["m" + ModuleIds.MODULE_ID_CHARGE, new Pair(this.chargeMsgObserver, this._toChargeMessage.bind(this))],
        ]);

        userCenter.clinic?.modulesIdList()?.forEach((id) => {
            LogUtils.d("OnlineMessageManager.register event = m" + id);
            const eventName = "m" + id;
            this.on(eventName, (data) => {
                const pair = moduleEventObservers.get(eventName);
                LogUtils.d(`OnlineMessageManager.call event = m${id}, pair = ${pair}, data=${data}`);
                if (pair != null) {
                    if (pair.second != null) {
                        const msg = pair.second(data);
                        pair.first.next(msg);
                    }
                }
            });
        });

        // 所有锁单加锁socket通信（包括门诊、收费、退费）
        this.on(__patientOrderSheetLock, (data: PatientOrderLockDetail) => {
            const msgData = JsonMapper.deserialize(PatientOrderLockDetail, { status: 1, ...data });
            this.patientOrderSheetLockMsgObserver.next(msgData);
        });
        // 所有锁单解锁socket通信（包括门诊、收费、退费）
        this.on(__patientOrderSheetUnLock, (data: PatientOrderLockDetail) => {
            const msgData = JsonMapper.deserialize(PatientOrderLockDetail, { status: 0, ...data });
            this.patientOrderSheetLockMsgObserver.next(msgData);
        });

        //新增系统通知推送消息
        this.on(__systemMessageNotify, (data: SystemMessageNotifyMsg) => {
            const msgData = JsonMapper.deserialize(SystemMessageNotifyMsg, data);
            msgData.id = msgData.id ?? UUIDGen.generate();
            this.systemMessageNotifyObserver.next(msgData);
        });

        //版本续费提醒
        this.on(__editionRenewalFee, (data: EditionRenewalMsg) => {
            this.editionRenewalFeeObserver.next(data);
        });

        this.eventEmitter.event.forEach((value, event) => {
            const hasListener = this.webSocket?.hasListener(event) ?? false;
            if (hasListener) return;

            this.webSocket?.on(event, (data) => {
                this.eventEmitter.emit(event, data);
            });
        });

        this.webSocket?.connect();
    }

    emit(eventName: string, msg: any) {
        LogUtils.d(`OnlineMessageManager.sendMsg event =${eventName}, msg = ${JSON.stringify(msg)}`);
        this.webSocket?.emit(eventName, msg);
    }

    on(event: string, listener: Listener) {
        const hasListener = this.webSocket?.hasListener(event) ?? false;
        if (!hasListener) {
            this.webSocket?.on(event, (data) => {
                this.eventEmitter?.emit(event, data);
            });
        }

        this.eventEmitter.on(event, listener);
    }

    off(event: string, listener?: Listener) {
        this.eventEmitter.off(event, listener);
        if (!this.eventEmitter.hasListeners(event)) {
            this.webSocket?.off(event);
        }
    }

    disconnect() {
        if (this.webSocket) {
            this.webSocket.disconnect();
            this.webSocket = undefined;
        }

        this.closeDisposables();
    }

    _toOutpatientMessage(data: any): WSOutpatientMessage {
        return JsonMapper.deserialize(WSOutpatientMessage, data);
    }

    _toChargeMessage(data: any): WSOutpatientMessage {
        return JsonMapper.deserialize(WSChargeMessage, data);
    }
}

class OnlineMessageSubject<T> extends Subject<T> {
    listener: Listener;
    eventName: string;

    constructor(eventName: string) {
        super();

        this.eventName = eventName;
        this.listener = (data: any) => {
            this.next(data);
        };

        onlineMessageManager.on(this.eventName, this.listener);
    }

    public dispose(): void {
        onlineMessageManager.off(this.eventName, this.listener);
    }
}

const onlineMessageManager = new OnlineMessageManager();

export { onlineMessageManager, OnlineMessageSubject };
