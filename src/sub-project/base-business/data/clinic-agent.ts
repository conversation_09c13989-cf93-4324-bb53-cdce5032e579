/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/16
 *
 * @description
 */
import { ClinicDoctorInfo, Employee, PatientsSourceTypesItem } from "./beans";
import { ABCApiNetwork } from "../../net";
import { fromJsonToDate, fromJsonToDateTime, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Clinic, ClinicConfig, userCenter } from "../../user-center/user-center";
import {
    ClinicDictionaryInfo,
    ConsultantList,
    DoctorDetailInfo,
    EmployeeBusAuthGrantType,
    EmployeeBusAuthRsp,
    ModuleItem,
    RoleItem,
} from "./clinic-data";
import _ from "lodash";
import { clinicScopeMemoryCache } from "../../common-base-module/cleanup/memory-cache";
import { UniqueKey } from "../../base-ui";
import { ClinicEdition } from "../../user-center/data/bean";
import { InventoryClinicConfig } from "../../inventory/data/inventory-bean";
import {
    OutpatientDataPermissionGoodsPriceType,
    OutpatientDataPermissionHistoryPrescriptionType,
} from "../../data/online-property-config-provder";
import { AddClinicEmployeeReq } from "../../user-center/add-clinic-employee/data/bean";
import { environment } from "../config/environment";

class GetClinicDoctorOwnRegfeeRsp {
    @JsonProperty({ type: Array, clazz: ClinicDoctorInfo })
    rows?: Array<ClinicDoctorInfo>;
}

export interface EmployeeSimpleInfo {
    id: string;
    employeeId: string;
    shortId?: string;
    name: string;
    namePy: string;
    namePyFirst: string;
    mobile?: string;
    headImgUrl?: string;
    handSign?: string;
    wechatOpenIdMp?: string;
    wechatNickName?: string;
}

export interface departmentInfo {
    id: string;
    name: string;
    tagId?: string;
    type?: number;
    isDefault?: number;
    mobile?: string;
    beds?: number;
    customId?: string;
    isClinical?: number;
    mainMedical?: string;
    mainMedicalCode?: string;
    mainMedicalName?: string;
    departmentAddress?: string;
    chainId?: string;
    clinicId?: string;
    secondMedical?: string;
    secondMedicalCode?: string;
    secondMedicalName?: string;
    startDate?: string;
    principal?: string;
    created?: string;
    status?: number;
    lastModified?: string;
}

export class GetEmployeesAssistDoctorItem {
    id?: string;
    name?: string;
    mobile?: string;
    shortId?: string;
    namePy?: string;
    namePyFirst?: string;
    headImgUrl?: string;
    handSign?: string;
    wechatOpenIdMp?: string;
    wechatNickName?: string;
}
export class GetEmployeesAssistDoctorRsp {
    @JsonProperty({ type: Array, clazz: GetEmployeesAssistDoctorItem })
    assistDoctors?: GetEmployeesAssistDoctorItem[];
    id?: string;
    name?: string;
    mobile?: string;
    shortId?: string;
    namePy?: string;
    namePyFirst?: string;
    headImgUrl?: string;
    handSign?: string;
    wechatOpenIdMp?: string;
    wechatNickName?: string;
    isAssistAll?: number;
}

export class GetOnlineDoctorStatusRsp {
    status?: number | null;
    isOnlineDoctor?: number;
    autoOnlineEnable?: boolean;
    @JsonProperty({ fromJson: fromJsonToDateTime })
    autoOnlineEndTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDateTime })
    autoOnlineStartTime?: Date;
    prescriptionUseCa?: number; // 使用电子签名开具处方 0:否 1是

    /**
     * @description 能否使用电子签名开具处方
     * @event 0 不能
     * @event  1 能
     */
    get canPrescriptionUseCa(): boolean {
        return !!this.prescriptionUseCa;
    }
}

export class GetIsCaSignatureReadRsp {
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    /**
     * @description 医生是否是第一次网诊完诊(查看是否有created字段：如果有则不是第一次网诊完诊，如果没有则为第一次网诊未完诊)
     */
    get isFirstOnlineConsultation(): boolean {
        return !this.created;
    }
}

class RecentUsedClinicItem {
    clinicId?: string;
    clinicName?: string;
    clinicType?: number;
    lastLoginDate?: string;
}

// class GetRecentUsedClinicsRsp {
//     @JsonProperty({type: Array, clazz: RecentUsedClinicItem})
//     clinics?: Array<RecentUsedClinicItem>;
// }

export class ClinicAirPharmacyConfig {
    clinicId?: string;
    clinicName?: string;
    openSwitch?: number;
    historySwitch?: number;
    addressProvinceId?: string;
    addressCityId?: string;
    innerFlag?: number;
    balance?: number;
    frozenBalance?: number;
    availableBalance?: number;
    isSupportAirPharmacy?: boolean;

    get canUseAirPharmacy(): boolean {
        return !!(this.isSupportAirPharmacy && this.openSwitch);
    }
}

export class ClinicVirtualPharmacyConfig {
    isVirtualPharmacyOpen?: number;
}

export class RegAndTherapyConfig {
    regFeeChargeNow?: boolean;
    regPrint?: boolean;
    regsHiddenAddress?: boolean;
    regsHiddenIdCard?: boolean;
    regsHiddenMedicalRecord?: boolean;
    regsHiddenSn?: boolean;
    regsHiddenSource?: boolean;
    regsHiddenProfession?: boolean;
    reservedFeeChargeNow?: boolean;
    reservedPrint?: boolean;
    regsShowVisitSource?: boolean;

    signInChargeNow?: boolean;
    signInPrint?: boolean;
    therapyHiddenAddress?: boolean;
    therapyHiddenIdCard?: boolean;
    therapyHiddenSn?: boolean;
    therapyHiddenSource?: boolean;
    therapyPrint?: boolean;
    therapySignInPrint?: boolean;
    therapyHiddenProfession?: boolean;
    regsHiddenReCommend?: boolean; //控制就诊推荐显示隐藏
}

class EmployeesMeClinicConfig {
    @JsonProperty({ type: RegAndTherapyConfig })
    config?: RegAndTherapyConfig;
}

interface CashierDataPermissionConfig {
    /**
     * @description 收费-能否查看药品进价
     * @event 0 不能
     * @event  1 能
     */
    isCanSeeGoodsCostPrice?: number;

    /**
     * @description 收费-能否查看查看患者就诊历史
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientHistory?: number;
    /**
     * @description 收费-能否查看查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
    /**
     * @description 收费-能否修改支付方式
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeModifyPayMode?: number;
}
interface CrmDataPermissionConfig {
    /**
     * @description 患者-修改首诊来源
     * @event 0 不能
     * @event 1 能
     */
    isCanModifyFirstFromAway?: number;
    /**
     * @description 患者-修改患者证件号
     * @event 0 不能
     * @event 1 能
     */
    isCanModifyIdCard?: number;
    /**
     * @description 患者-修改患者姓名
     * @event 0 不能
     * @event 1 能
     */
    isCanModifyName?: number;
    /**
     * @description 患者-修改患者档案号
     * @event 0 不能
     * @event 1 能
     */
    isCanModifySn?: number;
    /**
     * @description 患者-查看所有患者
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeAllPatients?: number;
    /**
     * @description 患者-查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
    /**
     * @description 患者-查看患者累计消费
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientPayAmount?: number;
}
interface DashboardDataPermissionConfig {
    /**
     * @description 工作台-收费员查看账目
     * @event 0 不允许查看账目
     * @event 1 只允许查看个人经手账目
     * @event 2 允许查看门店全部账目
     */
    chargerPermission?: number;

    /**
     * @description 工作台-医生查看诊疗收入
     * @event 0 不允许查看个人诊疗收入
     * @event 1 允许查看个人诊疗收入
     */
    doctorOutpatientFee?: number;
    /**
     * @description 工作台-医生查看挂号收入
     * @event 0 不允许查看个人挂号收入
     * @event 1 允许查看个人挂号收入
     */
    doctorRegistrationFee?: number;

    /**
     * @description 工作台-医生查看患者看板
     * @event 0 只允许查看自己的患者看板
     * @event 1 允许查看当天所有患者看板
     */
    kanbanPermission?: number;
}
interface InventoryDataPermissionConfig {
    /**
     * @description 库存-查看药品物资成本
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeGoodsCost?: number;

    /**
     * @description 库存-查看药品物资毛利
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeGoodsProfit?: number;
    /**
     * @description 库存-查看盘点药品价格
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeCheckGoodsPrice?: number;
    /**
     * @description 库存-查看报损药品价格
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeDamageGoodsPrice?: number;
    /**
     * @description 库存-查看领用药品价格
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeObtainGoodsPrice?: number;
    /**
     * @description 库存-查看调拨药品价格
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeTransGoodsPrice?: number;
    /**
     * @description 库存-查看生产出库价格
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeProductionGoodsPrice?: number;
    /**
     * @description 库存-设置建档权限
     * @event 0 不能
     * @event 1 能
     */
    isCanOperateGoodsArchives: number;
    /**
     * @description 库存-设置定价权限
     * @event 0 不能
     * @event 1 能
     */
    isCanOperateGoodsAdjustPrice: number;
}
interface OutpatientDataPermissionConfig {
    /**
     * @description 门诊-医生查看药品价格
     * @event 0 只允许查看药品总价
     * @event 1 允许查看药品明细
     * @event 2 不允许查看药品明细，总价
     */
    goodsPrice?: OutpatientDataPermissionGoodsPriceType;

    /**
     * @description 门诊-医生查看历史处方
     * @event 0 只允许查看自己开出的历史处方
     * @event 1 允许查看患者所有的历史处方
     * @event 2 不允许查看
     */
    historyPrescription?: OutpatientDataPermissionHistoryPrescriptionType;

    /**
     * @description 门诊-医生查看处方价格
     * @event 0 不允许
     * @event 1 允许
     */
    isCanSeePrescriptionPrice?: number;

    /**
     * @description 门诊-医生查看门诊单总价
     * @event 0 不允许
     * @event 1 允许
     */
    isCanSeeTotalPrice?: number;
    /**
     * @description 门诊-能否查看查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
}
interface PharmacyDataPermissionConfig {
    /**
     * @description 药房-发药员查看患者就诊历史
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientHistory?: number;
    /**
     * @description 药房-能否查看查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
}
interface StatisticsDataPermissionConfig {
    /**
     * @description 统计-查看药品物资成本
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeGoodsCost?: number;

    /**
     * @description 统计-查看药品物资毛利
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeGoodsProfit?: number;
}
interface RegistrationDataPermissionConfig {
    /**
     * @description 挂号-查看患者就诊历史
     * @event 0 不允许
     * @event 1 允许
     */
    medicalHistory?: number;
    /**
     * @description 挂号-能否查看查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
    /**
     * @description 挂号-能否修改支付方式
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeModifyPayMode?: number;
    /**
     * @description 挂号-能否修改、取消挂号预约
     * @event 0 不能
     * @event 1 能
     */
    isCanSeeModifyRegistration?: number;
}
interface NurseDataPermissionConfig {
    /**
     * @description 执行站-查看患者就诊历史
     * @event 0 不允许
     * @event 1 允许
     */
    medicalHistory?: number;
    /**
     * @description 执行站-查看已执行的单据(查看历史执行记录)
     * @event 0 能查看所有
     * @event 1 只能查看自己参与的（开单或执行）
     * @event 2 不能查看
     */
    executedSheetDetail?: number;
    /**
     * @description 挂号-能否查看查看患者手机号
     * @event 0 不能
     * @event 1 能
     */
    isCanSeePatientMobile?: number;
    /**
     * @description 执行站-查看历史执行单
     * @event 0 允许所有人查看
     * @event 1 只允许参与人查看（包括开单人及执行人）
     */
    historySheet?: number;
}
class EmployeeDataPermissionConfig {
    cashier?: CashierDataPermissionConfig;
    crm?: CrmDataPermissionConfig;
    dashboard?: DashboardDataPermissionConfig;
    inventory?: InventoryDataPermissionConfig;
    outpatient?: OutpatientDataPermissionConfig;
    pharmacy?: PharmacyDataPermissionConfig;
    statistics?: StatisticsDataPermissionConfig;
    registration?: RegistrationDataPermissionConfig;
    nurse?: NurseDataPermissionConfig;
}

export class EmployeesMeConfig {
    boundWechat?: number;
    // chainInfo?: any;
    @JsonProperty({ type: EmployeesMeClinicConfig })
    clinicInfo?: EmployeesMeClinicConfig;
    hasPassword?: number;
    headImgUrl?: string;
    id?: string;
    mobile?: string;
    name?: string;
    tags?: string;
    wechatNickName?: string;
    wechatSubscribe?: number;
    handSign?: string; // 医师签名

    /**
     * 人员数据权限
     */
    @JsonProperty({ type: EmployeeDataPermissionConfig })
    employeeDataPermission?: EmployeeDataPermissionConfig;
}

export interface ChainClinicsInfo {
    id: string;
    name: string;
    namePyFirst: string;
    nodeType: number;
    shortName: string;
    shortNamePyFirst: string;
    addressDetail: string;
    addressGeo: string;
    contactPhone: string;
    chainAdmin: number;
}

class GetChainClinicsRsp {
    rows?: ChainClinicsInfo[];
}

export class GetPatientsSourceTypesRsp {
    rows?: PatientsSourceTypesItem[];
}

class GetClinicModulesListRsp {
    id?: string;
    name?: string;
    children?: ModuleItem[];
}

class GetClinicRolesList {
    rows?: RoleItem[];
}

class AddClinicEmployeeRsp {
    chainId?: string;
    clinicId?: string;
    employeeId?: string;
    roleId?: number;
    role?: string;
    isDoctor?: number;
    moduleIds?: string;
    sort?: 1000;
    showInWeClinic?: number;
    roles?: number[];
    shebaoDoctorCode?: string;
    shebaoDoctorJobTitle?: string;
    shebaoChargerCode?: string;
    nationalDoctorCode?: string;
}

export class ClinicAgent {
    private static _allClinicEmployeesListV3?: DoctorDetailInfo[];

    static async getClinicEmployeesV3(useCache = false): Promise<DoctorDetailInfo[]> {
        if (useCache && this._allClinicEmployeesListV3) {
            return this._allClinicEmployeesListV3;
        }

        const rsp: DoctorDetailInfo[] = await ABCApiNetwork.get<{ rows: [] }>(`clinics/employees/clinic-employees`, {}).then(
            (rsp) => rsp.rows
        );

        this._allClinicEmployeesListV3 = rsp.map((item) => JsonMapper.deserialize(DoctorDetailInfo, item));

        return this._allClinicEmployeesListV3;
    }

    //获取诊所配置
    static async getClinicConfig(): Promise<ClinicConfig> {
        return ABCApiNetwork.get("clinics/current/config", { clazz: ClinicConfig });
    }

    //获取医生个人信息
    static async getClinicDoctorOwnRegfee(
        id: string = userCenter.employee?.id ?? "",
        isAssistant = 1,
        isChildcare = 0
    ): Promise<Array<ClinicDoctorInfo> | undefined> {
        return ABCApiNetwork.get(`clinics/employees/${id}/doctor-with-department`, {
            clazz: GetClinicDoctorOwnRegfeeRsp,
            queryParameters: {
                isAssistant: isAssistant,
                isChildcare: isChildcare,
            },
        }).then((rep) => rep.rows);
    }

    //获取诊所所有医生个人信息
    static async getClinicAllDoctorsRegfee(
        isAssistant = 1,
        isChildcare = 0,
        isAllEmployee = 1,
        allClinical = 1
    ): Promise<Array<ClinicDoctorInfo> | undefined> {
        return ABCApiNetwork.get("clinics/employees/doctors-with-department", {
            clazz: GetClinicDoctorOwnRegfeeRsp,
            queryParameters: {
                isAssistant: isAssistant,
                isChildcare: isChildcare,
                allEmployee: isAllEmployee, // 1代表查所有员工 不传后台默认为0
                allClinical: allClinical,
            },
        }).then((rep) => rep.rows);
    }

    //获取医助医生信息
    static async getClinicEmployeesAssistDoctors(): Promise<GetEmployeesAssistDoctorRsp> {
        return ABCApiNetwork.get("clinics/employees/get-assist-doctors", {
            clazz: GetEmployeesAssistDoctorRsp,
        });
    }
    /// 拉取诊所下的员工的摘要信息,带有拼音,用于本地搜索
    static getClinicEmployeesSimpleInfoCacheKey = "__getClinicEmployeesSimpleInfoCacheKey__";

    private static _getClinicEmployeesSimpleInfoCache?: { rows: EmployeeSimpleInfo[] };
    // 获取诊所员工的简单信息
    static async getClinicEmployeesSimpleInfo(useCache = false): Promise<EmployeeSimpleInfo[]> {
        if (this._getClinicEmployeesSimpleInfoCache && useCache) {
            if (!!this._getClinicEmployeesSimpleInfoCache) {
                return this._getClinicEmployeesSimpleInfoCache.rows;
            }
            const rsp = clinicScopeMemoryCache.get(ClinicAgent.getClinicEmployeesSimpleInfoCacheKey);
            if (rsp) return Promise.resolve(rsp.rows);
        }

        return ABCApiNetwork.get<{ rows: EmployeeSimpleInfo[] }>(`clinics/employees/list-by-clinic`).then((rsp) => {
            rsp.rows.map((it) => (it.employeeId = it.id));
            clinicScopeMemoryCache.set(ClinicAgent.getClinicEmployeesSimpleInfoCacheKey, rsp);
            this._getClinicEmployeesSimpleInfoCache = rsp;
            return rsp.rows;
        });
    }

    static getClinicDepartmentsOutpatientCacheKey = "__getClinicDepartmentsOutpatientCacheKey__";
    private static _getClinicDepartmentsOutpatientCache?: { rows: departmentInfo[] };

    // 获取门店门诊科室列表
    static async getClinicDepartmentsOutpatient(useCache = false): Promise<departmentInfo[]> {
        if (this._getClinicDepartmentsOutpatientCache && useCache) {
            if (!!this._getClinicDepartmentsOutpatientCache) {
                return this._getClinicDepartmentsOutpatientCache.rows;
            }
            const rsp = clinicScopeMemoryCache.get(ClinicAgent.getClinicDepartmentsOutpatientCacheKey);
            if (rsp) return Promise.resolve(rsp.rows);
        }

        return ABCApiNetwork.get<{ rows: departmentInfo[] }>(`clinics/departments/outpatient`, {
            queryParameters: {
                clinicId: userCenter.clinic?.clinicId,
            },
        }).then((rsp) => {
            clinicScopeMemoryCache.set(ClinicAgent.getClinicDepartmentsOutpatientCacheKey, rsp);
            this._getClinicDepartmentsOutpatientCache = rsp;
            return rsp.rows;
        });
    }

    /// 拉取连锁下的员工的摘要信息,带有拼音,用于本地搜索
    static getChainEmployeesSimpleInfoCacheKey = "__getChainEmployeesSimpleInfoCacheKey__";

    private static _getChainEmployeesSimpleInfoCache?: { rows: EmployeeSimpleInfo[] };
    // 获取连锁员工的简单信息
    static async getChainEmployeesSimpleInfo(useCache = false): Promise<EmployeeSimpleInfo[]> {
        if (this._getChainEmployeesSimpleInfoCache && useCache) {
            if (!!this._getChainEmployeesSimpleInfoCache) {
                return this._getChainEmployeesSimpleInfoCache.rows;
            }
            const rsp = clinicScopeMemoryCache.get(ClinicAgent.getChainEmployeesSimpleInfoCacheKey);
            if (rsp) return Promise.resolve(rsp.rows);
        }

        return ABCApiNetwork.get<{ rows: EmployeeSimpleInfo[] }>(`clinics/employees/list-by-chain`).then((rsp) => {
            rsp.rows.map((it) => (it.employeeId = it.id));
            clinicScopeMemoryCache.set(ClinicAgent.getChainEmployeesSimpleInfoCacheKey, rsp);
            this._getChainEmployeesSimpleInfoCache = rsp;
            return rsp.rows;
        });
    }

    static _patientRelativeAdvise?: GetPatientsSourceTypesRsp;

    // 获取患者来源-亲友推荐
    static async getPatientRelativeAdvise(cache = true): Promise<GetPatientsSourceTypesRsp> {
        if (cache && !!this._patientRelativeAdvise) {
            return this._patientRelativeAdvise;
        }
        return ABCApiNetwork.get<GetPatientsSourceTypesRsp>("crm/patients/source/types/").then((rsp) => {
            this._patientRelativeAdvise = rsp;
            return rsp;
        });
    }

    ///获取诊所下有相应权限的人员
    static async getClinicEmployeesByModuleIds(moduleIds: Array<number>): Promise<Array<Employee>> {
        return ABCApiNetwork.get<{ rows: Employee[] }>("clinics/employees", {
            queryParameters: { moduleId: moduleIds, showDisable: 1 },
        }).then((res) => {
            return res.rows?.map((item) => JsonMapper.deserialize(Employee, item)) ?? [];
        });
    }

    //拉取用户最近一次登录的诊所
    static async getRecentClinics(): Promise<RecentUsedClinicItem | undefined> {
        if (environment.isGlobalEnv) {
            return ABCApiNetwork.get<{
                rows: RecentUsedClinicItem[];
            }>("global-auth/login-logs").then((rsp) => {
                if (_.isEmpty(rsp.rows)) return undefined;
                return JsonMapper.deserialize(RecentUsedClinicItem, _.first(rsp.rows));
            });
        }
        return ABCApiNetwork.get<{
            rows: RecentUsedClinicItem[];
        }>("clinics/employees/login-logs", {
            queryParameters: {
                size: 1,
                chainFirst: 0,
            },
        }).then((rsp) => {
            if (_.isEmpty(rsp.rows)) return undefined;
            return JsonMapper.deserialize(RecentUsedClinicItem, _.first(rsp.rows));
        });
    }

    ///设置上下线
    static async updateOnlineDoctorStatus(on: boolean): Promise<boolean> {
        const rsp = await ABCApiNetwork.put("clinics/online-doctors/status", {
            body: { status: on ? 1 : 0 },
        });

        return rsp != null;
    }

    /// 修改是否使用电子签名开具处方配置
    static async updateOnlineDoctorElectronicSignature(doctorId: string, prescriptionUseCa: boolean): Promise<boolean> {
        const rsp = await ABCApiNetwork.put("clinics/online-doctors/prescription-use-ca", {
            body: { doctorId: doctorId, prescriptionUseCa: prescriptionUseCa ? 1 : 0 },
        });

        return rsp != null;
    }

    //  获取网诊医生 是否在线配置
    static async getOnlineDoctorStatus(doctorId = userCenter.employee?.id): Promise<GetOnlineDoctorStatusRsp | undefined> {
        const rsp = await ABCApiNetwork.get("clinics/online-doctors/status", {
            queryParameters: { doctorId },
            clazz: GetOnlineDoctorStatusRsp,
        });
        if (rsp?.status != undefined) {
            return rsp;
        }
        return undefined;
    }

    //  获取医生是否是第一次网诊完诊
    static async getIsCaSignatureRead(employeeId: string): Promise<GetIsCaSignatureReadRsp | undefined> {
        const rsp = await ABCApiNetwork.get("clinics/reminder/ca-service-agreement/read", {
            queryParameters: { employeeId },
            clazz: GetIsCaSignatureReadRsp,
        });
        if (rsp?.created != undefined) {
            return rsp;
        }
        return undefined;
    }

    //  修改医生第一次网诊完诊
    static async postCaSignatureRead(employeeId: string): Promise<GetIsCaSignatureReadRsp | undefined> {
        const rsp = await ABCApiNetwork.post("clinics/reminder/ca-service-agreement/read", {
            body: { employeeId },
            clazz: GetIsCaSignatureReadRsp,
        });
        if (rsp?.created != undefined) {
            return rsp;
        }
        return undefined;
    }

    ///根据员工id可以在微诊所里展示的医生的详细信息
    static async getCanShowMCDoctorDetailInfo(doctorId: string): Promise<DoctorDetailInfo | undefined> {
        const clinic = userCenter.clinic;
        return ABCApiNetwork.post<{
            rows: DoctorDetailInfo[];
            total: number;
            limit: number;
            offset: number;
        }>("clinics/doctors", {
            body: {
                ids: [doctorId],
                chainId: clinic!.chainId,
                showInWeClinic: 1,
            },
        }).then((rsp) => {
            if (_.isEmpty(rsp.rows)) return undefined;
            return JsonMapper.deserialize(DoctorDetailInfo, _.first(rsp.rows));
        });
    }

    //获取诊所列表
    static getClinicList(): Promise<Clinic[]> {
        if (environment.isGlobalEnv) {
            return ABCApiNetwork.get<{
                clinics: (Clinic & { id: string })[];
            }>("global-auth/clinic/joined?").then((rsp) => {
                if (!!rsp.clinics.length) {
                    return rsp.clinics.map((item) => JsonMapper.deserialize(Clinic, { ...item, clinicId: item.id }));
                }
                return [];
            });
        }
        return ABCApiNetwork.get<{ clinicList: Clinic[] }>("usercenter/list-clinic").then((rsp) => {
            if (rsp.clinicList) {
                return rsp.clinicList.map((item) => JsonMapper.deserialize(Clinic, item));
            }

            return [];
        });
    }

    static getCurrentDoctorsCacheKey = UniqueKey();

    //获取当前诊所下的医生列表
    static getCurrentDoctors(useCache = false): Promise<DoctorDetailInfo[]> {
        if (useCache) {
            const cache = clinicScopeMemoryCache.get(ClinicAgent.getCurrentDoctorsCacheKey);
            if (cache) return Promise.resolve(cache);
        }
        return ABCApiNetwork.get<{ rows: DoctorDetailInfo[] }>("clinics/employees/clinic-doctors").then((rsp) => {
            if (rsp.rows) {
                const ret = rsp.rows.map((item) => JsonMapper.deserialize(DoctorDetailInfo, item));
                clinicScopeMemoryCache.set(ClinicAgent.getCurrentDoctorsCacheKey, ret);
                return ret;
            }

            return [];
        });
    }

    // action, //动作，支持取值 login|reg|modify|wx_reg|rt_pw；
    static sendSMS(mobile: string, action: string, countryCode = "86"): Promise<boolean> {
        return ABCApiNetwork.post<Response>(environment.isGlobalEnv ? "global-auth/sendsms" : "clinics/employees/sendsms", {
            useRsp: true,
            body: {
                mobile: mobile,
                action: action ?? "",
                countryCode: countryCode,
            },
        }).then((rsp) => rsp.ok);
    }

    private static _airPharmacyConfig: ClinicAirPharmacyConfig;

    //获取门店配置
    static async getAirPharmacyConfig(withCache = false): Promise<ClinicAirPharmacyConfig> {
        if (this._airPharmacyConfig && withCache) {
            return this._airPharmacyConfig;
        }
        this._airPharmacyConfig = await ABCApiNetwork.get("clinics/air-pharmacy/config", {
            clazz: ClinicAirPharmacyConfig,
        });
        return this._airPharmacyConfig;
    }

    private static _virtualPharmacyConfig: ClinicVirtualPharmacyConfig;

    //获取门店配置
    static async getVirtualPharmacyConfig(withCache = false): Promise<ClinicVirtualPharmacyConfig> {
        if (this._virtualPharmacyConfig && withCache) {
            return this._virtualPharmacyConfig;
        }
        this._virtualPharmacyConfig = await ABCApiNetwork.get("goods/virtual-pharmacy/config", {
            clazz: ClinicVirtualPharmacyConfig,
        });
        return this._virtualPharmacyConfig;
    }

    private static _employeesMeConfig: EmployeesMeConfig;

    //获取当前员工的门店配置
    static async getEmployeesMeConfig(withCache = false): Promise<EmployeesMeConfig> {
        if (!!this._employeesMeConfig && withCache) {
            return this._employeesMeConfig;
        }
        this._employeesMeConfig = await ABCApiNetwork.get("clinics/employees/me", { clazz: EmployeesMeConfig });
        return this._employeesMeConfig;
    }

    /**
     * 获取连锁下各门店
     * @param displayChainAdminName 是否显示"总部"
     */
    static async getChainClinics(displayChainAdminName = false): Promise<ChainClinicsInfo[]> {
        return await ABCApiNetwork.get<GetChainClinicsRsp>("clinics/chain/clinics").then((rsp) => {
            if (rsp.rows) {
                return rsp.rows.map((item) => {
                    item.name = item.chainAdmin === 1 && displayChainAdminName ? "总部" : item.name;
                    item.shortName = item.chainAdmin === 1 && displayChainAdminName ? "总部" : item.shortName;
                    return item;
                });
            } else {
                return [];
            }
        });
    }

    /**
     * 获取当前门店的版本、功能权限等
     */
    static async getClinicsEditionCurrent(): Promise<ClinicEdition> {
        return await ABCApiNetwork.get("clinics/edition/current", { clazz: ClinicEdition });
    }

    static async updateOnlineDoctorAutoConfig(options: GetOnlineDoctorStatusRsp): Promise<GetOnlineDoctorStatusRsp> {
        const { autoOnlineEnable, autoOnlineStartTime, autoOnlineEndTime } = options;
        return ABCApiNetwork.put("clinics/online-doctors/auto/config", {
            body: {
                autoOnlineEnable: autoOnlineEnable ? 1 : 0,
                autoOnlineStartTime: autoOnlineStartTime?.format("HH:mm:ss"),
                autoOnlineEndTime: autoOnlineEndTime?.format("HH:mm:ss"),
            },
            clazz: GetOnlineDoctorStatusRsp,
        });
    }

    //获取连锁门店药品相关的配置
    static async getInventoryChainConfig(): Promise<InventoryClinicConfig> {
        return ABCApiNetwork.get(`goods/config`, { clazz: InventoryClinicConfig });
    }

    static __getClinicModulesList?: GetClinicModulesListRsp;
    static __getClinicRolesList?: GetClinicRolesList;

    /**
     * @description 获取诊所模块列表
     * @param useCache
     * @return GetClinicModulesListRsp
     */
    static async getClinicModulesList(useCache = true): Promise<GetClinicModulesListRsp> {
        if (useCache && !!this.__getClinicModulesList) {
            return this.__getClinicModulesList;
        }
        return ABCApiNetwork.get("clinics/modules", { clazz: GetClinicModulesListRsp }).then((rsp) => {
            this.__getClinicModulesList = rsp;
            return rsp;
        });
    }

    /**
     * @description 获取诊所角色列表
     * @param useCache
     * @return GetClinicRolesList
     */
    static async getClinicRolesList(useCache = true): Promise<GetClinicRolesList> {
        if (useCache && !!this.__getClinicRolesList) {
            return this.__getClinicRolesList;
        }
        return ABCApiNetwork.get("clinics/roles", { clazz: GetClinicRolesList }).then((rsp) => {
            this.__getClinicRolesList = rsp;
            return rsp;
        });
    }

    /**
     * @description 新增员工
     * @param params AddClinicEmployeeReq
     */
    static async addNewEmployee(params: AddClinicEmployeeReq): Promise<AddClinicEmployeeRsp> {
        return ABCApiNetwork.post("clinics/employees/invite", { body: params });
    }

    /**
     * 获取诊所所有护士信息
     */
    static async getClinicNurseList(): Promise<Employee[]> {
        const rsp: { rows: Employee[] } = await ABCApiNetwork.get(`clinics/employees`, {
            queryParameters: { role: 2 },
        });
        return rsp.rows ?? [];
    }

    /**
     * 获取诊所所有员工信息
     */
    static async getClinicAllEmployeeList(): Promise<Employee[]> {
        const rsp: { rows: Employee[] } = await ABCApiNetwork.get(`clinics/employees`);
        return rsp.rows ?? [];
    }

    /**
     * 续费到期--标记提醒已读
     */
    static async handleMarkReminderRead(): Promise<{ data?: string }> {
        return ABCApiNetwork.put("clinics/edition/mark-reminder-read");
    }

    /**
     * 根据roles查询咨询师列表
     */
    static async updateEmployeesConsultant(roles: number[]): Promise<ConsultantList[]> {
        const result: { rows: ConsultantList[] } = await ABCApiNetwork.post(`clinics/employees/list-by-condition`, {
            body: { roles },
        });
        return result?.rows ?? [];
    }

    /**
     * 获取诊所的全局字典信息
     */
    static async queryClinicDictionaryInfo(): Promise<ClinicDictionaryInfo> {
        return ABCApiNetwork.get("goods/dictionary-info", {
            clazz: ClinicDictionaryInfo,
        });
    }

    /**
     * 员工业务场景身份验证发送短信验证码
     */
    static async sendSmsCodeForEmployeeBusinessScene(params: {
        employeeId?: string; // 员工ID
        businessType?: string; // 业务类型（退费：charge.refundCheck）
        countryCode?: string; // 国家码
    }): Promise<{ Message?: string; Code?: string; sendStatus?: string }> {
        return await ABCApiNetwork.post("clinics/employees/bus-authentication/sendSmsVerifyCode", {
            body: params,
        });
    }

    /**
     *  员工业务场景生成二维码
     * @param params
     * @returns
     */
    static async getChargeRefundAuditWechatQrCode(params: {
        businessId: string; // 业务ID
        businessType: string; // 业务类型（退费：charge.refundCheck）
    }): Promise<EmployeeBusAuthRsp> {
        return ABCApiNetwork.get(`clinics/employees/bus-authentication/qrCode`, {
            queryParameters: params,
        });
    }

    /**
     * 员工业务场景身份验证
     * @param params
     * @returns
     */
    static async busAuthentication(params: {
        businessId?: string; // 业务ID
        businessType?: string; // 业务类型
        grantType?: EmployeeBusAuthGrantType; // 授权类型
        employeeId?: string; // 员工ID
        password?: string; // 密码
        verifyCode?: string; // 验证码
        authCode?: string; // 授权码
        onlyEmployeeSelf?: 0; // 是否限制员工自己使用
    }): Promise<{ accessToken?: string }> {
        return await ABCApiNetwork.post("/clinics/employees/bus-authentication", {
            body: params,
            clearUndefined: true,
        });
    }

    /**
     *  查询员工业务场景二维码状态
     * @param params
     * @returns
     */
    static async getChargeRefundAuditWechatQrCodeStatus(params: {
        qrCodeSceneKey: string; // 二维码场景key
    }): Promise<EmployeeBusAuthRsp> {
        return ABCApiNetwork.get(`clinics/employees/bus-authentication/qrCode-status`, {
            queryParameters: params,
            clazz: EmployeeBusAuthRsp,
        });
    }

    /**
     *  修改员工业务场景二维码状态
     * @param params
     * @returns
     */
    static async updateChargeRefundAuditWechatQrCodeStatus(params: {
        qrCodeSceneKey?: string; // 二维码场景key
        status?: number; // 二维码状态; 10：已扫；99：取消 cn.abcyun.cis.clinic.dto.EmployeeBusQrCodeRedisDto.Status
        scanEmployeeId?: string; // 扫码用户id(app这边不需要这个参数)
    }): Promise<EmployeeBusAuthRsp> {
        return ABCApiNetwork.put(`clinics/employees/bus-authentication/qrCode-status`, {
            body: params,
            clearUndefined: true,
            clazz: EmployeeBusAuthRsp,
        });
    }
}
