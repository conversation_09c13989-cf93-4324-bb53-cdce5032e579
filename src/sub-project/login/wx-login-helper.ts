import { WxApi } from "../base-business/wxapi/wx-api";
import { LicenseAgreementDialog } from "./license-agreement-dialog";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { BehaviorSubject } from "rxjs";
import { onlinePreferences } from "../base-business/preferences/online-preferences";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { AppInfo } from "../base-business/config/app-info";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2022/7/25
 *
 * @description
 * 是否允许微信登录，由于ios应用审核的问题，如果提供微信登录就需要提供apple账号登录，而apple登录对我们系统用处不大，暂时不提供
 * 所以在apple审核期间关闭微信登录
 */

class WxLoginHelper {
    _weChatInstalled = true; //微信是否安装
    _enableWxLogin = DeviceUtils.isAndroid() || DeviceUtils.isOhos(); //Android默认允许，ios默认关闭后面根据在线配置来打开

    // 是否
    public wxLoginStatusObserver = new BehaviorSubject<boolean>(this.isWxLoginEnable());

    constructor() {
        const kDisableWxLoginIOS = "disableWxLoginIOS";
        //监听隐私是否通过
        LicenseAgreementDialog.sLicenseAgreementObserver.subscribe((agree: boolean) => {
            if (!agree) return;

            WxApi.isWeChatInstalled().then((installed) => {
                this._weChatInstalled = installed ?? false;
                this.wxLoginStatusObserver.next(this.isWxLoginEnable());
            });

            DeviceUtils.isIOS() &&
                onlinePreferences
                    .getBool(kDisableWxLoginIOS)
                    .catch((/*ignore*/) => {
                        const disableWxLoginIOSCache: {
                            version: string;
                            value: boolean;
                        } = sharedPreferences.getObject(kDisableWxLoginIOS);

                        return disableWxLoginIOSCache && disableWxLoginIOSCache.version === AppInfo.appVersion
                            ? disableWxLoginIOSCache.value
                            : true;
                    })
                    .then((disable) => {
                        this._enableWxLogin = !disable;
                        sharedPreferences.setObject(kDisableWxLoginIOS, {
                            version: AppInfo.appVersion,
                            value: disable,
                        });
                        this.wxLoginStatusObserver.next(this.isWxLoginEnable());
                    });
        });
    }

    /**
     * 是否允许微信登录
     */
    isWxLoginEnable(): boolean {
        return LicenseAgreementDialog.hasAgreeLicense() && this._weChatInstalled && this._enableWxLogin;
    }
}

const wxLoginHelper = new WxLoginHelper();
export { wxLoginHelper };
