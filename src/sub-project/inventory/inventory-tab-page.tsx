/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/1/26
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { DividerLine, IconFontView, SizedBox } from "../base-ui";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../theme";
import { ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { GridView } from "../base-ui/views/grid-view";
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import { InventoryMedicineListBloc, WarnType } from "./inventory-goods/Inventory-medicine-list-bloc";
import { BlocHelper } from "../bloc/bloc-helper";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { InventoryMedicineListPage } from "./inventory-goods/Inventory-medicine-list";
import { Asset<PERSON>mage<PERSON>ie<PERSON> } from "../base-ui/views/asset-image-view";
import { Clinic, ModuleIds, userCenter } from "../user-center/user-center";
import { isUndefined, intersection, remove, isNil } from "lodash";
import URLProtocols from "../url-dispatcher/url-protocols";
import { InventoryInCreatePage } from "./inventory-in/inventory-in-create-page";
import { InventoryAgent } from "./data/inventory-agent";
import { BehaviorSubject } from "rxjs";
import { Badge } from "../base-ui/badge/badge";
import { StreamBuilder } from "../base-ui/views/stream-builder";
import { InventoryInConst } from "./inventory-in/inventory-in-data/inventory-in-const";
import { DINMittelschriftAlternate } from "../theme/text-styles";
import { AbcButton } from "../base-ui/views/abc-button";
import { BlocBuilder } from "../bloc";
import { AbcView } from "../base-ui/views/abc-view";
import { GoodsAgent, PharmacyListItem } from "../data/goods/goods-agent";
import { PortalTodoDetail } from "../data/portal/portal-bean";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { InventoryOutType } from "./inventory-out/inventory-out-data/inventory-out-bean";
import { PortalAgent } from "../data/portal/portal-agent";
import { InventoryInType } from "./inventory-in/inventory-in-data/inventory-in-bean";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { reportPointApi, runFuncStatPoint, StatisticalPointKeyConstants } from "../views/statistical-points";

interface InventoryTabPageProps {}

interface InventoryTabPageState {
    portalDetail?: PortalTodoDetail;
    stockWarnShortageCount?: number; //汇总（周转天数预警数量+库存预警数量）
    turnoverDaysWarnCount?: number; //周转天数预警数量
    shortageWarnCount?: number; //库存预警数量
    profitRatWarnCount?: number; //利润率预警数量
    expiredWarnCount?: number; //过期预警数量
}

enum ModuleTypes {
    scan,
    storageIn, // 采购入库 - 等同于之前的入库
    storageOut,
    exchange,
    storageCheck,
    purchase,
    //多库房：不修改原来主要逻辑--新增调整
    pharmacyTrans, //领用 -- 等同于出库->领用出库
    lossOut, //报损 -- 等同于出库 -> 报损出库
}

interface InventoryIndexItemProps {
    props: {
        warnType: WarnType;
        isSkipWarn?: boolean;
    };
    name: string;
    value: string | number;
    statPointKey: StatisticalPointKeyConstants;
}

const DINAlternateFontFamily = { fontFamily: DINMittelschriftAlternate };

export class InventoryTabPage extends BaseBlocNetworkPage<InventoryTabPageProps, InventoryMedicineListBloc, InventoryTabPageState> {
    _moduleList = [
        new _ModuleListItem(
            ModuleTypes.scan,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_PURCHASE_IN],
            "inventory_scan",
            "扫码",
            false,
            () => {
                InventoryInCreatePage.show({
                    createType: InventoryInType.scan,
                    draftId: InventoryInConst.inventoryInTypeScanDraftFileName,
                });
            }
        ),
        new _ModuleListItem(
            ModuleTypes.storageIn,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_PURCHASE_IN],
            "inventory_storage_in",
            "采购入库",
            false,
            (arg1) => {
                ABCNavigator.navigateToPage(`${URLProtocols.INVENTORY_IN_LIST}?type=${arg1?.type ?? 0}&no=${arg1?.no ?? 0}`);
            }
        ),
        new _ModuleListItem(
            ModuleTypes.pharmacyTrans,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_APPLY_IN],
            "inventory_storage_receive",
            "领用",
            false,
            () => {
                ABCNavigator.navigateToPage(`${URLProtocols.INVENTORY_RECEPTION_LIST}?type=${InventoryOutType.picking}`);
            }
        ),
        new _ModuleListItem(
            ModuleTypes.exchange,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_EXCHANGE],
            "inventory_exchange",
            "调拨",
            false,
            () => {
                ABCNavigator.navigateToPage(`${URLProtocols.INVENTORY_TRANS_LIST}?type=${userCenter.clinic?.isNormalClinic ? 1 : 0}`);
            }
        ),
        new _ModuleListItem(
            ModuleTypes.lossOut,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_LOSS_OUT],
            "inventory_storage_loss",
            "报损",
            false,
            () => {
                ABCNavigator.navigateToPage(`${URLProtocols.INVENTORY_OUT_LIST}?type=${InventoryOutType.frmLoss}`);
            }
        ),
        new _ModuleListItem(
            ModuleTypes.storageCheck,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_STOCK],
            "inventory_storage_check",
            "盘点",
            false,
            () => {
                clinicSharedPreferences.setBool("INVENTORY_DEBUGGER", false);
                ABCNavigator.navigateToPage(URLProtocols.INVENTORY_CHECK_LIST);
            },
            () => {
                // debugger模式处理部分异常数据
                clinicSharedPreferences.setBool("INVENTORY_DEBUGGER", true);
                ABCNavigator.navigateToPage(URLProtocols.INVENTORY_CHECK_LIST);
            }
        ),
        new _ModuleListItem(
            ModuleTypes.lossOut,
            [ModuleIds.MODULE_ID_INVENTORY, ModuleIds.MODULE_ID_INVENTORY_PRODUCE_OUT],
            "inventory_produce_out",
            "生产出库",
            false,
            () => {
                ABCNavigator.navigateToPage(`${URLProtocols.INVENTORY_OUT_LIST}?type=${InventoryOutType.produce}`);
            }
        ),
    ];

    constructor(props: InventoryTabPageProps) {
        super(props);
        //传limit = 1,当前显示内容并不需要里面的rows数据
        this.bloc = new InventoryMedicineListBloc({ queryParams: { limit: 1 } });
        this._updateModuleId();

        this.state = {
            portalDetail: undefined,
            stockWarnShortageCount: undefined, //汇总（周转天数预警数量+库存预警数量）
            turnoverDaysWarnCount: undefined, //周转天数预警数量
            shortageWarnCount: undefined, //库存预警数量
            profitRatWarnCount: undefined, //利润率预警数量
            expiredWarnCount: undefined, //过期预警数量
        };
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        let moduleList = this._moduleList;
        if (!!state.currentPharmacy) {
            // 多库存根据配置进行内容选择
            if (!state.currentPharmacy.__onlyShow && !isUndefined(state.currentPharmacy.no)) {
                const {
                    enablePurchaseIn, // 采购入库开关
                    enablePharmacyTransIn, // 领用
                    enableTransIn, // 调拨
                    enableLossOutIn, // 报损
                    enableCheckIn, // 盘点
                    enablePurchaseInOnlyView,
                } = state.currentPharmacy;
                moduleList = moduleList?.filter((item) => {
                    return (
                        (enablePurchaseIn && item.type == ModuleTypes.storageIn) ||
                        (!enablePurchaseInOnlyView && item.type == ModuleTypes.scan) ||
                        (enablePharmacyTransIn && item.type == ModuleTypes.pharmacyTrans) ||
                        (enableTransIn && item.type == ModuleTypes.exchange) ||
                        (enableLossOutIn && item.type == ModuleTypes.lossOut) ||
                        (enableCheckIn && item.type == ModuleTypes.storageCheck)
                    );
                });
            }
        }
        if (state.currentPharmacy && !state.currentPharmacy.isNormalPharmacy) {
            moduleList = moduleList?.filter((item) => item.type == ModuleTypes.storageIn);
        }
        /**
         * itemHeight={Math.round(pxToDp(105))}
         * @desc 怪异写法，为了兼容pxToDp产生的小数位导致界面显示异常
         */
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._renderTabPageHeaderBar()}
                {!!moduleList?.length && (
                    <ScrollView>
                        <GridView itemHeight={Math.round(Sizes.dp104)} crossAxisCount={2} showBottomLine={true} showNetLine={true}>
                            {moduleList.map((item) => this._renderGridItem(item))}
                        </GridView>
                    </ScrollView>
                )}
            </View>
        );
    }

    getAppBarTitle(): string {
        return "库存";
    }

    getAppBarTitleColor(): Color {
        return Colors.white;
    }

    getAppBarBgColor(): Color {
        return Colors.mainColor;
    }

    getStatusBarColor(): Color {
        return Colors.mainColor;
    }

    getAppBarBtnColor(): Color {
        return Colors.white;
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        if (state.hasMorePharmacy) {
            const { pharmacyList, currentPharmacy } = state;
            const pharmacyName = pharmacyList?.find((item) => item.no == currentPharmacy?.no)?.name ?? "本地药房";
            return (
                <AbcView
                    style={ABCStyles.rowAlignCenter}
                    onClick={() => {
                        this.bloc.requestModifyPharmacyType();
                    }}
                >
                    <SizedBox width={Sizes.dp16} />
                    <Text style={[TextStyles.t16MW]}>{pharmacyName}</Text>
                    <IconFontView
                        style={{ marginTop: DeviceUtils.isIOS() ? 3 : undefined }}
                        name={"arrow_down"}
                        color={Colors.white}
                        size={Sizes.dp16}
                    />
                </AbcView>
            );
        } else {
            return undefined;
        }
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    @runFuncStatPoint({ key: StatisticalPointKeyConstants.Inventory_TabPage })
    componentDidMount(): void {
        GoodsAgent.getGoodsClassificationWithStock(false).then().catch();

        BlocHelper.connectLoadingStatus(this.bloc, this);

        this._refreshDashboardTodoCount();

        PortalAgent.getPortalTodoDetail().then((rsp) => {
            this.setState({ portalDetail: rsp });
        });

        InventoryAgent.refreshDashboardTodoCount
            .subscribe(() => {
                this._refreshDashboardTodoCount();
            })
            .addToDisposableBag(this);
    }

    protected _updateModuleId(): void {
        function filterModules(modules: _ModuleListItem[]) {
            const ids = userCenter.clinic?.moduleIds?.split(",").map((item) => parseInt(item)) ?? [];
            if (userCenter.clinic?.roleId == Clinic.ROLEID_MANAGER || ids?.indexOf(ModuleIds.MODULE_ID_NONE) >= 0) {
                return modules;
            } else {
                let newModules: _ModuleListItem[] = [];
                const moduleIds = userCenter.clinic!.moduleIds;
                if (moduleIds != null) {
                    newModules = modules.filter(
                        (module) => intersection(module.ids, ids).length > 0 || module.ids.indexOf(ModuleIds.MODULE_ID_NONE) >= 0
                    );
                }
                return newModules;
            }
        }

        //单店并且没有开通多药房，没有调拨模块（单店开通了多药房，有店内调拨）
        if (userCenter.clinic?.isNormalClinic && !userCenter.inventoryClinicConfig?.isOpenMultiplePharmacy) {
            remove(this._moduleList, (item) => item.type == ModuleTypes.exchange);
        }

        // 门店已到期
        if (userCenter.clinicEdition?.isExpired) {
            remove(this._moduleList, (item) => item.type == ModuleTypes.scan);
        }

        this._moduleList = filterModules(this._moduleList);
    }

    protected _medicineMaterialsTitle(): string {
        return "药品/物资";
    }

    _inventoryIndexItem(): InventoryIndexItemProps[] {
        const state = this.bloc.currentState;
        if (!state.lastSearchResponse) return [];
        const { expiredWarnCount, profitRatWarnCount, stockWarnCount, stockWarnTurnoverDays } = state.lastSearchResponse;
        // 库存不足数=周转预警数+库存下限预警数,如果库存下限预警数>周转预警数，跳转库存预警，否则全部跳转周转天数
        const stockWarnCountTotal = (stockWarnCount ?? 0) + (stockWarnTurnoverDays ?? 0);
        return [
            {
                name: "库存不足",
                value: stockWarnCountTotal ?? 0,
                props: {
                    warnType: WarnType.stock,
                    isSkipWarn: (stockWarnCount ?? 0) > (stockWarnTurnoverDays ?? 0),
                },
                statPointKey: StatisticalPointKeyConstants.Inventory_tabPage_StockWarn,
            },
            {
                name: "效期预警",
                value: expiredWarnCount ?? 0,
                props: { warnType: WarnType.expired },
                statPointKey: StatisticalPointKeyConstants.Inventory_tabPage_ExpiredWarn,
            },
            {
                name: "毛利异常",
                value: profitRatWarnCount ?? 0,
                props: { warnType: WarnType.profitRat },
                statPointKey: StatisticalPointKeyConstants.Inventory_tabPage_ProfitRatWarn,
            },
        ];
    }

    protected _renderMedicineMaterials(): JSX.Element {
        const state = this.bloc.currentState;
        if (!state.lastSearchResponse) return <View />;
        const { saleCount } = state.lastSearchResponse;
        return (
            <ListSettingItem
                itemStyle={ListSettingItemStyle.expandIcon}
                style={{ paddingVertical: Sizes.dp12 }}
                title={this._medicineMaterialsTitle()}
                titleStyle={TextStyles.t18MW}
                content={(saleCount ?? 0).toString()}
                contentTextStyle={{ ...TextStyles.t22NW, ...DINAlternateFontFamily }}
                contentStyle={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end", marginRight: Sizes.dp5 }]}
                rightArrowColor={Colors.white}
                onClick={() => {
                    reportPointApi({ key: StatisticalPointKeyConstants.Inventory_tabPage_medicine });
                    InventoryMedicineListPage.show();
                }}
            />
        );
    }

    protected _renderTabPageHeaderBar(): JSX.Element {
        const state = this.bloc.currentState;
        if (!state.lastSearchResponse) return <View />;
        const moduleIds = userCenter?.clinic?.moduleIds?.split(",").map((item) => parseInt(item));
        if (
            userCenter?.clinic!.roleId != Clinic.ROLEID_MANAGER &&
            isNil(
                moduleIds?.find(
                    (item) =>
                        item == ModuleIds.MODULE_ID_INVENTORY_MEDICINE ||
                        item == ModuleIds.MODULE_ID_INVENTORY ||
                        item == ModuleIds.pharmacyGoods ||
                        item == ModuleIds.MODULE_ID_NONE
                )
            )
        )
            return <View />;
        //以前过滤掉代煎代配药房，现在放开
        // const showButtonView = state.currentPharmacy?.type != PharmacyType.virtual;
        // 对于鸿蒙系统来说，最外层不能设置颜色,所以<></>很有存在的必要
        return (
            <>
                <View style={[{ backgroundColor: Colors.mainColor, paddingHorizontal: Sizes.listHorizontalMargin }]}>
                    {this._renderMedicineMaterials()}

                    <DividerLine
                        style={{
                            height: Sizes.dpHalf,
                            backgroundColor: "#FFFFFF33",
                        }}
                        lineMargin={Sizes.dp8}
                    />
                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingTop: Sizes.dp16,
                                paddingBottom: Sizes.dp8,
                            },
                        ]}
                    >
                        <GridView
                            itemHeight={Math.round(Sizes.dp32)}
                            crossAxisCount={3}
                            showBottomLine={false}
                            showNetLine={false}
                            crossAxisSpacing={Sizes.dp34}
                            style={{ flex: 1 }}
                        >
                            {this._inventoryIndexItem().map((item, index) => {
                                // 第3列右对齐，目前第3列只有一个item,如果有多个，并且需要文案左对齐的话，还需要改造
                                const isLastCol = (index + 1) % 3 === 0;
                                return (
                                    <View
                                        key={item.name}
                                        style={[ABCStyles.rowAlignCenter, { justifyContent: isLastCol ? "flex-end" : undefined }]}
                                        onClick={() => {
                                            reportPointApi({ key: item.statPointKey });
                                            InventoryMedicineListPage.show({ ...item.props });
                                        }}
                                    >
                                        <Text style={TextStyles.t14NW.copyWith({ color: "rgba(255,255,266,0.5)" })}>{item.name}</Text>
                                        <SizedBox width={Sizes.dp4} />
                                        <Text style={[TextStyles.t16NW, DINAlternateFontFamily]}>{item.value}</Text>
                                    </View>
                                );
                            })}
                        </GridView>
                    </View>
                </View>
            </>
        );
    }

    private _renderGridItem(item: _ModuleListItem): JSX.Element {
        const state = this.bloc.currentState;
        const { iconName, text, disable, onClick, onLongClick, newMessageCount } = item;
        const color = disable ? Colors.T2 : Colors.T1;
        return (
            <StreamBuilder
                key={text}
                stream={newMessageCount}
                render={(value) => {
                    value = value ?? 0;
                    return (
                        <AbcButton
                            key={text}
                            style={{
                                ...ABCStyles.centerChild,
                                backgroundColor: Colors.white,
                                borderRadius: undefined,
                                padding: 0,
                                /**
                                 * 解决第二行样式不对
                                 */
                                marginBottom: 0.1,
                            }}
                            pressColor={Colors.P5}
                            onClick={!disable ? () => onClick?.(state.currentPharmacy) : undefined}
                            onLongClick={!disable ? () => onLongClick?.(state.currentPharmacy) : undefined}
                        >
                            <Badge value={value <= 0 ? undefined : value}>
                                <View
                                    style={[
                                        ABCStyles.rowAlignCenter,
                                        {
                                            paddingHorizontal: Sizes.dp16,
                                            paddingVertical: Sizes.dp0,
                                        },
                                    ]}
                                >
                                    <AssetImageView style={{ width: Sizes.dp24, height: Sizes.dp24 }} name={iconName} ignoreTheme={false} />
                                    <SizedBox width={Sizes.dp15} />
                                    <Text
                                        style={[TextStyles.t16MT1.copyWith({ color: color }), { width: Sizes.dp66, textAlign: "center" }]}
                                    >
                                        {text}
                                    </Text>
                                </View>
                            </Badge>
                        </AbcButton>
                    );
                }}
            />
        );
    }

    private _refreshDashboardTodoCount(): void {
        const currentPharmacy = InventoryAgent.getCurrentPharmacy();
        InventoryAgent.getInventoryDashboardTodoCount(currentPharmacy ? { pharmacyNo: currentPharmacy?.no } : undefined).then((rsp) => {
            this._moduleList.forEach((item) => {
                const type = item.type;
                let count;
                if (type == ModuleTypes.exchange) {
                    count = rsp.transCount;
                } else if (type == ModuleTypes.storageIn) {
                    count = rsp.stockInTodoCount ?? 0;
                } else if (type == ModuleTypes.lossOut) {
                    count = rsp.stockLossOutTodoCount ?? 0;
                } else if (type == ModuleTypes.storageCheck) {
                    count = rsp.stockCheckTodoCount ?? 0;
                } else if (type == ModuleTypes.pharmacyTrans) {
                    count = rsp.stockReceptTodoCount ?? 0;
                }
                if (!isUndefined(count)) {
                    item.newMessageCount.next(count ?? 0);
                }
            });
            this.setState({
                turnoverDaysWarnCount: rsp?.turnoverDaysWarnCount,
                shortageWarnCount: rsp?.shortageWarnCount,
                stockWarnShortageCount: rsp?.stockWarnShortageCount,
                expiredWarnCount: rsp?.expiredWarnCount,
                profitRatWarnCount: rsp?.profitRatWarnCount,
            });
        });
    }
}
export class DrugstoreInventoryTabPage extends InventoryTabPage {
    constructor(props: InventoryTabPageProps) {
        super(props);
        // 必须要重新调用一次，否则一直用的是基类的数据
        this._updateModuleId();
    }
    _moduleList = [
        new _ModuleListItem(
            ModuleTypes.storageCheck,
            [ModuleIds.pharmacyInventory, ModuleIds.pharmacyStocktaking],
            "inventory_storage_check",
            "盘点",
            false,
            () => {
                ABCNavigator.navigateToPage(URLProtocols.INVENTORY_CHECK_LIST);
            }
        ),
    ];
    _inventoryIndexItem(): InventoryIndexItemProps[] {
        const pharmacyIndexItem = super._inventoryIndexItem();
        const state = this.bloc.currentState;
        if (!state.lastSearchResponse) return [];
        const { unsalableWarnCount, costPriceWarnCount } = state.lastSearchResponse;
        pharmacyIndexItem.push(
            ...[
                {
                    name: "滞销商品",
                    value: unsalableWarnCount ?? 0,
                    props: { warnType: WarnType.unmarketableGoods },
                    statPointKey: StatisticalPointKeyConstants.None,
                },
                {
                    name: "进价异动",
                    value: costPriceWarnCount ?? 0,
                    props: { warnType: WarnType.purchasePriceChange },
                    statPointKey: StatisticalPointKeyConstants.None,
                },
            ]
        );
        return pharmacyIndexItem;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return undefined;
    }
    _medicineMaterialsTitle(): string {
        return "商品";
    }
}
class _ModuleListItem {
    type: ModuleTypes;
    iconName: string;
    text: string;
    disable: boolean;
    ids: ModuleIds[];
    newMessageCount: BehaviorSubject<number>;

    onClick?(arg1?: PharmacyListItem): void;
    constructor(
        type: ModuleTypes,
        ids: ModuleIds[] = [],
        iconName = "scan",
        text: string,
        disable = false,
        onClick?: (arg1?: PharmacyListItem) => void,
        onLongClick?: (arg1?: PharmacyListItem) => void
    ) {
        this.type = type;
        this.ids = ids;
        this.iconName = iconName;
        this.text = text;
        this.disable = disable;
        this.onClick = onClick;
        this.onLongClick = onLongClick;

        this.newMessageCount = new BehaviorSubject<number>(0);
    }

    onLongClick?(arg1?: PharmacyListItem): void;
}
