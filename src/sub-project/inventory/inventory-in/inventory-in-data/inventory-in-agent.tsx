/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2021-03-16
 *
 * @description
 */

import { ABCApiNetwork } from "../../../net";
import {
    ExtendInfo,
    GetInventoryInRsp,
    InventoryInAndOutType,
    InventoryInDraftDetail,
    InventoryInDraftReq,
    InventoryInSheetPageDetails,
    InventoryInUpdateItem,
    SupplierItem,
} from "./inventory-in-bean";
import { TimeUtils } from "../../../common-base-module/utils";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { Subject } from "rxjs";
import { InventoryInItem, SockInRspItem } from "../../../data/goods/goods-agent";
import { InventoryInDraftManage } from "./inventory-in-draft-manage";
import { TraceableCodeList } from "../../../base-business/data/beans";

export interface GetInventoryInSheetDetailsReq {
    id?: string;
    withStockId?: string;
    withReturnLeft?: string;
    mallOrderId?: string;
}

/**
 * 入库
 */
export class InventoryInRsp {
    isRecon?: string;
    isSettlement?: string;
    settlementInfo?: string;
    confirmUserId?: string;
    reconUserId?: string;
    id?: number;
    chainId?: string;
    orderNo?: string;
    supplierId?: string;
    toOrganId?: string;
    status?: number; //InventoryInStatus
    kindCount?: number;
    count?: number;
    sum?: number;
    amount?: number;
    amountExcludingTax?: number;
    comment?: Comment[];
    createdUserId?: string;
    createdDate?: string;
    lastModifiedUserId?: string;
    lastModifiedDate?: string;
    list?: SockInRspItem[];

    //  int isConfirm;
    confirmDate?: string;

    //  int isReview;
    reviewDate?: string;
    inDate?: string;
}

export class InventoryInReq {
    clinicId?: string;
    supplierId?: string;
    comment?: string;
    outOrderNo?: string;
    @JsonProperty({ type: Array, clazz: InventoryInItem })
    list?: InventoryInItem[];
    /**
     * 药房
     */
    pharmacyNo?: number;
    inorderDraftId?: string;
    pharmacyType?: number;
    type?: InventoryInAndOutType; //【入库/退货出库都有】入库单类型：0 采购入库 1商城订单入库 2随货单入库 3jenkins导入入库 10 退货出库
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    returnInOrderId?: number; //【退货出库特有/修改入库单的时候才有的字段】
    forceSubmit?: number;
}

export interface GetSupplierListReq {
    clinicId?: string;
    keyword?: string;
    offset?: string;
    limit?: string;
    status?: string;

    /**
     * 杭州代煎代配
     */
    pharmacyType?: number; //参数，不传默认为0本地实体药房(同一类型药房用相同供应商)
}

export interface UpdateInventoryInOrderInfoReq {
    lastModifiedDate: Date;
    outOrderNo: string;
    supplierId: string;
    id: string;
    inspectBy?: string;
}

interface UpdateInventoryInOrderInfoDetailItem {
    after?: string;
    before?: string;
    field?: string;
}

interface UpdateInventoryInOrderInfoDetail {
    fields: UpdateInventoryInOrderInfoDetailItem[];
}

export class UpdateInventoryInOrderInfoRsp {
    detail?: UpdateInventoryInOrderInfoDetail;
}

export interface AddNewSupplierReq {
    contact: string;
    license: string;
    mark: string;
    mobile: string;
    name: string;
    status: number;
    pharmacyType?: number;
    extendInfo?: ExtendInfo;
}
class PutInStockOrderConfirmItem {
    id?: string;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];
}
export interface PutInStockOrderConfirm {
    lastModifiedDate?: Date;
    list?: PutInStockOrderConfirmItem[];
    supplier?: string;
    comment?: string;
    pharmacyNo?: number;
    pass?: number; //入库单审核的值 1 通过，0 拒绝。入库单确认无用
}

export interface PutInStockOrderMedicineReq {
    orderId: string;
    medicineId: string;

    batchNo?: string;
    expiryDate?: string;
    goodsId?: string;
    lastModifiedDate?: Date;
    productionDate?: string;
    useCount?: number;
    useTotalCostPrice?: number;
    useUnit?: string;
    useUnitCostPrice?: number;
    traceableCodeList?: TraceableCodeList[];
}

export class PutInStockOrderDetailReq extends InventoryInReq {
    id?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    @JsonProperty({ type: Array, clazz: InventoryInUpdateItem })
    list?: InventoryInUpdateItem[];
}

export class PutInStockOrderMedicineRsp {
    batchNo?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    lastModifiedUserId?: string;
}

export class GetStockInDraftListRsp {
    @JsonProperty({ type: Array, clazz: InventoryInDraftDetail })
    rows?: InventoryInDraftDetail[];
}

export class InventoryInAgent {
    static InventoryInPublisher: Subject<number> = new Subject<number>();

    /**
     * 获取入库信息
     */
    static getInventoryIn(params: {
        status?: number;
        dateField?: string;
        clinicId?: string;
        supplierId?: string;
        offset?: number;
        limit?: number;
        begDate?: Date; // 开始 日期
        endDate?: Date; // 结束 日期
        withGoodsId?: string;
        // withReturnLeft:; // 向左返回
        settlementStatus?: number; // 结算状态
        pharmacyNo?: number; //药房序号，不传为pharmacyType的汇总
        pharmacyType?: number; //药房类型 0 本地 1 空中 2 虚拟 如果不传为默认0
        type?: number; //单据类型过滤(0--采购入库，10-退货出库)
    }): Promise<GetInventoryInRsp> {
        const { begDate, endDate, limit, offset, ...others } = params;
        return ABCApiNetwork.get("goods/stocks/in/orders", {
            queryParameters: {
                begDate: TimeUtils.formatDate(begDate),
                endDate: TimeUtils.formatDate(endDate),
                limit: limit,
                offset: offset,
                ...others,
            },
            clazz: GetInventoryInRsp,
            clearUndefined: true,
        });
    }

    /**
     * 新增入库
     */
    static addInventory(req: InventoryInReq): Promise<InventoryInRsp> {
        const { list, ...others } = req;
        return ABCApiNetwork.post("goods/stocks/in/orders", {
            clazz: InventoryInRsp,
            body: {
                ...others,
                list: list?.map((item) => {
                    const _item = item;

                    /**
                     * 后台只认 "yyyy-MM-dd" 格式
                     */
                    //@ts-ignore
                    _item.expiryDate = TimeUtils.formatDate(item.expiryDate);
                    //@ts-ignore
                    _item.productionDate = TimeUtils.formatDate(item.productionDate);
                    return _item;
                }),
            },
        }).then((rsp) => {
            this.InventoryInPublisher.next();
            return rsp;
        });
    }

    /**
     * 选择供应商列表
     */
    static getSupplierList(params: GetSupplierListReq): Promise<SupplierItem[]> {
        const { clinicId, keyword = "", offset = "", limit = "", status = "", ...others } = params;
        return ABCApiNetwork.get<{ rows: SupplierItem[] }>(`goods/supplier/search`, {
            queryParameters: {
                clinicId: clinicId,
                keyword: keyword,
                offset: offset,
                limit: limit,
                status: status, // 1 新建
                ...others,
            },
            clearUndefined: true,
        }).then((rsp) => rsp.rows?.map((item) => JsonMapper.deserialize(SupplierItem, item)) ?? []);
    }

    /**
     * 获取入库单详情
     */
    static getInventoryInSheetDetails(params: GetInventoryInSheetDetailsReq): Promise<InventoryInSheetPageDetails> {
        const { id, ...others } = params;
        return ABCApiNetwork.get(`goods/stocks/in/orders/${id}`, {
            queryParameters: { ...others },
            clazz: InventoryInSheetPageDetails,
        });
    }

    /**
     * @desc 修改入库单供应商和随货单号信息
     */
    static updateInventoryInOrderInfo(params: UpdateInventoryInOrderInfoReq): Promise<UpdateInventoryInOrderInfoRsp> {
        const { id, ...others } = params;
        return ABCApiNetwork.put(`goods/stocks/in/orders/${id}/orderinfo`, {
            body: others,
            clazz: UpdateInventoryInOrderInfoRsp,
        }).then((rsp) => {
            this.InventoryInPublisher.next();
            return rsp;
        });
    }

    /**
     * @desc 检查是否有重复的供应商name
     */
    static checkRepeatSupplier(name: string): Promise<[]> {
        return ABCApiNetwork.get(`goods/supplier/name/${name}`);
    }

    /**
     * @desc 检查是否有重复的序列证号
     */
    static checkRepeatLicense(license: string): Promise<[]> {
        return ABCApiNetwork.get<{ rows: [] }>(`goods/supplier/license/${license}`).then((rsp) => rsp.rows);
    }

    /**
     * @desc 新增供应商
     */
    static addNewSupplier(param: AddNewSupplierReq): Promise<SupplierItem> {
        return ABCApiNetwork.post(`goods/supplier`, { clazz: SupplierItem, body: param });
    }

    /**
     * 入库单审核
     * @desc 入库单审核
     * @param orderId
     * @param params
     */
    static putInStockOrderReview(
        orderId: string,
        params: { pass: number; comment?: string; lastModifiedDate?: Date; pharmacyNo?: number }
    ): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/in/orders/${orderId}/review`, { body: params }).then(() => this.InventoryInPublisher.next());
    }

    /**
     * 入库单确认入库
     * @desc 入库单审核
     * @param orderId
     * @param params
     */
    static putInStockOrderConfirm(orderId: string, params: PutInStockOrderConfirm): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/in/orders/${orderId}/confirm`, { body: params }).then(() =>
            this.InventoryInPublisher.next()
        );
    }

    /**
     * 入库单取消入库
     * @desc 入库单审核
     * @param orderId
     * @param params
     */
    static putInStockOrderReject(
        orderId: string,
        params: { comment?: string; lastModifiedDate?: Date; pass?: number; pharmacyNo?: number }
    ): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/in/orders/${orderId}/reject`, { body: params }).then(() => this.InventoryInPublisher.next());
    }

    /**
     * 入库单修改单条数据
     * @desc 修改单条数据
     * @param params
     */
    static putInStockOrderMedicine(params: PutInStockOrderMedicineReq): Promise<PutInStockOrderMedicineRsp> {
        const { medicineId, orderId, ...others } = params;
        return ABCApiNetwork.put(`goods/stocks/in/orders/${orderId}/${medicineId}`, {
            body: others,
            clazz: PutInStockOrderMedicineRsp,
        }).then((rsp) => {
            this.InventoryInPublisher.next();
            return rsp;
        });
    }

    /**
     * 入库单撤销
     * @desc 入库单撤销
     * @param orderId
     */
    static putInStockOrderRevoke(orderId?: string): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/in/orders/${orderId}/revoke`).then(() => this.InventoryInPublisher.next());
    }

    /**
     * 修改入库单
     * @desc 修改入库单
     */
    static putInStockOrderDetail(params: PutInStockOrderDetailReq): Promise<void> {
        const { id, ...others } = params;
        return ABCApiNetwork.put(`goods/stocks/in/orders/${id}`, {
            body: {
                ...others,
                list: others.list?.map((item) => {
                    const _item = item;
                    //@ts-ignore
                    _item.expiryDate = TimeUtils.formatDate(item.expiryDate);
                    //@ts-ignore
                    _item.productionDate = TimeUtils.formatDate(item.productionDate);
                    return _item;
                }),
            },
        }).then(() => this.InventoryInPublisher.next());
    }

    /**
     * 获取入库草稿单列表
     */
    static getStockInDraftList(): Promise<GetStockInDraftListRsp> {
        return ABCApiNetwork.get("goods/stocks/in/draft", {
            clazz: GetStockInDraftListRsp,
        });
    }

    /**
     * 新增入库草稿单
     */
    static createStockInDraft(body: InventoryInDraftReq): Promise<InventoryInDraftDetail> {
        const { list, pharmacyNo, ...others } = body;
        const _list = list.map((item) => {
            return {
                ...item,
                expiryDate: item.expiryDate?.format("yyyy-MM-dd"),
                productionDate: item.productionDate?.format("yyyy-MM-dd"),
            };
        });
        return ABCApiNetwork.post("goods/stocks/in/draft", {
            body: { pharmacyNo: pharmacyNo ?? 0, pharmacyType: 0, list: _list ?? [], ...others },
            clazz: InventoryInDraftDetail,
        }).then((rsp) => {
            InventoryInDraftManage.inventoryInDraftManageInstance.draftObserver.next();
            return rsp;
        });
    }

    /**
     * 删除入库草稿单
     */
    static deleteStockInDraft(draftId: string): Promise<InventoryInDraftDetail> {
        return ABCApiNetwork.delete(`goods/stocks/in/draft/${draftId}`, {
            clazz: InventoryInDraftDetail,
        }).then((rsp) => {
            InventoryInDraftManage.inventoryInDraftManageInstance.draftObserver.next();
            return rsp;
        });
    }

    /**
     * 删除入库草稿单
     */
    static getStockInDraftDetail(draftId: string): Promise<InventoryInDraftDetail> {
        return ABCApiNetwork.get(`goods/stocks/in/draft/${draftId}`, {
            clazz: InventoryInDraftDetail,
        }).then((rsp) => {
            return rsp;
        });
    }

    /**
     * 修改入库草稿单
     */
    static putStockInDraftDetail(draftId: string, body: InventoryInDraftReq): Promise<InventoryInDraftDetail> {
        const { list, pharmacyNo, ...others } = body;
        return ABCApiNetwork.put(`goods/stocks/in/draft/${draftId}`, {
            body: {
                pharmacyNo: pharmacyNo ?? 0,
                pharmacyType: 0,
                list: list.map((item) => {
                    return {
                        ...item,
                        expiryDate: item.expiryDate?.format("yyyy-MM-dd"),
                        productionDate: item.productionDate?.format("yyyy-MM-dd"),
                    };
                }),
                ...others,
            },
            clazz: InventoryInDraftDetail,
        }).then((rsp) => {
            return rsp;
        });
    }
}
