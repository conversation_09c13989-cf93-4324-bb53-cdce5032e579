/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/1
 *
 * @description
 */
import { ToolBar, ToolBarButtonStyle1 } from "../../base-ui";
import React from "react";
import { View, Text } from "@hippy/react";
import { InventoryMedicineItem } from "../data/inventory-draft";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventoryInMedicineAddEditViewBloc } from "./inventory-in-medicine-add-edit-view-bloc";
import { Colors, TextStyles } from "../../theme";
import { InventoryInMedicineAddEditView } from "./inventory-in-medicine-add-edit-view";
import { BlocBuilder } from "../../bloc";
import { BaseBlocPage } from "../../base-ui/base-page";
import { Toast } from "../../base-ui/dialog/toast";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";

export class InventoryMedicineEditResult {
    static actionDelete = 0;

    static actionChanged = 1;

    action: number;
    newItem?: InventoryMedicineItem;

    constructor(action: number, newItem?: InventoryMedicineItem) {
        this.action = action;
        this.newItem = newItem;
    }
}

interface InventoryMedicineEditPageProps {
    item: InventoryMedicineItem;
    isEditOnline?: boolean;
    asyncSaveTap?: (detail: InventoryMedicineItem) => void;
    isYiBaoCollect?: boolean;
}

export class InventoryInMedicineEditPage extends BaseBlocPage<InventoryMedicineEditPageProps, InventoryInMedicineAddEditViewBloc> {
    static editItem(
        item: InventoryMedicineItem,
        options?: { isEditOnline?: boolean; asyncSaveTap?: (detail: InventoryMedicineItem) => void; isYiBaoCollect?: boolean }
    ): Promise<InventoryMedicineEditResult> {
        const { isEditOnline, asyncSaveTap, isYiBaoCollect } = options || {};
        return ABCNavigator.navigateToPage(
            <InventoryInMedicineEditPage
                item={item}
                isEditOnline={isEditOnline}
                asyncSaveTap={asyncSaveTap}
                isYiBaoCollect={isYiBaoCollect}
            />
        );
    }

    bloc: InventoryInMedicineAddEditViewBloc;

    constructor(props: InventoryMedicineEditPageProps) {
        super(props);
        this.bloc = new InventoryInMedicineAddEditViewBloc({ isEditOnline: props.isEditOnline, isYiBaoCollect: props?.isYiBaoCollect });
        this.addDisposable(this.bloc);
        this.bloc.requestUpdateFromItem(this.props.item);
    }

    getAppBarTitle(): string {
        if (this.props.isEditOnline) return "修改药品";
        return "入库药品";
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        if (!state.isEdit) {
            return [
                <View
                    key={0}
                    onClick={async () => {
                        if (this.props.isEditOnline) {
                            await showConfirmDialog("请在 PC端完成入库单修改，app 功能正在建设中", "");
                            return;
                        }
                        this.bloc.requestModifyEditStatus();
                    }}
                >
                    <Text style={TextStyles.t16NM}>修改</Text>
                </View>,
            ];
        }
        if (this.props.isEditOnline) return [];
        return [
            <View
                key={0}
                onClick={() => {
                    ABCNavigator.pop(new InventoryMedicineEditResult(InventoryMedicineEditResult.actionDelete));
                }}
            >
                <Text style={TextStyles.t16NR2}>删除</Text>
            </View>,
        ];
    }

    getAppBar(): JSX.Element {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { v2DisableStatus } = this.props.item;
        return (
            <View style={{ flex: 1 }}>
                <View style={{ flex: 1 }}>
                    <InventoryInMedicineAddEditView bloc={this.bloc} isYiBaoCollect={this.props.isYiBaoCollect} />
                </View>

                {state.isEdit && (
                    <ToolBar hideWhenKeyboardShow={true}>
                        <ToolBarButtonStyle1
                            text={"保存"}
                            style={{
                                backgroundColor: !!v2DisableStatus ? Colors.P5 : Colors.mainColor,
                                borderColor: !!v2DisableStatus ? Colors.P5 : Colors.mainColor,
                                fontColor: !!v2DisableStatus ? Colors.T2 : Colors.white,
                            }}
                            onClick={
                                !!v2DisableStatus
                                    ? () => {
                                          Toast.show("当前药品已被禁用", { warning: true });
                                      }
                                    : () => {
                                          if (!this.bloc.validate()) return;
                                          if (this.props.asyncSaveTap) {
                                              this.props.asyncSaveTap(this.bloc.currentState.toInventoryMedicineItem());
                                          } else {
                                              ABCNavigator.pop(
                                                  new InventoryMedicineEditResult(
                                                      InventoryMedicineEditResult.actionChanged,
                                                      this.bloc.currentState.toInventoryMedicineItem()
                                                  )
                                              );
                                          }
                                      }
                            }
                        />
                    </ToolBar>
                )}
            </View>
        );
    }
}
