/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/31
 *
 * @description
 */
import { BaseBlocComponent } from "../../base-ui/base-component";
import { InventoryInMedicineAddEditViewBloc } from "./inventory-in-medicine-add-edit-view-bloc";
import { ScrollView, Style, Text, View } from "@hippy/react";
import React from "react";
import { ABCStyles, ABCStyleSheet, Colors, Sizes, TextStyles } from "../../theme";
import _ from "lodash";
import { IconFontView, SizedBox } from "../../base-ui";
import { GroupDivider } from "../../base-ui/divider-line";
import {
    ListSettingDateEditItem,
    ListSettingEditItem,
    ListSettingItem,
    ListSettingRadiosItem,
} from "../../base-ui/views/list-setting-item";
import { MedicineSellUnitSelectButton } from "../../charge/view/charge-views";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { InventoryConst } from "../inventory-const";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import colors from "../../theme/colors";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import { AbcView } from "../../base-ui/views/abc-view";
import { NumberUtils } from "../../common-base-module/utils";
import { UIUtils } from "../../base-ui/utils";
import { OverlayTips } from "../../base-ui/dialog/overlay-tips";
import { userCenter } from "../../user-center";

const styles = ABCStyleSheet.create({
    groupContainer: {
        paddingHorizontal: Sizes.listHorizontalMargin,
        backgroundColor: Colors.white,
    },
});

interface InventoryMedicineAddEditViewProps {
    bloc: InventoryInMedicineAddEditViewBloc;
    isYiBaoCollect?: boolean;
}

export class InventoryInMedicineAddEditView extends BaseBlocComponent<
    InventoryMedicineAddEditViewProps,
    InventoryInMedicineAddEditViewBloc
> {
    private _iconViewRef?: View | null;
    private _iconViewProRef?: View | null;
    private _stockInputRef?: AbcTextInput | null;
    constructor(props: InventoryMedicineAddEditViewProps) {
        super(props);
        this.bloc = this.props.bloc;
    }

    protected takeBlocOwnership(): boolean {
        return false;
    }

    private _inputStyle: Style = {
        width: Sizes.dp65,
        height: Sizes.dp32,
        textAlign: "center",
        borderRightWidth: Sizes.dp1,
        borderColor: colors.P6,
        fontSize: Sizes.dp16,
    };

    private _inputBorderStyle: Style = {
        flexDirection: "row",
        borderRadius: Sizes.dp3,
        borderWidth: Sizes.dp1,
    };

    componentWillReceiveProps(nextProps: Readonly<InventoryMedicineAddEditViewProps> /*, nextContext: any*/): void {
        this.bloc = nextProps.bloc;
    }

    private async _showTip(_icon?: View | null, text?: string): Promise<void> {
        const layout = await UIUtils.measureInWindow(_icon);
        OverlayTips.show(text ?? "", layout, {
            preferPosition: "center",
            backgroundColor: Colors.black,
            textStyle: { ...TextStyles.t14NW, marginHorizontal: Sizes.dp10 },
            arrowSize: Sizes.dp10,
            borderRadius: Sizes.dp4,
        }).then();
    }

    renderContent(): JSX.Element {
        const {
            goodsInfo,
            useCount,
            useUnit,
            costPrice,
            batchNumber,
            productionTime,
            expiredTime,
            showErrorHint,
            isEdit,
            showExpiryDateWaring,
            showProductionDateWaring,
            showExpiryDateTip,
            extendData,
        } = this.bloc.currentState;

        if (!goodsInfo) return <View />;

        let prescription = 0;

        if (
            goodsInfo.isChineseMedicine ||
            goodsInfo.isHealthMedicine ||
            goodsInfo.isHealthGood ||
            goodsInfo.isGoodsHomemade ||
            goodsInfo.isOtherGood
        ) {
            prescription = 2;
        } else if (goodsInfo.isGoods) {
            //【ID1002166】济华-商品支持输入小数
            prescription = 4;
        }

        let specification = goodsInfo.packageSpec;
        if (_.isEmpty(specification)) specification = `${goodsInfo.cMSpec} ${goodsInfo.pieceUnit}`;
        const manufacturer = goodsInfo?.manufacturerFull || goodsInfo?.manufacturer;
        return (
            <ScrollView>
                <View
                    style={[
                        ABCStyles.bottomLine,
                        { paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp10, backgroundColor: Colors.white },
                    ]}
                >
                    <View style={[ABCStyles.rowAlignCenter]}>
                        <Text style={[TextStyles.t16MT1, { flexShrink: 1, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                            {goodsInfo?.displayName ?? ""}
                        </Text>
                        <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp8, lineHeight: Sizes.dp20 }]}>
                            {goodsInfo?.packageSpec ?? ""}
                        </Text>
                    </View>
                    <SizedBox height={Sizes.dp4} />
                    <View style={[ABCStyles.rowAlignCenterSpaceBetween, { marginTop: Sizes.dp4 }]}>
                        <Text style={[TextStyles.t14NT2, { flexShrink: 1, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {manufacturer ?? ""}
                        </Text>
                        <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20 }]}>{`${goodsInfo?.displayTypeName}`}</Text>
                    </View>
                </View>

                <GroupDivider />
                <View style={styles.groupContainer}>
                    <ListSettingItem
                        title={"进价"}
                        starTitle={"*"}
                        contentHint={"输入药品进货单价"}
                        bottomLine={true}
                        contentBuilder={() => {
                            return (
                                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                                    <View
                                        style={[
                                            this._inputBorderStyle,
                                            {
                                                borderColor:
                                                    (showErrorHint && !_.isNumber(costPrice)) || costPrice == 0 ? Colors.R2 : colors.P6,
                                            },
                                            isEdit ? {} : { backgroundColor: Colors.D2, borderColor: Colors.P5 },
                                        ]}
                                    >
                                        <_MedicineSellUnitSelectButton unit={"¥"} />
                                        <AbcTextInput
                                            autoFocus={costPrice == undefined}
                                            style={{
                                                ...this._inputStyle,
                                                borderLeftWidth: 1,
                                                borderRadius: 1,
                                                borderColor: isEdit ? colors.P6 : Colors.P5,
                                            }}
                                            editable={isEdit}
                                            multiline={false}
                                            accessibilityLabel={"inventory_purchase_price"}
                                            defaultValue={
                                                !_.isUndefined(costPrice)
                                                    ? NumberUtils.formatPriceToFixedWithoutZero(costPrice ?? 0, 2).toString()
                                                    : ""
                                            }
                                            onEndEditing={() => {
                                                this._stockInputRef?.focus();
                                            }}
                                            onChangeText={(num) => this.bloc.requestUpdateCostPrice(StringUtils.parseFloat(num)!)}
                                            syncTextOnBlur={true}
                                            customKeyboardBuilder={new NumberKeyboardBuilder()}
                                            formatter={PrecisionLimitFormatter(InventoryConst.medicineCostPrice)} // 限制输入框仅输入数字
                                        />
                                        <_MedicineSellUnitSelectButton unit={useUnit} />
                                    </View>
                                </View>
                            );
                        }}
                    />

                    <ListSettingItem
                        title={"入库数量"}
                        starTitle={"*"}
                        bottomLine={true}
                        contentBuilder={() => {
                            return (
                                <View style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                                    <View
                                        style={[
                                            this._inputBorderStyle,
                                            {
                                                borderColor:
                                                    (showErrorHint && !_.isNumber(useCount)) || useCount == 0 ? Colors.R2 : colors.P6,
                                            },
                                            isEdit ? {} : { backgroundColor: Colors.D2, borderColor: Colors.P5 },
                                        ]}
                                    >
                                        <AbcTextInput
                                            ref={(ref) => (this._stockInputRef = ref)}
                                            style={{ ...this._inputStyle, borderColor: isEdit ? colors.P6 : Colors.P5 }}
                                            editable={isEdit}
                                            multiline={false}
                                            accessibilityLabel={"inventory_quantity_of_storage"}
                                            defaultValue={useCount?.toString() ?? ""}
                                            onChangeText={(text) => {
                                                const num = Number(text);
                                                this.bloc.requestUpdateUseNum(_.isNumber(num) ? num : 0);
                                            }}
                                            syncTextOnBlur={true}
                                            customKeyboardBuilder={new NumberKeyboardBuilder()}
                                            formatter={PrecisionLimitFormatter(prescription)} // 限制输入框仅输入数字
                                        />
                                        <_MedicineSellUnitSelectButton
                                            unit={useUnit}
                                            units={goodsInfo!.canUseUnits}
                                            onUnitSelected={(unit) => {
                                                if (isEdit) this.bloc.requestUpdateUseUnit(unit);
                                            }}
                                        />
                                    </View>
                                </View>
                            );
                        }}
                    />
                </View>

                <View style={styles.groupContainer}>
                    <ListSettingEditItem
                        title={"批号"}
                        contentHint={isEdit ? "输入批号" : "--"}
                        content={batchNumber ?? ""}
                        contentLeftAlign={false}
                        editable={isEdit}
                        contentTextStyle={[{ textAlign: "right" }]}
                        textInputStyle={TextStyles.t16NT1.copyWith({ color: isEdit ? Colors.T1 : Colors.T2 })}
                        onChanged={(value) => this.bloc.requestUpdateBatchNum(value)}
                        bottomLine={true}
                    />

                    <ListSettingDateEditItem
                        title={"生产日期"}
                        contentHint={isEdit ? "输入药品生产日期" : "--"}
                        startIcon={
                            showProductionDateWaring
                                ? () => {
                                      return (
                                          <View
                                              collapsable={false}
                                              ref={(ref) => {
                                                  this._iconViewProRef = ref;
                                              }}
                                              onClick={() => {
                                                  this._showTip(this._iconViewProRef, "生产日期不可选择未来时间").then();
                                              }}
                                          >
                                              <IconFontView
                                                  name={"information"}
                                                  size={Sizes.dp16}
                                                  color={Colors.Y2}
                                                  style={{ ...Sizes.paddingLTRB(Sizes.dp4) }}
                                              />
                                          </View>
                                      );
                                  }
                                : undefined
                        }
                        date={productionTime}
                        contentTextStyle={[TextStyles.t16NT1.copyWith({ color: isEdit ? Colors.T1 : Colors.T2 }), { textAlign: "right" }]}
                        contentHintTextStyle={[TextStyles.t16NT4, { textAlign: "right" }]}
                        editable={isEdit}
                        onChanged={(date) => this.bloc.requestUpdateProductionDate(date)}
                        bottomLine={true}
                    />

                    <ListSettingDateEditItem
                        title={"效期"}
                        contentHint={isEdit ? "输入药品有效期" : "--"}
                        startIcon={
                            showExpiryDateWaring
                                ? () => {
                                      return (
                                          <View
                                              collapsable={false}
                                              ref={(ref) => {
                                                  this._iconViewRef = ref;
                                              }}
                                              onClick={() => {
                                                  this._showTip(
                                                      this._iconViewRef,
                                                      showExpiryDateTip ? "效期不可小于或等于生产日期" : "该药品将在一年半之内过期"
                                                  ).then();
                                              }}
                                          >
                                              <IconFontView
                                                  name={"information"}
                                                  size={Sizes.dp16}
                                                  color={Colors.Y2}
                                                  style={{ ...Sizes.paddingLTRB(Sizes.dp4) }}
                                              />
                                          </View>
                                      );
                                  }
                                : undefined
                        }
                        date={expiredTime}
                        contentTextStyle={[TextStyles.t16NT1.copyWith({ color: isEdit ? Colors.T1 : Colors.T2 }), { textAlign: "right" }]}
                        contentHintTextStyle={[TextStyles.t16NT4, { textAlign: "right" }]}
                        editable={isEdit}
                        onChanged={(date) => this.bloc.requestUpdateExpiredDate(date)}
                    />
                    {userCenter.clinic?.isNanjingClinic && !!this.props?.isYiBaoCollect && (
                        <View>
                            <ListSettingItem
                                title={"医保码"}
                                content={extendData?.shebaoCode ?? ""}
                                contentTextStyle={[{ textAlign: "right" }]}
                                bottomLine={true}
                            />
                            <ListSettingEditItem
                                title={"平台产品编码"}
                                starTitle={"*"}
                                contentHint={isEdit ? "输入药品平台产品编码" : "--"}
                                content={extendData?.erpGoodsId}
                                contentLeftAlign={false}
                                editable={isEdit}
                                contentTextStyle={[{ textAlign: "right" }]}
                                textInputStyle={TextStyles.t16NT1.copyWith({ color: isEdit ? Colors.T1 : Colors.T2 })}
                                onChanged={(value) => this.bloc.requestModifyErpGoodsId(value)}
                                bottomLine={true}
                                maxLength={20}
                                contentHintTextStyle={Colors.R2}
                            />
                            <ListSettingRadiosItem
                                starTitle={"*"}
                                style={{ justifyContent: "space-between" }}
                                title={"应急采购"}
                                titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                                options={["是", "否"]}
                                enable={isEdit}
                                check={extendData?.emergencyFlag ? "是" : "否"}
                                marginBetweenItem={Sizes.dp24}
                                bottomLine={true}
                                onChanged={(value) => this.bloc.requestModifyEmergencyFlag(value === "是" ? 1 : 0)}
                            />
                            <ListSettingEditItem
                                title={"订单明细ID"}
                                starTitle={"*"}
                                contentHint={isEdit ? "输入药品订单明细ID" : "--"}
                                content={extendData?.erpOrderItemId ?? ""}
                                contentLeftAlign={false}
                                editable={isEdit}
                                contentTextStyle={[{ textAlign: "right" }]}
                                textInputStyle={TextStyles.t16NT1.copyWith({ color: isEdit ? Colors.T1 : Colors.T2 })}
                                onChanged={(value) => this.bloc.requestModifyErpOrderItemId(value)}
                                bottomLine={true}
                                maxLength={30}
                                contentHintTextStyle={Colors.R2}
                            />
                        </View>
                    )}
                </View>
            </ScrollView>
        );
    }
}

class _MedicineSellUnitSelectButton extends MedicineSellUnitSelectButton {
    render(): JSX.Element {
        const { unit, units } = this.props;
        const selectEnable = (units?.length ?? 0) > 1;
        const backgroundColor = !selectEnable ? Colors.D2 : undefined;

        return (
            <AbcView
                style={{
                    backgroundColor: backgroundColor,
                    justifyContent: "center",
                    alignItems: "center",
                }}
                onClick={() => this._onClick()}
            >
                <Text
                    style={[
                        TextStyles.t16MT1,
                        Sizes.paddingLTRB(Sizes.dp8, 0),
                        {
                            textAlign: "center",
                            lineHeight: Sizes.dp32,
                        },
                    ]}
                >
                    {unit ?? ""}
                </Text>
            </AbcView>
        );
    }
}
