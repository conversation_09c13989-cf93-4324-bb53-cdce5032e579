/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-07-1
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { actionEvent, EventName } from "../../bloc/bloc";
import { GoodsInfo, TraceableCodeList } from "../../base-business/data/beans";
import { InventoryMedicineItem } from "../data/inventory-draft";
import { LogUtils } from "../../common-base-module/log";
import { Toast } from "../../base-ui/dialog/toast";
import { InventoryConst } from "../inventory-const";
import { TimeUtils } from "../../common-base-module/utils";
import { InventoryExtendDataItem } from "../data/inventory-bean";
import { isNil } from "lodash";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { userCenter } from "../../user-center";

class State {
    goodsInfo?: GoodsInfo;
    qrcode?: string; //条形码

    useUnit?: string; //包装单位
    useCount?: number; //入库量

    costPrice?: number; //进价
    expiredTime?: Date; //效期
    productionTime?: Date; //效期
    addInventory?: number; // 入库数量
    batchNumber?: string; //批号

    showErrorHint?: boolean;

    isEdit = true;
    extendData?: InventoryExtendDataItem;
    traceableCodeList?: TraceableCodeList[];

    get showExpiryDateTip(): boolean {
        return TimeUtils.showExpiryDateTip(this.productionTime, this.expiredTime);
    }

    get validateExpirationTime(): boolean {
        return TimeUtils.validateExpirationTime(this.productionTime, this.expiredTime);
    }

    get showExpiryDateWaring(): boolean {
        return this.showExpiryDateTip || this.validateExpirationTime;
    }

    get showProductionDateWaring(): boolean {
        return !!this.productionTime && this.productionTime?.getTime() > new Date().getTime();
    }

    static fromInventoryMedicineItem(item: InventoryMedicineItem): State {
        const state = new State();
        state.goodsInfo = item.goodsInfo;
        state.qrcode = item.qrcode;
        state.useUnit = item.useUnit;
        state.useCount = item.useCount;
        state.costPrice = item.useUnitCostPrice;
        state.expiredTime = item.expiredTime;
        state.addInventory = item.addInventory;
        state.batchNumber = item.batchNumber;
        state.productionTime = item.productionDate;
        state.extendData = JsonMapper.deserialize(InventoryExtendDataItem, {
            ...item.extendData,
            emergencyFlag: item.extendData?.emergencyFlag ?? 0,
        });
        state.traceableCodeList = item.traceableCodeList;

        return state;
    }

    toInventoryMedicineItem(): InventoryMedicineItem {
        const item = new InventoryMedicineItem();
        item.goodsInfo = this.goodsInfo;
        item.qrcode = this.qrcode;
        item.useUnit = this.useUnit;
        item.useCount = this.useCount;
        item.useUnitCostPrice = this.costPrice;
        item.expiredTime = this.expiredTime;
        item.addInventory = this.addInventory;
        item.batchNumber = this.batchNumber;
        item.productionDate = this.productionTime;
        item.extendData = this.extendData;
        item.traceableCodeList = this.traceableCodeList;

        return item;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventUpdateFromInventoryMedicineItem extends _Event {
    item: InventoryMedicineItem;

    constructor(item: InventoryMedicineItem) {
        super();
        this.item = item;
    }
}

class _EventUpdateProductionDate extends _Event {
    constructor(date: Date) {
        super();
        this.date = date;
    }

    date: Date;
}

class _EventUpdateBatchNum extends _Event {
    constructor(batchNum?: string) {
        super();
        this.batchNum = batchNum;
    }

    batchNum?: string;
}

class _EventUpdateUseNum extends _Event {
    constructor(useNum: number) {
        super();
        this.useNum = useNum;
    }

    useNum: number;
}

class _EventUpdateUseUnit extends _Event {
    constructor(packageUnit?: string) {
        super();
        this.packageUnit = packageUnit;
    }

    packageUnit?: string;
}

class _EventUpdateCostPrice extends _Event {
    constructor(price: number) {
        super();
        this.price = price;
    }

    price: number;
}

class _EventUpdateExpiredDate extends _Event {
    time: Date;

    constructor(time: Date) {
        super();
        this.time = time;
    }
}

class _EventModifyEditStatus extends _Event {}
class _EventModifyErpGoodsId extends _Event {
    id?: string;
    constructor(id?: string) {
        super();
        this.id = id;
    }
}
class _EventModifyEmergencyFlag extends _Event {
    emergencyFlag?: number;
    constructor(emergencyFlag?: number) {
        super();
        this.emergencyFlag = emergencyFlag;
    }
}
class _EventModifyErpOrderItemId extends _Event {
    id?: string;
    constructor(id?: string) {
        super();
        this.id = id;
    }
}

class InventoryInMedicineAddEditViewBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryInMedicineAddEditViewBloc | undefined>(undefined);
    private _isEditOnline: boolean;
    private _isYiBaoCollect?: boolean;

    static fromContext(context: InventoryInMedicineAddEditViewBloc): InventoryInMedicineAddEditViewBloc {
        return context;
    }

    constructor(options?: { isEditOnline?: boolean; isYiBaoCollect?: boolean }) {
        super();

        this._isEditOnline = !!options?.isEditOnline;
        this._isYiBaoCollect = !!options?.isYiBaoCollect;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateExpiredDate, this._mapEventUpdateExpiredDate);
        map.set(_EventUpdateProductionDate, this._mapEventUpdateProductionDate);
        map.set(_EventUpdateCostPrice, this._mapEventUpdateCostPrice);
        map.set(_EventUpdateUseUnit, this._mapEventUpdateUseUnit);
        map.set(_EventUpdateUseNum, this._mapEventUpdateUseNum);
        map.set(_EventUpdateBatchNum, this._mapEventUpdateBatchNum);
        map.set(_EventUpdateFromInventoryMedicineItem, this._mapEventUpdateFromInventoryMedicineItem);
        map.set(_EventModifyEditStatus, this._mapEventModifyEditStatus);

        return map;
    }

    private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        this.innerState.isEdit = !this._isEditOnline;
        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }

        yield this.innerState.clone();
    }

    private async *_mapEventUpdateExpiredDate(event: _EventUpdateExpiredDate): AsyncGenerator<State> {
        this.innerState.expiredTime = event.time;

        yield this.innerState.clone();
    }

    private async *_mapEventUpdateProductionDate(event: _EventUpdateProductionDate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.productionTime = event.date;

        yield innerState.clone();
    }

    private async *_mapEventUpdateCostPrice(event: _EventUpdateCostPrice): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.costPrice = event.price;
        yield innerState.clone();
    }

    private async *_mapEventUpdateUseUnit(event: _EventUpdateUseUnit): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.useUnit = event.packageUnit;

        const units = innerState.goodsInfo!.stockInUnits;

        //选择小单位g时去掉小数位
        if (units.length > 1 && units.indexOf(this.innerState.useUnit ?? "") == 0) {
            innerState.useCount = Math.trunc(innerState.useCount ?? 0);
        }

        yield innerState.clone();
    }

    private async *_mapEventUpdateUseNum(event: _EventUpdateUseNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.useCount = event.useNum;
        yield innerState.clone();
    }

    private async *_mapEventUpdateBatchNum(event: _EventUpdateBatchNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.batchNumber = event.batchNum;
        yield innerState.clone();
    }

    private async *_mapEventUpdateFromInventoryMedicineItem(event: _EventUpdateFromInventoryMedicineItem): AsyncGenerator<State> {
        const isEdit = this.innerState.isEdit;
        this._innerState = State.fromInventoryMedicineItem(event.item);
        const innerState = this.innerState;
        innerState.isEdit = isEdit;
        innerState.useUnit = innerState.useUnit ?? innerState.goodsInfo?.unitPreferPackage;
        yield innerState.clone();
    }

    private async *_mapEventModifyEditStatus(): AsyncGenerator<State> {
        this.innerState.isEdit = true;
        this.update();
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }

    // 设置过期时间
    public requestUpdateExpiredDate(time: Date): void {
        this.dispatch(new _EventUpdateExpiredDate(time));
    }

    //设置生产日期
    public requestUpdateProductionDate(time: Date): void {
        this.dispatch(new _EventUpdateProductionDate(time));
    }

    //设置进价
    public requestUpdateCostPrice(price: number): void {
        this.dispatch(new _EventUpdateCostPrice(price));
    }

    //更新本次入库数量
    public requestUpdateUseNum(useNum: number): void {
        this.dispatch(new _EventUpdateUseNum(useNum));
    }

    //更新本次入库单位
    public requestUpdateUseUnit(useUnit: string): void {
        this.dispatch(new _EventUpdateUseUnit(useUnit));
    }

    //更新批号
    public requestUpdateBatchNum(batchNo: string): void {
        this.dispatch(new _EventUpdateBatchNum(batchNo));
    }

    public requestUpdateFromItem(item: InventoryMedicineItem): void {
        this.dispatch(new _EventUpdateFromInventoryMedicineItem(item));
    }

    public requestModifyEditStatus(): void {
        this.dispatch(new _EventModifyEditStatus());
    }
    requestModifyErpGoodsId(id?: string): void {
        this.dispatch(new _EventModifyErpGoodsId(id));
    }
    requestModifyEmergencyFlag(emergencyFlag?: number): void {
        this.dispatch(new _EventModifyEmergencyFlag(emergencyFlag));
    }
    requestModifyErpOrderItemId(id?: string): void {
        this.dispatch(new _EventModifyErpOrderItemId(id));
    }
    @actionEvent(_EventModifyErpGoodsId)
    private async *_mapEventModifyErpGoodsId(event: _EventModifyErpGoodsId): AsyncGenerator<State> {
        this.innerState.extendData = this.innerState.extendData ?? new InventoryExtendDataItem();
        this.innerState.extendData.erpGoodsId = event?.id;
        this.update();
    }
    @actionEvent(_EventModifyEmergencyFlag)
    private async *_mapEventModifyEmergencyFlag(event: _EventModifyEmergencyFlag): AsyncGenerator<State> {
        this.innerState.extendData = this.innerState.extendData ?? new InventoryExtendDataItem();
        this.innerState.extendData.emergencyFlag = event?.emergencyFlag;
        this.update();
    }
    @actionEvent(_EventModifyErpOrderItemId)
    private async *_mapEventModifyErpOrderItemId(event: _EventModifyErpOrderItemId): AsyncGenerator<State> {
        this.innerState.extendData = this.innerState.extendData ?? new InventoryExtendDataItem();
        this.innerState.extendData.erpOrderItemId = event?.id;
        this.update();
    }

    validate(): boolean {
        LogUtils.d("invalidate");
        const editor = this.innerState;
        if ((editor.useCount ?? 0) <= 0) {
            Toast.show("输入入库数量", { warning: true });
            return false;
        }
        if ((editor.useCount ?? 0) > InventoryConst.maxStockItemNum) {
            Toast.show(`入库数量大于最大允许值${InventoryConst.maxStockItemNum}`, {
                warning: true,
            });
            return false;
        }

        if (editor.costPrice == null) {
            Toast.show("输入进价", { warning: true });
            return false;
        }

        if ((editor.costPrice ?? 0) < 0) {
            Toast.show("进价不能为负", { warning: true });
            return false;
        }
        // 1、南京地区 2、采购方式为医保采集
        if (userCenter.clinic?.isNanjingClinic && this._isYiBaoCollect) {
            if (!editor.extendData?.erpGoodsId) {
                Toast.show("平台产品编码必填", { warning: true });
                return false;
            }
            if (!editor.extendData?.erpOrderItemId) {
                this.innerState.showErrorHint = true;
                Toast.show("订单明细ID必填", { warning: true });
                return false;
            }
            if (isNil(editor.extendData?.emergencyFlag)) {
                Toast.show("应急采购必选", { warning: true });
                this.innerState.showErrorHint = true;
                return false;
            }
        }

        return true;
    }
}

export { InventoryInMedicineAddEditViewBloc, State };
