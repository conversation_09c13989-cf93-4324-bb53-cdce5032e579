import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import {
    GoodsInfo,
    GoodsStockInfo,
    GoodsTypeId,
    ShebaoNationalView,
    SubClinicPricePriceMode,
    TraceableCodeList,
} from "../../base-business/data/beans";
import _, { isNil } from "lodash";
import { UniqueKey } from "../../base-ui";
import { ABCUtils } from "../../base-ui/utils/utils";
import { Clinic, ModuleIdUtils, userCenter } from "../../user-center/user-center";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { PharmacyListItem } from "../../data/goods/goods-agent";
import { FeatureAuthority } from "../../user-center/data/constants";
import { ChinesePrescriptionProcessingInfoRsp } from "../../outpatient/data/outpatient-beans";

enum InventoryCheckDetailStatus {
    create = 0,
    waitVerify = 10, // 待审核
    refused = 20, //审核拒绝
    finish = 30, //审核通过
    revoke = 40, //撤回
    onlineDraft = -30, //线上草稿
    draft = -20, //本地草稿
}
export enum SymbolSign {
    add, // +
    negative, // -
    nolossNogain, // 打平
}
class InventoryStockComputedDetail {
    pieceNum?: number;
    pieceCount?: number; //总库存:批次物理库存累加
    pieceUnit?: string;
    packageCount?: number; //总库存:批次物理库存累加;
    packageUnit?: string;
    dispGoodsCount?: string; //拼接好的库存字符串,给前端使用
    //门诊开单可售库存量: (总库存 - 禁售库存 - 锁定库存)
    stockPieceCount?: number; //门诊开单可售库存量: (总库存 - 禁售库存 - 锁定库存)
    stockPackageCount?: number; //门诊开单可售库存量: (总库存 - 禁售库存 - 锁定库存)
    dispStockGoodsCount?: string; //拼接好的库存字符串,给前端使用

    @JsonProperty({ name: "outPieceCount" })
    __apiOutPieceCount?: number;
    @JsonProperty({ name: "outPackageCount" })
    __apiOutPackageCount?: number;
    dispOutGoodsCount?: string;
    prohibitPieceCount?: number; //禁售库存: 过期停售+手动批次停售
    prohibitPackageCount?: number; //禁售库存: 过期停售+手动批次停售
    dispProhibitGoodsCount?: string; //拼接好的库存字符串,给前端使用
    lockingPieceCount?: number; //锁定库存：门诊开单锁库 + 出库调拨锁库
    lockingPackageCount?: number; //锁定库存：门诊开单锁库 + 出库调拨锁库
    dispLockingGoodsCount?: string; //拼接好的库存字符串,给前端使用

    get outPieceCount(): number | undefined {
        //可出库存:(总库存 - 锁定库存) 主要应用与库存模块的进销存操作
        if (!!this.__apiOutPieceCount) {
            return this.__apiOutPieceCount;
        }
        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));
        return diffCount % (this.pieceNum ?? 0);
    }

    get outPackageCount(): number {
        //可出库存:(总库存 - 锁定库存) 主要应用与库存模块的进销存操作;
        if (!!this.__apiOutPackageCount) {
            return this.__apiOutPackageCount;
        }

        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));

        return Math.floor(diffCount / (this.pieceNum ?? 1));
    }

    //考虑中药的情况，中药只有小单位
    comprehensiveOutPackageCount(typeId: number): number {
        if ([GoodsTypeId.medicineChinesePiece, GoodsTypeId.medicineChineseGranule].includes(typeId)) {
            return this.__apiOutPackageCount ?? 0;
        }
        return this.outPackageCount;
    }

    comprehensiveOutPieceCount(typeId: number): number {
        if ([GoodsTypeId.medicineChinesePiece, GoodsTypeId.medicineChineseGranule].includes(typeId)) {
            return this.__apiOutPieceCount ?? 0;
        }
        return this.outPieceCount ?? 0;
    }

    //锁定库存总量
    get displayLockTotalStock(): string {
        let str = "";
        if (!!this.dispLockingGoodsCount) {
            return this.dispLockingGoodsCount;
        }
        const pieceNum = this?.pieceNum ?? 0;
        if (!this?.packageUnit) {
            str += (this.lockingPackageCount ?? 0) * pieceNum + (this.lockingPieceCount ?? 0) + (this?.pieceUnit ?? "");
        } else {
            if (!!this.lockingPackageCount) {
                str += `${this.lockingPackageCount}${this.packageUnit}`;
            }
            if (!!this?.pieceUnit && !!this.lockingPieceCount) {
                str += `${this.lockingPieceCount}${this?.pieceUnit}`;
            }
            //大小单位都没有，显示0
            if (!this.lockingPackageCount && !this.lockingPieceCount) {
                str = `0${this?.packageUnit}`;
            }
        }
        return str;
    }

    get unitPreferPackage(): string {
        if (this.packageUnit) return this.packageUnit;
        if (this.pieceUnit) return this.pieceUnit;

        return "次";
    }

    /**
     * 拼接剩余库存信息, e.g: 余30盒5片
     * isInventory--是否是库存模块（库存模块那边 用pacakgeCount/pieceCount
     * 门诊开放那里用stockPacakgeCount /stockPieceCount）
     */
    public displayStockInfo(type: "all" | "canSell" | "canOut" | "lock" = "canSell"): string {
        let stock = "";
        let packageCount, pieceCount;
        if (type != "canSell") {
            packageCount = (type == "canOut" ? this.outPackageCount : undefined) ?? this.packageCount ?? 0;
            pieceCount = (type == "canOut" ? this.outPieceCount : undefined) ?? this.pieceCount ?? 0;
        } else {
            packageCount = this.stockPackageCount ?? 0;
            pieceCount = this.stockPieceCount ?? 0;
        }

        if (packageCount != 0) {
            stock = `${packageCount}${this.packageUnit}`;
        }

        if (pieceCount != 0) {
            stock = `${stock}${pieceCount}${this.pieceUnit}`;
        }

        //没有库存
        if (_.isEmpty(stock)) {
            stock = `0${this.unitPreferPackage}`;
        }

        return stock;
    }
}

export enum InventoryApiErrorCode {
    goodsStockNotEnough = 12010, //出库商品库存不足
    goodsDeleted = 12015, //商品已经被删除
    goodsDisabled = 12808, //商品已经被停用
    alreadyUpdate = 962, //当前库存信息已被修改，请刷新

    goodsStockRevertNotEnough = 12031, // 退货出库（库存不足）
    goodsStockRevertNotEnoughN = 470, // 退货出库（库存不足）

    goodsStockChecking = 12162, // 新建盘点单盘点中

    goodsStockSummaryErrorCode = 12100, //盘点单错误汇总信息

    unKnow = 471, //??

    saved = 12812, //已保存
    deleted = 12813, //已删除
    submitted = 12814, //已提交
}

export enum InventorySubTaskDetailsPageType {
    normal = "normal", //正常
    onlyRead = "onlyRead", //只读
    summary = "summary", // 汇总
}

export class PostStockCheckTaskReqItemsItemBatch {
    goodsId?: string;
    batchId?: number;
    batchNo?: string; // 终端自定义，用于页面显示
    packageCount?: number;
    pieceCount?: number;
    beforePackageCount?: number;
    beforePieceCount?: number;
    pieceNum?: number;
    draftBeforePackageCount?: number;
    draftBeforePieceCount?: number;
    id?: string;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];
    totalCostPriceChange?: number;
    totalPriceChange?: number;
    changedPackageCount?: number; // 按盘点变更数量进行盘点 正盘盈，负为亏 (opType为1的时候要传)
    changedPieceCount?: number; // 按盘点变更数量进行盘点(opType为1的时候要传)
}
export class PostStockCheckTaskReqItemsItem {
    @JsonProperty({ type: Array, clazz: PostStockCheckTaskReqItemsItemBatch })
    batchs?: PostStockCheckTaskReqItemsItemBatch[];
}

export class PostStockCheckTaskReq {
    parentTaskId?: string;
    @JsonProperty({ type: Array, clazz: PostStockCheckTaskReqItemsItem })
    list?: PostStockCheckTaskReqItemsItem[];
    taskId?: string;
}

export enum ExecuteCountInventoryItemStatus {
    notStarted, // 未开始盘点/未盘点 0
    pendingSales = 1, // 未完成盘点/盘点中 1
    taskCompleted = 10, // 本人已完成盘点任务 10
    submitted = 20, // 已提交 20
    deleted = 30, // 任务被删除 30

    //终端自定义显示
}
export class InventoryPharmacyInfo {
    name?: string;
    no?: number;
    type?: number;
}

export interface StockCheckScope {
    type?: number;
    subType?: number;
    cMSpec?: string;

    typeIdList?: string[];
    customTypeIdList?: string[];
}

export class StockCheckTaskInfo {
    chainId?: string;
    clinicId?: string;
    ownerId?: string;
    ownerName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    create?: Date;
    status?: number;
    statusName?: string;
    taskId?: string;
    parentTaskId?: string;
    parentTaskName?: string;
    taskName?: string;
    type?: number;
    kindCount?: number;
    taskCount?: number;
    finishCount?: number;
    typeIds?: number[];
    stockCheckScope?: StockCheckScope;

    sig?: string; //判断是否更新

    __taskId?: string; //终端使用字段

    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
}

/**
 * 盘点任务中，盘点的一个药品相关信息
 */
export class CheckTaskGoodItem {
    id?: string;
    batchId?: number;
    goodsId?: string;
    pieceNum?: number;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
    beforePieceCount?: number;
    beforePackageCount?: number;
    pieceCount?: number;
    packageCount?: number;
    packageCountChange?: number;
    pieceCountChange?: number;
    packageCostPrice?: any;
    packagePrice?: number;
    piecePrice?: number;
    totalCostPriceChange?: any;
    totalPriceChange?: number;
    batchNo?: string;
    manufacturer?: string;

    compareKey(): string {
        return `${this.goods?.id ?? UniqueKey()}${this.batchId}`;
    }

    __batchInfo?: GoodsBatchInfo;

    pharmacyNo?: number;
    @JsonProperty({ type: Array, clazz: PostStockCheckTaskReqItemsItemBatch })
    batchs?: PostStockCheckTaskReqItemsItemBatch[];
    //终端自定义--存储上次草稿库存量
    _draftBeforePackageCount?: number;
    _draftBeforePieceCount?: number;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];

    get selectBatch(): PostStockCheckTaskReqItemsItemBatch | undefined {
        return this.batchs?.find((item) => item?.batchId == this?.batchId);
    }

    displayStockInfo(goodsInfo: GoodsInfo, batchInfo: GoodsBatchInfo): string {
        let stock = "";
        const packageCount = batchInfo.packageCount ?? 0;
        const pieceCount = batchInfo.pieceCount ?? 0;
        if (packageCount > 0) {
            stock = `${packageCount}${goodsInfo.packageUnit}`;
        }

        if (pieceCount > 0) {
            stock = `${stock} ${pieceCount} ${goodsInfo.pieceUnit}`;
        }
        if (!stock.length) {
            return `0${this.goods?.packageUnit}0${this.goods?.pieceUnit}`;
        }
        return stock;
    }

    static stockInfoChange(checkTask: CheckTaskGoodItem, oldCheckTask: CheckTaskGoodItem): CheckTaskGoodItem {
        const count = checkTask.changeCount;
        checkTask.packageCountChange = Math.floor(count / (checkTask.goods?.pieceNum ?? 0));
        checkTask.pieceCountChange = count % (checkTask.goods?.pieceNum ?? 0);
        checkTask.beforePieceCount = checkTask.__batchInfo?.pieceCount ?? oldCheckTask.beforePieceCount;
        checkTask.beforePackageCount = checkTask.__batchInfo?.packageCount ?? oldCheckTask.beforePackageCount;
        //存储当前选中的账面库存
        checkTask._draftBeforePieceCount = checkTask.__batchInfo?.pieceCount ?? checkTask.beforePieceCount;
        checkTask._draftBeforePackageCount = checkTask.__batchInfo?.packageCount ?? checkTask.beforePackageCount;
        if (!!checkTask?.batchId) {
            checkTask.batchs?.forEach((item) => {
                if (item?.batchId == checkTask?.batchId) {
                    item.draftBeforePackageCount = checkTask?._draftBeforePackageCount;
                    item.draftBeforePieceCount = checkTask?._draftBeforePieceCount;
                    item.beforePackageCount = checkTask?.beforePackageCount;
                    item.beforePieceCount = checkTask?.beforePieceCount;
                }
            });
        } else {
            checkTask.batchs = [
                JsonMapper.deserialize(PostStockCheckTaskReqItemsItemBatch, {
                    goodsId: checkTask?.goodsId,
                    batchId: checkTask?.batchId,
                    packageCount: checkTask?.packageCount,
                    pieceCount: checkTask?.pieceCount,
                    beforePackageCount: checkTask?.beforePackageCount,
                    beforePieceCount: checkTask?.beforePieceCount,
                    pieceNum: checkTask?.pieceNum,
                    draftBeforePackageCount: checkTask?._draftBeforePackageCount,
                    draftBeforePieceCount: checkTask?._draftBeforePieceCount,
                    id: checkTask?.id,
                }),
            ];
        }
        return checkTask;
    }

    get changeCount(): number {
        const _beforePackageCount = this.beforePackageCount ?? 0;
        const _beforePieceCount = this.beforePieceCount ?? 0;
        const _packageCount = this.packageCount ?? 0;
        const _pieceCount = this.pieceCount ?? 0;
        return (
            _packageCount * (this.goods?.pieceNum ?? 0) +
            _pieceCount -
            (_beforePackageCount * (this.goods?.pieceNum ?? 0) + _beforePieceCount)
        );
    }

    get beforeStockDisplay(): string {
        let stockStr = "";
        if (this.beforePackageCount) {
            stockStr = `${this.beforePackageCount}${this.goods?.packageUnit}`;
        }
        if (this.beforePieceCount) {
            stockStr += `${this.beforePieceCount}${this.goods?.pieceUnit}`;
        }
        if (!stockStr.length) {
            return `${this.goods?.packageUnit ? `0${this.goods?.packageUnit}` : ""}0${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    get currentStockDisplay(): string {
        let stockStr = "";
        if (this.packageCount != undefined && this.goods?.packageUnit) {
            stockStr = `${this.packageCount}${this.goods?.packageUnit}`;
        }
        if (this.pieceCount != undefined && this.goods?.pieceUnit) {
            stockStr += `${this.pieceCount}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    get stockChangeDisplay(): string {
        let stockStr = "";
        if (this.changeCount > 0) {
            stockStr = "+";
        } else if (this.changeCount == 0) {
            return `0${this.goods?.packageUnit?.length ? this.goods?.packageUnit : this.goods?.pieceUnit ?? ""}`;
        } else if (this.changeCount < 0) {
            stockStr = "-";
        }

        let pieceCountChange;
        let packageCountChange = 0;
        if (!this.goods?.isChineseMedicine) {
            packageCountChange = parseInt((this.changeCount / (this.goods?.pieceNum ?? 0)).toString());
            pieceCountChange = Number(Math.abs(this.changeCount % (this.goods?.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.changeCount.toFixed(4));
        }

        if (packageCountChange) {
            stockStr += `${Math.abs(packageCountChange)}${this.goods?.packageUnit}`;
        }
        if (pieceCountChange) {
            stockStr += `${Math.abs(pieceCountChange)}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    //上次草稿库存量
    get draftLastStockDisplay(): string {
        let stockStr = "";
        if (this.selectBatch?.draftBeforePackageCount) {
            stockStr = `${this.selectBatch?.draftBeforePackageCount}${this.goods?.packageUnit}`;
        }
        if (this.selectBatch?.draftBeforePieceCount) {
            stockStr += `${this.selectBatch?.draftBeforePieceCount}${this.goods?.pieceUnit}`;
        }
        if (!stockStr.length) {
            return `${this.goods?.packageUnit ? `0${this.goods?.packageUnit}` : ""}0${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    //草稿账面库存量是否有变化
    get isDraftStockChange(): boolean {
        const lastDraftTotalStock =
                (this.selectBatch?.draftBeforePackageCount ?? 0) * (this.goods?.pieceNum ?? 0) +
                (this.selectBatch?.draftBeforePieceCount ?? 0),
            beforeTotalStock =
                (this.selectBatch?.beforePackageCount ?? 0) * (this.goods?.pieceNum ?? 0) + (this.selectBatch?.beforePieceCount ?? 0);
        return lastDraftTotalStock != beforeTotalStock;
    }
    //草稿账面库存量变化数值
    get draftChangeCount(): number {
        const _beforePackageCount = this.selectBatch?.beforePackageCount ?? 0;
        const _beforePieceCount = this.selectBatch?.beforePieceCount ?? 0;
        const _draftBeforePackageCount = this.selectBatch?.draftBeforePackageCount ?? 0;
        const _draftBeforePieceCount = this.selectBatch?.draftBeforePieceCount ?? 0;
        return (
            _beforePackageCount * (this.goods?.pieceNum ?? 0) +
            _beforePieceCount -
            (_draftBeforePackageCount * (this.goods?.pieceNum ?? 0) + _draftBeforePieceCount)
        );
    }
    //草稿账面库存量变化数值显示(新建页面使用)
    get draftStockChangeDisplay(): string {
        let stockStr = "";
        if (this.draftChangeCount > 0) {
            stockStr = "+";
        } else if (this.draftChangeCount == 0) {
            return `0${this.goods?.packageUnit?.length ? this.goods?.packageUnit : this.goods?.pieceUnit ?? ""}`;
        } else if (this.draftChangeCount < 0) {
            stockStr = "-";
        }

        let pieceCountChange;
        let packageCountChange = 0;
        if (!this.goods?.isChineseMedicine) {
            packageCountChange = parseInt((this.draftChangeCount / (this.goods?.pieceNum ?? 0)).toString());
            pieceCountChange = Number(Math.abs(this.draftChangeCount % (this.goods?.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.draftChangeCount.toFixed(4));
        }

        if (packageCountChange) {
            stockStr += `${Math.abs(packageCountChange)}${this.goods?.packageUnit}`;
        }
        if (pieceCountChange) {
            stockStr += `${Math.abs(pieceCountChange)}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    // 返回当前盘点盈亏数量
    get stockChangeMessage(): { sign: SymbolSign; packageCountChange: number; pieceCountChange: number } {
        const obj = {
            sign: SymbolSign.nolossNogain,
            packageCountChange: 0,
            pieceCountChange: 0,
        };
        if (this.changeCount > 0) {
            obj.sign = SymbolSign.add;
        } else if (this.changeCount == 0) {
            obj.sign = SymbolSign.nolossNogain;
        } else if (this.changeCount < 0) {
            obj.sign = SymbolSign.negative;
        }

        let pieceCountChange = 0;
        let packageCountChange = 0;
        if (!this.goods?.isChineseMedicine) {
            packageCountChange = parseInt((this.changeCount / (this.goods?.pieceNum ?? 0)).toString());
            pieceCountChange = Number((this.changeCount % (this.goods?.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.changeCount.toFixed(4));
        }

        obj.packageCountChange = packageCountChange;
        obj.pieceCountChange = pieceCountChange;

        return obj;
    }
}

export class SummaryTaskGoodItem extends CheckTaskGoodItem {
    cMSpec?: string;
    checkBatchType?: string;
    goodsId?: string;
    materialSpec?: string;
    medicineCadn?: string;
    medicineDosageForm?: string;
    medicineDosageNum?: number;
    medicineDosageUnit?: string;
    name?: string;
    ownerId?: string;
    ownerName?: string;
    packageUnit?: string;
    pieceNum?: number;
    pieceUnit?: string;
    subType?: number;
    taskId?: string;
    taskName?: string;
    type?: number;
}

//多人盘点，单个任务数据详情
export class CoCheckTaskData {
    chainId?: string;
    clinicId?: string;
    employeeId?: string;
    taskInfo?: StockCheckTaskInfo;
    comment?: string;

    @JsonProperty({ type: Array, clazz: CheckTaskGoodItem })
    list?: CheckTaskGoodItem[];

    sig?: string; // 判断是否与后台数据同步
    totalCostPriceChange?: number;
    totalPriceChange?: number;

    static CheckTaskGoodList2StockCheckTaskReq(data: CoCheckTaskData): PostStockCheckTaskReq {
        const params = {
            taskId: data.taskInfo?.taskId,
            parentTaskId: data.taskInfo?.parentTaskId,
            list: [] as { batchs: CheckTaskGoodItem[] }[],
        };
        const goodsInfoMap: Map<string, CheckTaskGoodItem[]> = new Map<string, CheckTaskGoodItem[]>();
        data.list?.forEach((item) => {
            const goodsId = item.goods?.id ?? item.goodsId ?? "";
            item.goodsId = goodsId;
            if (goodsInfoMap.has(goodsId)) {
                const array = goodsInfoMap.get(goodsId) ?? [];
                array.push(item);
                goodsInfoMap.set(goodsId, array);
            } else {
                goodsInfoMap.set(goodsId, [item]);
            }
        });
        goodsInfoMap.forEach((item) => {
            const _item = _.cloneDeep(item);

            params.list.push({
                batchs: _item.map((batchItem) => {
                    delete batchItem.goods;
                    batchItem.beforePackageCount = batchItem.beforePackageCount ?? 0;
                    batchItem.beforePieceCount = batchItem.beforePieceCount ?? 0;
                    batchItem.packageCount = batchItem.packageCount ?? 0;
                    batchItem.pieceCount = batchItem.pieceCount ?? 0;
                    return batchItem;
                }),
            });
        });

        return JsonMapper.deserialize(PostStockCheckTaskReq, { ...params });
    }

    displaySummary(canViewPrice: boolean): string {
        const completed = this?.taskInfo?.status == ExecuteCountInventoryItemStatus.taskCompleted;
        const str: string[] = [];
        // 如果当前是药店，创建、审核中的临时、全量盘点单可查看盈亏进价金额，是否能查看盈亏进价受数据权限中有无进价查看权限控制
        // 具体计算是根据list中的packageCostPrice代表进价,packagePrice代表售价，然后将小单位转换为大单位来计算，最后采取向下保留两位小数
        if (userCenter.clinic?.isDrugstoreButler && !completed) {
            const totalCostPrice = this.list?.reduce((prev, next) => {
                const profitAndLossInfo = next.stockChangeMessage;
                const { sign, packageCountChange, pieceCountChange } = profitAndLossInfo;
                let result = 0;
                const currentCostPrice =
                    ((pieceCountChange ?? 0) / (next.pieceNum ?? 1) + packageCountChange) * (next?.packageCostPrice ?? 0);
                if (sign == SymbolSign.add) {
                    result = prev + currentCostPrice;
                } else if (sign == SymbolSign.negative) {
                    result = prev - Math.abs(currentCostPrice);
                } else {
                    result = prev + currentCostPrice;
                }
                return Math.floor(result * 100) / 100;
            }, 0);
            const saleTotalPrice = this.list?.reduce((prev, next) => {
                const profitAndLossInfo = next.stockChangeMessage;
                const { sign, packageCountChange, pieceCountChange } = profitAndLossInfo;
                let result = 0;
                const currentCostPrice = ((pieceCountChange ?? 0) / (next.pieceNum ?? 1) + packageCountChange) * (next?.packagePrice ?? 0);
                if (sign == SymbolSign.add) {
                    result = prev + currentCostPrice;
                } else if (sign == SymbolSign.negative) {
                    result = prev - Math.abs(currentCostPrice);
                } else {
                    result = prev + currentCostPrice;
                }
                return Math.floor(result * 100) / 100;
            }, 0);
            str.push(
                `盈亏(进/售):${
                    _.isNumber(totalCostPrice) && !!canViewPrice ? ABCUtils.inventoryPriceWithRMB({ price: totalCostPrice }) : "--"
                } / ${_.isNumber(saleTotalPrice) ? ABCUtils.inventoryPriceWithRMB({ price: saleTotalPrice, showSymbols: false }) : "--"}`
            );
        }

        /**
         * @description 已拒绝的单子不显示盈亏金额
         */
        if ((!isNil(this.totalCostPriceChange) || !isNil(this.totalPriceChange)) && completed) {
            str.push(
                `盈亏(进/售):${
                    _.isNumber(this.totalCostPriceChange) && !!canViewPrice
                        ? ABCUtils.inventoryPriceWithRMB({ price: this.totalCostPriceChange })
                        : "--"
                } / ${
                    _.isNumber(this.totalPriceChange)
                        ? ABCUtils.inventoryPriceWithRMB({ price: this.totalPriceChange, showSymbols: false })
                        : "--"
                }`
            );
        }
        return str.join("，");
    }
}

//商品批次信息
export class GoodsBatchInfo extends InventoryStockComputedDetail {
    stockId?: number; // 库存ID
    batchId?: number; // 批次ID
    batchNo?: string; // 批号
    expiryDate?: string; // 到期日
    goodsId?: string; // 商品ID
    pieceNum?: number; // 件数
    pieceCount?: number; //
    packageCount?: number; // 包计数
    packageCostPrice?: number; // 包装成本价
    packagePrice?: number; // 包装销售价
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date;
    lockingPackageCount?: number; //调拨锁库中库存量
    lockingPieceCount?: number;
    availablePackageCount?: number; //扣除锁库后可用库存量
    availablePieceCount?: number;
    pharmacyName?: string;
    actualFillPackageCount?: number; // 自定义字段--实际填写的数量
    actualFillPieceCount?: number; // 自定义字段--实际填写的数量
    isChineseMedicine?: boolean; // 自定义字段---用来判断当前药品是否是中药处方
    draftBeforePackageCount?: number; // 自定义字段--草稿账面库存量(用于账面库存变化显示)
    draftBeforePieceCount?: number; // 自定义字段--草稿账面库存量(用于账面库存变化显示)

    get displayExpiryDate(): Date | undefined {
        if (this.expiryDate == undefined || this.expiryDate.length == 0) return undefined;
        return new Date(this.expiryDate);
    }

    displayCountDisplay(packageUnit?: string, pieceUnit?: string): string {
        let str = "";
        if (this.outPackageCount && !!packageUnit) {
            str += `${this.outPackageCount}${packageUnit}`;
        }
        if (this.outPieceCount) {
            str += `${this.outPieceCount}${pieceUnit}`;
        }
        return str;
    }
    get changeCount(): number {
        const _beforePackageCount = this.packageCount ?? 0;
        const _beforePieceCount = this.pieceCount ?? 0;
        const _packageCount = this.actualFillPackageCount ?? 0;
        const _pieceCount = this.actualFillPieceCount ?? 0;
        return _packageCount * (this?.pieceNum ?? 0) + _pieceCount - (_beforePackageCount * (this?.pieceNum ?? 0) + _beforePieceCount);
    }
    get stockChangeDisplay(): string {
        if (isNil(this.actualFillPackageCount) && isNil(this.actualFillPieceCount)) {
            return "-";
        }
        let stockStr = "";
        if (this.changeCount > 0) {
            stockStr = "+";
        } else if (this.changeCount == 0) {
            return `0${this.packageUnit?.length ? this.packageUnit : this.pieceUnit ?? ""}`;
        } else if (this.changeCount < 0) {
            stockStr = "-";
        }

        let pieceCountChange;
        let packageCountChange = 0;
        if (!this.isChineseMedicine) {
            packageCountChange = parseInt((this.changeCount / (this.pieceNum ?? 0)).toString());
            pieceCountChange = Number(Math.abs(this.changeCount % (this.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.changeCount.toFixed(4));
        }

        if (packageCountChange) {
            stockStr += `${Math.abs(packageCountChange)}${this.packageUnit}`;
        }
        if (pieceCountChange) {
            stockStr += `${Math.abs(pieceCountChange)}${this.pieceUnit}`;
        }
        return stockStr;
    }
    get beforeStockDisplay(): string {
        let stockStr = "";
        if (this.packageCount) {
            stockStr = `${this.packageCount}${this.packageUnit}`;
        }
        if (this.pieceCount) {
            stockStr += `${this.pieceCount}${this.pieceUnit}`;
        }
        if (!stockStr.length) {
            return `${this.packageUnit ? `0${this.packageUnit}` : ""}0${this.pieceUnit}`;
        }
        return stockStr;
    }
    //上次草稿库存量
    get draftLastStockDisplay(): string {
        let stockStr = "";
        if (this.draftBeforePackageCount) {
            stockStr = `${this.draftBeforePackageCount}${this.packageUnit}`;
        }
        if (this.draftBeforePieceCount) {
            stockStr += `${this.draftBeforePieceCount}${this.pieceUnit}`;
        }
        if (!stockStr.length) {
            return `${this.packageUnit ? `0${this.packageUnit}` : ""}0${this.pieceUnit}`;
        }
        return stockStr;
    }
    //草稿账面库存量是否有变化
    get isDraftStockChange(): boolean {
        const lastDraftTotalStock = (this.draftBeforePackageCount ?? 0) * (this.pieceNum ?? 0) + (this.draftBeforePieceCount ?? 0),
            beforeTotalStock = (this.packageCount ?? 0) * (this.pieceNum ?? 0) + (this.pieceCount ?? 0);
        return lastDraftTotalStock != beforeTotalStock;
    }
    //草稿账面库存量变化数值
    get draftChangeCount(): number {
        const _beforePackageCount = this.packageCount ?? 0;
        const _beforePieceCount = this.pieceCount ?? 0;
        const _draftBeforePackageCount = this.draftBeforePackageCount ?? 0;
        const _draftBeforePieceCount = this.draftBeforePieceCount ?? 0;
        return (
            _beforePackageCount * (this.pieceNum ?? 0) +
            _beforePieceCount -
            (_draftBeforePackageCount * (this.pieceNum ?? 0) + _draftBeforePieceCount)
        );
    }
    //草稿账面库存量变化数值显示(新建页面使用)
    get draftStockChangeDisplay(): string {
        let stockStr = "";
        if (this.draftChangeCount > 0) {
            stockStr = "+";
        } else if (this.draftChangeCount == 0) {
            return `0${this.packageUnit?.length ? this.packageUnit : this.pieceUnit ?? ""}`;
        } else if (this.draftChangeCount < 0) {
            stockStr = "-";
        }

        let pieceCountChange;
        let packageCountChange = 0;
        if (!this.isChineseMedicine) {
            packageCountChange = parseInt((this.draftChangeCount / (this.pieceNum ?? 0)).toString());
            pieceCountChange = Number(Math.abs(this.draftChangeCount % (this.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.draftChangeCount.toFixed(4));
        }

        if (packageCountChange) {
            stockStr += `${Math.abs(packageCountChange)}${this.packageUnit}`;
        }
        if (pieceCountChange) {
            stockStr += `${Math.abs(pieceCountChange)}${this.pieceUnit}`;
        }
        return stockStr;
    }
}

//商品批次信息(更加详细)
export class GoodsBatchInfoDetail extends GoodsBatchInfo {
    clinicId?: string;
    clinicName?: string;
    clinicType?: number;
    createdUserName?: string;
    expireStatus?: number; // 0 正常销售 1 过期销售 2 售完 10 过期停售 20 用户手动停售  // 汇总状态  100 销售中  110 售完  120 停售  130 已禁售
    expiredWarnFlag?: number;
    expiredWarnMonths?: string;
    fromAlias?: number;
    inId?: number;
    packageUnit?: string;
    pieceUnit?: string;
    subType?: number;
    supplierName?: string;
    totalCostPrice?: number;
    type?: number;
    statusName?: string;

    get purchasePrice(): string {
        const str = "";
        if (!isNil(this.packageCostPrice)) {
            return `${ABCUtils.formatMoney(this.packageCostPrice, true)}/${this.packageUnit || this.pieceUnit}`;
        }
        if (this.pieceCount) {
            return `${ABCUtils.formatMoney(this.pieceCount, true)}/${this.pieceUnit}`;
        }
        return str;
    }

    get stockDisplay(): string {
        let stockStr = "";
        if (this.packageCount) {
            stockStr = `${this.packageCount}${this.packageUnit ?? ""}`;
        }
        if (this.pieceCount) {
            stockStr += `${this.pieceCount}${this.pieceUnit ?? ""}`;
        }
        if (!stockStr.length) {
            return `${this.packageUnit ? `0${this.packageUnit}` : `0${this.pieceUnit}`}`;
        }
        return stockStr;
    }
}

export interface CustomType {
    name: string;
}
export class GoodsInventoryInfo extends GoodsInfo {
    domainId?: string;
    disable?: number;

    v2DisableStatus?: number; // 停用升级使用字段 0->正常 10-> 20->

    gradeId?: string;
    smartDispense?: any;
    atc?: string;
    origin?: string;
    certificateName?: string;
    certificateNo?: string;
    customTypeId?: number;
    customType?: CustomType;
    goodsTypeName?: string;
    goodsSpecStr?: string;
    _id?: number;
    chainId?: string;
    clinicId?: string;
    goodsId?: string;
    lastStockInId?: number;
    lastStockInOrderSupplier?: string;
    minCostStockInId?: number;
    minCostStockInOrderSupplier?: string;
    profitRat?: number;
    currentCount?: number;
    totalCost?: number;
    minExpiryDate?: string;
    recentAvgSell?: number;
    turnoverDays?: any;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastActiveDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    flushDate?: Date;
    prohibitStock?: number; // 禁止销售的库存 大单位 有禁止销售 这个字段才有
    purchaseStatus?: number;
    shebaoNationalView?: ShebaoNationalView;
    expiredWarnFlag?: boolean; //效期预警
    shortageWarnFlag?: boolean; //库存预警
    negativeProfitWarnFlag?: boolean; //毛利预警
    turnoverDaysWarnFlag?: boolean; // 周转天数
    fixedPackagePrice?: number; // 固定售价模式--零售价格
    fixedPiecePrice?: number; // 固定售价模式--零售价格
    // 通过gooodsId调用库存药品物资列表数据获得---是后台已经拼凑好的（字符串）
    @JsonProperty({ name: "chainPackagePrice" })
    __chainPackagePrice?: string;
    @JsonProperty({ name: "chainPiecePrice" })
    __chainPiecePrice?: string;
    nationalCode?: string; //医保对码
    nationalCodeId?: string;
    waitingEffectUpdatePriceItemId?: string; // 用于获取调价药品详情
}

export class InventoryStockDetailWithBatchInfo extends GoodsStockInfo {
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
    @JsonProperty({ type: Array, clazz: GoodsBatchInfo })
    batchs?: GoodsBatchInfo[];
}

export enum OrderByType {
    name = "displayName",
    stock = "dispGoodsCount", //当前库存
    profitRat = "profitRat",
    expired = "minExpiryDate",
    turnoverDays = "turnoverDays", // 周转天数
    shortageWarnFirst = "shortageWarnFirst", //库存预警
    availableStock = "dispStockGoodsCount", //可售库存
    turnoverDaysWarnFirst = "turnoverDaysWarnFirst", //周转天数预警
    unmarketableGoods = "avgSell", //滞销商品
    purchasePriceChange = "lastPackageCostPrice", //进价异动
}

export enum OrderType {
    asc = "asc", //升序
    desc = "desc", //降序
}

export interface GetGoodsInventoryInfoReq {
    goodsId?: string;
    type?: number;
    subType?: number;
    clinicId?: string;
    orderBy?: OrderByType;
    orderType?: OrderType;
    offset?: number;
    limit?: number;
    cMSpec?: string;
    keyword?: string;
    sbOverPrice?: number;
    sbGoingOverPrice?: number;
    sbGoingExpired?: number;
    sbNotMatched?: number;
    sbMatched?: number;
    sbNotPermit?: number;

    /**
     * 后台优化字段
     */
    noNeedCalSummeryInfo?: number; //优化汇总信息的拉取，分页，排序等情况下可以传1 后台将不计算汇总信息
    isDisplayMatchCode?: number; //前端是否展示社保对码列

    /**
     * 杭州代煎代配增加字段
     */
    pharmacyNo?: number;
    pharmacyType?: number;

    /**
     * 二级分类字段
     */
    typeId?: string[];
    customTypeId?: string[];
    onlyStock?: number;
    sbNationalMatched?: number;
    sbNationalNotMatched?: number;
    sbNationalNotPermit?: number;
    sbProNotPermit?: number;
    sbProNotMatched?: number;
    sbProMatched?: number;
    sbExpired?: number;
    sbNotExpired?: number;
    sbNotOverPrice?: number;
    /**
     * 药店相关
     */
    profitCategoryType?: number; //利润类型毛利类型: 1 异常高毛利 2 A类 4 B类 8 C类 16 D类 32 E类 类型不会太多
    costPriceWarn?: number; //进价告警 对应 costPriceWarnCount
    profitWarn?: number; //利润率告警 对应 profitWarnCount
    stockWarn?: number; //库存告警 对应 stockWarnCount
    expiredWarn?: number; //有效期告警 对应 expiredWarnCount
    unsalableWarn?: number; //滞销告警 对应 unsalableWarnCount
    unPriceWarn?: number; //未定进价 对应 unPriceWarnCount
}

export class GetGoodsInventoryInfoRsp {
    @JsonProperty({ type: Array, clazz: GoodsInventoryInfo })
    rows?: GoodsInventoryInfo[];
    count?: number;
    total?: number;
    saleCount?: number; //在销售的库存商品的数量
    profitRatWarnCount?: number; //有利润率告警的药品数量
    expiredWarnCount?: number; //有有效期告警的药品数量
    stockWarnTurnoverDays?: number; //有库存周转天数告警的药品数量
    stockWarnCount?: number; //有库存预警告警的药品数量
    shebaoOverPriceCount?: number;
    shebaoGoingOverPriceCount?: number;
    shebaoGoingExpireCount?: number;
    shebaoNotMatchCount?: number;
    query?: GetGoodsInventoryInfoReq;
    totalPrice?: number; //在售药品的所有库存商品的总售价
    totalCostPrice?: number; //在售药品的所有库存商品的总成本价
    medicalFeeGradeACount?: number; //【市】医保甲类总数
    medicalFeeGradeBCount?: number; //【市】医保乙类总数
    medicalFeeGradeCCount?: number; //【市】医保丙类总数
    medicalFeeGradeCount?: number; //【市】医保医保等级总数
    unPriceWarnCount?: number; //未定价商品的数量
    unsalableWarnCount?: number; //滞销商品的数量
    costPriceWarnCount?: number; //进价异常商品的数量
}

export class GetGoodsInventoryBatchInfoRsp {
    count?: number;
    expiredWarnCount?: number;

    @JsonProperty({ type: Array, clazz: GoodsBatchInfoDetail })
    rows?: GoodsBatchInfoDetail[];
}

export interface GoodsCheckOrdersItemCreatedUser {
    id: string;
    name: string;
}

export class GoodsCheckOrdersItemOrgan {
    id?: string;
    parentId?: string;
    nodeType?: number;
    name?: string;
    shortName?: string;
    clinicId?: string;
    chainId?: string;
    viewMode?: number;

    get displayName(): string {
        return (!!this.shortName ? this.shortName : this.name) ?? "";
    }
}

export class GoodsCheckOrdersItemComment {
    @JsonProperty({ fromJson: fromJsonToDate })
    time?: Date;
    content?: string;
    employeeId?: string;
}
// 其他库存修正单据
export enum GOODS_OTHER_ORDER_TYPE {
    // 盘点
    GOODS_CHECK = 7,
    // 调拨
    GOODS_TRANS = 7,
    // 修正报损出库单
    FIX_LOSS_OUT_ORDER = 41,
    // 修正科室消耗单
    FIX_DEPART_CONSUME_ORDER = 42,
    // 修正其他出库单
    FIX_OTHER_OUT_ORDER = 43,
    // 修正生产出库
    FIX_PRODUCTION_OUT_ORDER = 44,
    // 修正领用出库单
    FIX_RECEPTION_OUT_ORDER = 60,
    // 修正领用退出单
    FIX_RECEPTION_BACK_OUT_ORDER = 61,
    // 修正领用入库单
    FIX_RECEPTION_IN_ORDER = 62,
    // 修正领用退入单
    FIX_RECEPTION_BACK_IN_ORDER = 63,
}

export class GoodsCheckOrdersItem {
    id?: string;
    orderNo?: string;
    organ?: GoodsCheckOrdersItemOrgan;
    kindCount?: number;
    createdUser?: GoodsCheckOrdersItemCreatedUser;
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
    comment?: GoodsCheckOrdersItemComment[];
    countChange?: number;
    costAmount?: number;
    saleAmount?: number;

    @JsonProperty({ fromJson: fromJsonToDate })
    finishDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewDate?: Date;
    reviewUser?: GoodsCheckOrdersItemCreatedUser;
    status?: InventoryCheckDetailStatus;
    statusName?: string;
    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
    gspInstId?: string; //药店相关
    gspTaskClinicId?: string; //用来判断当前门店审核状态是否高亮

    __draftId?: string;
    __searchParams?: {
        //二级分类需求
        typeId?: string[];
        customTypeId?: string[];
    };
    type?: GOODS_OTHER_ORDER_TYPE;
    // 修正单类app暂时不支持修改，禁用
    get disableAmendmentOrder(): boolean {
        return this.type == GOODS_OTHER_ORDER_TYPE.GOODS_CHECK;
    }

    get isOnlineDraft(): boolean {
        return this.status == InventoryCheckDetailStatus.onlineDraft;
    }
}

export class GoodsCheckOrdersItemCoworkTasks {
    name?: string;
    status?: string;
    progress?: string;
    type?: number;
    parentTaskId?: string;
    organ?: GoodsCheckOrdersItemOrgan;
    createdUser?: GoodsCheckOrdersItemCreatedUser;
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
}

export interface SearchParams {
    withStock?: string;
    onlyStock?: string;
    types?: number[];
    subTypes?: number[];
    cMSpec?: string[];
    disable?: number;
    clinicId?: string;
    inorderConfig?: string;
    pharmacyNo?: number;

    //二级分类需求
    typeId?: string[];
    customTypeId?: string[];
}

export class InventoryDashboardTodoCount {
    //和药房无关的TODO
    purchaseTodoCount?: number; //采购单待审核数量
    settlementTodoCount?: number; //结算单待审核数量
    profitRatWarnCount?: number; //利润率预警数量
    stockWarnShortageCount?: number; //汇总（周转天数预警数量+库存预警数量）
    turnoverDaysWarnCount?: number; //周转天数预警数量
    shortageWarnCount?: number; //库存预警数量
    stockSocialDiagnosisTreatmentWarn?: number; //非库存商品社保诊疗预警数量
    stockSocialGoodsWarn?: number; //非库存商品社保药品预警数量
    expiredWarnCount?: number; //过期预警数量

    //入库
    stockInTodoCount?: number; //入库红点数量
    stockInTodoPharmacyNoList?: []; // 入库红点药房列表

    //科室出库
    stockDeptOutTodoCount?: number; //科室出库单待审核数量
    stockDeptOutTodoPharmacyNoList?: [];

    //盘点
    stockCheckTodoCount?: number;
    stockCheckTodoPharmacyNoList?: [];

    //其他出库
    stockOtherOutTodoCount?: number;
    stockOtherOutTodoPharmacyNoList?: [];

    //调拨
    stockTransTodoCount?: number;
    stockTransTodoPharmacyNoList?: [];

    //消耗出库
    stockLossOutTodoCount?: number;
    stockLossOutTodoPharmacyNoList?: [];

    //领用
    stockReceptTodoCount?: number;
    stockReceptTodoPharmacyNoList?: [];

    //药品数量
    westCount?: number; //中西西药数量
    additionalCount?: number; //商品
    chineseGranuleCount?: number; //颗粒
    chinesePiecesCount?: number; //饮片
    medicalMaterialCount?: number; //耗材
    materialCount?: number; //耗材
    eyeCount?: number; //眼科

    loadingFromRedis?: number;

    get transCount(): number {
        return this.stockTransTodoCount ?? 0;
    }

    get allCount(): number {
        return (
            (this.stockInTodoCount ?? 0) +
            (this.stockReceptTodoCount ?? 0) +
            (this.stockTransTodoCount ?? 0) +
            (this.stockCheckTodoCount ?? 0) +
            (this.stockLossOutTodoCount ?? 0)
        );
    }

    /**
     * 待发药数量
     */
    get toBeDeliveryCount(): number {
        return (
            (this.westCount ?? 0) +
            (this.additionalCount ?? 0) +
            (this.chineseGranuleCount ?? 0) +
            (this.chinesePiecesCount ?? 0) +
            (this.medicalMaterialCount ?? 0) +
            (this.materialCount ?? 0) +
            (this.eyeCount ?? 0)
        );
    }
}

export interface ReviewDialogProps {
    pass?: boolean;
    reviewOrgan?: string;
    content?: string;
    inPharmacyName?: string;
    outPharmacyName?: string;
    pharmacyName?: string;
    isReception?: boolean; //领用还是退回
    needReason?: boolean; // 需要填写原因(用于同意时也可以输入内容)
    needReasonContTips?: string; //填写的内容提示
}

export interface ReviewDialogResult {
    status: boolean;
    comment?: string;
}

export class ChainReview {
    goodsTodoReminderCycle?: number;
    stockCheckChainReview?: boolean;
    stockInChainReview?: boolean;
    stockOutChainReview?: boolean;
    stockTransChainReview?: boolean;
    stockTransDiffPrice?: boolean;
    stockReceptionChainReview?: boolean; //领用模块权限

    costPrecision?: number;
    priceMode?: number; // 定价方式（1 按进价加成 2 按固定售价）
    virtualOpenPharmacyFlag?: number;
    chainExternalFlag?: number; //第4bit位 1 表示整个连锁不支持进价加成
    // 不支持进价加成
    get isNotSupportPriceMakeUpMode(): boolean {
        return !isNil(this.chainExternalFlag) && (this.chainExternalFlag & 8) === 8;
    }
    // 支持进价加成
    get isSupportPriceMakeUpMode(): boolean {
        return !isNil(this.chainExternalFlag) && (this.chainExternalFlag & 8) !== 8;
    }
    //    当前地区售价不能为0
    get currentRegionPriceNonZero(): boolean {
        return !isNil(this.chainExternalFlag) && !!(this.chainExternalFlag & 0x80);
    }
}
export class InOutTaxList {
    inTaxRat?: number;
    outTaxRat?: number;
    typeId?: number;
    typeName?: string;
}
export class GoodsPkgCostPriceMakeUpMinMaxItem {
    max?: number; //最大
    min?: number; //最小
    typeId?: number; //进销税的类型Id 西药 中药饮片，中药颗粒,中成药,自制成品，保健药品，保健食品，其他药品,医用材料 12, 14, 15, 16, 25, 26, 27, 28, 17
    typeName?: string; //进销税的名字 西药 中药饮片，中药颗粒,中成药,自制成品，保健药品，保健食品，其他药品,医用材料 12, 14, 15, 16, 25, 26, 27, 28, 17
}
class SubClinicPrice {
    maxPricePercent?: number;
    minPricePercent?: number;
    subSetPrice?: number;
    subSetPriceAllClinics?: number;
    subSetPriceClinics?: Clinic[];
    priceMode?: SubClinicPricePriceMode; //定价方式 1 固定售价 3 进价加成
    @JsonProperty({ type: Array, clazz: GoodsPkgCostPriceMakeUpMinMaxItem })
    priceModeMinMaxConfig?: GoodsPkgCostPriceMakeUpMinMaxItem[]; //进价加成售价模式-各种Goods类型的最大最小加成比例
    // 开启进价加成
    get isOpenPriceMakeUpMode(): boolean {
        return this.priceMode == SubClinicPricePriceMode.purchaseMarkup;
    }
}
class GoodsPurchaseCycleDays {
    days?: number;
    name?: string;
    typeId?: number;
}
class StockGoodsConfig {
    clinicId?: string;
    goodsPurchaseCycleDays?: GoodsPurchaseCycleDays[];
    stockDaysOfDayAvgSell?: number;
    disableExpiredGoods?: number;
    disableNoStockGoods?: number;
    stockWarnGoodsTurnoverDays?: number;
    stockWarnGoodsWillExpiredMonth?: number;
}

class PharmacyListConfigExtend {
    tag?: string;
}
export class ExternalPharmacyConfig {
    chargeDisableAutoDispense?: number; //是否关闭收费处的同时发药按钮，1--关闭，0--不禁用
    forceOpenAuditAndCompound?: number; //是否受系统药房设置中处方审核、调配的开关控制，1--不受开关控制
}

class EmployeesConfig {
    id?: string;
    name?: string;
}

export class PharmacyListConfig {
    chainId?: string;
    clinicId?: string;
    defaultGoodsTypeIdList?: number[];
    dispenseFlag?: number; //10--代表不能发药（叶开泰定制）
    innerFlag?: number; //内置药房标记 内置药房不能删出
    name?: string; //药房名字
    no?: number; //药房号
    stockCutType?: number; //库存扣减类型 0 库存够才扣(不能扣成负) 1库存不足可扣(可以扣成负)
    type?: number; //药房类型 0 本地药房 1 空中药房 2 虚拟药房 10 合作药房
    typeName?: string; //药房类型的名称 0 本地药房 1 空中药房 2 虚拟药房
    @JsonProperty({ type: PharmacyListConfigExtend })
    extendInfo?: PharmacyListConfigExtend;
    @JsonProperty({ type: ExternalPharmacyConfig })
    externalPharmacyConfig?: ExternalPharmacyConfig;

    /*----------------------------多库房新增字段---------------------------*/
    id?: string;
    remark?: number;
    enablePurchase?: number; // 采购入库开关
    enableDispense?: number; // 调剂发药
    enablePharmacyTrans?: number; // 领用
    enableTrans?: number; // 调拨
    enableDepartmentOut?: number; //科室消耗
    enableOtherOut?: number; //其他
    enableLossOut?: number; //报损
    enableCheck?: number; //盘点
    members?: number; //成员数
    defaultMember?: number; //是否默认有库房权限人员 0=否（指定成员） 1=是
    @JsonProperty({ type: Array, clazz: EmployeesConfig })
    employees?: EmployeesConfig[];
    status?: number; //0--停用，1---启用
    orderCheckStatus?: number; //盘点是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderDepartmentOutStatus?: number; //科室消耗是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderDispenseStatus?: number; //发药是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderLossOutStatus?: number; //报损是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderOtherOutStatus?: number; //其他出库是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderPharmacyTransStatus?: number; //领用是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderPurchaseStatus?: number; //采购是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderTransStatus?: number; //调拨是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    /*-------------------------end-多库房新增字段-end------------------------*/
    /**
     * 采购入库模块是否展示
     */
    get enablePurchaseIn(): boolean {
        return this.enablePurchase === 1 || (this.enablePurchase == 0 && !this.orderPurchaseStatus);
    }

    /**
     * 采购模块仅查看不可编辑
     */
    get enablePurchaseInOnlyView(): boolean {
        return this.enablePurchase === 0 && !this.orderPurchaseStatus;
    }

    /**
     * 领用模块是否展示
     */
    get enablePharmacyTransIn(): boolean {
        return this.enablePharmacyTrans === 1 || (this.enablePharmacyTrans == 0 && !this.orderPharmacyTransStatus);
    }

    /**
     * 领用模块仅查看不可编辑
     */
    get enablePharmacyTransInOnlyView(): boolean {
        return this.enablePharmacyTrans === 0 && !this.orderPharmacyTransStatus;
    }

    /**
     * 调拨模块是否展示
     */
    get enableTransIn(): boolean {
        return this.enableTrans === 1 || (this.enableTrans == 0 && !this.orderTransStatus);
    }

    /**
     * 调拨模块仅查看不可编辑
     */
    get enableTransInOnlyView(): boolean {
        return this.enableTrans === 0 && !this.orderTransStatus;
    }

    /**
     * 报损模块是否展示
     */
    get enableLossOutIn(): boolean {
        return this.enableLossOut === 1 || (this.enableLossOut == 0 && !this.orderLossOutStatus);
    }

    /**
     * 报损模块仅查看不可编辑
     */
    get enableLossOutInOnlyView(): boolean {
        return this.enableLossOut === 0 && !this.orderLossOutStatus;
    }

    /**
     * 盘点模块是否展示
     */
    get enableCheckIn(): boolean {
        return this.enableCheck === 1 || (this.enableCheck == 0 && !this.orderCheckStatus);
    }

    /**
     * 盘点模块仅查看不可编辑
     */
    get enableCheckInOnlyView(): boolean {
        return this.enableCheck === 0 && !this.orderCheckStatus;
    }
}
export enum PharmacyFlag {
    notOpenPharmacy = 0, // 未开启药房
    singlePharmacy = 10, // 开启单药房
    manyPharmacy = 20, // 开启多药房
}

/**
 * 搜索就诊类型 位运算,1=门诊 2=住院 3=全部
 * */
export const SearchSceneTypeEnum = Object.freeze({
    outpatient: 1,
    hospital: 2,
    all: 3,
});

// 追溯码采集强校验
export const TraceCodeCollectStrictCountFlag = 0x20000;

class DepartmentsItem {
    id?: string;
    name?: string;
}
class CustomTypesViewItem {
    id?: string;
    innerFlag?: number;
    isDeleted?: number;
    name?: string;
    sort?: number;
    supplierId?: string;
    typeId?: string;
}
class GoodsTypesItem {
    count?: number; //goods数量
    @JsonProperty({ type: Array, clazz: CustomTypesViewItem })
    customTypes?: CustomTypesViewItem[];
    goodsCMSpec?: string; //类型 CMSPec
    goodsSubType?: number; //类型 subType
    goodsType?: number; //类型 type
    id?: string;
    isAirPharmacy?: number;
    isLeaf?: number; //是否是叶子节点
    name?: string;
    parentId?: string;
    sort?: number;
}
class PharmacyItemInfo {
    pharmacyType?: number;
    pharmacyNo?: number;
    name?: string;
}
class PharmacyConfigRuleProcessInfo {
    type?: number;
    subType?: number;
    name?: string;
    children?: PharmacyConfigRuleProcessInfo[];
}
export class GoodsPharmacyConfigRuleList {
    @JsonProperty({ type: Array, clazz: DepartmentsItem })
    departments?: DepartmentsItem[];
    @JsonProperty({ type: Array, clazz: GoodsTypesItem })
    goodsTypes?: GoodsTypesItem[];
    id?: string;
    innerFlag?: number; //默认规则,0=否 1=是
    @JsonProperty({ type: PharmacyItemInfo })
    pharmacy?: PharmacyItemInfo;
    sceneType?: number;
    status?: number; //状态 0=停用1=启用
    @JsonProperty({ type: Array, clazz: DepartmentsItem })
    wardAreas?: DepartmentsItem[]; //病区
    sort?: number; //排序
    @JsonProperty({ type: Array, clazz: PharmacyConfigRuleProcessInfo })
    processInfos?: PharmacyConfigRuleProcessInfo[];
    __sort?: number; //终端自定义排序
}
class TraceCodeFormatRuleCheckItem {
    goodsType?: number;
    maxLength?: number;
    minLength?: number;
}
class UsableTraceCodeGoodsTypeIdItem {
    CMSPec?: string;
    goodsSubType?: number;
    goodsType?: number;
    id?: number;
    name?: string;
    parentId?: number;
}
export class TraceCodeConfig {
    traceCodeForceCheckScenes?: number[];
    @JsonProperty({ type: Array, clazz: TraceCodeFormatRuleCheckItem })
    traceCodeFormatRuleCheckList?: TraceCodeFormatRuleCheckItem[];
    @JsonProperty({ type: Array, clazz: UsableTraceCodeGoodsTypeIdItem })
    usableTraceCodeGoodsTypeIdList?: UsableTraceCodeGoodsTypeIdItem[];
    shebaoDismountingCollectStrategy?: number; // 是否开启拆零不采功能-------没有字段或者null:无限制 0:拆零不采集 1:拆零采集
}
export class InventoryClinicConfig {
    @JsonProperty({ type: ChainReview })
    chainReview?: ChainReview;
    inOutTaxList?: InOutTaxList[]; //【门店视图,门店的属性,不是连锁的】 自定义税率 修改如果为null 表示这里不做修改
    @JsonProperty({ type: SubClinicPrice })
    subClinicPrice?: SubClinicPrice;
    stockGoodsConfig?: StockGoodsConfig; //库存设置
    @JsonProperty({ type: Array, clazz: PharmacyListConfig })
    pharmacyList?: PharmacyListConfig[]; //【门店视图,门店的属性,不是连锁的】 药房列表
    openPharmacyFlag?: PharmacyFlag; //【门店视图,门店的属性,不是连锁的】 本地药房开启标记 0 未开启药房 10 开启单药房 20 开启多药房
    needSummeryGoodsStat?: number; //【门店视图,门店的属性,不是连锁的】 第一位本地药房是否要算goodsstat的汇总,第3位待检代配药房是否需要汇总
    viewMode?: number; //总部门店的viewMode
    @JsonProperty({ type: Array, clazz: GoodsPharmacyConfigRuleList })
    pharmacyRuleList?: GoodsPharmacyConfigRuleList[]; //库房下达规则列表
    clinicExternalFlag?: number; // 门店本地搜索各模块开关、追溯码强校验开关等
    // 追溯码强校验开关
    get isStrictCountWithTraceCodeCollect(): boolean {
        return !!((this.clinicExternalFlag || 0) & TraceCodeCollectStrictCountFlag);
    }
    dentistryChainId?: string;
    @JsonProperty({ type: TraceCodeConfig })
    traceCodeConfig?: TraceCodeConfig; // 追溯码相关
    // 开启多药房
    get isOpenMultiplePharmacy(): boolean {
        const canUseMultiPharmacy = userCenter.clinicEdition?.checkCurrentStateAvailable(FeatureAuthority.MULTI_PHARMACY);
        return !!canUseMultiPharmacy && this.openPharmacyFlag == PharmacyFlag.manyPharmacy;
    }
    get coClinicPharmacy(): PharmacyListConfig[] {
        return this.pharmacyList?.filter((item) => item.type == 10 && item.status != 0) || [];
    }
    // 开启合作药房
    get isOpenCoClinicPharmacy(): boolean {
        return !!this.coClinicPharmacy.length;
    }
    // 开关控制多药房
    get showMultiPharmacy(): boolean {
        return this.openPharmacyFlag == PharmacyFlag.manyPharmacy;
    }

    /**
     * 根据goodsTypeId查询可用药房
     * @param typeIds
     * @param customTypeId
     * @param departmentId
     * @example 1 | [1,2,3] | undefined
     */
    filterPharmacyListWithGoodsType(
        typeIds?: GoodsTypeId | GoodsTypeId[],
        customTypeId?: number,
        departmentId?: string
    ): (PharmacyListConfig | undefined)[] {
        if (!typeIds) {
            return this.pharmacyList?.filter((pharmacy) => !pharmacy.no) ?? [];
        } else if (typeIds instanceof Array) {
            return typeIds.map((idx) => {
                return this.getDefaultPharmacy({
                    departmentId: departmentId,
                    goodsInfo: {
                        typeId: idx,
                        customTypeId: customTypeId,
                    },
                });
            });
        } else {
            const result = this.getDefaultPharmacy({
                departmentId: departmentId,
                goodsInfo: {
                    typeId: typeIds,
                    customTypeId: customTypeId,
                },
            });
            return [result];
        }
    }

    /**
     * 获取当前药品的默认药房配置信息
     * @param typeId
     * @param departmentId
     */
    getDefaultPharmacyInfo(typeId?: GoodsTypeId, departmentId?: string): PharmacyListConfig | undefined {
        const pharmacyList = this.filterEnablePharmacyList;
        if (!pharmacyList?.length) {
            return;
        }
        const western = this.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: {
                typeId: GoodsTypeId.medicineWest,
            },
        });
        const pieces = this.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: {
                typeId: GoodsTypeId.medicineChinesePiece,
            },
        });
        const granule = this.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: {
                typeId: GoodsTypeId.medicineChineseGranule,
            },
        });
        const material = this.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: {
                typeId: GoodsTypeId.material,
            },
        });
        const goods = this.getDefaultPharmacy({
            departmentId: departmentId,
            goodsInfo: {
                typeId: GoodsTypeId.goods,
            },
        });

        const goodsPharmacyCol = new Map();
        goodsPharmacyCol.set(GoodsTypeId.medicineWest, western);
        goodsPharmacyCol.set(GoodsTypeId.medicineChinesePatent, western);

        goodsPharmacyCol.set(GoodsTypeId.medicineChinesePiece, pieces);
        goodsPharmacyCol.set(GoodsTypeId.medicineChineseGranule, granule);

        goodsPharmacyCol.set(GoodsTypeId.material, material);
        goodsPharmacyCol.set(GoodsTypeId.materialMedical, material);
        goodsPharmacyCol.set(GoodsTypeId.materialLogistics, material);
        goodsPharmacyCol.set(GoodsTypeId.materialFixedAssets, material);

        goodsPharmacyCol.set(GoodsTypeId.goods, goods);
        goodsPharmacyCol.set(GoodsTypeId.goodsHomemade, goods);
        goodsPharmacyCol.set(GoodsTypeId.healthMedicine, goods);
        goodsPharmacyCol.set(GoodsTypeId.healthGood, goods);
        goodsPharmacyCol.set(GoodsTypeId.otherGood, goods);
        return goodsPharmacyCol.get(typeId);
    }

    /**
     * 门诊、零售、执行站、住院各处开具药品【可选开单库房】不能选择未开通【调剂发药】【科室消耗】库房
     */
    get filterNormalPharmacyList(): PharmacyListConfig[] | undefined {
        return this.pharmacyList?.filter(
            (t) => t.type == PharmacyType.normal && t.status == 1 && (!!t?.enableDispense || !!t?.enableDepartmentOut)
        );
    }

    get filterCoClinicNormalPharmacyList(): PharmacyListConfig[] | undefined {
        return this.pharmacyList?.filter((t) => t.type == PharmacyType.coClinic && t.status == 1);
    }

    //过滤启用的库房列表
    get filterEnablePharmacyList(): GoodsPharmacyConfigRuleList[] | undefined {
        return this.pharmacyRuleList?.filter((t) => t.status == 1 && t.pharmacy?.pharmacyType == PharmacyType.normal);
    }

    /**
     * 库房下达规则
     * @param options
     */
    getDefaultPharmacy(options: {
        departmentId?: string; // 科室id
        wardAreaId?: string; // 病区id
        goodsInfo?: {
            typeId?: number; // 商品类型id
            customTypeId?: number; // 自定义商品类型id
        }; // 商品
        processInfo?: ChinesePrescriptionProcessingInfoRsp;
    }): PharmacyListConfig | undefined {
        const {
            departmentId, // 科室id
            wardAreaId, // 病区id
            goodsInfo, // 商品
            processInfo, // 加工方式
        } = options;
        let curSceneType = SearchSceneTypeEnum.outpatient;
        if (departmentId && !wardAreaId) {
            curSceneType = SearchSceneTypeEnum.outpatient;
        } else if (wardAreaId && !departmentId) {
            curSceneType = SearchSceneTypeEnum.hospital;
        }
        const list = this.filterEnablePharmacyList ?? [];
        if (!list.length) {
            return;
        }

        const typeId = goodsInfo?.typeId,
            customTypeId = goodsInfo?.customTypeId;
        const availableList = list.filter((item) => {
            const { sceneType, goodsTypes, departments, wardAreas, processInfos: processMatchInfo = [] } = item;

            // matchScene   匹配使用场景
            const matchScene = sceneType === SearchSceneTypeEnum.all || sceneType === curSceneType;

            // matchType    匹配商品类型
            // goodsTypes === null 不设置代表命中所有规则
            const matchType =
                goodsTypes === null ||
                goodsTypes?.some((it) => {
                    let flag = true;
                    if (it.customTypes) {
                        flag = it.customTypes.some((i) => {
                            return i && customTypeId === i.id;
                        });
                    }
                    return `${typeId}` === `${it.id}` && flag;
                });
            // matchScope   匹配使用范围 不存在配置代表命中所有
            let matchScope = true;
            if (curSceneType === SearchSceneTypeEnum.outpatient && departments && departments.length > 0) {
                matchScope = departments.some((it) => {
                    return it && departmentId === it.id;
                });
            }
            if (curSceneType === SearchSceneTypeEnum.hospital && wardAreas && wardAreas.length > 0) {
                matchScope = wardAreas.some((it) => {
                    return it && wardAreaId === it.id;
                });
            }
            // matchProcess   匹配加工方式 不存在配置代表命中所有
            let matchProcess = true;
            if (processMatchInfo && processMatchInfo.length) {
                if (processInfo) {
                    const { usageType, usageSubType } = processInfo;

                    matchProcess = !!processMatchInfo.find((it) => {
                        if (it.children && it.children.length) {
                            return it.children.find((child) => {
                                return child.type === usageType && child.subType === usageSubType;
                            });
                        }
                        return it.type === usageType;
                    });
                } else {
                    // 配置了加工规则，但是没传加工不用匹配
                    matchProcess = false;
                }
            }
            // status === 1 规则启用
            return item.status === 1 && matchScene && matchType && matchScope && matchProcess;
        });
        const pharmacy = availableList[0]?.pharmacy || {};
        return JsonMapper.deserialize(PharmacyListConfig, {
            type: pharmacy?.pharmacyType,
            no: pharmacy?.pharmacyNo,
            name: pharmacy?.name,
        });
    }

    /**
     * 筛选出有权限的库房列表(停用库房如果有历史单据还是可见的)
     */
    inventoryPharmacyList(): PharmacyListConfig[] {
        const list = this.pharmacyList?.filter((t) => !t?.type) ?? [];
        return list.filter((item) => {
            // 拥有库存权限的人
            if (item.defaultMember) {
                return userCenter.clinic?.isManager || userCenter.clinic?.can(ModuleIdUtils.All_INVENTORY_MODULE_LIST);
            }
            // 库房指定成员可见
            return item?.employees?.some((user) => user.id === userCenter.employee?.id);
        });
    }

    /**
     * 本地多药房列表
     */
    get localPharmacyList(): PharmacyListConfig[] {
        return this.pharmacyList?.filter((t) => !t?.type) ?? [];
    }
}

/**
 * 根据当前库房信息-过滤库房数据（全部库房展示所有，对应库房只显示对应库房信息）
 * @param list
 * @param currentPharmacy
 */
export const __filterRelativePharmacyData = <
    T extends {
        pharmacy?: InventoryPharmacyInfo;
        outPharmacy?: InventoryPharmacyInfo;
        inPharmacy?: InventoryPharmacyInfo;
        transType?: number;
        type?: number;
    }
>(
    list: Array<T> | undefined,
    currentPharmacy?: PharmacyListItem
): Array<T> | undefined => {
    return list?.filter((t) => {
        const pharmacy = !!t?.pharmacy
            ? t?.pharmacy
            : _.isNumber(t?.transType) && t?.type == 0
            ? t?.inPharmacy
            : _.isNumber(t?.transType) && t?.type == 1
            ? t?.outPharmacy
            : t.type == 20 && !!t?.inPharmacy
            ? t?.inPharmacy
            : t?.outPharmacy;
        return currentPharmacy?.name == "全部库房" ? true : pharmacy?.no == currentPharmacy?.no;
    });
};
export const PlatFormType = Object.freeze([
    {
        type: 1,
        name: "市平台",
    },
    {
        type: 10,
        name: "省平台",
    },
]);
export const PurchaseType = Object.freeze([
    {
        type: 1,
        name: "自行采购",
    },
    {
        type: 10,
        name: "医保集采",
    },
]);
// 外层采购方式与采购平台
export class ExtendData {
    platformType?: number; //采购平台  purchaseType=10时，医保集采的品台 1 市平台 10 省平台
    purchaseType?: number; //采购方式 1 自行采购 10 医保集采
    get isYiBaoCollect(): boolean {
        return this.purchaseType == 10;
    }
    // 采购方式名称
    get purchaseModeName(): string {
        return PurchaseType.find((t) => t.type == this.purchaseType)?.name ?? "";
    }
    //     采购平台名称
    get platformName(): string {
        return PlatFormType.find((t) => t.type == this.platformType)?.name ?? "";
    }
}
enum EmergencyFlagEnum {
    normal = 0, //不急
    emergency = 1, //紧急
}
// list中每个药品的扩展字段
export class InventoryExtendDataItem {
    emergencyFlag?: EmergencyFlagEnum; //紧急状态 null 无含义 0 不急 1 紧急
    erpGoodsId?: string; //采购平台对应的goodsID
    erpOrderId?: string; //采购平台对应的订单ID,产品交互不展示这一列，看交互一个入库单里面可以混多个采购单。能收集就收集，上报到医保光一个orderItemId可能不好看
    erpOrderItemId?: string; //采购平台对应的详单ID, 根据platformType平台是两个平台ID，两个平台ID可能重复，重复ID医保平台怎么看
    inspectorName?: string; //验收人
    platformType?: number; //采购平台   purchaseType=10时，医保集采的品台 1 市平台 10 省平台
    purchaseType?: number; // 采购方式  1 自行采购 10 医保集采
    shebaoCode?: string; //社保编码
}
export const PriceAdjustmentItemStatus = Object.freeze({
    DRAFT: 0, // 草稿
    REVIEW: 1, // 待审核
    GOODS_IN: 2, // 待生效
    FINISH: 3, // 已生效
    REFUSE: 4, // 已驳回
    WITH_DRAW: 5, // 已撤回
});
export class WaitingEffectPriceItem {
    id?: string;
    itemId?: string;
    status?: number;
    orderId?: string;
    goods?: GoodsInfo;
    packageCostPrice?: number;
    avgPackageCostPrice?: number;
    beforeProfitRat?: number;
    profitRat?: number;
    beforePackagePrice?: number;
    beforePiecePrice?: number;
    packageOpType?: number;
    pieceOpType?: number;
    afterPackagePrice?: number;
    afterPiecePrice?: number;
    beforePriceType?: number;
    afterPriceType?: number;
    targetType?: number;
    beforeDiscountType?: number;
    afterDiscountType?: number;
}
export class InventoryGoodsAdjustInfo {
    orderId?: string;
    orderNo?: string;
    created?: string;
    status?: number;
    statusName?: string;
    opType?: number;
    sourceType?: number;
    packageCostPrice?: number;
    goods?: GoodsInfo;
    avgPackageCostPrice?: number;
    beforeProfitRat?: number;
    profitRat?: number;
    beforePackagePrice?: number;
    beforePiecePrice?: number;
    packageOpType?: number;
    pieceOpType?: number;
    afterPackagePrice?: number;
    afterPiecePrice?: number;
    beforePriceType?: number;
    afterPriceType?: number;
    @JsonProperty({ type: Array, clazz: WaitingEffectPriceItem })
    waitingEffectPriceList?: WaitingEffectPriceItem[];
}
