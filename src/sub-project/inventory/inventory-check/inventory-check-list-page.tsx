/**
 * create by <PERSON><PERSON>
 * desc: 盘点单列表
 * create date 2021/1/29
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { IconFontView, SizedBox, Spacer } from "../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { BaseBlocPage, NetworkView } from "../../base-ui/base-page";
import { InventoryCheckListPageBloc } from "./inventory-check-list-page-bloc";
import { MenuItem, PopMenu } from "../../base-ui/views/pop-menu";
import { GoodsCheckOrdersItem, GoodsCheckOrdersItemCoworkTasks } from "../data/inventory-bean";
import colors from "../../theme/colors";
import { AbcView } from "../../base-ui/views/abc-view";
import { TimeUtils } from "../../common-base-module/utils";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { ABCUtils } from "../../base-ui/utils/utils";
import { BlocHelper } from "../../bloc/bloc-helper";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventoryCheckCreatePage } from "./inventory-check-create-page";
import { InventoryFullCheckTaskInvoicePage } from "./inventory-full-check-task-invoice-page";
import { FilterItem, Filters } from "../../base-ui/searchBar/search-bar-bean";
import { CommonFilterId } from "../../base-ui/searchBar/search-bar";
import { Range } from "../../base-ui/utils/value-holder";
import { RangePicker } from "../../base-ui/picker/range-picker";
import { ABCEmptyView } from "../../base-ui/views/empty-view";
import { InventoryCheckSearchBarView } from "./views/inventory-check-search-bar-view";
import { userCenter } from "../../user-center";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { InventoryCheckDetailStatus, InventoryCheckEnterType } from "./data/inventory-check-bean";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { BlocBuilder } from "../../bloc";
import { InventoryModuleQLAddView, InventoryModuleQLSummaryDisplay } from "../views/inventory-list-page-views";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";

interface InventoryCheckListPageProps {}

export class InventoryCheckListPage extends BaseBlocPage<InventoryCheckListPageProps, InventoryCheckListPageBloc> {
    private filter: Filters;

    constructor(props: InventoryCheckListPageProps) {
        super(props);
        this.bloc = new InventoryCheckListPageBloc();

        this.filter = new Filters();
    }

    async componentDidMount(): Promise<void> {
        const _filterList = [
            {
                filters: [
                    {
                        title: "全部时间",
                        id: CommonFilterId.all,
                        exclusive: true,
                        isDefault: true,
                        select: true,
                    },
                    {
                        title: "今天",
                        id: CommonFilterId.timeToday,
                        exclusive: true,
                        timeRange: new Range<Date>(TimeUtils.getTodayStart(), TimeUtils.getTodayStart()),
                    },
                    {
                        title: "昨天",
                        id: CommonFilterId.timeYesterday,
                        exclusive: true,
                        timeRange: new Range<Date>(TimeUtils.getYesterdayStart(), TimeUtils.getYesterdayStart()),
                    },
                    {
                        title: "本周",
                        id: CommonFilterId.timeThisWeek,
                        exclusive: true,
                        timeRange: new Range<Date>(TimeUtils.getThisWeekFirstDay(), TimeUtils.getTodayEnd()),
                    },
                    {
                        title: "本月",
                        id: CommonFilterId.timeThisMonth,
                        exclusive: true,
                        timeRange: new Range<Date>(TimeUtils.getThisMonthFirstDay(), TimeUtils.getTodayEnd()),
                    },
                    {
                        title: "今年",
                        id: CommonFilterId.timeThisYear,
                        exclusive: true,
                        timeRange: new Range<Date>(TimeUtils.getThisYearFirstDay(), TimeUtils.getTodayEnd()),
                    },
                    {
                        title: "选择时间",
                        defaultTitle: "选择时间",
                        id: CommonFilterId.asyncFilter,
                        exclusive: true,
                        asyncChange: async (item: FilterItem) => {
                            const timeRange = await RangePicker.show(item.timeRange);
                            if (timeRange) {
                                item.title = `${timeRange.start?.format("yyyy-MM-dd")}\n${timeRange.end?.format("yyyy-MM-dd")}`;
                                item.timeRange = timeRange;
                                return item;
                            }
                        },
                    },
                ],
            },
        ];
        if (userCenter.clinic?.isChainAdminClinic) {
            const clinicFilterGroup: { id: string; filters: any } = { id: "clinic", filters: [] };
            clinicFilterGroup.filters?.push({
                id: CommonFilterId.asyncFilter + CommonFilterId.others - 1,
                title: "全部门店",
                exclusive: true,
                info: { id: "" },
                select: true,
                isDefault: true,
            });
            const chainClinicsList = await ClinicAgent.getChainClinics().catchIgnore();
            if (!!chainClinicsList) {
                chainClinicsList.map((clinic, index) => {
                    clinicFilterGroup.filters.push({
                        id: CommonFilterId.asyncFilter + CommonFilterId.others + index,
                        title: clinic.chainAdmin ? "总部" : clinic?.shortName?.length ? clinic.shortName : clinic.name,
                        exclusive: true,
                        info: clinic,
                    });
                });
                _filterList.push(clinicFilterGroup);
            }
        }
        this.filter = Filters.createFilters(_filterList);
        this.forceUpdate();
    }

    @runFuncBeforeCheckExpired()
    async handleCreateInvoice(): Promise<void> {
        const temporary = 1;
        const whole = 2;
        const menuItems: MenuItem<number>[] = [];
        menuItems.push(
            new MenuItem({
                value: temporary,
                icon: undefined,
                text: "临时盘点",
            })
        );
        menuItems.push(
            new MenuItem({
                value: whole,
                icon: undefined,
                text: "全量盘点",
            })
        );
        const select = await PopMenu.show(menuItems, { x: Sizes.dp8, y: Sizes.dp40 }, { x: Sizes.dp12, y: 0 });
        if (select == temporary) {
            this.bloc.requestTemporaryCheck();
        } else if (select == whole) {
            this.bloc.requestWholeCheck();
        }
    }

    getAppBarTitle(): string {
        return "盘点单";
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            isOpenMultiplePharmacy = state.pharmacyInfoConfig?.isOpenMultiplePharmacy,
            pharmacyName = state.currentPharmacy?.name;
        return isOpenMultiplePharmacy ? (
            <View style={[ABCStyles.rowAlignCenter]}>
                <Text style={[TextStyles.t16MB]}>盘点单</Text>
                <SizedBox width={Sizes.dp4} />
                <Text style={[TextStyles.t14NM]}>{`${isOpenMultiplePharmacy ? "(" + pharmacyName + ")" : ""}`}</Text>
            </View>
        ) : undefined;
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        if (state.currentPharmacy?.enableCheckInOnlyView) return [];
        return [<InventoryModuleQLAddView key={"checkBtn"} onClick={this.handleCreateInvoice.bind(this)} />];
    }

    private _renderFilterView(): JSX.Element {
        return (
            <InventoryCheckSearchBarView
                editable={false}
                filters={this.filter}
                onChange={(text) => {
                    this.bloc.requestSearchGoods(text);
                }}
                value={this.bloc.currentState.params.goodsName}
                placeholder={"药品名称/条形码"}
                onFilterChange={(filters) => {
                    this.bloc.requestChangeFilter(filters);
                }}
            />
        );
    }

    private _renderClinicCheckSummary(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            canViewPrice = state.canViewPrice;
        return (
            <InventoryModuleQLSummaryDisplay>{`共 ${detail?.count ?? 0} 条盘点单，盈亏数量 ${detail?.stat?.count ?? 0}${
                canViewPrice ? `，盈亏金额(进价) ${ABCUtils.formatPriceWithRMB(detail?.stat?.costAmount ?? 0, false)}` : ""
            }，盈亏金额(售价) ${ABCUtils.formatPriceWithRMB(detail?.stat?.saleAmount ?? 0, false)}`}</InventoryModuleQLSummaryDisplay>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._renderFilterView()}
                <_InventoryCheckListPageView />
                {this._renderClinicCheckSummary()}
            </View>
        );
    }
}
class _InventoryCheckListPageView extends NetworkView {
    static contextType = InventoryCheckListPageBloc.Context;

    componentDidMount() {
        BlocHelper.connectLoadingStatus(
            InventoryCheckListPageBloc.fromContext(this.context),
            this,
            (state) => !state.taskList.length && !state.checkList.length
        );
    }

    emptyContent(): JSX.Element {
        return <ABCEmptyView tips={"暂无盘点记录"} />;
    }

    renderContent(): JSX.Element {
        const state = InventoryCheckListPageBloc.fromContext(this.context).currentState;
        const viewListDetail: any[] = [];
        state.taskList.forEach((item) => {
            viewListDetail.push({
                viewType: _InventoryCheckTaskListItem,
                checkInfo: item,
            });
        });
        state.checkList.forEach((item) => {
            viewListDetail.push({
                viewType: _InventoryCheckListItem,
                checkInfo: item,
                canViewPrice: state.canViewPrice,
                onDelete: (detail: GoodsCheckOrdersItem) => {
                    InventoryCheckListPageBloc.fromContext(this.context).requestDeleteInventoryCheckDraft(detail);
                },
            });
        });
        //将临时盘点中的草稿排序到最前面
        viewListDetail.sort((a, b) => {
            const { status: status_A = 0, lastModifiedDate: lastModifiedDate_A } = a.checkInfo || {};
            const { status: status_B = 0, lastModifiedDate: lastModifiedDate_B } = b.checkInfo || {};
            if (
                status_A < InventoryCheckDetailStatus.create &&
                (lastModifiedDate_A?.getTime() ?? 0) > (lastModifiedDate_B?.getTime() ?? 0)
            ) {
                return -1;
            } else if (
                status_B < InventoryCheckDetailStatus.create &&
                (lastModifiedDate_A?.getTime() ?? 0) < (lastModifiedDate_B?.getTime() ?? 0)
            ) {
                return 1;
            } else {
                return 0;
            }
        });

        const views: JSX.Element[] = viewListDetail.map((item) => {
            const { viewType, ...others } = item;
            return React.createElement(viewType, { ...others });
        });

        return (
            <View style={{ flex: 1 }}>
                <AbcListView
                    finished={!state.hasMore}
                    initialListSize={10}
                    scrollEventThrottle={300}
                    getRowKey={(index) => index.toString()}
                    numberOfRows={views.length}
                    dataSource={views}
                    renderRow={(item) => item}
                    onRefresh={() => {
                        InventoryCheckListPageBloc.fromContext(this.context).requestReloadData();
                    }}
                    onEndReached={() => {
                        InventoryCheckListPageBloc.fromContext(this.context).requestLoadMore();
                    }}
                />
            </View>
        );
    }
}

interface _InventoryCheckListItemProps {
    canViewPrice?: boolean;
    checkInfo?: GoodsCheckOrdersItem;
    onDelete?(item?: GoodsCheckOrdersItem): void;
}

const _InventoryCheckListItem: React.FC<_InventoryCheckListItemProps> = ({ checkInfo, canViewPrice, onDelete }) => {
    const statusName = checkInfo?.statusName,
        status = checkInfo?.status,
        isDraft =
            (checkInfo?.status ?? 0) == InventoryCheckDetailStatus.draft ||
            (checkInfo?.status ?? 0) == InventoryCheckDetailStatus.onlineDraft;
    const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;
    let textStyle = TextStyles.t16NT3.copyWith({ lineHeight: Sizes.dp24 }); // 默认灰色样式
    if (status == InventoryCheckDetailStatus.waitVerify) {
        if (isDrugstoreButler && checkInfo?.gspTaskClinicId == userCenter.clinic?.clinicId) {
            textStyle = textStyle.copyWith({ color: Colors.B2 });
        } else if (!isDrugstoreButler && userCenter.clinic?.isChainAdminClinic) {
            textStyle = textStyle.copyWith({ color: Colors.Y2 });
        }
    } else if (status == InventoryCheckDetailStatus.finish) {
        if (isDrugstoreButler) {
            textStyle = textStyle.copyWith({ color: Colors.canAppointmentColor });
        } else {
            textStyle = textStyle.copyWith({ color: Colors.black });
        }
    } else if (status == InventoryCheckDetailStatus.refused && isDrugstoreButler) {
        textStyle = textStyle.copyWith({ color: Colors.R6 });
    } else if (status == InventoryCheckDetailStatus.revoke && isDrugstoreButler) {
        textStyle = textStyle.copyWith({ color: Colors.black });
    } else if (isDraft && isDrugstoreButler) {
        textStyle = textStyle.copyWith({ color: Colors.Y2 });
    }
    const disable = checkInfo?.disableAmendmentOrder;

    return (
        <AbcView
            style={[{ backgroundColor: colors.white }]}
            onClick={async () => {
                if (disable) {
                    await showConfirmDialog("修正类单据请在 PC 端查看", "");
                    return;
                }

                ABCNavigator.navigateToPage(
                    <InventoryCheckCreatePage
                        id={isDraft ? undefined : checkInfo?.id}
                        draftId={checkInfo?.__draftId}
                        createType={checkInfo?.isOnlineDraft ? InventoryCheckEnterType.onlineDraft : InventoryCheckEnterType.manual}
                    />
                );
            }}
        >
            <View
                style={[
                    ABCStyles.bottomLine,
                    Sizes.marginLTRB(Sizes.dp16, 0, 0, 0),
                    Sizes.paddingLTRB(0, Sizes.dp10, Sizes.dp16, Sizes.dp10),
                ]}
            >
                <View style={{ flex: 1, alignItems: "center", flexDirection: "row" }}>
                    <Text style={[TextStyles.t16MB1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {isDraft ? `最后编辑：${TimeUtils.formatDatetimeAsRecent(checkInfo?.lastModifiedDate)}` : checkInfo?.orderNo ?? ""}
                    </Text>
                    <Text style={[textStyle, { marginHorizontal: Sizes.dp4 }]}>{`[${(!!statusName ? statusName : "草稿") ?? "--"}]`}</Text>
                    <Spacer />
                    {isDraft && (
                        // 当为草稿时显示删除按钮
                        <AbcView
                            key={"draft"}
                            style={{ marginRight: Sizes.dp8 }}
                            onClick={() => {
                                onDelete?.(checkInfo);
                            }}
                        >
                            <IconFontView
                                name={"trash"}
                                color={Colors.red}
                                size={Sizes.dp16}
                                style={{
                                    marginRight: -Sizes.dp10,
                                    paddingVertical: Sizes.dp10,
                                    paddingHorizontal: Sizes.dp8,
                                }}
                            />
                        </AbcView>
                    )}
                    {!isDraft && checkInfo?.status == InventoryCheckDetailStatus.finish && canViewPrice && (
                        <Text style={[TextStyles.t16NT1]}>{ABCUtils.formatPriceWithRMB(checkInfo?.saleAmount ?? 0, false) ?? ""}</Text>
                    )}
                </View>
                <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp8 }]}>
                    <Text
                        style={[
                            TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }),
                            {
                                flexShrink: 1,
                                marginRight: Sizes.dp6,
                            },
                        ]}
                        numberOfLines={1}
                    >
                        {`${checkInfo?.organ?.shortName ?? checkInfo?.organ?.name ?? "--"}`}
                    </Text>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>
                        {`${TimeUtils.formatDate(checkInfo?.createdDate) ?? ""}`}
                    </Text>
                    <Spacer />
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>{`${checkInfo?.createdUser?.name ?? ""}`}</Text>
                </View>
            </View>
        </AbcView>
    );
};

interface _InventoryCheckTaskListItemProps {
    checkInfo?: GoodsCheckOrdersItemCoworkTasks;
}

const _InventoryCheckTaskListItem: React.FC<_InventoryCheckTaskListItemProps> = ({ checkInfo }) => {
    return (
        <AbcView
            style={[{ backgroundColor: colors.white }]}
            onClick={() => {
                ABCNavigator.navigateToPage(<InventoryFullCheckTaskInvoicePage id={checkInfo!.parentTaskId!} type={checkInfo!.type!} />);
            }}
        >
            <View
                style={[
                    ABCStyles.bottomLine,
                    Sizes.marginLTRB(Sizes.dp16, 0, 0, 0),
                    Sizes.paddingLTRB(0, Sizes.dp10, Sizes.dp16, Sizes.dp10),
                ]}
            >
                <View style={{ flex: 1, alignItems: "center", flexDirection: "row" }}>
                    <Text style={[TextStyles.t16MB1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {checkInfo?.name ?? ""}
                    </Text>
                    <SizedBox width={Sizes.dp4} />
                    <Text
                        style={[
                            TextStyles.t16NY2.copyWith({
                                color: checkInfo?.status == "已完成" ? Colors.canAppointmentColor : Colors.Y2,
                            }),
                        ]}
                    >
                        {`[${checkInfo?.status}${checkInfo?.type == 1 && checkInfo.progress ? checkInfo.progress : ""}]`}
                    </Text>
                    <Spacer />
                </View>
                <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp8 }]}>
                    <Text
                        style={[
                            TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }),
                            {
                                flexShrink: 1,
                                marginRight: Sizes.dp6,
                            },
                        ]}
                        numberOfLines={1}
                    >
                        {`${checkInfo?.organ?.shortName ?? checkInfo?.organ?.name ?? "--"}`}
                    </Text>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>
                        {`${TimeUtils.formatDate(checkInfo?.createdDate) ?? ""}`}
                    </Text>
                    <Spacer />
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>{`${checkInfo?.createdUser?.name ?? ""}`}</Text>
                </View>
            </View>
        </AbcView>
    );
};
