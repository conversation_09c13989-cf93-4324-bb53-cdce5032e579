/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/1/27
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { BaseBlocPage, NetworkView } from "../../base-ui/base-page";
import { InventoryMedicineListBloc, OrderTypeAndByType, WarnType } from "./Inventory-medicine-list-bloc";
import { <PERSON><PERSON>elper } from "../../bloc/bloc-helper";
import { FilterGroup, FilterItem, Filters } from "../../base-ui/searchBar/search-bar-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { CommonFilterId } from "../../base-ui/searchBar/search-bar";
import { GoodsTypeId, SubClinicPricePriceMode } from "../../base-business/data/beans";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { BaseComponent } from "../../base-ui/base-component";
import { GoodsInventoryInfo } from "../data/inventory-bean";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { IconFontView, SizedBox, Spacer, UniqueKey } from "../../base-ui";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { AbcView } from "../../base-ui/views/abc-view";
import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";
import { InventorySearchView } from "./views/inventory-search-view";
import { ABCEmptyView } from "../../base-ui/views/empty-view";
import { ABCUtils } from "../../base-ui/utils/utils";
import { userCenter } from "../../user-center";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import _ from "lodash";
import { BlocBuilder } from "../../bloc";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { InventoryAgent } from "../data/inventory-agent";
import { AbcTag } from "../../base-ui/abc-app-library/common/abc-tag";
import { InventoryGoodsQrcodeScanPage } from "./inventory-goods-qrcode-scan-page";
import { CreateDirectoryTypeEnum } from "./data/inventory-goods-bean";
import { showOptionsBottomSheet } from "../../base-ui/abc-app-library/dialog/sheet-menu";
import { StatisticalPointKeyConstants } from "../../views/statistical-points";

interface InventoryMedicineListProps {
    warnType?: WarnType;
    isSkipWarn?: boolean; //库存不足--是否跳转库存预警排序
}

export class InventoryMedicineListPage extends BaseBlocPage<InventoryMedicineListProps, InventoryMedicineListBloc> {
    protected filters: Filters;

    constructor(props: InventoryMedicineListProps) {
        super(props);
        this.filters = new InventoryMedicineListPageFilters(props.warnType);
        this.bloc = new InventoryMedicineListBloc({
            queryParams: {},
            warnType: props.warnType,
            isSkipWarn: props?.isSkipWarn,
        });
    }
    static async show(options?: InventoryMedicineListProps): Promise<string> {
        if (userCenter.clinic?.isDrugstoreButler) {
            return DrugstoreInventoryMedicineListPage.show(options);
        } else {
            return ABCNavigator.navigateToPage(<InventoryMedicineListPage {...options} />);
        }
    }

    getAppBarTitle(): string {
        return "药品/物资";
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        const state = this.bloc.currentState,
            isOpenMultiplePharmacy = state.pharmacyInfoConfig?.isOpenMultiplePharmacy,
            pharmacyName = state.currentPharmacy?.name;
        return isOpenMultiplePharmacy ? (
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16MB}>{"药品/物资"}</Text>
                <SizedBox width={Sizes.dp4} />
                <Text style={TextStyles.t14NM}>{`${isOpenMultiplePharmacy ? "(" + pharmacyName + ")" : ""}`}</Text>
            </View>
        ) : undefined;
    }

    componentDidMount(): void {
        if (userCenter.clinic?.isChainAdminClinic) {
            const filterGroup = JsonMapper.deserialize(FilterGroup, { id: "clinic", filters: [] });
            filterGroup.filters?.push(
                JsonMapper.deserialize(FilterItem, {
                    id: CommonFilterId.asyncFilter + CommonFilterId.others - 1,
                    title: "全部门店",
                    exclusive: true,
                    info: { id: "" },
                    select: true,
                    isDefault: true,
                })
            );
            ClinicAgent.getChainClinics().then((rsp) => {
                rsp.map((clinic, index) => {
                    filterGroup.id = "clinic";
                    filterGroup.filters!.push(
                        JsonMapper.deserialize(FilterItem, {
                            id: CommonFilterId.asyncFilter + CommonFilterId.others + index,
                            title: clinic.chainAdmin ? "总部" : clinic?.shortName?.length ? clinic.shortName : clinic.name,
                            exclusive: true,
                            info: clinic,
                        })
                    );
                });
                this.filters = _.cloneDeep(this.filters);
                this.filters.filters.push(filterGroup);
                this.forceUpdate();
            });
        }

        const currentPharmacy = InventoryAgent.getCurrentPharmacy();
        if (currentPharmacy && currentPharmacy.type == PharmacyType.virtual) {
            const filterGroup = JsonMapper.deserialize(FilterGroup, { id: "medicineType", filters: [] });
            filterGroup.filters = [
                JsonMapper.deserialize(FilterItem, {
                    title: "中药饮片",
                    id: CommonFilterId.allStatus + GoodsTypeId.medicineChinesePiece,
                    exclusive: true,
                }),
                JsonMapper.deserialize(FilterItem, {
                    title: "中药颗粒",
                    id: CommonFilterId.allStatus + GoodsTypeId.medicineChineseGranule,
                    exclusive: true,
                }),
            ];
            this.filters = _.cloneDeep(this.filters);
            _.fill(this.filters.filters, filterGroup, 1, 2);
            this.forceUpdate();
        }
    }

    async _handleCreateInvoice(): Promise<void> {
        const state = this.bloc.currentState;
        const list = [
            { name: "新建药品", index: CreateDirectoryTypeEnum.CreateMedicine },
            { name: `新建${userCenter.clinic?.isDrugstoreButler ? "器械" : "物资"}`, index: CreateDirectoryTypeEnum.CreateSupplies },
            { name: "新建商品", index: CreateDirectoryTypeEnum.CreateGoods },
        ];

        const initIndex = list!.findIndex((i) => i.index == CreateDirectoryTypeEnum.CreateMedicine) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择新建类型",
            options: [...list.map((item) => item.name)],
            itemHeight: Sizes.dp48,
            showCloseButton: true,
            optionsWidgets: list.map((item, index) => (
                <AbcView
                    key={index}
                    style={[
                        ABCStyles.rowAlignCenter,
                        {
                            flex: 1,
                            paddingVertical: Sizes.dp14,
                            marginBottom: Sizes.dp16,
                            borderRadius: Sizes.dp4,
                            backgroundColor: Colors.dialogBtnPress,
                        },
                    ]}
                >
                    <Text style={[TextStyles.t14NT2.copyWith({ color: Colors.t2 }), { flex: 1, textAlign: "center" }]} numberOfLines={1}>
                        {item.name ?? ""}
                    </Text>
                </AbcView>
            )),
            crossAxisCount: 1,
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
        });

        if (result && result.length) {
            let _createDirectoryType: number = CreateDirectoryTypeEnum.CreateMedicine;
            const _result = (_.first(result) ?? 0) <= 1 ? (_.first(result) ?? 0) + 1 : 7;

            switch (_result) {
                case CreateDirectoryTypeEnum.CreateMedicine:
                    _createDirectoryType = CreateDirectoryTypeEnum.CreateMedicine;
                    break;
                case CreateDirectoryTypeEnum.CreateSupplies:
                    _createDirectoryType = CreateDirectoryTypeEnum.CreateSupplies;
                    break;
                case CreateDirectoryTypeEnum.CreateGoods:
                    _createDirectoryType = CreateDirectoryTypeEnum.CreateGoods;
            }
            const _addMedicine: boolean = await InventoryGoodsQrcodeScanPage.show({
                createDirectoryType: _createDirectoryType,
                pharmacyNo: state.currentPharmacy?.no,
                clinicId: state.queryParams.clinicId,
                withStock: "",
                onlyStock: "",
            }).then();

            if (_addMedicine) {
                this.bloc.requestReload();
            }
        }
    }

    _renderSearchBar(): JSX.Element {
        const state = this.bloc.currentState,
            currentPharmacy = state.currentPharmacy,
            queryParams = state.queryParams;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp8, Sizes.listHorizontalMargin, Sizes.dp8),
                    { backgroundColor: Colors.white },
                ]}
            >
                <AbcView
                    style={[
                        ABCStyles.rowAlignCenter,
                        { paddingHorizontal: Sizes.dp9, backgroundColor: Colors.P4, borderRadius: Sizes.dp2, height: Sizes.dp32 },
                    ]}
                    key={"search"}
                    onClick={() => {
                        ABCNavigator.navigateToPage(
                            <InventoryMedicineSearchPage
                                placeholder={"输入药品名称/条形码搜索"}
                                searchParams={{
                                    onlyStock: queryParams.clinicId?.length ? "1" : "",
                                    withStock: "",
                                    disable: undefined,
                                    clinicId: queryParams.clinicId,
                                    pharmacyNo: currentPharmacy?.no ?? 0,
                                }}
                                callback={(goodsInfo) =>
                                    this.bloc.requestModifyMedicineInfo(JsonMapper.deserialize(GoodsInventoryInfo, goodsInfo))
                                }
                            />
                        );
                    }}
                >
                    <IconFontView name={"Search"} size={Sizes.dp14} color={Colors.T6} />
                    <SizedBox width={Sizes.dp6} />
                    <Text style={TextStyles.t14NT3} key={"placeholder"}>
                        {"药品名称/条形码"}
                    </Text>
                </AbcView>
            </View>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        if (!state.canCreateMedicine) return [];
        return [
            <AbcView
                key={"InventoryAddMedicine"}
                style={[ABCStyles.centerChild, { width: Sizes.dp32, height: Sizes.dp32 }]}
                onClick={this._handleCreateInvoice.bind(this)}
            >
                <IconFontView name={"plus_circle_glyph"} color={Colors.mainColor} size={Sizes.dp20} />
            </AbcView>,
        ];
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }
    _summaryCategoryText(): string {
        return "药品/物资";
    }
    private _renderClinicMedicineSummary(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.lastSearchResponse;
        const checkDrugMaterialCost = state.checkDrugMaterialCost;
        const strArr: string[] = [];
        strArr.push(`共 ${detail?.saleCount ?? 0} 种${this._summaryCategoryText()}可售`);
        if (checkDrugMaterialCost && state.currentPharmacy?.type != PharmacyType.virtual) {
            strArr.push(`合计成本 ${ABCUtils.formatPriceWithRMB(detail?.totalCostPrice ?? 0, false)}`);
        }
        strArr.push(`售价 ${ABCUtils.formatPriceWithRMB(detail?.totalPrice ?? 0, false)}`);
        return (
            <View style={{ backgroundColor: Colors.theme4, paddingVertical: Sizes.dp4 }}>
                <Text style={[TextStyles.t12NM.copyWith({ lineHeight: Sizes.dp16 }), { textAlign: "center", flex: 1 }]}>
                    {`${strArr.join("，")}`}
                </Text>
            </View>
        );
    }

    private _renderFilterInputView(): JSX.Element {
        return (
            <View style={{ backgroundColor: Colors.white }}>
                <InventorySearchView
                    filters={this.filters}
                    onFilterChange={(filters) => {
                        this.bloc.requestChangeFilter(filters);
                    }}
                />
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this._renderFilterInputView()}
                {this._renderSearchBar()}
                <_InventoryMedicineListView />
                {this._renderClinicMedicineSummary()}
            </View>
        );
    }
}
export class DrugstoreInventoryMedicineListPage extends InventoryMedicineListPage {
    static async show(options?: InventoryMedicineListProps): Promise<string> {
        return ABCNavigator.navigateToPage(<DrugstoreInventoryMedicineListPage {...options} />);
    }
    getAppBarTitle(): string {
        return "商品";
    }
    getAPPBarCustomTitle(): JSX.Element | undefined {
        return undefined;
    }

    _summaryCategoryText(): string {
        return "商品";
    }
    constructor(props: InventoryMedicineListProps) {
        super(props);
        this.filters = new DrugstoreInventoryMedicineListPageFilters(props.warnType);
    }
}
interface _InventoryMedicineListProps {}

class _InventoryMedicineListView extends NetworkView<_InventoryMedicineListProps> {
    constructor(props: _InventoryMedicineListProps) {
        super(props);
    }

    static contextType = InventoryMedicineListBloc.Context;

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(
            InventoryMedicineListBloc.fromContext(this.context),
            this,
            (state) => !state.goodsInventoryList.length
        );
    }

    emptyContent(): JSX.Element {
        return <ABCEmptyView tips={"暂无药品/物资记录"} />;
    }

    reloadData(): void {
        InventoryMedicineListBloc.fromContext(this.context).requestReload();
    }

    renderContent(): JSX.Element {
        const bloc = InventoryMedicineListBloc.fromContext(this.context),
            state = bloc.currentState;
        const { checkDrugMaterialCost, checkDrugMaterialProfit } = state;
        return (
            <AbcListView
                loading={state.loading}
                scrollEventThrottle={300}
                initialListSize={10}
                numberOfRows={state.goodsInventoryList.length}
                dataSource={state.goodsInventoryList}
                getRowKey={(index) => state.goodsInventoryList[index].id ?? UniqueKey()}
                renderRow={(data) => (
                    <_InventoryMedicineItemView
                        goodsInfo={data}
                        onChange={(medicine) => bloc.requestModifyMedicineInfo(medicine)}
                        checkDrugMaterialCost={checkDrugMaterialCost}
                        checkDrugMaterialProfit={checkDrugMaterialProfit}
                    />
                )}
                onRefresh={() => {
                    bloc.requestReload();
                }}
                onEndReached={() => {
                    bloc.requestLoadMore();
                }}
            />
        );
    }
}

interface _InventoryMedicineItemViewProps {
    goodsInfo: GoodsInventoryInfo;
    checkDrugMaterialCost: boolean; // 能否查看药品物资成本
    checkDrugMaterialProfit: boolean; // 能否查看药品物资毛利

    onChange?(arg1: GoodsInventoryInfo): void;
}

class _InventoryMedicineItemView extends BaseComponent<_InventoryMedicineItemViewProps> {
    constructor(props: _InventoryMedicineItemViewProps) {
        super(props);
    }

    getTextColor = () => {
        // 如果商品已停用，显示为灰色
        if (!!this.props.goodsInfo?.v2DisableStatus) {
            return Colors.T6;
        }

        // 如果周转天数警告标志为真，显示为黄色
        if (this.props.goodsInfo?.turnoverDaysWarnFlag) {
            return Colors.Y2;
        }

        // 默认显示为常规文本颜色
        return Colors.T4;
    };

    render(): JSX.Element {
        const goodsInfo = this.props.goodsInfo;
        const { checkDrugMaterialCost, checkDrugMaterialProfit } = this.props;
        const purchaseMarkup = goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup;
        return (
            <AbcView
                style={[
                    Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10),
                    {
                        marginBottom: Sizes.dp8,
                        backgroundColor: Colors.white,
                    },
                ]}
                onClick={() => {
                    this.props.onChange?.(goodsInfo);
                }}
            >
                <View style={[ABCStyles.rowAlignCenter, {}]}>
                    <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {goodsInfo?.displayName ?? ""}
                    </Text>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { marginLeft: Sizes.dp8 }]}>
                        {(goodsInfo?.isChineseMedicine ? goodsInfo?.extendSpec : goodsInfo?.packageSpec) ?? ""}
                    </Text>
                    <Spacer />
                    {!!goodsInfo.prohibitStock && (
                        <AbcTag
                            text={"部分停售"}
                            borderColor={Colors.t3}
                            textStyle={TextStyles.t10NT2.copyWith({ lineHeight: Sizes.dp10 })}
                            style={{ borderWidth: Sizes.dp1, ...Sizes.paddingLTRB(Sizes.dp4, Sizes.dp4, Sizes.dp4, Sizes.dp4) }}
                        />
                    )}
                    <Text
                        style={[
                            TextStyles.t16MT1.copyWith({
                                lineHeight: Sizes.dp24,
                                color: goodsInfo?.shortageWarnFlag ? Colors.Y2 : Colors.T1,
                            }),
                            { marginLeft: Sizes.dp12 },
                        ]}
                    >
                        {goodsInfo?.displayStockInfo() ?? ""}
                    </Text>
                </View>
                <SizedBox height={Sizes.dp8} />
                <View style={ABCStyles.rowAlignCenter}>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { marginRight: Sizes.dp8 }]}>
                        {goodsInfo?.displayTypeName ?? ""}
                    </Text>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {goodsInfo?.displayManufacturer ?? ""}
                    </Text>
                    <Spacer />
                    {!!goodsInfo?.v2DisableStatus && (
                        <Text
                            style={[TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 }), { marginTop: Sizes.dp8, textAlign: "right" }]}
                        >
                            {"已停用"}
                        </Text>
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />
                <View style={ABCStyles.rowAlignCenter}>
                    {checkDrugMaterialCost && (
                        <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1 }]}>
                            {`最近进价 ${ABCUtils.formatMoney(goodsInfo?.lastPackageCostPrice ?? 0, false)}`}
                        </Text>
                    )}

                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1 }]}>
                        {`售价 ${
                            purchaseMarkup
                                ? userCenter.clinic?.isChainAdminClinic
                                    ? goodsInfo.chainPackagePrice ?? ""
                                    : goodsInfo.clinicPackagePrice ?? ""
                                : ABCUtils.formatMoney(goodsInfo?.packagePrice ?? 0, false)
                        }`}
                    </Text>
                    {checkDrugMaterialProfit && (
                        <Text
                            style={[
                                TextStyles.t14NT4.copyWith({
                                    lineHeight: Sizes.dp20,
                                    color: goodsInfo?.negativeProfitWarnFlag ? Colors.Y2 : Colors.T4,
                                }),
                                { flex: 1 },
                            ]}
                            numberOfLines={1}
                            ellipsizeMode={"head"}
                        >
                            {`毛利 ${goodsInfo?.profitRat ?? 0}%`}
                        </Text>
                    )}
                </View>
                <SizedBox height={Sizes.dp8} />
                <View style={ABCStyles.rowAlignCenter}>
                    <Text
                        style={[
                            TextStyles.t14NT4.copyWith({
                                color: this.getTextColor(),
                                lineHeight: Sizes.dp20,
                            }),
                        ]}
                    >
                        {`周转天数 ${goodsInfo?.turnoverDays ?? "-"}`}
                    </Text>
                    <SizedBox width={Sizes.dp8} />
                    <Text
                        style={[
                            TextStyles.t14NT4.copyWith({
                                color: goodsInfo?.expiredWarnFlag ? Colors.Y2 : Colors.T4,
                                lineHeight: Sizes.dp20,
                            }),
                        ]}
                    >
                        {`最近效期 ${goodsInfo?.minExpiryDate ?? "-"}`}
                    </Text>
                </View>
            </AbcView>
        );
    }
}

class InventoryMedicineListPageFilters extends Filters {
    combineFilters(): FilterGroup[] {
        return [
            JsonMapper.deserialize(FilterGroup, {
                id: "orderType",
                statPoint: {
                    key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort,
                },
                filters: [
                    JsonMapper.deserialize(FilterItem, {
                        title: "默认排序",
                        id: OrderTypeAndByType.notType,
                        isDefault: true,
                        exclusive: true,
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "名字首字母升序",
                        id: OrderTypeAndByType.nameAsc,
                        exclusive: true,
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "库存余量升序",
                        id: OrderTypeAndByType.stockAsc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_StockAsc },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "库存余量降序",
                        id: OrderTypeAndByType.stockDesc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_StockDesc },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "周转天数升序",
                        id: OrderTypeAndByType.turnoverDaysAsc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_TurnoverDaysAsc },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "周转天数降序",
                        id: OrderTypeAndByType.turnoverDaysDesc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_TurnoverDaysDesc },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "最近效期升序",
                        id: OrderTypeAndByType.expiredAsc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_Expired },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "毛利升序",
                        id: OrderTypeAndByType.profitRatAsc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_ProfitRat },
                    }),
                    JsonMapper.deserialize(FilterItem, {
                        title: "毛利降序",
                        id: OrderTypeAndByType.profitRatDesc,
                        exclusive: true,
                        statPoint: { key: StatisticalPointKeyConstants.Inventory_Goods_FilterSort_ProfitRat },
                    }),
                ],
            }),
            JsonMapper.deserialize(FilterGroup, {
                id: "medicineType",
                filters: [
                    JsonMapper.deserialize(FilterItem, {
                        title: "全部类型",
                        id: CommonFilterId.asyncFilter,
                        exclusive: true,
                    }),
                ],
            }),
            // JsonMapper.deserialize(FilterGroup, {
            //     id: "warnType",
            //     filters: [
            //         JsonMapper.deserialize(FilterItem, {
            //             title: "库存不足",
            //             id: CommonFilterId.others + WarnType.stock,
            //             exclusive: true,
            //         }),
            //         JsonMapper.deserialize(FilterItem, {
            //             title: "效期预警",
            //             id: CommonFilterId.others + WarnType.expired,
            //             exclusive: true,
            //         }),
            //         JsonMapper.deserialize(FilterItem, {
            //             title: "毛利异常",
            //             id: CommonFilterId.others + WarnType.profitRat,
            //             exclusive: true,
            //         }),
            //     ],
            // }),
        ];
    }
    selectOrderType(selectWarnType?: WarnType): OrderTypeAndByType {
        let selectOrderType = OrderTypeAndByType.notType;
        switch (selectWarnType) {
            case WarnType.expired:
                selectOrderType = OrderTypeAndByType.expiredAsc;
                break;
            case WarnType.stock:
                selectOrderType = OrderTypeAndByType.notType;
                break;
            case WarnType.profitRat:
                selectOrderType = OrderTypeAndByType.profitRatAsc;
                break;
            default:
                selectOrderType = OrderTypeAndByType.notType;
        }
        return selectOrderType;
    }
    constructor(selectWarnType?: WarnType) {
        super();
        this.filters = this.combineFilters();

        const filterGroup = this.filters.find((group) => group.id == "orderType");

        filterGroup?.filters?.map((filter) => {
            if (filter.id == this.selectOrderType(selectWarnType)) {
                filter.select = true;
            }
            return filter;
        });

        // if (selectWarnType) {
        //     const filterGroup = this.filters.find((group) => group.id == "warnType");
        //     filterGroup?.filters?.map((filter) => {
        //         if (filter.id! - CommonFilterId.others == selectWarnType) {
        //             filter.select = true;
        //         }
        //         return filter;
        //     });
        // }
    }
}
class DrugstoreInventoryMedicineListPageFilters extends InventoryMedicineListPageFilters {
    constructor(selectWarnType?: WarnType) {
        super(selectWarnType);
    }
    combineFilters(): FilterGroup[] {
        const pharmacyFilters = super.combineFilters();
        pharmacyFilters[0].filters!.push(
            ...[
                JsonMapper.deserialize(FilterItem, {
                    title: "30日销量升序",
                    id: OrderTypeAndByType.daySalesVolumeAsc,
                    exclusive: true,
                }),
                JsonMapper.deserialize(FilterItem, {
                    title: "30日销量降序",
                    id: OrderTypeAndByType.daySalesVolumeDesc,
                    exclusive: true,
                }),
                JsonMapper.deserialize(FilterItem, {
                    title: "最近进价升序",
                    id: OrderTypeAndByType.recentPurchasePriceAsc,
                    exclusive: true,
                }),
                JsonMapper.deserialize(FilterItem, {
                    title: "最近进价降序",
                    id: OrderTypeAndByType.recentPurchasePriceDesc,
                    exclusive: true,
                }),
            ]
        );
        return pharmacyFilters;
    }
    selectOrderType(selectWarnType?: WarnType): OrderTypeAndByType {
        let pharmacySelectOrderType = super.selectOrderType(selectWarnType);
        switch (selectWarnType) {
            case WarnType.unmarketableGoods:
                pharmacySelectOrderType = OrderTypeAndByType.daySalesVolumeAsc;
                break;
            case WarnType.purchasePriceChange:
                pharmacySelectOrderType = OrderTypeAndByType.recentPurchasePriceDesc;
                break;
        }
        return pharmacySelectOrderType;
    }
}
