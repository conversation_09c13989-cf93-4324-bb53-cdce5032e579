import { ABCApiNetwork } from "../../net";
import {
    EcAuthBindListRsp,
    OrderActualPriceTrendItem,
    OrderCloudPlatformInfo,
    OrderCloudReq,
    OrderProductSaleTopItem,
    OrderSummaryInfo,
} from "./order-cloud-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";

export class OrderCloudAgent {
    /**
     * 获取当前门店/连锁绑定的电商列表
     * @param includeHistory (是否包含历史绑定的电商店铺，1：包含，非1：不包含)
     */
    static async getEcAuthBindList(includeHistory?: number): Promise<EcAuthBindListRsp> {
        return ABCApiNetwork.get("ec/auth/bind/list", {
            queryParameters: { includeHistory },
            clazz: EcAuthBindListRsp,
        });
    }

    /**
     * 查询订单实际价格趋势
     * @param params
     */
    static async queryOrderActualPriceTrend(params: OrderCloudReq): Promise<OrderActualPriceTrendItem[]> {
        const dayFormat = "yyyy-MM-dd";
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get<OrderActualPriceTrendItem[]>("sc/stat/b2c/order/actual/price/trend", {
            queryParameters: {
                beginDate: beginDate?.format(dayFormat),
                endDate: endDate?.format(dayFormat),
                ...others,
            },
            clearUndefined: true,
        }).then((data) => data.map((item) => JsonMapper.deserialize(OrderActualPriceTrendItem, item)));
    }

    /**
     * 查询订单商品销售额top
     * @param params
     */
    static async queryOrderProductSaleTop(params: OrderCloudReq): Promise<OrderProductSaleTopItem[]> {
        const dayFormat = "yyyy-MM-dd";
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("sc/stat/b2c/order/product/sale/top", {
            queryParameters: {
                beginDate: beginDate?.format(dayFormat),
                endDate: endDate?.format(dayFormat),
                ...others,
            },
            clearUndefined: true,
        });
    }

    /**
     * 查询订单选择
     * @param params
     */
    static queryOrderSelection(params: OrderCloudReq): Promise<OrderCloudPlatformInfo> {
        const dayFormat = "yyyy-MM-dd";
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("sc/stat/b2c/order/selection", {
            queryParameters: {
                beginDate: beginDate?.format(dayFormat),
                endDate: endDate?.format(dayFormat),
                ...others,
            },
            clearUndefined: true,
        });
    }

    /**
     * 查询订单汇总
     * @param params
     */
    static asyncOrderSummary(params: OrderCloudReq): Promise<OrderSummaryInfo> {
        const dayFormat = "yyyy-MM-dd";
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("sc/stat/b2c/order/summary", {
            queryParameters: {
                beginDate: beginDate?.format(dayFormat),
                endDate: endDate?.format(dayFormat),
                ...others,
            },
            clearUndefined: true,
        });
    }
}
