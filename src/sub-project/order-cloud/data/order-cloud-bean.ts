import { fromJsonToDate, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Range } from "../../base-ui/utils/value-holder";

export enum EcType {
    PDD = 1, // 拼多多
    ELEME = 2, //饿了么
    JD = 3, //京东
    MEITUAN = 4, //美团
}
export class PostShareReportOrderCloudData {
    dateRange?: Range<Date>;
    orderSummaryData?: OrderSummaryInfo;
    orderCloudDailyRevenueList?: OrderActualPriceTrendItem[];
}
export class EcAuthBindItem {
    bindClinicId?: string; //绑定的门店id（如果还未绑定就为空）
    bindClinicName?: string; //绑定的门店名（如果还未绑定就为空)
    clientId?: string; //电商开放平台开发者id
    ecMallId?: string; //ABC内部店铺ID
    ecType?: EcType; //电商类型，1 拼多多，2 饿了么，3 京东，4 美团
    expireTime?: string; //授权失效时间
    extMallId?: string; //外部系统电商ID
    mallDesc?: string; //店铺描述
    mallLogo?: string; //店铺logo
    mallName?: string; //店铺名
    status?: number; //授权状态，0：已取消/已失效，1：已授权，2：授权过期
    //平台名称
    get _platform(): string {
        switch (this.ecType) {
            case EcType.PDD:
                return "拼多多";
            case EcType.ELEME:
                return "饿了么";
            case EcType.JD:
                return "京东";
            case EcType.MEITUAN:
                return "美团";
            default:
                return "";
        }
    }
}
export class EcAuthBindListRsp {
    keyword?: string;
    limit?: number;
    offset?: number;
    @JsonProperty({ clazz: EcAuthBindItem, type: Array })
    rows?: EcAuthBindItem[];
    total?: number;
    // 拼多多电商数据
    get pddList(): EcAuthBindItem[] {
        return this.rows?.filter((t) => t.ecType == EcType.PDD) ?? [];
    }
    // 订单云出现时机(现在只显示拼多多的)
    get isShowOrderCloud(): boolean {
        return !!this.pddList?.length;
    }
    // 对电商平台去重
    get uniqueEcMallList(): EcAuthBindItem[] {
        const map = new Map();
        this.pddList?.forEach((item) => {
            map.set(item.ecMallId, item);
        });
        return Array.from(map.values());
    }
    // 对平台去重
    get uniqueEcTypeList(): EcAuthBindItem[] {
        const map = new Map();
        this.pddList?.forEach((item) => {
            map.set(item.ecType, item);
        });
        return Array.from(map.values());
    }
}

export class OrderCloudReq {
    beginDate?: Date;
    endDate?: Date;
    clinicId?: string; //子店id
    ecTypeId?: number; //电商类型id
    mallId?: string; //网店id
    groupBy?: string; //日期分组：day ，mouth
}
export class OrderProductSaleTopItem {
    amount?: number;
    productId?: string;
    productName?: string;
}
export class OrderSummaryInfo {
    orderNumber?: number;
    averagePerDayOrderNumber?: number;
    orderAmount?: number;
    averagePerDayOrderAmount?: number;
    refundOrderNumber?: number;
    averagePerDayRefundOrderNumber?: number;
    refundOrderAmount?: number;
    averagePerDayRefundOrderAmount?: number;
}
export class OrderActualPriceTrendItem {
    @JsonProperty({ fromJson: fromJsonToDate })
    date!: Date;
    amount!: number;

    dateTime(): Date {
        return this.date;
    }
}
class MallItem {
    id?: number;
    mallName?: string;
    mallDesc?: string;
}
class EcTypeInfoItem {
    [key: number]: string;
}
export class OrderCloudPlatformInfo {
    @JsonProperty({ clazz: EcTypeInfoItem, type: Array })
    ecTypeList?: EcTypeInfoItem[];
    @JsonProperty({ clazz: MallItem, type: Array })
    mallList?: MallItem[];
}
