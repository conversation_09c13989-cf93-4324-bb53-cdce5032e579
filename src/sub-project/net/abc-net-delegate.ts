/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/7/11
 *
 * @description
 *
 */
import { ABCApiNetworkDelegate } from "./api";
import { environment, ServerEnvType } from "../base-business/config/environment";
import { EventBase, eventCenter } from "../base-business/event-center/event-center";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";
import { Completer } from "../common-base-module/async/completer";
import { ABCApiNetwork } from "./index";
import { LogUtils } from "../common-base-module/log";
import _ from "lodash";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { userCenter } from "../user-center";
import { AbcPlatformMix } from "../base-business/native-modules/abc-platform-mix";
import { ABCDeviceLoginError } from "../common-base-module/common-error";
import { TimeUtils } from "../common-base-module/utils";
import SignService from "./sign-service";
import { AppInfo } from "../base-business/config/app-info";
import { DeviceUtils } from "../base-ui/utils/device-utils";

const kPreferencesApiGrayFlag = "api_grayflag"; //灰度标志
const kPreferencesGetTokenDate = "api_token_get_date"; //token刷新日期

export class DeviceLoginSuccess extends EventBase {}

class AbcNetDelegate implements ABCApiNetworkDelegate {
    async extraHeaders(url: string): Promise<Headers | undefined> {
        const headers = new Headers();
        const appToken = userCenter.appToken();
        //abc-bis-goods-services这个服务是bis分区，不同于region分区，需要特殊处理,其token名称是cis-weapp-token
        headers.append(url.includes(environment.mallHostname) ? "cis-weapp-token" : "x-app-token", appToken ?? "");
        headers.append("APP_UA", environment.appUA() || "");
        headers.append("abc-client-info", AppInfo.pluginInfo?.repoTag || "inside");
        headers.append("Content-Type", "application/json");
        headers.append("Content-Type", "application/json");

        if (url.includes("plugin/latestPluginList")) {
            const zone = userCenter.getZone();
            headers.append("zone", zone ?? "0");
        }

        ///强制网关走x-app-token校验，避免token更新、cookie未更新问题
        headers.append("Cookie", "");
        const grayFlag = this.grayFlag;
        if (grayFlag) {
            headers.append("grayflag", grayFlag);
        }

        //加签
        const { sign, ts } = SignService.generateSignature(url, appToken);
        headers.append("X-Abc-Sign", sign);
        headers.append("X-Abc-Ts", ts);

        return headers;
    }

    private _prefixUrl?: string;

    private _currentServerEnvType?: ServerEnvType;

    grayFlag?: string; //灰度标志
    private isProcessInvalidateToken = false;

    private isProcessInvalidateError = false;

    tokenRefreshDate?: Date; //token刷新日期

    constructor() {
        this.grayFlag = sharedPreferences.getString(kPreferencesApiGrayFlag);
        const _tokenDate = sharedPreferences.getString(kPreferencesGetTokenDate);
        if (!!_tokenDate) {
            this.tokenRefreshDate = new Date(_tokenDate);
        }
    }

    clearDeviceLoginStatusForClinicSwitch() {
        this.deviceLoginCompleter = undefined;
        this.deviceLoginSuccess = false;
    }

    //////////////////////////////////////////////////////////////////////
    //ABCApiNetworkDelegate override
    //////////////////////////////////////////////////////////////////////
    prefixHost(): string | undefined {
        if (this._currentServerEnvType !== environment.serverEnvType) {
            this._currentServerEnvType = environment.serverEnvType;
            this._prefixUrl = undefined;
        }

        if (!this._prefixUrl) this._prefixUrl = `${environment.serverHostScheme}://${environment.serverHostName}/api/v2/mobile`;
        return this._prefixUrl;
    }

    async onRsp(rawRsp: Response, json: any) {
        const error = json?.error;
        //统一处理token失效问题

        if (error && (error.code === 401 || error.code === 402) && !userCenter.isLogouting && !this.isProcessInvalidateToken) {
            this.isProcessInvalidateToken = true;

            !!error.message && (await showConfirmDialog(`${error.message}`, ""));

            await userCenter.logout(true).then().catchIgnore();
            this.isProcessInvalidateToken = false;
        } else if (error && (error.code === 900001 || error.code === 900002)) {
            if (!this.isProcessInvalidateError) {
                this.isProcessInvalidateError = true;
                const { ShutdownAnnouncementPage } = require("../views/global-notification-pages/shutdown-announcement-page");
                // 网关错误，此时跳转维护页面
                const data = JSON.parse(error.detail);
                ShutdownAnnouncementPage.show({ url: data.configUrl });
            }
        } else {
            this.isProcessInvalidateError = false;
        }
    }

    async onWillRequest(method: string, path: string): Promise<boolean> {
        //原始请求与新登录方式，需主动更新host为新登录方式
        if (environment.isNormalEnv && path.includes("global-auth")) {
            await environment.setReginApiObj(environment._serverGlobalHostname);
        }

        if (!!this.tokenRefreshDate) {
            const difference = TimeUtils.difference(new Date(this.tokenRefreshDate), new Date());
            if (!!difference.inDays) {
                const _tokenRefreshDate = this.tokenRefreshDate;
                this.tokenRefreshDate = new Date();
                try {
                    await userCenter.refreshToken();
                } catch (e) {
                    this.tokenRefreshDate = _tokenRefreshDate;
                }
            }
        }

        if (path == this.deviceLoginPath || this.deviceLoginSuccess || environment.isGlobalEnv || path.includes("login-by-code"))
            return true;

        return this.deviceLogin()
            .then((rsp) => {
                if (!rsp) throw "设备注册失败";
                return true;
            })
            .catch((error) => {
                throw new ABCDeviceLoginError(error, method, path);
            });
    }

    deviceLoginPath = "deviceLogin";
    deviceLoginSuccess = false; //设备登录碾功
    deviceLoginCompleter?: Completer<boolean>;

    deviceLogin(ignoreDeviceLoginSuccess = false): Promise<boolean> {
        if (environment.isGlobalEnv) return new Promise((resolve) => resolve(true));
        //如果已经发送了一个请求，直接返回未完成的
        if (this.deviceLoginCompleter) return this.deviceLoginCompleter.promise;
        this.deviceLoginCompleter = new Completer();

        const completer = this.deviceLoginCompleter;
        const self = this;

        async function doDeviceLogin(): Promise<boolean> {
            const rsp = await ABCApiNetwork.post<Response>(self.deviceLoginPath, {
                useRsp: true,
                body: {},
            });
            if (rsp.status == 200) {
                await self.parseGrayFlag(rsp.headers, true).catchIgnore();
                LogUtils.d("deviceLogin");
                self.deviceLoginSuccess = true;

                !ignoreDeviceLoginSuccess && eventCenter.post(new DeviceLoginSuccess());

                return true;
            }
            return false;
        }

        doDeviceLogin()
            .then((ret) => {
                completer?.resolve(ret);
                this.deviceLoginCompleter = undefined;
            })
            .catch((error) => {
                completer.reject(error);
                this.deviceLoginCompleter = undefined;
            });

        return this.deviceLoginCompleter.promise;
    }

    //解析灰度标记
    async parseGrayFlag(headers: { [key: string]: any }, immediately: boolean): Promise<void> {
        if (!headers) return;
        const grayflag = "grayflag";
        let grayFlagValue = headers[grayflag];
        if (_.isArray(grayFlagValue)) {
            grayFlagValue = _.first(grayFlagValue);
        }
        if (DeviceUtils.isOhos() && (this._currentServerEnvType == ServerEnvType.dev || this._currentServerEnvType == ServerEnvType.test)) {
            await AbcPlatformMix.setGrayFlag("").catchIgnore();
            if (immediately) this.grayFlag = grayFlagValue;
        }
        if (grayFlagValue != undefined) {
            await sharedPreferences.setString(kPreferencesApiGrayFlag, grayFlagValue ?? "");
            await AbcPlatformMix.setGrayFlag(grayFlagValue).catchIgnore();
            if (immediately) this.grayFlag = grayFlagValue;
        }
    }

    init() {
        this._prefixUrl = undefined;
    }
}

const abcNetDelegate = new AbcNetDelegate();

export { abcNetDelegate };
