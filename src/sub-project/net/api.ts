/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-19
 *
 * @description 封装与后台api http请求
 */
import { LogUtils } from "../common-base-module/log";
import _ from "lodash";
import { JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { errorToStr } from "../common-base-module/utils";
import { AnyType } from "../common-base-module/common-types";
import { environment } from "../base-business/config/environment";

export interface ABCApiNetworkDelegate {
    extraHeaders(url: string): Promise<Headers | undefined>;

    prefixHost(): string | undefined;

    onRsp(rawRsp: Response, json: any): void;

    /**
     * 请求前回调，如果返回false,请求中断
     * @param method
     * @param path
     */
    onWillRequest(method: string, path: string): Promise<boolean>;
}

class ABCApiError extends Error {
    code: number;
    msg: string;
    detail?: AnyType;
    url?: string;
    method?: string;

    constructor(code: number, msg: string, detail?: AnyType, url?: string, method?: string) {
        super(`code:${code}, msg = ${msg}, url = ${url}, method =${url}`);
        this.code = code;
        this.msg = msg;
        this.detail = detail;
        this.url = url;
        this.method = method;
    }
}

class ABCNetworkError extends Error {
    summaryMessage?: string;
    detailError: any;
    url?: string;
    method?: string;

    constructor(summaryMessage?: string, detailError?: AnyType, url?: string, method?: string) {
        super(`${summaryMessage}:detailError = ${detailError}, url=${url},method=${method}`);
        this.summaryMessage = summaryMessage;
        this.detailError = detailError;
        this.url = url;
        this.method = method;
    }
}

class ABCNetworkTimeoutError extends Error {
    detailError: any;
    url?: string;
    method?: string;

    constructor(detailError?: AnyType, url?: string, method?: string) {
        super(`detailError=${detailError}, url = ${url}, method=${method}`);
        this.detailError = detailError;

        this.url = url;
        this.method = method;
    }
}

interface ReqOptions<T> {
    //请求相关，audioUrl 参数
    queryParameters?: any; //{key:}
    //在post, put,之类请求的body
    body?: any;

    ///处理响应相关
    useBody?: boolean;
    useRsp?: boolean;
    clazz?: { new (): T };

    silent?: boolean; // 是否打印日志， false
    clearUndefined?: boolean; //是否删除queryParameters中undefined数据
}

class ABCApiNetwork {
    delegate?: ABCApiNetworkDelegate;

    /**
     * Get 请求
     * @param path
     * @param options 参数
     */
    get<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("GET", path, options);
    }

    /**
     * post 请求
     * @param path
     * @param options 参数
     */
    post<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("POST", path, options);
    }

    /**
     * put 请求
     * @param path
     * @param options 参数
     */
    put<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("PUT", path, options);
    }

    /**
     * put 请求
     * @param path
     * @param options 参数
     */
    delete<T>(path: string, options?: ReqOptions<T>): Promise<T> {
        return this._doRequest("DELETE", path, options);
    }

    /**
     * 发送网络请求 请求
     * @param method 请求类型
     * @param path 请求路径
     * @param options 参数
     */
    async _doRequest<T>(method: "GET" | "POST" | "PUT" | "DELETE", path: string, options?: ReqOptions<T>): Promise<T> {
        let queryParameters = "";
        await this.delegate?.onWillRequest(method, path);
        const silent = options?.silent ?? false;
        if (options && options.queryParameters) {
            const keys = Object.getOwnPropertyNames(options.queryParameters);
            keys.forEach((key) => {
                const value = options.queryParameters[key];
                if (options.clearUndefined && _.isNil(value)) {
                    return;
                }
                if (!_.isEmpty(queryParameters)) {
                    queryParameters += "&";
                }
                if (_.isArray(value)) {
                    queryParameters += value
                        .filter((item) => !options.clearUndefined || !_.isNil(item))
                        .map((item) => {
                            return `${key}=${encodeURIComponent(item)}`;
                        })
                        .join("&");
                } else {
                    if (!options.clearUndefined || !_.isNil(value)) {
                        queryParameters += `${key}=${encodeURIComponent(value)}`;
                    }
                }
            });
        }

        if (!_.isEmpty(queryParameters)) {
            queryParameters += "&";
        }

        queryParameters = queryParameters + "t=" + new Date().getTime();
        // 如果path路径中含有"://"，则认为是完整的url，目前商城bis服务分区不同于region分区，接口是全路径传入，不走mobile
        let fullPath: string;
        if (path.includes("://")) {
            if (method == "GET") {
                fullPath = `${path}?${queryParameters}`;
            } else {
                fullPath = path;
            }
        } else {
            fullPath = `${this.urlPrefix}/${path}?${queryParameters}`;
        }
        const headers = await this._getHeaders(fullPath);

        //global分区接口全部走api服务，和pc一致
        if (environment.isGlobalEnv) {
            fullPath = fullPath.replace("/v2/mobile", "");
        }

        //这个配置接口无法走global分区进行拉取，只能强制走环境分区
        if (path == "appproperty/propertyList") {
            fullPath = `${environment.serverHostScheme}://${environment._serverNormalHostname}/api/v2/mobile/${path}?${queryParameters}`;
        }

        if (!silent) {
            LogUtils.d(`ABCApiNetwork._doRequest req fullPath = ${fullPath}, method = ${method}, headers = ${JSON.stringify(headers)}`);
            LogUtils.d(`ABCApiNetwork._doRequest req fullPath = ${fullPath}, body = ${JSON.stringify(options?.body)}`);
        }

        return fetch(fullPath, {
            body: options?.body ? JSON.stringify(options?.body) : undefined,
            method: method,
            headers: headers,
        })
            .then(async (rsp) => {
                try {
                    const json = await rsp.json();
                    return {
                        rsp: rsp,
                        json: json,
                        jsonParserError: undefined,
                    };
                } catch (error) {
                    return {
                        rsp: rsp,
                        json: null,
                        jsonParserError: error,
                    };
                }
            })
            .then(({ rsp, json, jsonParserError }) => {
                this.delegate?.onRsp(rsp, json);

                if (!silent)
                    LogUtils.d(
                        `ABCApiNetwork._doRequest rsp fullPath = ${fullPath}, rsp.ok = ${rsp.ok}, rsp.status = ${
                            rsp.status
                        }, rsp = ${JSON.stringify(json)}`
                    );

                if (!rsp.ok) {
                    const message = json?.error?.message;
                    throw new ABCApiError(rsp.status, message || rsp.statusText, { ...json }, fullPath, method);
                }

                if (options && options.useRsp) {
                    return rsp;
                }

                if (jsonParserError) throw jsonParserError;

                if (options && options.useBody) {
                    if (_.isNil(json)) return undefined;

                    if (options.clazz) {
                        return JsonMapper.deserialize(options.clazz, json);
                    }
                    return json;
                }

                const data = json["data"];
                if (_.isNil(data)) return undefined;
                if (options != null && options.clazz) {
                    return JsonMapper.deserialize(options.clazz, data);
                } else return data;
            })
            .catch((e) => {
                if (e instanceof ABCApiError) throw e;
                if (e.code === "-1009" && e.userInfo && e.userInfo.NSLocalizedDescription) {
                    throw new ABCNetworkError(e.userInfo.NSLocalizedDescription as string, e, fullPath, method);
                }

                if (e.code === "-1001" && e.userInfo && e.userInfo.NSLocalizedDescription) {
                    throw new ABCNetworkTimeoutError(e, fullPath, method);
                }

                if (e === "timeout") throw new ABCNetworkTimeoutError(e, fullPath, method);

                if (!silent) LogUtils.e(`ABCApiNetwork._doRequest rsp fullPath = ${fullPath}, error = ${errorToStr(e)}`);
                throw new ABCNetworkError(undefined, e, fullPath, method);
            });
    }

    private _getHeaders(url: string): Promise<Headers | undefined> {
        if (this.delegate) return this.delegate?.extraHeaders(url);

        return Promise.resolve(undefined);
    }

    private get urlPrefix() {
        return this.delegate?.prefixHost() ?? "";
    }
}

export default new ABCApiNetwork();
export { ABCApiError, ABCNetworkError, ABCNetworkTimeoutError };
