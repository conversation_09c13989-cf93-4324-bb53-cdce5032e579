/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2024/7/18
 * @Copyright 成都字节流科技有限公司© 2024
 */
import CryptoJS from "crypto-js";
import { isNumber } from "lodash";
import { LogUtils } from "../common-base-module/log";
import { UrlUtils } from "../common-base-module/utils";
export const HEADER_ABC_TS = "__abc-ts__";
export const REQUEST_PATH = "__abc-path__";
export const REQUEST_TOKEN = "__abc-secret__";

export default class SignService {
    static generateSignature(
        fullPath: string,
        token = ""
    ): {
        sign: string;
        ts: string;
    } {
        const timestamp = Date.now().toString();
        //解析url
        const uri = UrlUtils.parseUrl(fullPath);
        const { path, query } = uri!;
        //构建签名参数map
        const signParams: Map<string, string> = new Map();
        signParams.set(HEADER_ABC_TS, timestamp);
        signParams.set(REQUEST_TOKEN, token);
        signParams.set(REQUEST_PATH, path as string);
        // 遍历查询参数
        if (query && !!Object.keys(query).length) {
            for (const key of Object.keys(query)) {
                const value = query[key] as string;
                if (isNumber(value) || value) {
                    signParams.set(key, value);
                }
            }
        }
        return {
            sign: SignService.signUtils(signParams),
            ts: timestamp,
        };
    }

    static signUtils(paramMap: Map<string, string>): string {
        //对参数按照key进行字典序排序
        const sortedParamMap = new Map([...paramMap.entries()].sort());

        //拼接参数字符串
        const paramsArr = [];
        for (const [key, value] of sortedParamMap) {
            paramsArr.push(`${key}=${value}`);
        }
        const paramStr = paramsArr.join("&");
        LogUtils.d(`SignService.paramStr = ${paramStr}`);
        LogUtils.d(`SignService.sign = ${CryptoJS.MD5(paramStr).toString(CryptoJS.enc.Hex)}`);
        return CryptoJS.MD5(paramStr).toString(CryptoJS.enc.Hex);
    }
}
