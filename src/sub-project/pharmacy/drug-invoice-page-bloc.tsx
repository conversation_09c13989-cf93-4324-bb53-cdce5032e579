/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/6/12
 */
import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import {
    AuditedStatus,
    CompoundedStatus,
    DispensedByEmployee,
    DispensePreCheckDetail,
    DispensingForm,
    DispensingFormItem,
    DispensingSheetCalculateResult,
    DispensingStatus,
    DrugDispensingDisabledStatus,
    DrugOperationRecord,
    GetDrugInvoiceDetail,
    PharmacyAdditionalList,
} from "./data/pharmacy-bean";
import { DeliveryInfo, Employee, GoodsType } from "../base-business/data/beans";
import { userCenter } from "../user-center";
import { PharmacyDataAgent } from "./data/pharmacy-data-agent";
import _, { isNil } from "lodash";
import { ModuleIds } from "../user-center/user-center";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr, NumberUtils } from "../common-base-module/utils";
import { fromJsonToDate, JsonMapper } from "../common-base-module/json-mapper/json-mapper";
import { Toast } from "../base-ui/dialog/toast";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { ClinicDispensingConfig, OnlinePropertyConfigProvider, PharmacyConfig } from "../data/online-property-config-provder";
import { ClinicAgent, EmployeesMeConfig } from "../base-business/data/clinic-agent";
import { of, Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../common-base-module/common-error";
import { ChargeSourceFormType } from "../charge/data/charge-beans";
import { PharmacyDialog } from "./views/pharmacy-dialog";
import { createDrugRefundNewDialog } from "./drug-refund-dialog-new";
import { AbcMap } from "../base-ui/utils/abc-map";
import { StepStatusViewPage } from "./views/step-status-view-page";
import { LogUtils } from "../common-base-module/log";
import { ABCUtils } from "../base-ui/utils/utils";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { StepStatusList } from "../base-ui/abc-steps/abc-steps";
import { OutpatientAgent } from "../outpatient/data/outpatient";
import { IndeterminateCheckboxStatus } from "../base-ui/views/abc-checkbox";
import { DispensingReSendDialog } from "./views/dispensing-re-send-dialog";
import { SelectDrugDeliveryItemsDialog } from "./views/select-drug-delivery-items-dialog";
import { storeLastWithdrawalTime } from "../data/memory-operation";
import { showStateLogoConfirmDialog } from "../charge/view/state-logo-dialog-builder";
import { PharmacyBatchList, PharmacyInsufficientBatchInventoryDialog } from "./views/pharmacy-insufficient-batch-inventory-dialog";
import { AbcSet } from "../base-ui/utils/abc-set";
import { Sizes } from "../theme";
import { PatientOrderAgent } from "../base-business/data/patient-order/patient-order-agent";
import { PatientOrderLockDetail, PatientOrderLockType } from "../base-business/data/patient-order/patient-order-bean";
import { onlineMessageManager } from "../base-business/msg/online-message-manager";
import { TraceCode } from "../base-business/data/trace-code-beans";
import { ShebaoAgent } from "../base-business/mix-agent/shebao-agent";
import { ClinicShebaoConfig } from "../base-business/mix-agent/data/shebao-bean";
import { ABCApiError } from "../net";
import { BatchesInsufficientInventory } from "../base-business/drug-batches-insufficient-inventory/batches-beans";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { DrugBatchesInsufficientInventoryDialog } from "../base-business/drug-batches-insufficient-inventory/drug-batches-insufficient-inventory-dialog";
import { InventoryCheckAgent } from "../inventory/inventory-check/data/inventory-check-agent";

class State {
    invoiceDetail?: GetDrugInvoiceDetail;
    sourceInvoiceDetail?: GetDrugInvoiceDetail;
    otherDispensingSheets?: GetDrugInvoiceDetail[];
    loading?: boolean = false;
    loadError?: any;

    clientEmployees?: Array<Employee>;

    preDispenseDetail?: DispensePreCheckDetail;

    //药房配置
    pharmacyConfig?: PharmacyConfig;

    //多药房配置
    pharmacyInfoConfig?: InventoryClinicConfig;

    employeesMeConfig?: EmployeesMeConfig;

    diagnoseCount?: number; //就诊历史
    drugOperationRecords?: DrugOperationRecord[];

    operationRecordsSelected?: boolean = false; // 是否选中操作记录

    drugDoctorList?: Employee[]; //发药医生列表
    selectedDrugDoctor?: DispensedByEmployee[]; //选中的发药医生
    dispensingConfig?: ClinicDispensingConfig;

    isEnableCA?: boolean; // 门店是否是否启用CA电子签名
    prescriptionUseCa?: boolean; //电子签名状态  1 启用 0 关闭
    doctorSignatureStatus?: number; // 电子签名状态 0 已绑定 1 已失效 undefined 未绑定
    isDoctorSignatureExpired?: boolean; // 医生签名已过期
    needShowAgreementPage?: boolean; // 是否需要在勾选时弹出《数字证书服务协议》页面

    sheBaoConfig?: ClinicShebaoConfig;

    // 默认选中的发药人
    get defaultDispensers(): Employee[] {
        const defaultDispensersList: Employee[] = [];
        if (this.invoiceDetail?.dispensedByEmployee?.length) {
            for (let i = 0; i < this.invoiceDetail.dispensedByEmployee.length; i++) {
                defaultDispensersList.push(
                    JsonMapper.deserialize(Employee, {
                        employeeId: this.invoiceDetail.dispensedByEmployee[i].id,
                        employeeName: this.invoiceDetail.dispensedByEmployee[i].name,
                    })
                );
            }
        }
        return defaultDispensersList ?? [];
    }

    allEmployees: Employee[] = [];
    _matchEmployees: Employee[] = [];
    selectDispenser: AbcSet<Employee> = new AbcSet<Employee>(undefined, (item) => item.employeeId ?? ""); // 已选发药人
    get matchEmployees(): Employee[] {
        return this._matchEmployees;
    }

    set matchEmployees(list: Employee[]) {
        this._matchEmployees = list.sort((item) => {
            return item.selected ? -1 : 1;
        });
    }

    lockDetails: Map<PatientOrderLockType, PatientOrderLockDetail> = new Map<PatientOrderLockType, PatientOrderLockDetail>(); // 锁单信息
    get lockDetail(): PatientOrderLockDetail | undefined {
        return this.lockDetails.get(PatientOrderLockType.chargeSheetRefund);
    }
    // 锁单中
    get orderIsLocking(): boolean {
        const lockDetail = this.lockDetails.get(PatientOrderLockType.chargeSheetRefund);
        const hasIdentity = !!lockDetail?.identity;
        return hasIdentity && !!lockDetail?.status;
    }

    // 是否开启整单发退药
    get isWholeSheetOperateEnabled(): boolean {
        return this.pharmacyConfig?.showWholeSheetOperateEnable ?? false;
    }

    // 是否开启取药时间
    get isOpenTakeMedicine(): boolean {
        return !!this.dispensingConfig?.isOpenTakeMedicationTime;
    }

    //检测某个药品是否选中
    isMedicineSelect(item: DispensingFormItem): boolean {
        return (this.invoiceDetail?.isVirtualPharmacy ? item.virtualChecked : item.checked) ?? false;
    }

    get showTotalPrice(): boolean {
        return (this.pharmacyConfig?.showPriceType ?? 0) > 0;
    }

    get showSinglePrice(): boolean {
        return (this.pharmacyConfig?.showPriceType ?? 0) > 1;
    }

    get showPrescriptionReview(): boolean {
        return !!this.pharmacyConfig?.showPrescriptionReview;
    }

    // 叶开泰 不可发药 (审核、调配、加工、快递按钮不可点击 发药按钮disable)
    get disableDispensing(): boolean {
        return this.invoiceDetail?.pharmacyDispenseFlag == DrugDispensingDisabledStatus.DISPENSE_BY_OPENAPI;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    // 在发药单已退药或者已关闭状态下，页面不可以编辑
    get canEditPage(): boolean {
        return !this.invoiceDetail?.isUnDispensed && !this.invoiceDetail?.isCanceled && !this.disableDispensing;
    }

    // 判断中药退药的类型---是否是按剂退药(部分发药退药时某味药只能全退)
    get isDoseRefundType(): boolean {
        return (
            (this.invoiceDetail?.chineseMedicineUndispenseType == 0 || this.invoiceDetail?.chineseMedicineUndispenseType == 2) &&
            !this.isPartDispensing
        );
    }

    // 判断当前是否是部分发药
    get isPartDispensing(): boolean {
        return this.invoiceDetail?.isPartDispensed == 1 ?? false;
    }

    //中药处方中当前选中的项目
    get selectedChinesePrescriptionItems(): AbcMap<DispensingForm, DispensingFormItem[]> {
        const map = new AbcMap<DispensingForm, DispensingFormItem[]>();
        const invoiceDetail1 = this.invoiceDetail;
        invoiceDetail1?.dispensingForms
            ?.filter((t) => t.sourceFormType == ChargeSourceFormType.chinesePrescription)
            ?.forEach((form) => {
                const formItems: DispensingFormItem[] = [];
                form.dispensingFormItems
                    ?.filter((f) => (this.invoiceDetail?.isVirtualPharmacy ? f.isWaiting : f.canDispense))
                    ?.forEach((formItem) => {
                        if (formItem.checked__ ?? (invoiceDetail1?.isVirtualPharmacy ? formItem.virtualChecked : formItem.checked) ?? false)
                            formItems.push(formItem);
                    });
                if (formItems.length > 0) map.set(form, formItems);
            });

        return map;
    }

    // 处方全选状态
    selectedChinesePrescriptionItemsStatus(form: DispensingForm): IndeterminateCheckboxStatus {
        let checkStatus = IndeterminateCheckboxStatus.none;

        //如果当前发药单是虚拟药房，则不检查库存不足
        const canDispenseLength = form.dispensingFormItems?.filter((t) =>
            this.invoiceDetail?.isVirtualPharmacy ? t.isWaiting : t.canDispense
        )?.length;

        if (
            this.selectedChinesePrescriptionItems.size &&
            this.selectedChinesePrescriptionItems.has(form) &&
            !!this.selectedChinesePrescriptionItems.get(form)?.length
        ) {
            checkStatus =
                this.selectedChinesePrescriptionItems.get(form)?.length == canDispenseLength
                    ? IndeterminateCheckboxStatus.all
                    : IndeterminateCheckboxStatus.some;
        }
        return checkStatus;
    }

    //除了中药处方和套餐中当前选中的项目
    get selectedWesternPrescriptionItems(): AbcMap<DispensingForm | DispensingFormItem, DispensingFormItem[]> {
        const map = new AbcMap<DispensingForm | DispensingFormItem, DispensingFormItem[]>();
        const invoiceDetail1 = this.invoiceDetail;
        invoiceDetail1?.dispensingForms
            ?.filter((t) => t.sourceFormType != ChargeSourceFormType.chinesePrescription)
            ?.forEach((form) => {
                const formItems: DispensingFormItem[] = [];

                form.dispensingFormItems?.forEach((formItem) => {
                    if (form.sourceFormType == ChargeSourceFormType.package) {
                        const composeItems: DispensingFormItem[] = [];
                        formItem?.composeChildren?.forEach((composeItem) => {
                            if (composeItem.checked__ ?? composeItem.checked ?? composeItem.usageInfo?.checked ?? false) {
                                composeItems.push(composeItem);
                            }
                        });
                        if (composeItems.length > 0) {
                            map.set(formItem, composeItems);
                        }
                    } else {
                        if (formItem.checked__ ?? formItem.checked ?? formItem.usageInfo?.checked ?? false) formItems.push(formItem);
                    }
                });
                if (formItems.length > 0) map.set(form, formItems);
            });

        return map;
    }

    /**
     * @description 发药员查看患者就诊历史
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.pharmacy?.isCanSeePatientHistory;
    }
    /**
     * @description 药房-查看患者手机号
     * @return true 能;
     * @return false 不能;
     */
    get canSeePatientPhone(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.pharmacy?.isCanSeePatientMobile;
    }

    //无需受是否开启处方审核、调配开关的控制
    forceOpenAuditAndCompound(pharmacyNo?: number): boolean {
        return !!this.pharmacyInfoConfig?.pharmacyList?.find(
            (t) => t?.no == pharmacyNo && (!t?.externalPharmacyConfig || t?.externalPharmacyConfig?.forceOpenAuditAndCompound == 1)
        );
    }

    copyTo(state: State): void {
        Object.assign(state, this);
    }

    /**
     * 检查当前发药单中是否存在停售或库存不足的药品
     * @returns 如果存在停售或库存不足的药品，返回 true；否则返回 false
     */
    hasUnavailableDrugs(): boolean {
        const invoiceDetail = this.invoiceDetail;
        if (!invoiceDetail || !invoiceDetail.dispensingForms || invoiceDetail.dispensingForms.length === 0) {
            return false;
        }
        const isVirtualPharmacy = invoiceDetail.isVirtualPharmacy ?? false;
        // 遍历所有处方
        for (const form of invoiceDetail.dispensingForms) {
            if (!form.dispensingFormItems || form.dispensingFormItems.length === 0) {
                continue;
            }

            // 检查每个处方中的药品
            for (const item of form.dispensingFormItems) {
                // 自备药品不需要校验库存不足
                if (item.isSelfProvided) {
                    continue;
                }

                // 如果是套餐，检查套餐中的每个子项
                if (form.sourceFormType === ChargeSourceFormType.package && item.composeChildren) {
                    for (const subItem of item.composeChildren) {
                        // 自备药品不需要校验库存不足
                        if (subItem.isSelfProvided) {
                            continue;
                        }

                        // 虚拟药房只检查是否待发药，实体药房检查是否可发药（待发药或部分发药，且库存充足）
                        if (isVirtualPharmacy) {
                            if (!subItem.isWaiting) {
                                return true;
                            }
                        } else {
                            if (!subItem.canDispense) {
                                return true;
                            }
                        }
                    }
                } else {
                    // 虚拟药房只检查是否待发药，实体药房检查是否可发药（待发药或部分发药，且库存充足）
                    if (isVirtualPharmacy) {
                        if (!item.isWaiting) {
                            return true;
                        }
                    } else {
                        if (!item.canDispense) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }
}

export class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        state.copyTo(newState);
        return newState;
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventUpdateDeliveryInfo extends _Event {
    info: DeliveryInfo;

    constructor(info: DeliveryInfo) {
        super();
        this.info = info;
    }
}

class _EventReloadData extends _Event {}

class _EventStatusUpdate extends _Event {}

class _EventRefundMedicine extends _Event {}

class _EventReDispensing extends _Event {}

class _EventDispensingMedicine extends _Event {}

class _EventToggleItemSelect extends _Event {
    item: DispensingFormItem;
    form?: DispensingForm | DispensingFormItem;

    constructor(item: DispensingFormItem, form?: DispensingForm | DispensingFormItem) {
        super();
        this.item = item;
        this.form = form;
    }
}

class _EventUpdateDeliveryStatus extends _Event {
    info: DeliveryInfo;

    constructor(info: DeliveryInfo) {
        super();
        this.info = info;
    }
}

class _EventUpdateProcessStatus extends _Event {}
class _EventUpdatePrescriptionStatus extends _Event {}
class _EventUpdatePrescriptionCompoundStatus extends _Event {}

class _EventCheckDispensingListStatus extends _Event {
    callback: () => void;
    constructor(callback: () => void) {
        super();
        this.callback = callback;
    }
}

class _EventChangeMedicineCount extends _Event {
    formItem: DispensingFormItem;
    count?: number;
    constructor(formItem: DispensingFormItem, count?: number) {
        super();
        this.formItem = formItem;
        this.count = count;
    }
}

class _EventChangeDoseCount extends _Event {
    form: DispensingForm;
    count?: number;
    constructor(form: DispensingForm, count?: number) {
        super();
        this.form = form;
        this.count = count;
    }
}

class _EventChangeOperationRecordsStatus extends _Event {
    status: boolean;
    constructor(status: boolean) {
        super();
        this.status = status;
    }
}

class _EventModifySelectAllStatus extends _Event {
    form: DispensingForm | DispensingFormItem;
    status: boolean;
    sourceFormType?: ChargeSourceFormType;
    constructor(form: DispensingForm | DispensingFormItem, status: boolean, sourceFormType?: ChargeSourceFormType) {
        super();
        this.form = form;
        this.status = status;
        this.sourceFormType = sourceFormType;
    }
}
class _EventCancelAuditDispense extends _Event {}
class _EventCancelCompoundDispense extends _Event {}
class _EventCheckStockNotEnoughBatch extends _Event {
    formItem: DispensingFormItem;
    constructor(formItem: DispensingFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventSelectItemDispenser extends _Event {
    data?: Employee;
    constructor(data?: Employee) {
        super();
        this.data = data;
    }
}
class _EventConfirmSelectDispenser extends _Event {
    data?: Employee[];
    constructor(data?: Employee[]) {
        super();
        this.data = data;
    }
}
class DrugInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<DrugInvoicePageBloc | undefined>(undefined);

    private readonly invoiceId: string;
    private _loadDataTrigger: Subject<number> = new Subject<number>();

    private _getHistoryListTrigger = new Subject<number>(); // 就诊历史

    constructor(invoiceId: string) {
        super();
        this.invoiceId = invoiceId;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    static fromContext(context: DrugInvoicePageBloc): DrugInvoicePageBloc {
        return context;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventToggleItemSelect, this._mapEventToggleItemSelect);
        map.set(_EventDispensingMedicine, this._mapEventDispengingDrug);
        map.set(_EventRefundMedicine, this._mapEventRefundMedicine);
        map.set(_EventStatusUpdate, this._mapEventStatusUpdate);
        map.set(_EventReloadData, this._mapEventReloadData);
        map.set(_EventUpdateDeliveryInfo, this._mapEventUpdateDeliveryInfo);
        map.set(_EventUpdateDeliveryStatus, this._mapEventUpdateDeliveryStatus);
        map.set(_EventUpdateProcessStatus, this._mapEventUpdateProcessStatus);
        map.set(_EventUpdatePrescriptionStatus, this._mapEventUpdatePrescriptionStatus);
        map.set(_EventModifySelectAllStatus, this._mapEventModifySelectAllStatus);
        return map;
    }

    private async _initPageConfig(): Promise<void> {
        await Promise.all([
            OnlinePropertyConfigProvider.instance.getPharmacyConfig().catchIgnore(),
            ClinicAgent.getEmployeesMeConfig().catchIgnore(),
            userCenter.getInventoryChainConfig(false).catchIgnore(),
            ClinicAgent.getClinicEmployeesByModuleIds([ModuleIds.MODULE_ID_PHARMACY]).catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicDispensingConfig().catchIgnore(),
            OnlinePropertyConfigProvider.instance.getClinicBasicSetting().catchIgnore(),
            ClinicAgent.getOnlineDoctorStatus(userCenter.employee?.id ?? "").catchIgnore(),
            ClinicAgent.getIsCaSignatureRead(userCenter.employee?.id ?? "").catchIgnore(),
            OutpatientAgent.getSupervisionSccaDoctorCaInfo(userCenter.employee?.id ?? "", true).catchIgnore(),
        ]).then((rsp) => {
            const [
                pharmacyConfig,
                employeesMeConfig,
                pharmacyInfoConfig,
                drugDoctorList,
                dispensingConfig,
                clinicBasicSetting,
                onlineDoctorStatus,
                isCaSignatureRead,
                supervisionSccaDoctorCaInfo,
            ] = rsp;
            this.innerState.pharmacyConfig = pharmacyConfig;
            this.innerState.employeesMeConfig = employeesMeConfig;
            this.innerState.pharmacyInfoConfig = pharmacyInfoConfig;
            this.innerState.drugDoctorList = drugDoctorList;
            this.innerState.dispensingConfig = dispensingConfig;
            this.innerState.isEnableCA = clinicBasicSetting?.isEnableCA == 1; // 门店是否开启CA电子签名
            this.innerState.prescriptionUseCa = onlineDoctorStatus?.prescriptionUseCa == 1; // 获取医生是否勾选CA电子签名
            this.innerState.needShowAgreementPage = isCaSignatureRead?.isFirstOnlineConsultation; // 是否需要首次弹窗
            this.innerState.doctorSignatureStatus = supervisionSccaDoctorCaInfo?.body?.status; // 电子签名状态
            this.innerState.isDoctorSignatureExpired = supervisionSccaDoctorCaInfo?.body?.isExpired; // 医生签名已过期
        });
        this.innerState.sheBaoConfig = await ShebaoAgent.getClinicShebaoConfig().catchIgnore();
    }
    private _initPageTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    if (!this.innerState.invoiceDetail) {
                        this.innerState.loading = true;
                        this.innerState.loadError = null;
                        this.update();
                    }
                    return Promise.all([
                        PharmacyDataAgent.getDispensingDetail(this.invoiceId), // 获取发药详情
                        PharmacyDataAgent.getDrugOperationRecords(this.invoiceId), // 获取发药记录
                    ])
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe(async (rsp) => {
                this.innerState.loading = false;
                if (rsp instanceof ABCError) {
                    this.innerState.loadError = rsp;
                } else {
                    this.innerState.invoiceDetail = rsp[0].dispensingSheet;
                    this.innerState.sourceInvoiceDetail = rsp[0].dispensingSheet;
                    this.innerState.invoiceDetail?.dispensingForms?.map((form) => {
                        // 开启了整单发退药开关，默认全选，包括停售、库存不足的药品
                        if (this.innerState.isWholeSheetOperateEnabled) {
                            this.innerState.invoiceDetail?.dispensingForms?.forEach((form) => {
                                form.dispensingFormItems?.forEach((item) => {
                                    if (form.sourceFormType == ChargeSourceFormType.package) {
                                        item?.composeChildren?.forEach((subItem) => {
                                            subItem.checked__ = true;
                                        });
                                    } else {
                                        item.checked__ = true;
                                    }
                                });
                            });
                        }

                        if (form.__isChineseForm) {
                            if (form.isFormSupportDispenseByDose) {
                                form.doseCount = form.remainingDoseCount; // 中药默认全选
                                form.dispensingFormItems?.map((item) => {
                                    item.checked__ = _.cloneDeep(item.usageInfo?.checked);
                                });
                            }
                            form.isNeedAssignmentTime = true;
                        }
                    });
                    if (this.innerState.invoiceDetail?.patient?.id) this._getHistoryListTrigger.next(0);
                    this.innerState.otherDispensingSheets = rsp[0].otherDispensingSheets;
                    this.innerState.drugOperationRecords = rsp[1];
                    if (this.innerState.invoiceDetail?.isWaiting && _.isEmpty(this.innerState.invoiceDetail?.dispensedByEmployee)) {
                        const employee = userCenter.employee;
                        this.innerState.invoiceDetail.dispensedByName = employee?.name;
                        this.innerState.invoiceDetail.dispensedByIds = employee?.id?.split(",");
                        this.innerState.invoiceDetail?.dispensedByEmployee?.push(
                            JsonMapper.deserialize(DispensedByEmployee, {
                                id: employee?.id,
                                name: employee?.name,
                            })
                        );
                    }
                    //     获取锁单列表
                    //加锁前查询当前锁单详情
                    if (!!this.innerState.invoiceDetail?.patientOrderId) {
                        const lockDetail = await PatientOrderAgent.getPatientOrdersListLocks(this.innerState.invoiceDetail.patientOrderId, [
                            PatientOrderLockType.chargeSheet,
                        ]).catchIgnore();
                        if (!!lockDetail?.length) {
                            const chargeSheetRefundInfo = lockDetail?.find(
                                (t) => t.businessKey == PatientOrderLockType.chargeSheet && t.value?.chargeRefundOrder && t.status
                            );
                            // 药房目前只展示退费锁收费单
                            if (!!chargeSheetRefundInfo) {
                                this.innerState.lockDetails.set(PatientOrderLockType.chargeSheetRefund, chargeSheetRefundInfo);
                            }
                        }
                    }

                    this._initDispensersData();
                }
                this.update();
            })
            .addToDisposableBag(this);

        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.invoiceDetail?.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                () => {
                    this.update();
                }
            )
            .addToDisposableBag(this);

        // 锁单socket接口（药房模块只显示退费锁单）
        onlineMessageManager.patientOrderSheetLockMsgObserver.subscribe((data) => {
            if (data.key != this.innerState.invoiceDetail?.patientOrderId) return;
            if (data.businessKey != PatientOrderLockType.chargeSheet) return;
            if (data.businessKey == PatientOrderLockType.chargeSheet && data.status && !data.value?.chargeRefundOrder) return;
            const _data = !!data.status ? data : { employeeId: null, employeeName: null, identity: null };
            if (!data.businessKey) return;
            let detail = this.innerState.lockDetails.get(PatientOrderLockType.chargeSheetRefund);
            detail = Object.assign(detail ?? {}, _data);
            this.innerState.lockDetails.set(PatientOrderLockType.chargeSheetRefund ?? "", detail!);
            this.update();
        });
    }
    async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        if (!!this.invoiceId) this._loadDataTrigger.next();
    }

    // 初始发药人列表数据
    private _initDispensersData(): void {
        this.innerState.allEmployees =
            this.innerState.drugDoctorList?.map((item) => {
                return {
                    ...item,
                    selected: !!this.innerState.defaultDispensers?.find((t) => t.employeeId == item?.employeeId),
                };
            }) ?? [];
        this._collectMatchData();
        this.innerState.defaultDispensers?.forEach((employee) => {
            this.innerState.matchEmployees.forEach((item) => {
                if (employee.employeeId == item.employeeId /*&& !selectDispenser.has(event.employeeInfo)*/) {
                    this.innerState.selectDispenser.add(item);
                }
            });
        });
    }

    //过滤出符合条件的项
    _collectMatchData(searchText?: string): void {
        const keyword = (searchText ?? "").toLowerCase();
        if (keyword.length === 0) {
            this.innerState.matchEmployees = this.innerState.allEmployees;
        }

        this.innerState.matchEmployees = this.innerState.allEmployees.filter(
            (item) =>
                (item.employeeName?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.employeeNamePy?.toLowerCase().indexOf(keyword) ?? -1) >= 0 ||
                (item.employeeNamePyFirst?.toLowerCase().indexOf(keyword) ?? -1) >= 0
        );
    }

    async *_mapEventUpdate(/*ignore: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState;
    }

    update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    async *_mapEventToggleItemSelect(event: _EventToggleItemSelect): AsyncGenerator<State> {
        const { item, form } = event;
        const { invoiceDetail } = this.innerState;

        if (item.isDispensed) {
            await Toast.show("已发", { warning: true });
            return;
        }
        if (item.isSelfProvided) return; // 如果为自备药，则不检查库存

        //如果为虚拟药房，则不检查库存不足
        if (invoiceDetail?.isVirtualPharmacy) {
            if (!item.isWaiting) return;
        } else {
            if (!item.canDispense) return;

            if (!item.stockInfo().stockEnough) {
                await Toast.show("库存不足", { warning: true });
                return;
            }
        }
        const itemChecked = this.innerState.invoiceDetail?.isVirtualPharmacy ? !event.item.virtualChecked : !item.checked;

        if (!!form && form instanceof DispensingForm && form?.__isChineseForm) {
            if (form.isFormSupportDispenseByDose) {
                if (!form.dispensingFormItems?.find((item) => item.isSectional || item.isDispensed)) {
                    item.checked__ = itemChecked;
                } else {
                    form.dispensingFormItems
                        ?.filter((f) => f.canDispense && !f.isSelfProvided)
                        ?.forEach((item) => {
                            item.checked__ = itemChecked;
                        });
                }
            } else {
                item.checked__ = itemChecked;
            }

            const { selectedChinesePrescriptionItems } = this.innerState;

            if (
                selectedChinesePrescriptionItems.get(form)?.length !=
                form.dispensingFormItems?.filter((f) => f.canDispense && !f.isSelfProvided).length
            ) {
                // 非全选
                form.usageInfo!.doseCount__ = undefined;
                form.doseCount = undefined;
                form.dispensingFormItems?.forEach((item) => {
                    item.doseCount = item.remainingDoseCount;
                });
            } else {
                // 全选
                form.doseCount = form?.remainingDoseCount;
                form.dispensingFormItems?.forEach((item) => {
                    item.doseCount = form?.remainingDoseCount;
                });
            }
        } else {
            item.checked__ = itemChecked;
            // 对于套餐而言，此时的item是composeChildren中的项，不是真正的item，所以在勾选的时候，需要适配对应的item,防止在发药时套餐没选中还是传过去了
            if (form instanceof DispensingFormItem && form.productType == GoodsType.package) {
                form.checked__ = item.checked__;
            }
        }

        this.update();
    }

    handleDispenseForm(invoiceDetail: GetDrugInvoiceDetail): GetDrugInvoiceDetail {
        invoiceDetail?.dispensingForms?.forEach((form) => {
            if (form.__isChineseForm) {
                if (
                    form.isFormSupportDispenseByDose &&
                    this.innerState.selectedChinesePrescriptionItemsStatus(form) == IndeterminateCheckboxStatus.all
                ) {
                    // 按剂发药全选
                } else {
                    // 按剂发药单选
                    form.doseCount = undefined; // 如果按药品发药(非全选&&处方未发药) 需要将剂数置为undefined
                }
                // 中药处方是根据doseCount+unitCount计算，修改剂数时，无法确认对应哪个批次的数量，所以批次信息不传
                form.dispensingFormItems = form.dispensingFormItems?.map((formItem) => {
                    formItem.dispensingFormItemBatches = undefined;
                    formItem.goodsLockBatchItemList = undefined;
                    return formItem;
                });
            }
        });

        invoiceDetail?.dispensingForms?.forEach((forms) => {
            forms.dispensingFormItems?.forEach((item) => {
                _.remove(item.composeChildren ?? [], (item) => (invoiceDetail.isVirtualPharmacy ? !item.virtualChecked : !item.checked));
            });

            _.remove(
                forms.dispensingFormItems ?? [],
                (item) => (invoiceDetail.isVirtualPharmacy ? !item.virtualChecked : !item.checked) || item.isDispensed
            );
        });
        _.remove(invoiceDetail?.dispensingForms ?? [], (item) => !item.dispensingFormItems?.length);
        return invoiceDetail;
    }
    async computeDispenseCount(invoiceDetail: GetDrugInvoiceDetail): Promise<Set<String> | void> {
        if (_.isEmpty(invoiceDetail.dispensingForms)) {
            await Toast.show("没有有效的发药项", { warning: true });
            return;
        }

        const goodsIds: Set<String> = new Set<String>();
        invoiceDetail?.dispensingForms?.forEach((form) => {
            form.dispensingFormItems?.forEach((formItem) => {
                if (form.sourceFormType == ChargeSourceFormType.package) {
                    formItem.composeChildren?.forEach((composeItem) => {
                        if (!!composeItem.unitCount) goodsIds.add(composeItem.productInfo!.id!);
                    });
                } else {
                    if (!!formItem.unitCount) goodsIds.add(formItem.productInfo!.id!);
                }
            });
        });
        return goodsIds;
    }

    // getTraceCodeShouldCollectCountInfo(trData: DispensingFormItem) {
    //     const { unit, productInfo } = trData;
    //     const { packageUnit, pieceNum } = productInfo ?? {};
    //     const dispensingCount = this.getUnitCount(trData);
    //     const safePieceNum = pieceNum ?? 1;
    //     const isBigUnit = packageUnit === unit;
    //
    //     if (isBigUnit) {
    //         // 大单位情况：dispensingCount 就是大单位数量
    //         const bigShouldCollectCount = Math.round(dispensingCount);
    //         const smallShouldCollectCount = Math.round(dispensingCount * safePieceNum - bigShouldCollectCount * safePieceNum);
    //         return {
    //             bigShouldCollectCount,
    //             smallShouldCollectCount,
    //         };
    //     }
    //
    //     // 小单位情况：需要转换为大单位和剩余小单位
    //     const bigShouldCollectCount = Math.floor(dispensingCount / safePieceNum);
    //     const smallShouldCollectCount = dispensingCount - bigShouldCollectCount * safePieceNum;
    //     return {
    //         bigShouldCollectCount,
    //         smallShouldCollectCount,
    //     };
    // }

    // 获取实际unitCount
    getUnitCount(item: DispensingFormItem): number {
        const { productType, status, unitCount, doseCount, dispensingFormId, remainingUnitCount } = item;
        // 药房有dispensingFormId的情况下，不需要乘以item上的数量
        const isCompose = productType == GoodsType.package;
        if (status === undefined && !dispensingFormId && isCompose) {
            return (unitCount ?? 0) * (unitCount || 1);
        }
        let res = (remainingUnitCount || unitCount || 1) * (doseCount || 1);
        if (status === DispensingStatus.unDispensed) {
            res = -res;
        }
        return res;
    }

    // 发药时检查，发药药品是医保结算&&需要采集追溯码的
    // * 当实采数量与应采数量不符的时候，拦截发药alert：xxx、xxx等3个药品，追溯码实采不等于应采，请在PC端完成追溯码采集后再完成发药
    private async checkTraceCodeCollectCount(invoiceDetail: GetDrugInvoiceDetail): Promise<boolean> {
        // 是否开启追溯码采集场景(没开通的话，不做校验)
        const traceCodeCollectionCheck = userCenter.clinicTraceCodeConfig?.collectionCheck || 0;
        if (!traceCodeCollectionCheck) return true;
        // 医保结算
        const isYiBaoCollect = !!invoiceDetail?.isShebaoPay;
        // 拆零不采集模式
        const hasEnableDismountingMode = userCenter.inventoryClinicConfig?.traceCodeConfig?.shebaoDismountingCollectStrategy == 0;
        if (isYiBaoCollect) {
            // 需要查询采集追溯码商品的应采数量
            const needTraceCollectForms = invoiceDetail?.dispensingForms?.filter((item) => !item?.__isChineseForm);
            if (!needTraceCollectForms?.length) return true;
            // 打平所有需要采集追溯码的商品，套餐只取子商品
            const needTraceCollectFormsItem = needTraceCollectForms?.flatMap((form) => {
                const formItems = form.dispensingFormItems ?? [];
                // 如果是套餐（sourceFormType为11），只返回composeChildren
                if (form.sourceFormType === 11) {
                    return formItems.flatMap((item) => item.composeChildren ?? []);
                }
                // 普通商品直接返回formItems
                return formItems;
            });
            if (!needTraceCollectFormsItem?.length) return true;
            // 应采等于发药数量（如果是拆零不采集，向下取整，比如发药数量是25包，规格是10包/盒,那么应采数量为2盒；其他模式就是发药数量）

            for (const item of needTraceCollectFormsItem ?? []) {
                // 如果当前药品是无码商品无需采集、未对码药品无需采集、拆零发药无需采集，不需要校验
                if (
                    TraceCode.isNoTraceCodeGoods(item?.productInfo) ||
                    !TraceCode.supportCollect(item?.productInfo) ||
                    (!isNil(item.shebaoDismountingFlag) && TraceCode.isShebaoDismountingFlag(item.shebaoDismountingFlag))
                )
                    continue;
                //     应采数量
                let shouldCollectCount = 0;
                const dismountingModeCount = () => {
                    if (item.unit == item.productInfo?.pieceUnit) {
                        const floorChange = Math.floor((item.displayUnitCount ?? 0) / (item?.productInfo?.pieceNum ?? 1));
                        return floorChange * (item.productInfo?.pieceNum ?? 0);
                    }
                    return (item.displayUnitCount ?? 0) * (item.productInfo?.pieceNum ?? 0);
                };
                if (hasEnableDismountingMode) {
                    shouldCollectCount = dismountingModeCount();
                } else {
                    if (item.unit == item.productInfo?.packageUnit) {
                        shouldCollectCount = (item.displayUnitCount ?? 0) * (item.productInfo?.pieceNum ?? 0);
                    } else {
                        shouldCollectCount = item.displayUnitCount ?? 0;
                    }
                }
                //     如果当前录入了追溯码
                // 全部将追溯码实采的数量转换成小单位
                const actualCollectCount =
                    item.traceableCodeList?.reduce((prev, next) => {
                        const traceCodeUnitCount = TraceCode.isShouldApplyPieceUnit(next)
                            ? next.hisPieceCount
                            : (next.hisPackageCount ?? 0) * (item.productInfo?.pieceNum ?? 0);
                        return NumberUtils.preciseAdd(prev, traceCodeUnitCount ?? 0);
                    }, 0) ?? 0;
                if (shouldCollectCount != actualCollectCount) {
                    await Toast.show(`追溯码实采不等于应采，请在PC端完成追溯码采集后再完成发药`, { warning: true });
                    return false;
                }
            }

            //  下面暂时注释，app暂时没有做追溯码采集相关的，目前只做提示
            // // 转换为接口所需的参数格式
            // const traceCodeReqList = needTraceCollectFormsItem
            //     ?.filter((form) => {
            //         const productInfo = form.productInfo;
            //         const hasEnableDismountingMode =
            //             userCenter.inventoryClinicConfig?.traceCodeConfig?.shebaoDismountingCollectStrategy == 0;
            //         return (
            //             !TraceCode.isNullCodeGoods(productInfo) &&
            //             TraceCode.isSupportTraceCode(productInfo?.typeId) &&
            //             (hasEnableDismountingMode || !!form?.traceableCodeList?.length)
            //         );
            //     })
            //     .map((item) => {
            //         const productInfo = item?.productInfo;
            //         const shebao = productInfo?.shebao || {};
            //         const unitCount = this.getUnitCount(item),
            //             doseCount = 1;
            //         let pieceCount, packageCount;
            //
            //         const count = (Math.abs(unitCount) || 1) * (doseCount || 1);
            //         if (!!productInfo?.dismounting) {
            //             pieceCount = count;
            //         } else {
            //             packageCount = count;
            //         }
            //
            //         return JsonMapper.deserialize(TraceCodeListItem, {
            //             goodsId: productInfo?.goodsId,
            //             goodsSubType: productInfo?.subType,
            //             goodsType: productInfo?.type,
            //             keyId: item.keyId ?? UUIDGen.generate(),
            //             limitUnitType: shebao?.limitUnitType,
            //             medicineCadn: productInfo?.medicineCadn,
            //             medicineNmpn: productInfo?.medicineNmpn,
            //             packageCount: packageCount,
            //             packageUnit: productInfo?.packageUnit,
            //             patientOrderId: invoiceDetail?.patientOrderId,
            //             pieceCount: pieceCount,
            //             pieceNum: productInfo?.pieceNum,
            //             pieceUnit: productInfo?.pieceUnit,
            //             shebaoMedicineName: shebao?.shebaoMedicineNmpn,
            //             shebaoMedicineNmpn: shebao?.shebaoMedicineNmpn,
            //             shebaoPackageUnit: shebao?.shebaoPackageUnit,
            //             shebaoPieceNum: shebao?.shebaoPieceNum,
            //             shebaoPieceUnit: shebao?.shebaoPieceUnit,
            //             shebaoPriceLimit: shebao?.specialPriceLimit,
            //             noList: item?.traceableCodeList?.map((e) => {
            //                 // hisPackageCount与hisPieceCount只能存在一个，如果条件判断没有，默认传hisPackageCount
            //                 const isPackageUnit = item.unit == productInfo?.packageUnit;
            //                 const isPieceUnit = item.unit == productInfo?.pieceUnit;
            //
            //                 let hisPackageCount: number | undefined;
            //                 let hisPieceCount: number | undefined;
            //
            //                 if (isPackageUnit) {
            //                     hisPackageCount = e.hisPackageCount;
            //                 } else if (isPieceUnit) {
            //                     hisPieceCount = e.hisPieceCount;
            //                 } else {
            //                     // 默认传hisPackageCount
            //                     hisPackageCount = e.hisPackageCount;
            //                 }
            //                 return JsonMapper.deserialize(TraceNoListItem, {
            //                     no: e?.no,
            //                     traceCodeLockId: TraceCode.getTraceCodeLockId(SceneTypeEnum.PHARMACY, item),
            //                     hisPackageCount,
            //                     hisPieceCount,
            //                 });
            //             }),
            //         });
            //     });
            //
            // // 调用接口
            // const traceCodeReq = JsonMapper.deserialize(TraceCodeReq, {
            //     list: traceCodeReqList,
            //     patientOrderId: invoiceDetail?.patientOrderId,
            //     scene: TraceCodeScenesEnum.PHARMACY, // PHARMACY 场景
            //     shebaoHisType: this.innerState.sheBaoConfig?.basicInfo?.hospitalType ?? "1",
            //     shebaoRegion: this.innerState.sheBaoConfig?.region,
            // });
            // const result = await GoodsAgent.getCollectCodeCountList(traceCodeReq);
            // if (result && !!result?.length) {
            //     // 对比追溯码实采与应采数量
            //     for (const resultItem of result) {
            //         // 根据keyId找到对应的请求项
            //         const reqItem = traceCodeReqList?.find((req) => req.keyId === resultItem.keyId);
            //         if (!reqItem) continue;
            //
            //         // 获取应采数量（接口返回）
            //         const shouldCollectCount = resultItem.traceableCodeNum ?? 0;
            //
            //         // 获取实采数量（noList的长度）
            //         const actualCollectCount = reqItem.noList?.length ?? 0;
            //
            //         // 如果实采不等于应采，提示错误
            //         if (actualCollectCount !== shouldCollectCount) {
            //             await Toast.show(`追溯码实采不等于应采，请在PC端完成追溯码采集后再完成发药`, { warning: true });
            //             return false;
            //         }
            //     }
            // }
        }

        // 所有检查都通过，返回true
        return true;
    }

    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    async handleDrugCheckData(data: any): Promise<void> {
        const postReq = BatchesInsufficientInventory.batchInsufficientListForAutoCheck({
            goodsInfoList: data,
            type: "dispensing",
        });
        if (!postReq) return;
        const loadingDialog = new LoadingDialog();
        loadingDialog.show(100);
        try {
            const res = await InventoryCheckAgent.postGoodsStocksCheckOrders(postReq);
            if (res) {
                loadingDialog.success("提交成功").then(() => {
                    this.dispatch(new _EventDispensingMedicine());
                });
            }
        } catch (error) {
            await showConfirmDialog("提交失败", error?.detail?.error?.message);
        } finally {
            loadingDialog.hide();
        }
    }
    async *_mapEventDispengingDrug(/*ignore: _EventDispensingMedicine*/): AsyncGenerator<State> {
        // 如果开启了整单发退药开关，需要判断当前是否存在停售、库存不足的药品，如果存在则不允许发药，需要提示
        if (this.innerState.isWholeSheetOperateEnabled) {
            if (this.innerState.hasUnavailableDrugs()) {
                await showConfirmDialog("提示", "发药单内包含停售/库存不足商品，请医生调整门诊并收费后再发药", "知道了");
                return;
            }
        }
        let invoiceDetail = JsonMapper.deserialize(GetDrugInvoiceDetail, this.innerState.invoiceDetail);
        invoiceDetail = this.handleDispenseForm(invoiceDetail);
        let dispenseIds = await this.computeDispenseCount(invoiceDetail);
        if (dispenseIds && !dispenseIds?.size) {
            await Toast.show("没有有效的发药项", { warning: true });
            return;
        }

        // 发药时检查，发药药品是医保结算&&需要采集追溯码的
        // * 当实采数量与应采数量不符的时候，拦截发药alert：xxx、xxx等3个药品，追溯码实采不等于应采，请在PC端完成追溯码采集后再完成发药
        const isTraceCodeCollectCountValid = await this.checkTraceCodeCollectCount(invoiceDetail);
        if (!isTraceCodeCollectCountValid) return;
        //如果没有选择发药人，默认当前操作人，解决在没有选择发药人时，发药完成后不显示发药人的问题
        if (!this.innerState.invoiceDetail?.dispensedByIds && !_.isEmpty(this.innerState.invoiceDetail!.dispensedByEmployee)) {
            this.innerState.invoiceDetail!.dispensedByEmployee?.map((item) => {
                if (item.id) {
                    this.innerState.invoiceDetail!.dispensedByIds = [];
                    this.innerState.invoiceDetail?.dispensedByIds?.push(item.id);
                }
            });
        }
        // 如果一个药品存在多个批次，则可进行对应批次的选择(中药不支持批次发药)
        let batchCount = 0;
        !!invoiceDetail.dispensingForms
            ?.filter((item) => !item?.__isChineseForm)
            ?.forEach((form) => {
                form.dispensingFormItems?.forEach((formItem) => {
                    batchCount += formItem.dispensingFormItemBatches?.length ?? 0;
                });
            });
        if (!!batchCount) {
            const result = await SelectDrugDeliveryItemsDialog.show({ dispenseDetail: invoiceDetail });
            if (!result) return;
            invoiceDetail = this.handleDispenseForm(result);
            dispenseIds = await this.computeDispenseCount(invoiceDetail);
        }
        if (!dispenseIds) return;
        const select = await showQueryDialog("是否完成发药？", `共计：${dispenseIds.size}种`);
        if (select != DialogIndex.positive) return;

        const dialog = new LoadingDialog("正在发药");
        dialog.show();

        try {
            const rsp = await PharmacyDataAgent.dispenseMedicine({
                id: this.innerState.invoiceDetail!.id!,
                forms: invoiceDetail?.dispensingForms ?? [],
                deliverType: this.innerState.invoiceDetail!.deliveryType!,
                deliveryInfo: this.innerState.invoiceDetail!.deliveryInfo!,
                dispenserId: this.innerState.invoiceDetail!.dispensedByIds!,
                isReDispense: this.innerState.invoiceDetail?.isReDispense,
                isDecoction: this.innerState.invoiceDetail?.isDecoction,
            });
            await dialog.success("发药成功", 1000);
            await dialog.hide();

            if (rsp.status == DispensingStatus.dispensed) {
                // AbcHostBridge.sendMessage(ToHostMessageType.pharmacyDrug, {});
                ABCNavigator.pop();
                return;
            }

            this._loadDataTrigger.next();
            return rsp;
        } catch (error) {
            dialog.hide().then(async () => {
                // 需求背景：依码支付结算时确定不拆零，但发药时批次无整盒无法发药；锁定批次后发药时批次库存不足
                // 药品批次库存不足
                if (error instanceof ABCApiError && error?.detail?.error?.code == 84115) {
                    const list = BatchesInsufficientInventory.batchInsufficientList({
                        goodsInfoList: error.detail.error.detail,
                        needQueryStockGoods: invoiceDetail?.dispensingForms?.flatMap((form) => form.dispensingFormItems ?? []) ?? [],
                        type: "dispensing",
                    });
                    if (!!list?.length) {
                        const result = await showBottomPanel(<DrugBatchesInsufficientInventoryDialog list={list} />);
                        if (!result) return;
                        await this.handleDrugCheckData(error.detail.error.detail);
                    }
                } else {
                    await showConfirmDialog("", `发药失败: ${errorToStr(error)}`);
                }
            });
        } finally {
            await dialog.hide();
        }
    }

    async *_mapEventRefundMedicine(): AsyncGenerator<State> {
        //退药
        if (!this.innerState.invoiceDetail) return;
        const refundStatus = await createDrugRefundNewDialog({
            invoiceDetail: this.innerState.invoiceDetail,
            isWholeSheetOperateEnabled: this.innerState.isWholeSheetOperateEnabled,
        });
        if (refundStatus != null) {
            this._loadDataTrigger.next();
        }
    }

    async *_mapEventStatusUpdate(/*ignore: _EventStatusUpdate*/): AsyncGenerator<State> {
        this._loadDataTrigger.next();
    }

    async *_mapEventReloadData(/*ignore: _EventReloadData*/): AsyncGenerator<State> {
        this._loadDataTrigger.next();
    }

    async *_mapEventUpdateDeliveryInfo(event: _EventUpdateDeliveryInfo): AsyncGenerator<State> {
        const loadingDialog = new LoadingDialog("正在保存");
        loadingDialog.show();
        try {
            await PharmacyDataAgent.updateDeliveryInfo(
                this.innerState.invoiceDetail!.id!,
                event.info.deliveryCompany!.id!,
                event.info.deliveryOrderNo!,
                event.info.deliveryCompany!.name!,
                event.info.addressProvinceId!,
                event.info.addressProvinceName!,
                event.info.addressCityId!,
                event.info.addressCityName!,
                event.info.addressDistrictId!,
                event.info.addressDistrictName!,
                event.info.addressDetail!,
                event.info.deliveryName!,
                event.info.deliveryMobile!,
                event.info.deliveryPayType!
            );
            await loadingDialog.success("保存成功", 1000);
        } catch (error) {
            if (this.isDisposed) {
                await loadingDialog.fail(`保存失败：${errorToStr(error)}`, 1000);
            }
            this.update();
            return;
        }
        this.innerState.invoiceDetail!.deliveryInfo = event.info;

        this.update();
    }

    async *_mapEventUpdateDeliveryStatus(event: _EventUpdateDeliveryStatus): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        if (!detail) return;

        //TEST TEST TEST TEST TEST TEST TEST TEST
        // const rrr = await PharmacyDialog.show(detail.dispensingForms);
        //TEST TEST TEST TEST TEST TEST TEST TEST

        if (detail?.notNeedDelivery || detail?.finishDelivery) return;
        const dialogIndex = await PharmacyDialog.showDeliveryConfirmationDialog({
            patient: this.innerState.invoiceDetail!.patient,
            deliveryInfo: event.info,
            dispensingForms: this.innerState.otherDispensingSheets,
        });

        if (dialogIndex != DialogIndex.positive) return;

        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.updateDeliveryStatus(detail.id!);
            this._loadDataTrigger.next();
            await dialog.success("发货成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`发货失败:${errorSummary(error)}`);
            }
        }
    }

    async *_mapEventUpdateProcessStatus(/*event: _EventUpdateProcessStatus*/): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        const needProcessedIds: string[] = [];
        if (!detail || detail?.dispensingForms?.length === 0) return;
        const needProcessList = detail.dispensingForms?.filter((form) => form.processedStatus == 1);
        if (!needProcessList || needProcessList.length === 0) return;
        const currentCheckedList: { form: DispensingForm[]; onChangeOperatorId: string } | undefined =
            await PharmacyDialog.showProcessedDialog({
                list: detail.dispensingForms ?? [],
                drugDoctorList: this.innerState.drugDoctorList,
                dispensedByName: userCenter.employee?.name,
                dispensedById: userCenter.employee?.id,
                isOpenTakeMedicine: this.innerState.isOpenTakeMedicine,
            });
        if (!currentCheckedList || currentCheckedList.form.length === 0) return;
        const needProcessedList = currentCheckedList.form.filter((t) => t.processedStatus != 2);
        if (!needProcessedList || needProcessedList.length === 0) return;
        //需要过滤掉不需要加工的商品
        needProcessedList
            ?.filter((t) => t.processedStatus != 0)
            .forEach((t) => {
                needProcessedIds.push(t?.id ?? "");
            });

        //在没有需要加工的商品时，不调用接口
        if (_.isEmpty(needProcessedIds)) return;
        // 取药时间列表
        const meetDrugTimeList = currentCheckedList.form?.filter((t) => !!t.takeMedicationTime);
        const drugTimeList = meetDrugTimeList?.map((item) => {
            return JsonMapper.deserialize(PharmacyAdditionalList, {
                id: item.id,
                takeMedicationTime: fromJsonToDate(item.takeMedicationTime)?.format("yyyy-MM-dd HH:mm"),
            });
        });
        // 记忆上一次取药时间
        drugTimeList?.forEach((item) => {
            if (!!item?.takeMedicationTime) {
                storeLastWithdrawalTime(item.takeMedicationTime);
            }
        });

        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.updateProcessStatus(detail.id!, needProcessedIds, currentCheckedList.onChangeOperatorId, drugTimeList);
            this._loadDataTrigger.next();
            await dialog.success("加工成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`加工失败:${errorSummary(error)}`);
            }
        }
    }

    // 更新审核状态
    async *_mapEventUpdatePrescriptionStatus(/*event: _EventUpdatePrescriptionStatus*/): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        const { doctorSignatureStatus, isDoctorSignatureExpired, prescriptionUseCa, needShowAgreementPage, isWholeSheetOperateEnabled } =
            this.innerState;
        if (!detail || detail?.dispensingForms?.length === 0) return;
        const auditList = detail.dispensingForms?.filter((form) => form.auditedStatus == 1);
        if (!auditList || auditList.length === 0) return;
        const allPrescriptionList = detail.dispensingForms ?? [];
        let prescriptionList: DispensingForm[] = [];

        const prescriptionType = [
            ChargeSourceFormType.westernPrescription,
            ChargeSourceFormType.infusionPrescription,
            ChargeSourceFormType.chinesePrescription,
            ChargeSourceFormType.externalPrescription,
        ];
        prescriptionList = allPrescriptionList.filter((form) => prescriptionType.some((item) => item == form.sourceFormType));
        let auditPrescriptionList: { form: DispensingForm[]; onChangeOperatorId: string; modifyCaStatus?: boolean } | undefined =
            await PharmacyDialog.show({
                list: prescriptionList,
                clientEmployees: this.innerState.clientEmployees,
                drugDoctorList: this.innerState.drugDoctorList,
                dispensedByName: userCenter.employee?.name,
                dispensedById: userCenter.employee?.id,
                isEnableCA: this.innerState.isEnableCA,
                doctorSignatureStatus: doctorSignatureStatus,
                isExpired: isDoctorSignatureExpired,
                prescriptionUseCa: prescriptionUseCa,
                needShowAgreementPage: needShowAgreementPage,
                isWholeSheetOperateEnabled: isWholeSheetOperateEnabled, // 添加整单发退药开关状态
            });
        if (!auditPrescriptionList?.form || auditPrescriptionList.form.length === 0) return;
        const formIds: string[] = [];
        auditPrescriptionList = {
            form: auditPrescriptionList.form.filter((t) => t.auditedStatus == 1),
            onChangeOperatorId: auditPrescriptionList.onChangeOperatorId,
        };
        if (!auditPrescriptionList?.form || auditPrescriptionList.form.length == 0) return;
        auditPrescriptionList.form.forEach((item) => formIds.push(item?.id ?? ""));

        if (auditPrescriptionList.modifyCaStatus) {
            this.innerState.prescriptionUseCa = auditPrescriptionList.modifyCaStatus;
        }
        await ClinicAgent.updateOnlineDoctorElectronicSignature(
            userCenter.employee?.id ?? "",
            !!this.innerState.prescriptionUseCa
        ).catchIgnore();
        // 点击确定后，如果get接口返回值没有created字段(needShowAgreementPage == true)，则调用post接口通知这次弹过窗了
        if (this.innerState.needShowAgreementPage) {
            ClinicAgent.postCaSignatureRead(userCenter.employee?.id ?? "").catchIgnore();
        }
        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.updatePrescriptionStatus(detail.id!, formIds, auditPrescriptionList.onChangeOperatorId);
            this._loadDataTrigger.next();
            await dialog.success("审核成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`审核失败:${errorSummary(error)}`);
            }
        }
    }

    // 更新调配状态
    @actionEvent(_EventUpdatePrescriptionCompoundStatus)
    async *_mapEventUpdatePrescriptionCompoundStatus(/*event: _EventUpdatePrescriptionCompoundStatus*/): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        if (!detail || detail?.dispensingForms?.length === 0) return;
        const compoundList = detail.dispensingForms?.filter((form) => form.compoundedStatus == CompoundedStatus.wait);
        if (!compoundList || compoundList.length === 0) return;
        const allPrescriptionList = detail.dispensingForms ?? [];
        let prescriptionList: DispensingForm[] = [];

        const prescriptionType = [
            ChargeSourceFormType.westernPrescription,
            ChargeSourceFormType.infusionPrescription,
            ChargeSourceFormType.chinesePrescription,
            ChargeSourceFormType.externalPrescription,
        ];
        prescriptionList = allPrescriptionList.filter((form) => prescriptionType.some((item) => item == form.sourceFormType));
        let compoundPrescriptionList: { form: DispensingForm[]; dispensedByIds: string[] } | undefined =
            await PharmacyDialog.showCompoundDialog({
                list: prescriptionList,
                drugDoctorList: this.innerState.drugDoctorList,
                dispensedByEmployee: [
                    JsonMapper.deserialize(DispensedByEmployee, {
                        id: userCenter.employee?.id,
                        name: userCenter.employee?.name,
                    }),
                ],
                isWholeSheetOperateEnabled: this.innerState.isWholeSheetOperateEnabled, // 添加整单发退药开关状态
            });
        if (!compoundPrescriptionList?.form || compoundPrescriptionList.form.length === 0) return;

        const formIds: string[] = [];
        compoundPrescriptionList = {
            form: compoundPrescriptionList.form.filter((t) => t.compoundedStatus == CompoundedStatus.wait),
            dispensedByIds: compoundPrescriptionList.dispensedByIds,
        };
        if (!compoundPrescriptionList?.form || compoundPrescriptionList.form.length == 0) return;
        compoundPrescriptionList.form.forEach((item) => formIds.push(item?.id ?? ""));
        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.updatePrescriptionCompoundStatus(detail.id!, formIds, compoundPrescriptionList.dispensedByIds);
            this._loadDataTrigger.next();
            await dialog.success("调配成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`调配失败:${errorSummary(error)}`);
            }
        }
    }

    @actionEvent(_EventCheckDispensingListStatus)
    async *_mapEventCheckDispensingListStatus(event: _EventCheckDispensingListStatus): AsyncGenerator<State> {
        const { invoiceDetail } = this.innerState;
        const traceList: StepStatusList[] = [];
        invoiceDetail?.dispensingForms?.map((item, index) => {
            const _traceList: StepStatusList = {
                tabTitle: `处方${ABCUtils.toChineseNum(index + 1)}`,
                stepList:
                    item.externalTrace?.map((it) => ({
                        id: it.content ?? "",
                        title: it.content ?? "",
                        complete: !!it.time,
                        date: it.time?.format("yyyy-MM-dd  HH:mm:ss") ?? "",
                    })) ?? [],
            };
            if (!!_traceList.stepList.length) {
                traceList.push(_traceList);
            }
        });
        if (!traceList.length) return;

        const select = await StepStatusViewPage.show({
            title: "发药单状态",
            data: traceList,
        });
        LogUtils.d("select ===" + JSON.stringify(select));
        event.callback();
        this.update();
    }

    @actionEvent(_EventChangeMedicineCount)
    private async *_mapEventChangeMedicineCount(event: _EventChangeMedicineCount): AsyncGenerator<State> {
        const { formItem, count } = event;
        if (count != null) {
            formItem.unitCount = count;
        }

        this.update();
    }

    async *_mapEventModifySelectAllStatus(event: _EventModifySelectAllStatus): AsyncGenerator<State> {
        if (event.form instanceof DispensingFormItem) {
            if (event.status) {
                event.form.composeChildren?.forEach((item) => {
                    if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                        return;
                    }
                    item.checked__ = true;
                });
            } else {
                event.form.composeChildren?.forEach((item) => {
                    if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                        return;
                    }
                    if (item.isSelfProvided) return; // 反选忽略自备
                    item.checked__ = false;
                });
            }
        } else if (event.sourceFormType == ChargeSourceFormType.goods) {
            /// 商品，要去更改物资和商品两个form
            this.innerState.invoiceDetail?.dispensingForms
                ?.filter((f) => f.__isGoodsForm || f.__isMaterialForm)
                .forEach((form) => {
                    const { dispensingFormItems } = form;
                    dispensingFormItems?.forEach((item) => {
                        if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                            return;
                        }
                        if (event.status) {
                            item.checked__ = true;
                        } else {
                            if (item.isSelfProvided) return; // 反选忽略自备
                            item.checked__ = false;
                        }
                    });
                });
        } else {
            const { dispensingFormItems, isFormSupportDispenseByDose } = event.form;
            if (event.form.__isChineseForm) {
                if (isFormSupportDispenseByDose && !dispensingFormItems?.every((item) => item.status == DispensingStatus.waiting)) {
                    Toast.show("该处方已部分发剂数，只可整个处方一起发药", { warning: true }).then();
                }
                dispensingFormItems?.forEach((item) => {
                    if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                        return;
                    }
                    if (event.status) {
                        item.checked__ = true;
                    } else {
                        // 取消全选时重置药品剂数和单个药品总量
                        if (item.isSelfProvided) return; // 反选忽略自备
                        item.checked__ = false;
                    }
                    event.form.doseCount = event.form.remainingDoseCount; // 重置处方可发药剂数
                    item.doseCount = item.remainingDoseCount; // 重置单个药品总量
                });
            } else {
                if (event.status) {
                    dispensingFormItems?.forEach((item) => {
                        if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                            return;
                        }
                        item.checked__ = true;
                    });
                } else {
                    dispensingFormItems?.forEach((item) => {
                        if (this.innerState.invoiceDetail?.isVirtualPharmacy ? !item.isWaiting : !item.canDispense) {
                            return;
                        }
                        if (item.isSelfProvided) return; // 反选忽略自备
                        item.checked__ = false;
                    });
                }
            }
        }

        this.update();
    }

    @actionEvent(_EventChangeOperationRecordsStatus)
    private async *_mapChangeOperationRecordsStatus(event: _EventChangeOperationRecordsStatus): AsyncGenerator<State> {
        const { status } = event;
        if (status != null) {
            this.innerState.operationRecordsSelected = status;
        }
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    @actionEvent(_EventChangeDoseCount)
    private async *_mapEventChangeDoseCount(event: _EventChangeDoseCount): AsyncGenerator<State> {
        const { form, count } = event;
        if (_.isNaN(count)) return;
        form.doseCount = count; // 用于后台传参
        // 修改剂数后更新处方子项总数
        //判断是否需要修改子项剂数
        form.dispensingFormItems?.forEach((item) => {
            item.doseCount = count;
        });

        this.update();
    }
    @actionEvent(_EventCancelAuditDispense)
    async *_mapEventCancelAuditDispense(): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        if (!detail || detail?.dispensingForms?.length === 0) return;
        const auditedList = detail.dispensingForms?.filter((form) => form.auditedStatus == AuditedStatus.finish);
        if (!auditedList?.length) return;
        let prescriptionList: DispensingForm[] = [];

        const prescriptionType = [
            ChargeSourceFormType.westernPrescription,
            ChargeSourceFormType.infusionPrescription,
            ChargeSourceFormType.chinesePrescription,
            ChargeSourceFormType.externalPrescription,
        ];
        prescriptionList = auditedList.filter((form) => prescriptionType.some((item) => item == form.sourceFormType));

        const auditPrescriptionList: { form: DispensingForm[] } | undefined = await PharmacyDialog.show({
            list: prescriptionList,
            title: "是否撤销审核？",
            isShowOperator: false,
            matchStatus: AuditedStatus.finish,
            confirmText: "确认",
            isWholeSheetOperateEnabled: this.innerState.isWholeSheetOperateEnabled,
        });
        if (!auditPrescriptionList?.form?.length) return;
        const formIds: string[] = [];
        auditPrescriptionList.form.forEach((item) => formIds.push(item?.id ?? ""));
        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.cancelAuditDispensingList(detail.id!, {
                formIds,
            });
            this._loadDataTrigger.next();
            await dialog.success("撤销审核成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`撤销审核失败:${errorSummary(error)}`);
            }
        }
    }
    @actionEvent(_EventCancelCompoundDispense)
    async *_mapEventCancelCompoundDispense(): AsyncGenerator<State> {
        const detail = this.innerState.invoiceDetail;
        if (!detail || detail?.dispensingForms?.length === 0) return;
        const compoundList = detail.dispensingForms?.filter((form) => form.compoundedStatus == CompoundedStatus.finish);
        if (!compoundList?.length) return;
        let prescriptionList: DispensingForm[] = [];

        const prescriptionType = [
            ChargeSourceFormType.westernPrescription,
            ChargeSourceFormType.infusionPrescription,
            ChargeSourceFormType.chinesePrescription,
            ChargeSourceFormType.externalPrescription,
        ];
        prescriptionList = compoundList.filter((form) => prescriptionType.some((item) => item == form.sourceFormType));
        const compoundPrescriptionList: { form: DispensingForm[] } | undefined = await PharmacyDialog.showCompoundDialog({
            list: prescriptionList,
            title: "是否撤销调配？",
            isShowOperator: false,
            matchStatus: CompoundedStatus.finish,
            confirmText: "确认",
        });
        if (!compoundPrescriptionList?.form?.length) return;
        const formIds: string[] = [];
        compoundPrescriptionList.form.forEach((item) => formIds.push(item?.id ?? ""));
        const dialog = new LoadingDialog();
        dialog.show();
        try {
            await PharmacyDataAgent.cancelCompoundDispensingList(detail.id!, { formIds });
            this._loadDataTrigger.next();
            await dialog.success("撤销调配成功");
        } catch (error) {
            if (this.isDisposed) {
                await dialog.fail(`撤销调配失败:${errorSummary(error)}`);
            }
        }
    }

    @actionEvent(_EventCheckStockNotEnoughBatch)
    async *_mapEventCheckStockNotEnoughBatch(event: _EventCheckStockNotEnoughBatch): AsyncGenerator<State> {
        const formItem = event.formItem;
        if (!formItem || !formItem?.dispensingFormItemBatches?.length) return;
        if (!formItem?.productInfo?.isPurchasePrice && !formItem.productInfo?.medicalInsuranceLockBatch) return;
        let tips = "";
        if (formItem?.productInfo?.isPurchasePrice) {
            tips = "本商品售价按批次进价加成，需退费重开发药或盘回批次库存后发药";
        } else if (formItem.productInfo?.medicalInsuranceLockBatch) {
            tips = "批次库存不足，本商品医保价按批次限价且已医保支付，需退费重开或盘回批次库存";
        }
        const batchList: PharmacyBatchList[] = [];
        formItem?.dispensingFormItemBatches?.forEach((item) => {
            const { packageCount = 0, pieceCount = 0, cutPackageCount = 0, cutPieceCount = 0 } = item?.batchInfo || {};
            const { packageUnit = "", pieceUnit = "", pieceNum = 0, canDismounting } = formItem?.productInfo || {};
            const deductibleInventory = (!canDismounting ? cutPackageCount : cutPieceCount) ?? 0; // 可抵扣的库存量
            batchList.push(
                JsonMapper.deserialize(PharmacyBatchList, {
                    batchId: !!item?.batchId ? item.batchId.toString() : "",
                    surplusStock: (() => {
                        let stock = "";
                        if (!packageUnit) {
                            stock += packageCount * pieceNum + pieceCount + pieceUnit;
                        } else {
                            if (!!packageCount) {
                                stock += `${packageCount}${packageUnit}`;
                            }
                            if (pieceUnit && !!pieceCount) {
                                stock += `${pieceCount}${pieceUnit}`;
                            }
                            //大小单位都没有，显示0
                            if (!packageCount && !pieceCount) {
                                stock = `0${packageUnit}`;
                            }
                        }
                        return stock;
                    })(),
                    dispensingStock: `${Math.min(item.unitCount ?? 0, formItem.remainingUnitCount ?? 0)} ${formItem?.unit ?? ""}`,
                    batchNo: item.batchNo,
                    expirationDate: fromJsonToDate(item.batchInfo?.expiryDate)?.format("yyyy-MM-dd"),
                    highlight: deductibleInventory < Math.min(item.unitCount ?? 0, formItem.remainingUnitCount ?? 0),
                })
            );
        });
        await showStateLogoConfirmDialog({
            title: <PharmacyInsufficientBatchInventoryDialog tipsContent={tips} batchList={batchList} />,
            contentPadding: Sizes.paddingLTRB(Sizes.dp24),
        });
    }

    @actionEvent(_EventSelectItemDispenser)
    async *_mapEventSelectItemDispenser(event: _EventSelectItemDispenser): AsyncGenerator<State> {
        if (!event.data) return;
        const selectDispenser = this.innerState.selectDispenser;
        if (selectDispenser.has(event.data)) {
            selectDispenser.delete(event.data);
        } else {
            if (selectDispenser.values().length < 5) {
                // 判断已选人数是否超过最大数
                selectDispenser.add(event.data);
            } else {
                Toast.show("最多可选择5个发药人", { warning: true });
            }
        }
        this.update();
    }
    @actionEvent(_EventConfirmSelectDispenser)
    async *_mapEventConfirmSelectDispenser(event: _EventConfirmSelectDispenser): AsyncGenerator<State> {
        const employee = event.data;
        if (!employee?.length) return;
        /**
         * 后台未改动接收的（dispensedById,dispensedByName参数为数组类型） 这里暂时注释掉
         */
        const employeeNameList = employee?.filter((t) => !!t.employeeName)?.map((item) => item.employeeName ?? "");
        const employeeIdList = employee?.filter((t) => !!t.employeeId)?.map((item) => item.employeeId ?? "");
        this.innerState.invoiceDetail = this.innerState.invoiceDetail ?? new GetDrugInvoiceDetail();

        if (!_.isEmpty(employee)) {
            this.innerState.invoiceDetail.dispensedByIds = employeeIdList;
            this.innerState.invoiceDetail.dispensedByName = employeeNameList.join("、");
            this.innerState.invoiceDetail.dispensedByEmployee = [];
            employee?.forEach((item) => {
                this.innerState.invoiceDetail!.dispensedByEmployee!.push(
                    JsonMapper.deserialize(DispensedByEmployee, {
                        id: item.employeeId,
                        name: item.employeeName,
                    })
                );
            });
        } else if (_.isArray(employee) && _.isEmpty(employee)) {
            //在不选择发药人时，需要将之前默认的发药人清空（需要区分是不勾选发药人还是不更改发药人）
            //不更改发药人，employee返回的是undefined；不选择发药人，employee返回的是[]
            this.innerState.invoiceDetail.dispensedByIds = [];
            this.innerState.invoiceDetail.dispensedByName = "";
            this.innerState.invoiceDetail.dispensedByEmployee = [];
        }
        this.innerState.matchEmployees =
            this.innerState.drugDoctorList?.map((item) => {
                return {
                    ...item,
                    selected: !!this.innerState.defaultDispensers?.find((t) => t.employeeId == item?.employeeId),
                };
            }) ?? [];
        this.update();
    }
    //重新发药
    requestReDispensing(): void {
        this.dispatch(new _EventReDispensing());
    }

    //点击了一条药品，切换选择状态
    requestToggleItemSelect(item: DispensingFormItem, form?: DispensingForm | DispensingFormItem): void {
        // 开启了整单发退药开关，则不能编辑
        if (this.innerState.isWholeSheetOperateEnabled) {
            return;
        }
        this.dispatch(new _EventToggleItemSelect(item, form));
    }

    //发药
    requestDispensingDrug(): void {
        this.dispatch(new _EventDispensingMedicine());
    }

    //退药
    requestRefundMedicine(): void {
        this.dispatch(new _EventRefundMedicine());
    }

    @actionEvent(_EventReDispensing)
    private async *_mapEventReDispensing(): AsyncGenerator<State> {
        const invoiceDetail = this.innerState.invoiceDetail;
        if (!invoiceDetail) return;
        const dialog = new LoadingDialog("正在加载");
        dialog.show();

        // 需要进行预发药校验
        const result = await PharmacyDataAgent.preCheckReDispense(invoiceDetail.id!).catch((e) => new ABCError(e));
        if (result instanceof ABCError) {
            return await dialog.fail(`发药失败:${errorSummary(result)}`);
        }
        await dialog.hide();
        if (!result) return;
        this.innerState.preDispenseDetail = result;
        // 不需要前端再手动该发药状态了，因为后台改了，所以reDispenseWithBatchInfo为0时，直接调用重新发药接口就行
        if (
            !result.reDispenseWithBatchInfo ||
            (result.dispensingSheetCalculateResult && result.dispensingSheetCalculateResult.reDispenseByOriginBatch)
        ) {
            const queryResult = await showQueryDialog("是否需要重新发药？", "该发药单已全部退药，是否确定打开该发药单并重新发药");
            if (queryResult == DialogIndex.positive) {
                const reDispenseResult = await PharmacyDataAgent.reDispenseDetail(
                    result?.id ?? "",
                    !result.reDispenseWithBatchInfo
                        ? JsonMapper.deserialize(DispensingSheetCalculateResult, {
                              id: result?.id ?? "",
                          })
                        : result?.dispensingSheetCalculateResult
                ).catch((e) => new ABCError(e));
                if (reDispenseResult instanceof ABCError) {
                    return Toast.show(`发药失败：${errorSummary(reDispenseResult)}`);
                }
                this._loadDataTrigger.next();
            }
        } else {
            const reResult = await DispensingReSendDialog.show({ dispensingCheckDetail: result });
            if (!reResult) return;
            const reDispenseResult = await PharmacyDataAgent.reDispenseDetail(
                reResult?.id ?? "",
                reResult?.dispensingSheetCalculateResult
            ).catch((e) => new ABCError(e));
            if (reDispenseResult instanceof ABCError) {
                return Toast.show(`发药失败：${errorSummary(reDispenseResult)}`);
            }
            this._loadDataTrigger.next();
        }
    }

    //重新加载
    requestReloadData(): void {
        this.dispatch(new _EventReloadData());
    }

    requestUpdateDeliveryInfo(deliveryInfo: DeliveryInfo): void {
        this.dispatch(new _EventUpdateDeliveryInfo(deliveryInfo));
    }

    /**
     * 更新快递状态
     */
    requestUpdateDeliveryStatus(deliveryInfo: DeliveryInfo): void {
        this.dispatch(new _EventUpdateDeliveryStatus(deliveryInfo));
    }

    /**
     * 更新加工状态
     */
    requestUpdateProcessStatus(): void {
        this.dispatch(new _EventUpdateProcessStatus());
    }

    /**
     * 更新处方状态
     */
    requestUpdatePrescriptionStatus(): void {
        this.dispatch(new _EventUpdatePrescriptionStatus());
    }

    /**
     * 更改处方全选的状态
     * @param form
     * @param status
     * @param sourceFormType
     */
    modifySelectAllStatus(form: DispensingForm | DispensingFormItem, status: boolean, sourceFormType?: ChargeSourceFormType): void {
        if (this.innerState.isWholeSheetOperateEnabled) return;
        this.dispatch(new _EventModifySelectAllStatus(form, status, sourceFormType));
    }

    /**
     * 更新处方调配状态
     */
    requestUpdatePrescriptionCompoundStatus(): void {
        this.dispatch(new _EventUpdatePrescriptionCompoundStatus());
    }

    /**
     * 查看发药单状态
     */
    requestCheckDispensingListStatus(callback: () => void): void {
        this.dispatch(new _EventCheckDispensingListStatus(callback));
    }

    /**
     * 修改发药数量
     * @param formItem
     * @param count
     * @private
     */
    public requestChangeMedicineCount(formItem: DispensingFormItem, count?: number): void {
        this.dispatch(new _EventChangeMedicineCount(formItem, count));
    }

    /**
     * 修改中药发药剂数
     * @param form
     * @param count
     * @private
     */
    public requestChangeDoseCount(form: DispensingForm, count?: number): void {
        this.dispatch(new _EventChangeDoseCount(form, count));
    }

    /**
     * 修改操作记录状态
     * @private
     * @param status
     */
    public requestChangeOperationRecordsStatus(status: boolean): void {
        this.dispatch(new _EventChangeOperationRecordsStatus(status));
    }

    /**
     * 撤销审核发药单
     */
    requestCancelAuditDispense(): void {
        this.dispatch(new _EventCancelAuditDispense());
    }

    /**
     * 撤销调配发药单
     */
    requestCancelCompoundDispense(): void {
        this.dispatch(new _EventCancelCompoundDispense());
    }
    requestCheckStockNotEnoughBatch(formItem: DispensingFormItem): void {
        this.dispatch(new _EventCheckStockNotEnoughBatch(formItem));
    }
    // 勾选发药人
    requestSelectItemDispenser(data?: Employee): void {
        this.dispatch(new _EventSelectItemDispenser(data));
    }
    //  确认勾选的发药人
    requestConfirmSelectDispenser(data?: Employee[]): void {
        this.dispatch(new _EventConfirmSelectDispenser(data));
    }
}

export { DrugInvoicePageBloc, State };
