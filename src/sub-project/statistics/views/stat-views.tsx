/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/6/28
 */
import React from "react";
import { Style, Text, View } from "@hippy/react";
import { BaseComponent } from "../../base-ui/base-component";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { SizedBox, Spacer, IconFontView } from "../../base-ui";
import { DINAlternate } from "../../theme/text-styles";
import { NumberUtils } from "../../common-base-module/utils";
import { ABCUtils } from "../../base-ui/utils/utils";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import abcI18Next from "../../language/config";

interface StatItemProps {
    title: string;
    count: number | string; // 总金额(本金+赠金)
    principal?: number; // 本金
    present?: number; // 赠金
    countStyle?: Style | Style[];
    titleStyle?: Style | Style[];
    style?: Style | Style[];
    showNum?: boolean;
    dollarStyle?: Style | Style[];
    dividerLineColor?: Color;
    iconStyle?: Style | Style[];
}

const DINAlternateFontFamily = { fontFamily: DINAlternate };

export class StatItem extends BaseComponent<StatItemProps> {
    constructor(props: StatItemProps) {
        super(props);
    }
    static defaultProps = {
        showNum: true,
    };

    private formatDisplayValue(count: number | string, showNum: boolean): string | number {
        if (showNum) return count;
        const numValue = Number(count);
        return Number.isNaN(numValue) ? count : ABCUtils.formatPrice(numValue);
    }

    subTitleView(options: { title?: string; value?: number }): JSX.Element {
        const { title, value } = options;
        if (!value) return <View />;
        return (
            <View style={{ marginRight: Sizes.dp8 }}>
                <Text numberOfLines={1} style={TextStyles.t10NS2.copyWith({ lineHeight: Sizes.dp12 })}>
                    {`${title} ${ABCUtils.formatPrice(value)}`}
                </Text>
            </View>
        );
    }

    subTitleListView(): JSX.Element {
        const { principal, present } = this.props;
        return (
            <View style={{ flexDirection: "row", flexWrap: "wrap", justifyContent: "stretch" }}>
                {!!principal && this.subTitleView({ title: "本金", value: principal })}
                {!!present && this.subTitleView({ title: "赠金", value: present })}
            </View>
        );
    }

    render(): JSX.Element {
        const { title, count, style, countStyle, titleStyle, showNum, dollarStyle } = this.props;
        return (
            <View style={[{ padding: Sizes.dp16 }, flattenStyles(style)]}>
                <View style={{ flexDirection: "row", alignItems: "flex-end" }}>
                    {!showNum && (
                        <Text
                            style={[
                                TextStyles.t12BW,
                                flattenStyles(dollarStyle ?? countStyle),
                                { fontSize: Sizes.dp12, marginBottom: DeviceUtils.isAndroid() ? -Sizes.dp2 : Sizes.dp1 },
                            ]}
                        >
                            {abcI18Next.t("￥")}
                        </Text>
                    )}
                    <Text style={[DINAlternateFontFamily, TextStyles.t20BM, flattenStyles(countStyle)]}>
                        {this.formatDisplayValue(count, showNum ?? true)}
                    </Text>
                </View>
                {this.subTitleListView()}
                <SizedBox height={Sizes.dp8} />
                <Text numberOfLines={1} style={[TextStyles.t14NT2, flattenStyles(titleStyle)]}>
                    {title}
                </Text>
            </View>
        );
    }
}
interface PerformanceStatItemProps extends StatItemProps {
    showRightLine?: boolean;
    layOut?: () => void; // 说明
}

export class PerformanceStatItem extends BaseComponent<PerformanceStatItemProps> {
    constructor(props: PerformanceStatItemProps) {
        super(props);
    }

    private _cardViewH = 0; // 卡片高度

    static defaultProps = {
        showNum: true,
        showRightLine: true,
    };

    subTitleView(options: { title?: string; value?: number }): JSX.Element {
        const { title, value } = options;
        if (!value) return <View />;
        return (
            <View style={{ marginRight: Sizes.dp8 }}>
                <Text numberOfLines={1} style={TextStyles.t10NS2.copyWith({ lineHeight: Sizes.dp12 })}>
                    {`${title} ${ABCUtils.formatPrice(value)}`}
                </Text>
            </View>
        );
    }

    subTitleListView(): JSX.Element {
        const { principal, present } = this.props;
        return (
            <View style={{ flexDirection: "row", flexWrap: "wrap", justifyContent: "stretch" }}>
                {!!principal && this.subTitleView({ title: "本金", value: principal })}
                {!!present && this.subTitleView({ title: "赠金", value: present })}
            </View>
        );
    }
    formatDisplayValue(count: number | string, showNum: boolean): string | number {
        if (showNum) return count;
        const numValue = Number(count);
        return Number.isNaN(numValue) ? count : ABCUtils.formatPrice(numValue);
    }

    render(): JSX.Element {
        const { title, count, style, countStyle, titleStyle, showNum, showRightLine, layOut, dividerLineColor, iconStyle, dollarStyle } =
            this.props;
        return (
            <View style={[{ flex: 1, flexDirection: "row" }]}>
                <View
                    style={[{ flex: 1, paddingLeft: Sizes.dp24 }, flattenStyles(style)]}
                    onLayout={(evt) => {
                        //@ts-ignore
                        this._cardViewH = evt.layout.height ?? 0;
                        this.forceUpdate();
                    }}
                >
                    <View style={{ flexDirection: "row", alignItems: "flex-end" }}>
                        {!showNum && (
                            <Text
                                style={[
                                    TextStyles.t12BT1,
                                    flattenStyles(dollarStyle ?? countStyle),
                                    { fontSize: Sizes.dp12, marginBottom: DeviceUtils.isAndroid() ? -Sizes.dp1 : Sizes.dp1 },
                                ]}
                            >
                                {abcI18Next.t("￥")}
                            </Text>
                        )}
                        <Text style={[DINAlternateFontFamily, TextStyles.t20BT1, flattenStyles(countStyle)]}>
                            {this.formatDisplayValue(count, showNum ?? true)}
                        </Text>
                    </View>
                    {this.subTitleListView()}
                    <Spacer />
                    <SizedBox height={Sizes.dp8} />
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={[TextStyles.t14NT6, flattenStyles(titleStyle)]}>{title}</Text>
                        {layOut && (
                            <IconFontView
                                name={"information"}
                                size={Sizes.dp14}
                                color={"rgba(255, 255, 255, 0.7)"}
                                style={iconStyle}
                                onClick={() => layOut()}
                            />
                        )}
                    </View>
                </View>
                {!!showRightLine && (
                    <View style={{ width: Sizes.dpHalf, height: this._cardViewH, backgroundColor: dividerLineColor ?? Colors.P5 }} />
                )}
            </View>
        );
    }
}

interface StatTextItemViewProps {
    title: string;
    content1?: string; // 中部内容1
    tailContent?: string; // 尾部内容
    titleStyle?: Style | Style[];
    content1Style?: Style | Style[]; // 中部内容1样式
    tailContentStyle?: Style | Style[]; // 尾部样式
    showNum?: boolean;
}

export class StatTextItemView extends BaseComponent<StatTextItemViewProps> {
    constructor(props: StatTextItemViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { title, content1, tailContent, content1Style, tailContentStyle, showNum } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { paddingVertical: Sizes.listHorizontalMargin }]}>
                <Text style={[TextStyles.t16NB, { flexShrink: 1 }]} numberOfLines={1}>
                    {title}
                </Text>
                {!!content1 && !!content1.length ? (
                    <View style={{ flexGrow: 1, paddingHorizontal: Sizes.dp16 }}>
                        <Text style={[TextStyles.t16NB, { flex: 1, textAlign: "right" }, flattenStyles(content1Style)]} numberOfLines={1}>
                            {content1}
                        </Text>
                    </View>
                ) : (
                    <Spacer />
                )}
                {!!tailContent && !!tailContent.length && (
                    <View
                        style={[
                            { width: Sizes.dp120, flexDirection: "row", alignItems: "flex-end", justifyContent: "flex-end" },
                            flattenStyles(tailContentStyle),
                        ]}
                    >
                        {!showNum && <Text style={[TextStyles.t12BB, { marginBottom: Sizes.dp1 }]}>{abcI18Next.t("￥")}</Text>}
                        <Text style={TextStyles.t16NB} numberOfLines={1}>
                            {tailContent}
                        </Text>
                    </View>
                )}
            </View>
        );
    }
}

// 用于发药日报-发药详情
export class MedicationDailyReportTextItemView extends StatTextItemView {
    render(): JSX.Element {
        const { title, content1, tailContent, content1Style, tailContentStyle } = this.props;
        return (
            <View
                style={[
                    ABCStyles.bottomLine,
                    { flexDirection: "row", justifyContent: "space-between", paddingVertical: Sizes.listHorizontalMargin },
                ]}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text style={[TextStyles.t16NB, { marginRight: Sizes.dp8 }]} numberOfLines={1}>
                        {title}
                    </Text>
                    {!!content1 && !!content1.length && (
                        <Text style={[TextStyles.t16NB, flattenStyles(content1Style)]} numberOfLines={1}>
                            {content1}
                        </Text>
                    )}
                </View>
                {!!tailContent && !!tailContent.length && (
                    <View style={{ justifyContent: "flex-end", flexDirection: "row", flexWrap: "wrap", marginLeft: Sizes.dp24, flex: 1 }}>
                        <Text style={{ textAlign: "right" }}>
                            <Text style={[TextStyles.t16NB, flattenStyles(tailContentStyle)]} numberOfLines={2}>
                                {tailContent}
                            </Text>
                        </Text>
                    </View>
                )}
            </View>
        );
    }
}

interface PercentItemViewProps {
    name: string;
    percent: number;
    amount: number;
    drawTopLine?: boolean;
    style?: Style | Style[];
    showNum?: boolean;
}

export class PercentItemView extends BaseComponent<PercentItemViewProps> {
    constructor(props: PercentItemViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { name, percent, amount, style, showNum } = this.props;
        return (
            <View key={name} style={[{ marginBottom: Sizes.dp24 }, flattenStyles(style)]}>
                <View style={{ flexDirection: "row", alignItems: "flex-end", height: Sizes.dp24, marginBottom: Sizes.dp4 }}>
                    <Text style={TextStyles.t16NT0}>{name} </Text>
                    <Text style={[TextStyles.t14NT6, { marginBottom: Sizes.dp1 }]}>
                        ({NumberUtils.formatMaxFixed(Math.max(percent * 100, 0), 2, 2)}%)
                    </Text>
                    <Spacer />
                    {!showNum && <Text style={[TextStyles.t12NB, { marginBottom: Sizes.dp1 }]}>{abcI18Next.t("￥")}</Text>}
                    <Text style={TextStyles.t16NT0}>{showNum ? amount : ABCUtils.formatPrice(amount)}</Text>
                </View>
                <View style={{ flex: 1, flexDirection: "row", backgroundColor: Colors.bg1, borderRadius: Sizes.dp4, height: Sizes.dp7 }}>
                    <View style={{ flex: percent * 100, backgroundColor: Colors.mainColor, borderRadius: Sizes.dp4 }} />
                    <View style={{ flex: (1 - percent) * 100 }} />
                </View>
            </View>
        );
    }
}
