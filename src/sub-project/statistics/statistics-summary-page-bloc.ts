/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/6/28
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { EventName } from "../bloc/bloc";
import { userCenter } from "../user-center";
import { BaseLoadingState } from "../bloc/bloc-helper";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import URLProtocols from "../url-dispatcher/url-protocols";
import {
    AchievementDispensingPersonnelDetail,
    StatSummaryCardType,
    StatSummaryData,
    StatSummaryEmployeeType,
} from "./data/statistics-bean";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import {
    GetChargeGoodsAchievementRsp,
    GetChargeGoodsAchievementTransactionRsp,
    GetExecuteSummaryRsp,
    GetManagementOutpatientDailyDetailRsp,
    GetManagementRevenueRetailDetailRsp,
    StatisticsAgent,
} from "./data/statistics-agent";
import { ABCError } from "../common-base-module/common-error";
import _ from "lodash";
import { CheckAccountConfig, DataPermission, OnlinePropertyConfigProvider } from "../data/online-property-config-provder";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";

export interface ModuleSummaryViewData {
    title: string;
    count: number | string;
    sort: number;
}

const KEmployeeBaseSummary = "kEmployeeBaseSummary";
const KEmployeeBaseSummaryConfig = "kEmployeeBaseSummaryConfig";

export class State extends BaseLoadingState {
    loading = true;

    statSummaryDetail?: StatSummaryData;
    disposingStatSummaryDetail?: AchievementDispensingPersonnelDetail;
    doctorStatSummaryDetail?: GetManagementOutpatientDailyDetailRsp;

    statClinicSummaryDetail?: GetManagementRevenueRetailDetailRsp;

    treatmentExecuteDetail?: GetExecuteSummaryRsp;

    chargeGoodsAchievementDetail?: GetChargeGoodsAchievementRsp;

    chargeGoodsAchievementTransactionDetail?: GetChargeGoodsAchievementTransactionRsp;

    //诊所统计
    clinicSummary: ModuleSummaryViewData[] = [];
    //门诊统计
    outpatientSummary: ModuleSummaryViewData[] = [];
    //治疗理疗统计
    treatmentSummary: ModuleSummaryViewData[] = [];
    //收费日报
    chargeSummary: ModuleSummaryViewData[] = [];
    //发药日报
    disposingSummary: ModuleSummaryViewData[] = [];

    get allSummary(): ModuleSummaryViewData[] {
        const list = _.concat(
            this.clinicSummary,
            this.outpatientSummary,
            this.treatmentSummary,
            this.disposingSummary,
            this.chargeSummary
        ).sort((a, b) => a.sort - b.sort);
        return list;
    }

    // 数据权限
    dataPermission?: DataPermission;

    // 收费员 不允许查看账目
    get cashierNotAllowedCheck(): boolean {
        return this.dataPermission?.dashboard?.chargerPermission == CheckAccountConfig.notCheck;
    }

    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class StatisticsSummaryPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<StatisticsSummaryPageBloc | undefined>(undefined);

    private _loadAllStatSummaryDetailTrigger: Subject<number> = new Subject<number>();
    private initUpdate: boolean;

    static fromContext(context: StatisticsSummaryPageBloc): StatisticsSummaryPageBloc {
        return context;
    }

    constructor(options?: { initUpdate: boolean }) {
        super();

        this.initUpdate = options?.initUpdate ?? true;

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventShowModuleDetail, this._mapEventShowModuleDetail);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private _resetBaseSummaryInfo(): void {
        this.innerState.clinicSummary = [];
        this.innerState.outpatientSummary = [];
        this.innerState.chargeSummary = [];
        this.innerState.disposingSummary = [];
        this.innerState.treatmentSummary = [];
    }

    getLocalSummaryInfo(): void {
        const rsp = clinicSharedPreferences.getObject(KEmployeeBaseSummary);
        if (!!rsp) {
            const allRsp = JSON.parse(rsp);
            this.innerState.statSummaryDetail = allRsp.statSummaryDetail;
            this.innerState.disposingStatSummaryDetail = allRsp.disposingStatSummaryDetail;
            this.innerState.doctorStatSummaryDetail = allRsp.doctorStatSummaryDetail;
            this.innerState.statClinicSummaryDetail = allRsp.statClinicSummaryDetail;
            this.innerState.treatmentExecuteDetail = allRsp.treatmentExecuteDetail;
            this.innerState.chargeGoodsAchievementDetail = allRsp.chargeGoodsAchievementDetail;
            this.innerState.chargeGoodsAchievementTransactionDetail = allRsp.chargeGoodsAchievementTransactionDetail;
        }
    }

    private _updateBaseData(): void {
        this._loadAllStatSummaryDetailTrigger.next();
    }

    setLocalSummaryInfo(): void {
        const obj = {
            statSummaryDetail: this.innerState.statSummaryDetail,
            disposingStatSummaryDetail: this.innerState.disposingStatSummaryDetail,
            doctorStatSummaryDetail: this.innerState.doctorStatSummaryDetail,
            statClinicSummaryDetail: this.innerState.statClinicSummaryDetail,
            treatmentExecuteDetail: this.innerState.treatmentExecuteDetail,
            chargeGoodsAchievementDetail: this.innerState.chargeGoodsAchievementDetail,
            chargeGoodsAchievementTransactionDetail: this.innerState.chargeGoodsAchievementTransactionDetail,
        };
        clinicSharedPreferences.setObject(KEmployeeBaseSummary, JSON.stringify(obj));
    }

    private _updateBaseSummaryInfo(): void {
        if (userCenter.clinic) {
            const { isManager, isDoctor, isChargeEmployee, isDispensingEmployee, isTherapist } = userCenter.clinic;
            const innerState = this.innerState;
            if (isManager) {
                const { statClinicSummaryDetail } = innerState;
                const patientCountOutpatient = statClinicSummaryDetail?.patientCompleteCount ?? 0,
                    patientCountRetail = statClinicSummaryDetail?.retailTraffic ?? 0,
                    allCount = statClinicSummaryDetail?.arrivalPeopleCount ?? 0;
                this.innerState.clinicSummary.push({ title: "到店人次", count: allCount, sort: 1 });
                this.innerState.clinicSummary.push({ title: "门诊人数", count: patientCountOutpatient, sort: 2 });
                this.innerState.clinicSummary.push({ title: "零售人数", count: patientCountRetail, sort: 3 });
            }

            if (isDoctor) {
                const { doctorStatSummaryDetail } = innerState;
                const patientCount = doctorStatSummaryDetail?.patientCount ?? 0,
                    prescriptionCount = doctorStatSummaryDetail?.prescriptionCount ?? 0,
                    registrationCount = doctorStatSummaryDetail?.registrationCount ?? 0;
                this.innerState.outpatientSummary.push({ title: "就诊人次", count: patientCount, sort: 4 });
                this.innerState.outpatientSummary.push({ title: "开方数", count: prescriptionCount, sort: 8 });
                this.innerState.outpatientSummary.push({ title: "明日预约", count: registrationCount, sort: 11 });
            }

            if (isChargeEmployee /*&& !innerState.cashierNotAllowedCheck*/) {
                const { statSummaryDetail } = innerState;
                const patientCount = statSummaryDetail?.patientCount ?? 0,
                    totalAmount = statSummaryDetail?.totalAmount ?? 0,
                    rechargeCount = statSummaryDetail?.rechargeCount ?? 0;
                this.innerState.chargeSummary.push({ title: "营业收费人次", count: patientCount, sort: 6 });
                this.innerState.chargeSummary.push({
                    title: "收费金额",
                    count: innerState.cashierNotAllowedCheck ? "--" : totalAmount,
                    sort: 9,
                });
                this.innerState.chargeSummary.push({ title: "充值人次", count: rechargeCount, sort: 13 });
            }

            if (isDispensingEmployee) {
                const { disposingStatSummaryDetail, isDentistry } = innerState;
                const dispensingNumber = disposingStatSummaryDetail?.dispensingNumber ?? 0,
                    typeNumber = disposingStatSummaryDetail?.typeNumber ?? 0,
                    allPrescription = disposingStatSummaryDetail?.__allPrescriptionCount ?? 0;
                this.innerState.disposingSummary.push({ title: "发药单数", count: dispensingNumber, sort: 7 });
                if (!isDentistry) this.innerState.disposingSummary.push({ title: "发药处方", count: allPrescription, sort: 14 });
                this.innerState.disposingSummary.push({ title: "发药种数", count: typeNumber, sort: 15 });
            }

            if (isTherapist) {
                const { treatmentExecuteDetail, chargeGoodsAchievementTransactionDetail } = innerState;
                this.innerState.treatmentSummary.push({
                    title: "执行次数",
                    count: treatmentExecuteDetail?.summary?.totalCount ?? 0,
                    sort: 5,
                });
                this.innerState.treatmentSummary.push({
                    title: "服务人次",
                    count: treatmentExecuteDetail?.summary?.servicePersonCount ?? 0,
                    sort: 10,
                });
                this.innerState.treatmentSummary.push({
                    title: "开单数",
                    count: chargeGoodsAchievementTransactionDetail?.total?.count ?? 0,
                    sort: 12,
                });
            }
        }
    }

    getLocalSummaryConfig(): void {
        const config = clinicSharedPreferences.getObject(KEmployeeBaseSummaryConfig);
        if (!!config) {
            this.innerState.dataPermission = JSON.parse(config);
        }
    }

    setLocalSummaryConfig(): void {
        clinicSharedPreferences.setObject(KEmployeeBaseSummaryConfig, JSON.stringify(this.innerState.dataPermission));
    }

    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.getLocalSummaryInfo();
        this.getLocalSummaryConfig();
        this._updateBaseSummaryInfo();
        this.update();
        this._loadAllStatSummaryDetailTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.startLoading();
                    this.update();
                    return Promise.all([
                        StatisticsAgent.getStatSummaryData(
                            new Date(),
                            new Date(),
                            userCenter.clinic!.clinicId!,
                            true,
                            userCenter.employee?.id,
                            StatSummaryEmployeeType.charger
                        ).catchIgnore(),
                        StatisticsAgent.getAchievementDispensingPersonnel({
                            beginDate: new Date(),
                            endDate: new Date(),
                            operationType: 2,
                            employeeId: userCenter.employee?.id,
                        }).catchIgnore(),
                        StatisticsAgent.getManagementOutpatientDailyDetail({
                            beginDate: new Date(),
                            endDate: new Date(),
                            employeeId: userCenter.employee?.id,
                        }).catchIgnore(),
                        StatisticsAgent.getOperationOverviewDetail({
                            beginDate: new Date(),
                            endDate: new Date(),
                            clinicId: userCenter.clinic?.isChainAdminClinic ? "" : userCenter.clinic?.clinicId,
                        }).catchIgnore(),
                        StatisticsAgent.getExecuteSummary({
                            beginDate: new Date(),
                            endDate: new Date(),
                            employeeId: userCenter.employee?.id,
                            clinicId: userCenter.clinic?.isChainAdminClinic ? "" : userCenter.clinic?.clinicId,
                            offset: 0,
                            size: 1,
                        }).catchIgnore(),
                        StatisticsAgent.getChargeGoodsAchievement({
                            beginDate: new Date(),
                            endDate: new Date(),
                            employees: [{ id: userCenter.employee?.id, name: userCenter.employee?.name }],
                            clinicId: userCenter.clinic?.isChainAdminClinic ? "" : userCenter.clinic?.clinicId,
                            offset: 0,
                            size: 1,
                        }).catchIgnore(),
                        StatisticsAgent.getChargeGoodsAchievementTransaction({
                            beginDate: new Date(),
                            endDate: new Date(),
                            employees: [{ id: userCenter.employee?.id, name: userCenter.employee?.name }],
                            clinicId: userCenter.clinic?.isChainAdminClinic ? "" : userCenter.clinic?.clinicId,
                            offset: 0,
                            size: 1,
                        }).catchIgnore(),
                    ])
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((allRsp) => {
                this.innerState.stopLoading();
                if (allRsp instanceof ABCError) {
                    this.innerState.setLoadingError(allRsp);
                } else {
                    this.innerState.statSummaryDetail = allRsp[0];
                    this.innerState.disposingStatSummaryDetail = _.first(allRsp?.[1]?.list ?? []);
                    this.innerState.doctorStatSummaryDetail = allRsp[2];
                    this.innerState.statClinicSummaryDetail = allRsp[3];
                    this.innerState.treatmentExecuteDetail = allRsp[4];
                    this.innerState.chargeGoodsAchievementDetail = allRsp[5];
                    this.innerState.chargeGoodsAchievementTransactionDetail = allRsp[6];

                    this._resetBaseSummaryInfo();
                    this._updateBaseSummaryInfo();
                    this.setLocalSummaryInfo();
                }
                this.update();
            })
            .addToDisposableBag(this);

        // if (this.initUpdate) {
        this._updateBaseData();
        // }
        //切换诊所后执行
        userCenter.sUpdatedUserCenterObserver
            .subscribe(() => {
                this._updateBaseData();
            })
            .addToDisposableBag(this);

        // 拉取诊所数据权限
        OnlinePropertyConfigProvider.instance
            .getClinicDataPermission()
            .toObservable()
            .subscribe((res) => {
                this.innerState.dataPermission = res;
                this.setLocalSummaryConfig();
                this.update();
            })
            .addToDisposableBag(this);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventShowModuleDetail(/*event: _EventShowModuleDetail*/): AsyncGenerator<State> {
        const clinic = userCenter.clinic;
        const modules: boolean[] = [
            !!clinic?.isDoctor,
            !!clinic?.isChargeEmployee,
            !!clinic?.isDispensingEmployee,
            !!clinic?.isTherapist,
            !!clinic?.isManager,
        ];
        if (modules.filter((item) => item).length > 1) {
            clinic?.isChainAdminClinic
                ? ABCNavigator.navigateToPage(URLProtocols.STAT_CLINIC_PERFORMANCE) //总店 直接到数据分析详情页面
                : ABCNavigator.navigateToPage(URLProtocols.STAT_SUMMARY); //门店 跳转业绩分析汇总模块
        } else if (modules.filter((item) => item).length == 1) {
            //单角色 跳转相应的业绩分析模块
            if (clinic?.isManager) {
                //管理员直接到数据分析详情页面
                // ABCNavigator.navigateToPage(URLProtocols.STAT_PERFORMANCE);
                ABCNavigator.navigateToPage(URLProtocols.STAT_CLINIC_PERFORMANCE);
            } else if (clinic?.isDoctor) {
                ABCNavigator.navigateToPage(`${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.outpatientDailyReport}`); // 门诊日报
            } else if (clinic?.isChargeEmployee) {
                ABCNavigator.navigateToPage(`${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.chargeDailyReport}`); // 收银日报
            } else if (clinic?.isDispensingEmployee) {
                ABCNavigator.navigateToPage(`${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.dispensingDailyReport}`); // 发药日报
            } else if (clinic?.isTherapist) {
                ABCNavigator.navigateToPage(`${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.treatmentAndPhysiotherapy}`); // 理疗师日报
            }
        }
    }

    public requestShowModuleDetail(): void {
        this.dispatch(new _EventShowModuleDetail());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventShowModuleDetail extends _Event {}
