import { ABCApiNetwork } from "../../net";
import {
    StatDailyRevenue,
    StatSummaryData,
    PersonalAchievement,
    AchievementDispensingPersonnelDetail,
    AchievementScreeningInformationData,
    ChargeDailyReportDetail,
    OperationOverviewData,
    StatSummaryEmployeeType,
    GetRevenueExpenseReport,
    ExecuteSummaryItem,
    ChargeGoodsAchievement,
    EmployeesSelectionItem,
    WorkbenchCardInformation,
    WorkbenchDiagnosisTreatmentExpense,
    WorkbenchRegistrationExpense,
    WorkbenchTotalInvoicedAmountPersonTime,
    InpatientWorkReportRsp,
    BillingPerformanceData,
    PhysicPatientAnalysisData,
} from "./statistics-bean";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { NumberUtils, TimeUtils } from "../../common-base-module/utils";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/29
 *
 * @description
 */

interface BaseStatReq {
    beginDate?: Date;
    endDate?: Date;
    clinicId?: string;
}

interface AnalysisOfOutpatients {
    beginDate?: Date;
    endDate?: Date;
    clinicId?: string;
    offset?: number; // 分页起始
    limit?: number; // 分页大小
}

interface GetRevenuesStatDetailReq extends BaseStatReq {
    groupBy?: string;
}

interface GetAchievementDispensingPersonnelReq extends BaseStatReq {
    employeeId?: string;
    dispensingStatus?: number;
    processType?: number;
    operationType?: number;
}

interface GetAchievementExecuteSummaryReq extends BaseStatReq {
    employeeId?: string;
    offset?: number;
    size?: number;
}
class EmployeesItem {
    id?: string;
    name?: string;
}

interface GetChargeGoodsAchievementReq extends BaseStatReq {
    employees?: EmployeesItem[];
    offset?: number;
    size?: number;
    includeReg?: number;
    includeWriter?: number;

    copyWriterId?: string; // 代录人ID
    employeeTypeEnum?: string; // ("SELLER": 开单人 , "AGENT": 代录人);
    isContainOthersWriterAchievement?: number; // 是否包含其他人代录业绩 1:包含 0不包含
}

interface GetOperationChargeCashierListDetailReq extends BaseStatReq {
    payModes?: string;
    feeType1?: string;
    feeType2?: string;
}

interface GetOutpatientSummaryDetailReq extends BaseStatReq {
    employeeId?: string;
}
interface HospitalStatRevenueReq extends BaseStatReq {
    includeReg?: boolean;
}
interface BillingPerformanceReq extends BaseStatReq {
    departmentId?: string;
}

export class GetAchievementDispensingPersonnelRsp {
    @JsonProperty({ type: Array, clazz: AchievementDispensingPersonnelDetail, name: "data" })
    list?: AchievementDispensingPersonnelDetail[];
}

export class GetOperationChargeCashierListDetailRsp {
    @JsonProperty({ type: Array, clazz: ChargeDailyReportDetail })
    result?: ChargeDailyReportDetail[];
}

export class GetOutpatientSummaryDetailRsp {
    patientCount?: number;
    doctorVisitCount?: number;
    doctorRevisitCount?: number;
}

export class GetManagementOutpatientDailyDetailRsp {
    patientCount?: number;
    prescriptionCount?: number;
    registrationCount?: number;
}

export class GetManagementRevenueRetailDetailRsp extends OperationOverviewData {}

export class GetManagementAnalysisOfOutpatientsReports {
    @JsonProperty({ fromJson: fromJsonToDate })
    date?: Date; // 日期
    clinicId?: string; // 门店ID
    clinicName?: string; // 门店名
    registrations?: number; // 挂号人数
    arrivalPeopleCount?: number; // 到店人数
    clinicNewCustomers?: number; // 新客
    clinicOldCustomers?: number; // 老客
    retailTraffic?: number; // 零售
    patientCompleteCount?: number; // 完诊
    firstVisitPatientCount?: number; // 初诊人次
    returnVisitPatientCount?: number; // 复诊人次
    firstVisitAmount?: number; // 初诊金额
    firstVisitAmountPerSheet?: number; // 初诊客单价
    returnVisitAmount?: number; // 复诊客单价
    returnVisitAmountPerSheet?: number;
    returnVisitPatientPercent?: string; //复诊占比
    chargeCount?: number; // 付费人数
    sheetDropRatio?: string; // 跑单率
    patientCompletePercent?: string;
    dropCount?: number; // 跑单人数
}

export interface GetManagementAnalysisOfOutpatientsSummary {
    arrivalPeopleCount?: number; // 合计-到店人数
    chargeCount?: number; // 合计-付费人数
    clinicId?: string; // 合计ID
    clinicName?: string; // 合计
    clinicNewCustomers?: number; // 合计-门店新客
    clinicOldCustomers?: number; // 合计-门店老客
    date?: string; // 合计
    dropCount?: number; // 合计-跑单人数
    firstVisitAmount?: number; // 合计-初诊金额
    firstVisitAmountPerSheet?: number; // 合计-初诊客单价
    firstVisitPatientCount?: number; // 合计-初诊人次
    patientCompleteCount?: number; // 合计-弯针人次
    patientCompletePercent?: string; // 合计-完诊占比 %
    registrations?: number; // 合计-预约挂号人数
    retailTraffic?: number; // 合计-零售客流量
    returnVisitAmount?: number; // 合计-复诊金额
    returnVisitAmountPerSheet?: number; // 合计-复诊客单价
    returnVisitPatientCount?: number; // 合计-复诊人次
    returnVisitPatientPercent?: string; // 合计-复诊占比 %
    sheetDropRatio?: string; // 合计-跑单率 %
}

export class GetManagementAnalysisOfOutpatients {
    reports?: GetManagementAnalysisOfOutpatientsReports[];
    summary?: GetManagementAnalysisOfOutpatientsSummary; // 运营日报门店合计
}

// 运营详情-零售患者分析
export class GetAnalysisOfRetailPatients {
    patientCount?: string; // 零售患者人次
    oldPatientCount?: string; // 零售-老客人次
    newPatientCount?: string; // 零售-新客人次
    oldAmount?: string; // 零售-老客实收金额
    newAmount?: string; // 零售-新客实收金额
}

class OutpatientSummary {
    @JsonProperty({ fromJson: NumberUtils.formatPriceToFixedWithoutZero })
    cumulativeAmount?: number;
    outpatientCount?: number;
    patientId?: string;
    payCount?: number;
    retailCount?: number;
}
//患者就诊信息统计详情
class GetOutpatientSummaryRsp {
    @JsonProperty({ type: Array, clazz: OutpatientSummary })
    rows?: OutpatientSummary[];
}

class GetExecuteSummaryRspSummary {
    count?: number;
    totalCount?: number;
    totalAmount?: number;
    totalActualAmount?: number;
    executeItemCount?: number;
    servicePersonCount?: number; //服务人次
}

export class GetExecuteSummaryRsp {
    // count?: number;
    // executeItemCount?: number;
    // // executorList: []
    // @JsonProperty({ type: Array, clazz: ExecuteSummaryItem })
    // list?: ExecuteSummaryItem[];
    // totalActualAmount?: number;
    // totalAmount?: number;
    // totalCount?: number;
    // servicePersonCount?: number; //服务人次

    header?: [];
    @JsonProperty({ type: Array, clazz: ExecuteSummaryItem })
    data?: ExecuteSummaryItem[];
    summary?: GetExecuteSummaryRspSummary;
}

class GetChargeGoodsAchievementTotal {
    count?: number;
    data: number[] = [];

    get totalSum(): number {
        return this.data[1] ?? 0;
    }
}

export class GetChargeGoodsAchievementRsp {
    // count?: number;
    // totalSum?: number;
    data?: ChargeGoodsAchievement[];
    @JsonProperty({ type: GetChargeGoodsAchievementTotal })
    total?: GetChargeGoodsAchievementTotal;
}

export class GetChargeGoodsAchievementTransactionRsp {
    // count?: number;
    // totalSum?: number;
    total?: GetChargeGoodsAchievementTotal;
}

export enum EmployeeTypeEnum {
    SELLER = "SELLER", //  开单人
    DOCTOR = "DOCTOR", // 医生
    NURSE = "NURSE", // 护士
    AGENT = "AGENT", // 代录人
}

export class GetStatConfigSelectRsp {
    chainId?: string;
    clinicId?: string;
    arrearsStatTiming?: number; // [欠费收入统计时机] 1:欠费时计入收入 2:还款时计入收入 （ps:将影响营收，运营相关统计）

    commissionType?: number; // [计提金额计算口径] 1按实收进行提成（ps:实收：已计算折扣、优惠和议价项目的价格） 2按原价进行提成（ps:原价：未计算折扣、优惠和议价项目的原始价格）

    arrearsCommissionTiming?: number; // [欠费收入计提时机] 1:欠费时计提 2:还款时计提

    isCardOpeningFee?: number; // [卡项费用计提] 开卡费用 0:禁用 1:启用
    isIncludePromotionCard?: number; // [卡项费用计提] 卡项抵扣金额（按原价计提）0:关闭 1:开启（ps:收费时因直接抵扣卡内次数，实收为0，该项目的售价为卡项抵扣金额）

    isComposeShareEqually?: number;
}

export class StatisticsAgent {
    /**
     * 获取开单业绩概要信息
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @param clinicId 门店ID
     * @param isIncludeReg 是否包含挂号费
     * @param includeWriter 是否包含代录业绩
     * @param employeeId 开单人ID
     * @param copyWriterId 代录人ID
     * @param employeeTypeEnum
     * @param isContainOthersWriterAchievement  开单业绩是否包含他人代录业绩( 1:包含 0:不包含)
     * */
    static getPersonalAchievement(
        beginDate: Date,
        endDate: Date,
        clinicId: string,
        isIncludeReg = true,
        includeWriter = true,
        employeeId = "",
        copyWriterId = "",
        employeeTypeEnum = EmployeeTypeEnum.SELLER,
        isContainOthersWriterAchievement = true
    ): Promise<PersonalAchievement> {
        const dayFormat = "yyyy-MM-dd";
        //兼容异常情况：sc/stat/revenue/overview/summary?1725418419146&beginDate=2024-09-04&endDate=2024-09-03
        if (!!endDate && beginDate.getTime() > endDate.getTime()) {
            beginDate = endDate;
        }
        return ABCApiNetwork.get("stat/achievement/personnel/info", {
            clazz: PersonalAchievement,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
                includeReg: isIncludeReg ? 1 : 0,
                includeWriter: includeWriter ? 1 : 0,
                employeeId: employeeId,
                copyWriterId: copyWriterId,
                employeeTypeEnum: employeeTypeEnum,
                isContainOthersWriterAchievement: isContainOthersWriterAchievement ? 1 : 0,
            },
        }).then((rsp) => {
            // 前端生成feetypes列表
            const { data } = rsp;
            const _header = rsp.displayFeeTypes;
            data?.map((item) => {
                item.feeTypes = _header.map((__item) => {
                    return {
                        name: __item.label ?? "",
                        value: item[__item.prop ?? ""] as number,
                        field: __item.field ?? "",
                    };
                });
                return item;
            });
            return rsp;
        });
    }

    /**
     * 开单业绩 （开单人/代录人筛选列表）
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @param clinicId 门店id
     * @param employeeTypeEnum ("SELLER": 开单人 , "DOCTOR": 医生 , NURSE: "护士" , "AGENT": 代录人);
     * @param includeWriter 是否包含代录 (0 不包含 1 包含) 默认传0
     */

    static getAchievementEmployeesSelection(
        beginDate: Date,
        endDate: Date,
        clinicId: string,
        employeeTypeEnum: EmployeeTypeEnum
    ): Promise<EmployeesSelectionItem[]> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get<EmployeesSelectionItem[]>("stat/revenue/employees/selection", {
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
                employeeTypeEnum: employeeTypeEnum,
                includeWriter: 0,
            },
        }).then((rsp) => rsp?.map((item) => JsonMapper.deserialize(EmployeesSelectionItem, item)));
    }

    /**
     * 获取工作台今日汇总 卡片信息-(首诊患者/门诊量/处方量/复诊患者)
     * @param beginDate
     * @param endDate
     * @param clinicId
     * */
    static getWorkbenchCardInformation(beginDate: Date, endDate: Date, clinicId: string): Promise<WorkbenchCardInformation> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("portal/overview/employee", {
            clazz: WorkbenchCardInformation,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
            },
        });
    }

    /**
     * 获取工作台今日汇总 挂号费用
     * @param beginDate
     * @param endDate
     * @param employeeId
     * */
    static getWorkbenchRegistrationExpense(
        beginDate: Date,
        endDate: Date,
        clinicId: string,
        employeeId: string
    ): Promise<WorkbenchRegistrationExpense> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/revenue/overview/job-summary/income/doctor", {
            clazz: WorkbenchRegistrationExpense,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
                doctorId: employeeId,
                offset: 0,
                size: 9,
            },
        });
    }

    /**
     * 获取工作台今日汇总 诊疗费用
     * @param beginDate
     * @param endDate
     * @param employeeId
     * */
    static getWorkbenchDiagnosisTreatmentExpense(
        beginDate: Date,
        endDate: Date,
        employeeId: string
    ): Promise<WorkbenchDiagnosisTreatmentExpense> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/revenue/overview/workbench/income/doctor", {
            clazz: WorkbenchDiagnosisTreatmentExpense,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                employeeId: employeeId,
            },
        }).then((rsp) => {
            // 前端生成_header列表
            const _header = rsp.displayFeeTypes;
            rsp.header?.map((header) => {
                header._feeType = _header;
            });

            return rsp;
        });
    }

    /**
     * 获取工作台今日汇总 开单金额合计、零售人次
     * @param beginDate
     * @param endDate
     * @param employeeId
     * @param clinicId
     * @param scope 1.只统计挂号费 2.只统计诊疗收费 3.不允许查看个人挂号/诊疗收入 (undefined允许查看个人挂号/诊疗收入)
     * */
    static getWorkbenchTotalInvoicedAmountPersonTime(
        beginDate: Date,
        endDate: Date,
        employeeId: string,
        clinicId: string,
        scope?: number
    ): Promise<WorkbenchTotalInvoicedAmountPersonTime> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/execute/overview/workbench/execute/amount", {
            clazz: WorkbenchTotalInvoicedAmountPersonTime,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                employeeId: employeeId,
                clinicId: clinicId,
                scope: scope,
            },
            clearUndefined: true,
        });
    }

    /**
     * 获取开单业绩（门店、开单人、费用分类、支付方式相关信息）
     * @param beginDate
     * @param endDate
     * @param clinicId
     * @param isIncludeReg
     * @param includeWriter
     */
    static getAchievementScreeningInformation(
        beginDate: Date,
        endDate: Date,
        clinicId: string,
        isIncludeReg = true,
        includeWriter = true
    ): Promise<AchievementScreeningInformationData> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/achievement/charge/select", {
            clazz: AchievementScreeningInformationData,
            queryParameters: {
                employeeTypeEnum: "SELLER",
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
                includeReg: isIncludeReg ? 1 : 0,
                includeWriter: includeWriter ? 1 : 0,
            },
        });
    }

    /**
     * 获取经营统计概要信息（就诊人数，门诊费...）
     * @param beginDate
     * @param endDate
     * @param clinicId
     * @param isIncludeReg
     * @param employeeId
     * @param employeeType
     */
    static getStatSummaryData(
        beginDate: Date,
        endDate: Date,
        clinicId: string,
        isIncludeReg = true,
        employeeId = "",
        employeeType?: StatSummaryEmployeeType
    ): Promise<StatSummaryData> {
        //兼容异常情况：sc/stat/revenue/overview/summary?1725418419146&beginDate=2024-09-04&endDate=2024-09-03
        if (!!endDate && beginDate.getTime() > endDate.getTime()) {
            beginDate = endDate;
        }
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/revenue/overview/summary", {
            clazz: StatSummaryData,
            queryParameters: {
                beginDate: beginDate.format(dayFormat),
                endDate: endDate.format(dayFormat),
                clinicId: clinicId,
                includeReg: isIncludeReg ? 1 : 0,
                employeeId: employeeId,
                employeeType: employeeType,
            },
            clearUndefined: true,
        });
    }

    static getRevenuesStatDetail(params: GetRevenuesStatDetailReq): Promise<StatDailyRevenue[]> {
        const { beginDate, endDate, clinicId, ...others } = params;
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get<StatDailyRevenue[]>("stat/revenue/overview/revenue/trend", {
            queryParameters: {
                clinicId: clinicId,
                beginDate: beginDate?.format(dayFormat),
                endDate: endDate?.format(dayFormat),
                ...others,
            },
        }).then((rsp) => rsp.map((item) => JsonMapper.deserialize(StatDailyRevenue, item)));
    }

    /**
     * 发药员业绩统计
     * @param params GetAchievementDispensingPersonnelReq
     */
    static getAchievementDispensingPersonnel(params: GetAchievementDispensingPersonnelReq): Promise<GetAchievementDispensingPersonnelRsp> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get<GetAchievementDispensingPersonnelRsp>("stat/achievement/dispensing/operation/personnel", {
            queryParameters: {
                dispensingStatus: "",
                processType: "",
                ...others,
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
            },
            clazz: GetAchievementDispensingPersonnelRsp,
        });
    }

    /**
     * 获取收营员业绩详情
     * @param params
     */
    static getOperationChargeCashierListDetail(
        params: GetOperationChargeCashierListDetailReq
    ): Promise<GetOperationChargeCashierListDetailRsp> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/operation/charge/cashier/list", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: GetOperationChargeCashierListDetailRsp,
            clearUndefined: true,
        });
    }

    /**
     * 获取业绩统计-医生-汇总接口
     * @param params
     */
    static getOutpatientSummaryDetail(params: GetOutpatientSummaryDetailReq): Promise<GetOutpatientSummaryDetailRsp> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/outpatient/summary", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: GetOutpatientSummaryDetailRsp,
            clearUndefined: true,
        });
    }

    /**
     * 获取今日工作-医生统计
     * @param params
     */
    static getManagementOutpatientDailyDetail(params: GetOutpatientSummaryDetailReq): Promise<GetManagementOutpatientDailyDetailRsp> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/management/outpatient/daily", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: GetManagementOutpatientDailyDetailRsp,
            clearUndefined: true,
        });
    }

    /**
     * 运营概况-运营日报
     * @param params
     */
    static getManagementRevenueOperationDaily(params: AnalysisOfOutpatients): Promise<GetManagementAnalysisOfOutpatients> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/management/daily/report", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: GetManagementAnalysisOfOutpatients,
            clearUndefined: true,
        });
    }
    // stat/management/revenue/retail

    /**
     * 运营概况 (客流、门诊、零售、新客、老客人次)
     * @param params
     */
    static getOperationOverviewDetail(params: BaseStatReq): Promise</*OperationOverviewData*/ GetManagementRevenueRetailDetailRsp> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/management/general", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: /*OperationOverviewData*/ GetManagementRevenueRetailDetailRsp,
            clearUndefined: true,
        });
    }

    /**
     * 运营概况-零售患者分析
     * @param params
     */
    static getManagementRevenueRetailDetail(params: BaseStatReq): Promise<GetAnalysisOfRetailPatients> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("stat/management/revenue/retail", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: GetAnalysisOfRetailPatients,
            clearUndefined: true,
        });
    }

    /**
     * 营收概况-费用报表（门店、费用类型、支付方式）
     */
    static getRevenueChargeReport(beginDate: Date, endDate: Date, clinicId: string, isIncludeReg = true): Promise<GetRevenueExpenseReport> {
        const dayFormat = "yyyy-MM-dd";

        return ABCApiNetwork.get("stat/operation/charge/report/selection", {
            clazz: GetRevenueExpenseReport,
            queryParameters: {
                beginDate: beginDate.format(dayFormat ?? new Date()),
                endDate: endDate.format(dayFormat ?? new Date()),
                clinicId: clinicId,
                includeReg: isIncludeReg ? 1 : 0,
            },
        });
    }

    static getOutpatientSummary(params: { clinicId: string; patientId: string }): Promise<OutpatientSummary[]> {
        return ABCApiNetwork.get("stat/patient/index", {
            queryParameters: params,
            clazz: GetOutpatientSummaryRsp,
        }).then((rsp) => rsp.rows ?? []);
    }

    /**
     * 获取执行业绩
     * @param params
     */
    static getExecuteSummary(params: GetAchievementExecuteSummaryReq): Promise<GetExecuteSummaryRsp> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/execute/summary", {
            queryParameters: {
                beginDate: (params.beginDate ?? new Date()).format(dayFormat),
                endDate: (params.endDate ?? new Date()).format(dayFormat),
                employeeId: params.employeeId,
                clinicId: params.clinicId,
                offset: params.offset,
                size: params.size,
            },
            clazz: GetExecuteSummaryRsp,
        });
    }

    /**
     * 获取开单业绩-人员维度
     * @param params
     */
    static getChargeGoodsAchievement(params: GetChargeGoodsAchievementReq): Promise<GetChargeGoodsAchievementRsp> {
        const dayFormat = "yyyy-MM-dd";
        return ABCApiNetwork.get("stat/achievement/charge/goods", {
            queryParameters: {
                beginDate: (params.beginDate ?? new Date()).format(dayFormat),
                endDate: (params.endDate ?? new Date()).format(dayFormat),
                employees: JSON.stringify(params.employees ?? []),
                clinicId: params.clinicId,
                offset: params.offset,
                size: params.size,
                employeeTypeEnum: params.employeeTypeEnum ?? EmployeeTypeEnum.SELLER,
                copyWriterId: params.copyWriterId ?? "",
                isContainOthersWriterAchievement: params.isContainOthersWriterAchievement ?? 1,
            },
            clazz: GetChargeGoodsAchievementRsp,
        });
    }

    /**
     * 获取开单业绩-单据
     * @param params
     */
    static getChargeGoodsAchievementTransaction(params: GetChargeGoodsAchievementReq): Promise<GetChargeGoodsAchievementTransactionRsp> {
        const dayFormat = "yyyy-MM-dd HH:mm:ss";
        return ABCApiNetwork.post("stat/achievement/charge/transaction", {
            body: {
                ...params,
                beginDate: (params.beginDate ?? new Date()).format(dayFormat),
                endDate: (params.endDate ?? new Date()).format(dayFormat),
            },
            clazz: GetChargeGoodsAchievementRsp,
        });
    }

    /**
     * 获取统计口径设置
     */
    static async getStatConfigSelect(): Promise<GetStatConfigSelectRsp> {
        return ABCApiNetwork.get("stat/config/select", { clazz: GetStatConfigSelectRsp });
    }

    /**
     * 获取住院病区统计信息
     */
    static async queryStatInpatientWorkReport(params: BaseStatReq): Promise<InpatientWorkReportRsp> {
        const { beginDate, endDate, ...others } = params;
        return await ABCApiNetwork.get("sc/stat/his/inpatient/work/report/ward", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clazz: InpatientWorkReportRsp,
            clearUndefined: true,
            useBody: true,
        });
    }

    /**
     * 医院统计-营收概况
     * @param params
     */
    static async queryStatRevenueHospitalSummary(params: HospitalStatRevenueReq): Promise<StatSummaryData> {
        const { beginDate, endDate, includeReg, ...others } = params;
        return await ABCApiNetwork.get("sc/stat/revenue/overview/hospital/summary", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                includeReg: includeReg ? 1 : 0,
                ...others,
            },
            clearUndefined: true,
            clazz: StatSummaryData,
        });
    }

    /**
     * 获取医院统计-开单业绩
     * @param params
     */
    static async queryStatAchievementChargeAll(params: BillingPerformanceReq): Promise<BillingPerformanceData[]> {
        const { beginDate, endDate, ...others } = params;
        return await ABCApiNetwork.get("sc/stat/achievement/charge/all/list-for-app", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clearUndefined: true,
        });
    }

    /**
     * 医院统计--体检患者分析
     * @param params
     */
    static async queryStatPhysicalPatientAnalysis(params: BaseStatReq): Promise<PhysicPatientAnalysisData> {
        const { beginDate, endDate, ...others } = params;
        return ABCApiNetwork.get("sc/stat/his/pe/order/patient/analyse", {
            queryParameters: {
                beginDate: TimeUtils.formatDate(beginDate ?? new Date()),
                endDate: TimeUtils.formatDate(endDate ?? new Date()),
                ...others,
            },
            clearUndefined: true,
            clazz: PhysicPatientAnalysisData,
        });
    }
}
