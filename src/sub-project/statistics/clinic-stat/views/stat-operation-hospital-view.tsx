import { SateSummeryIncomeLineChartView, StatOperationView, StatOperationViewProps } from "../stat-operation-view";
import { Text, View, ScrollView } from "@hippy/react";
import React from "react";
import { FilterGroupId } from "../../../base-ui/searchBar/search-bar";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { PercentItemView, StatItem } from "../../views/stat-views";
import { GridView } from "../../../base-ui/views/grid-view";
import { userCenter } from "../../../user-center";
import { IconFontView, SizedBox } from "../../../base-ui";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { FeatureAuthority } from "../../../user-center/data/constants";
import { DINAlternateFontFamily, incomeTrendLabelDataList, RevenueOverviewDepartmentResp } from "../../data/statistics-bean";
import abcI18Next from "../../../language/config";
import { AbcView } from "../../../base-ui/views/abc-view";
import { PerformanceStatDialog } from "../../views/performance-stat-layout-dialog";
import { AbcEmptyItemView } from "../../../base-ui/views/empty-view";
import { AbcBasePanel } from "../../../base-ui/abc-app-library";
import { StatTitleBarView } from "./stat-title-bar-view";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { DepartmentIncomeDialog } from "./department-income-dialog";
import _ from "lodash";

export class StatOperationHospitalView extends StatOperationView {
    constructor(props: StatOperationViewProps) {
        super(props);
        //  如果日期存在，默认为传入日期
        this._handleDate();
    }
    _handleDate(): void {
        const date = this.props?.date;
        if (!!date) {
            // 因为没有id，无法确认是哪个时间筛选，所以直接指定为自定义时间筛选
            const timeFilters = this.filters.filters?.find((t) => t.id == FilterGroupId.time)?.filters;
            timeFilters?.forEach((item) => {
                if (item.timeRange) {
                    item.select = false;
                } else {
                    item.select = true;
                    item.timeRange = {
                        start: date,
                        end: date,
                    };
                }
            });
        }
    }

    /**
     * 计算不包含小数点的长度
     * @param count
     * @private
     */
    private getIntegerLength(count: string | number): number {
        const countStr = String(count);
        if (isNaN(Number(countStr))) return 0;
        return countStr.includes(".") ? countStr.length - 1 : countStr.length; // 调整逻辑，直接计算整数部分长度
        // 注意：上述逻辑对于小数点后的数字长度处理可能不是完全准确的，如果需要精确到小数点后的位数，需要另外的逻辑
    }

    // 判断位数是否大于8（不考虑小数点后的部分）
    private isLargerThan8(count: string | number): boolean {
        return this.getIntegerLength(count) > 8;
    }

    /**
     * 总体收入概览
     */
    _renderOverviewTotalIncomeView(): JSX.Element {
        const isOpenPhysicalSystem = userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.PHYSICAL_EXAMINATION);
        const { hospitalStatSummary, arrearsStatTimingStr } = this.bloc.currentState;
        const {
            totalAmount = 0,
            rechargeOutpatient = 0,
            rechargeRetail = 0,
            hospitalAmount = 0,
            peChargeAmount = 0,
            preDepositAmount = 0,
            rechargeMember = 0,
            rechargePrincipalMember = 0, // 会员充值金额(本金)
            rechargePresentMember = 0, // 会员充值金额(赠金)
            promotionCardRecharge = 0,
            promotionCardRechargePrincipal = 0, // 卡项充值金额(本金)
            promotionCardRechargePresent = 0, // 卡项充值金额(赠金)
            depositAmount = 0,
        } = hospitalStatSummary || {};
        // 字体是否需要缩小
        const isTotalAmountFsSmall =
                this.isLargerThan8(totalAmount) ||
                this.isLargerThan8(rechargeOutpatient) ||
                this.isLargerThan8(rechargeRetail) ||
                this.isLargerThan8(hospitalAmount) ||
                this.isLargerThan8(peChargeAmount),
            isAdvancePaymentFsSmall =
                this.isLargerThan8(preDepositAmount) ||
                this.isLargerThan8(rechargeMember) ||
                this.isLargerThan8(promotionCardRecharge) ||
                this.isLargerThan8(depositAmount);
        const list: {
            title: string;
            value: number;
            fontSmall: boolean;
            onClick: () => void;
            data: { title: string; count: number; show?: boolean; principal?: number; present?: number }[];
        }[] = [
            {
                title: "总收入",
                value: totalAmount,
                fontSmall: isTotalAmountFsSmall,
                onClick: () => {
                    PerformanceStatDialog.showPerformanceStatLayoutDialog({
                        title: "总收入",
                        content: arrearsStatTimingStr,
                    }).then();
                },
                data: [
                    {
                        title: "门诊",
                        count: rechargeOutpatient,
                    },
                    {
                        title: "零售",
                        count: rechargeRetail,
                    },
                    {
                        title: "住院",
                        count: hospitalAmount,
                    },
                    {
                        title: "体检",
                        count: peChargeAmount,
                        show: isOpenPhysicalSystem,
                    },
                ],
            },
            {
                title: "预缴金",
                value: preDepositAmount,
                fontSmall: isAdvancePaymentFsSmall,
                onClick: () => {
                    PerformanceStatDialog.showPerformanceStatLayoutDialog({
                        title: "预缴金",
                        content: "预缴金=会员充值+卡项充值+住院押金",
                    }).then();
                },
                data: [
                    {
                        title: "会员充值",
                        count: rechargeMember,
                        principal: rechargePrincipalMember,
                        present: rechargePresentMember,
                    },
                    {
                        title: "卡项充值",
                        count: promotionCardRecharge,
                        principal: promotionCardRechargePrincipal,
                        present: promotionCardRechargePresent,
                    },
                    {
                        title: "住院押金",
                        count: depositAmount,
                    },
                ],
            },
        ];

        return (
            <>
                {list?.map((item, keyIndex) => {
                    const indexList = item.data?.filter((subItem) => subItem.show !== false);
                    return (
                        <AbcBasePanel
                            key={item.title}
                            panelStyle={{
                                backgroundColor: Colors.mainColor,
                                marginBottom: keyIndex < list.length - 1 ? Sizes.dp16 : 0,
                                padding: Sizes.dp16,
                            }}
                            contentStyle={ABCStyles.rowAlignCenter}
                        >
                            <View style={{ width: Sizes.dp109 }}>
                                <View style={{ flexDirection: "row", alignItems: "flex-end" }}>
                                    <Text style={[TextStyles.t12NW, { fontWeight: "700", lineHeight: Sizes.dp18 }]}>
                                        {abcI18Next.t("￥")}
                                    </Text>
                                    <Text
                                        style={[
                                            DINAlternateFontFamily,
                                            TextStyles.t20BW.copyWith({
                                                lineHeight: Sizes.dp20,
                                                fontWeight: "700",
                                                fontSize: item.fontSmall ? Sizes.dp16 : Sizes.dp20,
                                            }),
                                        ]}
                                    >
                                        {`${ABCUtils.formatPrice(item?.value ?? 0)}`}
                                    </Text>
                                </View>
                                <SizedBox height={Sizes.dp8} />
                                <AbcView style={[ABCStyles.rowAlignCenter]} onClick={() => item.onClick()}>
                                    <Text style={[TextStyles.t12NW, { opacity: 0.7 }]}>{item.title}</Text>
                                    <SizedBox width={Sizes.dp4} />
                                    <IconFontView name={"information"} size={Sizes.dp14} color={"rgba(255,255,255,0.7)"} />
                                </AbcView>
                            </View>
                            <View style={{ flex: 1, position: "relative" }}>
                                <View style={{ width: Sizes.dpHalf, backgroundColor: Colors.divingLineColor, ...ABCStyles.absoluteFill }} />
                                <GridView
                                    style={{ flex: 1, paddingLeft: Sizes.dp16 }}
                                    crossAxisCount={2}
                                    mainAxisSpacing={Sizes.dp16}
                                    crossAxisSpacing={Sizes.dp16}
                                >
                                    {indexList?.map((subItem, index) => {
                                        return (
                                            <StatItem
                                                key={index}
                                                title={subItem.title}
                                                count={subItem.count}
                                                style={{ padding: 0 }}
                                                countStyle={{
                                                    fontWight: "700",
                                                    color: Colors.white,
                                                    lineHeight: Sizes.dp20,
                                                    fontSize: item.fontSmall ? Sizes.dp16 : Sizes.dp20,
                                                }}
                                                titleStyle={[TextStyles.t12NW, { opacity: 0.7 }]}
                                                showNum={false}
                                                dollarStyle={{ fontWight: "700", lineHeight: Sizes.dp18 }}
                                                principal={subItem.principal}
                                                present={subItem.present}
                                            />
                                        );
                                    })}
                                </GridView>
                            </View>
                        </AbcBasePanel>
                    );
                })}
            </>
        );
    }

    private _convertUndefinedDepartmentTo(data: RevenueOverviewDepartmentResp[]): RevenueOverviewDepartmentResp[] {
        const convertData = _.cloneDeep(data);
        const undeterminedDepartment = convertData?.filter((item) => !item.name), //未确定科室
            undeterminedDepartmentValue =
                undeterminedDepartment?.reduce((prev, next) => {
                    return prev + (next.value ?? 0);
                }, 0) ?? 0;
        if (undeterminedDepartment?.length) {
            convertData.push({
                name: "未确定科室",
                value: undeterminedDepartmentValue,
            });
        }
        return convertData;
    }

    /**
     * 科室收入分析(需要降序，过滤掉没有收入的项(包括0))
     */
    _renderDepartmentIncomeAnalysisView(): JSX.Element {
        const { hospitalStatSummary } = this.bloc.currentState;
        let totalFee = 0;
        if (!hospitalStatSummary?.departments?.length) return <View />;
        hospitalStatSummary?.departments?.forEach((item) => {
            totalFee += item?.value ?? 0;
        });
        const departmentIncomeList = hospitalStatSummary?.departments?.filter((item) => !!item?.value);
        const convertDepartmentList = this._convertUndefinedDepartmentTo(departmentIncomeList);
        return (
            <>
                {this._renderPercentageView({
                    title: "科室收入分析",
                    totalFee,
                    dataList: convertDepartmentList || [],
                    isShowMore: convertDepartmentList?.length > 5,
                })}
            </>
        );
    }

    /**
     * 收入细分
     */
    _renderIncomeSegmentationView(): JSX.Element {
        const views: JSX.Element[] = [];
        const { hospitalStatSummary } = this.bloc.currentState;
        if (!hospitalStatSummary?.feeTypes?.length) return <View />;
        hospitalStatSummary.feeTypes?.forEach((item, index) => {
            views.push(
                <StatItem
                    key={index}
                    title={item.name ?? "-"}
                    count={item.value}
                    showNum={false}
                    style={{ backgroundColor: Colors.bg1, borderRadius: Sizes.dp6, paddingHorizontal: Sizes.dp12 }}
                    countStyle={TextStyles.t16BB.copyWith({ fontWeight: "700", lineHeight: Sizes.dp18 })}
                    titleStyle={TextStyles.t12NT6}
                    dollarStyle={{ fontWeight: "700", color: Colors.black, lineHeight: Sizes.dp16 }}
                />
            );
        });

        return (
            <AbcBasePanel panelStyle={{ marginTop: Sizes.dp16, ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20) }}>
                <StatTitleBarView title={"费用分类"} />
                <GridView style={{ borderRadius: Sizes.dp6 }} crossAxisCount={3} crossAxisSpacing={Sizes.dp8} mainAxisSpacing={Sizes.dp8}>
                    {views}
                </GridView>
            </AbcBasePanel>
        );
    }

    /**
     * 支付方式
     */
    _renderPaymentModeView(): JSX.Element {
        const { hospitalStatSummary } = this.bloc.currentState;
        let totalFee = 0;
        if (!hospitalStatSummary?.payModes?.length) return <View />;
        hospitalStatSummary.payModes?.forEach((item) => {
            totalFee += item.value;
        });
        return <>{this._renderPercentageView({ title: "支付方式", totalFee, dataList: hospitalStatSummary.payModes || [] })}</>;
    }

    _checkMoreInfo(): void {
        const state = this.bloc.currentState,
            { hospitalStatSummary } = state;
        let totalFee = 0;
        hospitalStatSummary?.departments?.forEach((item) => {
            totalFee += item.value ?? 0;
        });
        const convertDepartmentList = this._convertUndefinedDepartmentTo(hospitalStatSummary?.departments ?? [])?.filter((t) => !!t?.name);
        showBottomPanel(<DepartmentIncomeDialog title={"科室收入分析"} totalFee={totalFee} data={convertDepartmentList} />, {
            topMaskHeight: Sizes.dp160,
        }).then();
    }

    _renderPercentageView(options: { title: string; dataList: any[]; totalFee: number; isShowMore?: boolean }): JSX.Element {
        const { title, dataList, totalFee, isShowMore } = options;
        const data = (() => {
            if (isShowMore) {
                return dataList?.filter((item) => !!item.name).slice(0, 5);
            }
            return dataList?.filter((item) => !!item.name);
        })();
        return (
            <AbcBasePanel panelStyle={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20), { marginTop: Sizes.dp16 }]}>
                <StatTitleBarView title={title} isShowMore={!!isShowMore} onClick={() => this._checkMoreInfo()} />
                {data?.length ? (
                    data?.map((item, index) => (
                        <PercentItemView
                            key={index}
                            name={item.name ?? "-"}
                            percent={item.value > 0 ? item.value / totalFee : 0}
                            amount={item.value}
                            drawTopLine={false}
                            style={{ marginBottom: index == data.length - 1 ? 0 : Sizes.dp24 }}
                        />
                    ))
                ) : (
                    <AbcEmptyItemView
                        tips={"暂无数据"}
                        style={{
                            paddingTop: 0,
                            ...ABCStyles.centerChild,
                            borderRadius: Sizes.dp6,
                            marginTop: Sizes.dp16,
                            marginBottom: Sizes.dp34,
                        }}
                    />
                )}
            </AbcBasePanel>
        );
    }

    _renderSateSummeryIncomeLabelView(): JSX.Element {
        const labels = incomeTrendLabelDataList;
        const { incomeTrendLabelType } = this.bloc.currentState;
        return (
            // 顶部时间标签选择
            <View style={{ flexDirection: "row" }}>
                {labels.map((item, index) => {
                    const select = incomeTrendLabelType == item.type;
                    return (
                        <AbcView key={index} onClick={() => this.bloc.requestUpdateIncomeTrendLabelType(item.type)}>
                            <View
                                style={[
                                    Sizes.paddingLTRB(Sizes.dp7, Sizes.dp1),
                                    {
                                        justifyContent: "center",
                                        marginLeft: Sizes.dp4,
                                        borderRadius: Sizes.dp2,
                                        backgroundColor: select ? Colors.theme2Mask8 : Colors.bg1,
                                    },
                                ]}
                            >
                                <Text style={[select ? TextStyles.t14BM : TextStyles.t14Nt2, { lineHeight: Sizes.dp20 }]}>
                                    {item.title}
                                </Text>
                            </View>
                        </AbcView>
                    );
                })}
            </View>
        );
    }

    /**
     * 收入趋势
     */
    _renderSateSummeryIncomeView(): JSX.Element {
        const { statDailyRevenueList } = this.bloc.currentState;
        if (!statDailyRevenueList)
            return (
                <AbcEmptyItemView
                    tips={"暂无数据"}
                    style={{
                        paddingTop: 0,
                        ...ABCStyles.centerChild,
                        borderRadius: Sizes.dp6,
                        marginTop: Sizes.dp16,
                        marginBottom: Sizes.dp34,
                    }}
                />
            );
        return (
            <AbcBasePanel panelStyle={{ marginTop: Sizes.dp16, marginBottom: Sizes.dp34, ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp20) }}>
                <StatTitleBarView title={"收入趋势"} rightContent={this._renderSateSummeryIncomeLabelView()} />
                <SateSummeryIncomeLineChartView />
            </AbcBasePanel>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this._renderFilterView()}
                <ScrollView
                    style={{ paddingHorizontal: Sizes.dp8, backgroundColor: Colors.transparent }}
                    showsVerticalScrollIndicator={false}
                >
                    {this._renderOverviewTotalIncomeView()}
                    {this._renderDepartmentIncomeAnalysisView()}
                    {this._renderIncomeSegmentationView()}
                    {this._renderPaymentModeView()}
                    {this._renderSateSummeryIncomeView()}
                </ScrollView>
            </View>
        );
    }
}
