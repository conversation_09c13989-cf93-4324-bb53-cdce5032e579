/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-07-24
 *
 * @description
 */

import { Bloc, BlocEvent } from "../bloc";
import React from "react";
import { actionEvent, EventName } from "../bloc/bloc";
import { Clinic, userCenter } from "../user-center/user-center";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ClinicAgent } from "../base-business/data/clinic-agent";
import { ABCError } from "../common-base-module/common-error";
import { showConfirmDialog } from "../base-ui/dialog/dialog-builder";
import _ from "lodash";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { errorSummary } from "../common-base-module/utils";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { URLProtocols } from "../url-dispatcher";
import { pluginManager } from "../plugin/plugin-manager";
import { abcNetDelegate } from "../net/abc-net-delegate";
import { runtimeConstants } from "../data/constants/runtime-constants-manager";

export enum ClinicDisplayItemType {
    groupTitle,
    normalClinic,
    singleClinic,
}

export class ClinicDisplayItemData {
    clinicId?: string;
    title?: string;
    clinic?: Clinic;
    type?: ClinicDisplayItemType;
    isGroupLast = false;
    namePy?: string;
    namePyFirst?: string;

    constructor(
        clinicId?: string,
        title?: string,
        clinic?: Clinic,
        type?: ClinicDisplayItemType,
        isGroupLast?: boolean,
        namePy?: string,
        namePyFirst?: string
    ) {
        this.clinicId = clinicId;
        this.title = title;
        this.clinic = clinic;
        this.type = type;
        this.isGroupLast = isGroupLast ?? false;
        this.namePy = namePy;
        this.namePyFirst = namePyFirst;
    }
}

class _ClinicGroupData {
    title: string;
    clinics: _ClinicItemData[] = [];

    constructor(title: string) {
        this.title = title;
    }
}

class _ClinicItemData {
    clinicId?: string;
    title?: string;
    clinic?: Clinic;
    namePy?: string;
    namePyFirst?: string;

    constructor(clinicId: string, title?: string, clinic?: Clinic, namePy?: string, namePyFirst?: string) {
        this.clinicId = clinicId;
        this.title = title;
        this.clinic = clinic;
        this.namePy = namePy;
        this.namePyFirst = namePyFirst;
    }
}

class State {
    loading = true;
    loadingError: any;

    chosenClinicId?: string;
    dataList?: ClinicDisplayItemData[];
    matchDataList?: ClinicDisplayItemData[];
    showSearch = false;

    clone(): State {
        return Object.assign(new State(), this);
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {}

class _EventRequestSwitchClinic extends _Event {
    clinic: ClinicDisplayItemData;

    constructor(clinic: ClinicDisplayItemData) {
        super();

        this.clinic = clinic;
    }
}

class _EventFilterSearchContent extends _Event {
    text: string;
    constructor(text: string) {
        super();
        this.text = text;
    }
}
class _EventUpdateSearchState extends _Event {}

class ClinicChangePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ClinicChangePageBloc | undefined>(undefined);

    static fromContext(context: ClinicChangePageBloc): ClinicChangePageBloc {
        return context;
    }

    private _loadDataTrigger = new Subject<number>();

    private jumpUrl?: string;

    constructor(jumpUrl?: string) {
        super();
        this.jumpUrl = jumpUrl;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventRequestSwitchClinic, this._mapEventRequestSwitchClinic);

        return map;
    }

    private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        this.innerState.chosenClinicId = userCenter.clinic?.clinicId;
        this.addDisposable(this._loadDataTrigger);
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loading = true;
                    this.innerState.loadingError = undefined;
                    this.update();

                    return ClinicAgent.getClinicList()
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((_rsp) => {
                this.innerState.loading = false;
                if (_rsp instanceof ABCError) {
                    this.innerState.loadingError = _rsp.detailError;
                    this.update();
                    return;
                }

                if (_rsp.length == 0) {
                    showConfirmDialog(
                        `没有加入${runtimeConstants.PRODUCT_NAME_THUMB}`,
                        `你的账号未加入任何${runtimeConstants.PRODUCT_NAME_THUMB}，请先登录电脑端进行创建`
                    ).then(() => userCenter.logout());
                    return;
                }

                const clinicGroupMap = new Map<String, _ClinicGroupData>();

                //过滤掉连锁单店模式下的总部
                const rsp = _rsp.filter((clinic) => clinic.isChainSubClinic || clinic.isNormalClinic || clinic.isChainAdminClinic);
                rsp.forEach((clinic) => {
                    const groupName = clinic.isNormalClinic ? "" : clinic.chainName ?? "";
                    if (!clinicGroupMap.has(groupName)) {
                        const groupData = new _ClinicGroupData(groupName);
                        clinicGroupMap.set(groupName, groupData);
                    }
                    clinicGroupMap
                        .get(groupName)!
                        .clinics.push(
                            new _ClinicItemData(clinic.clinicId, clinic.displayName, clinic, clinic?.namePy, clinic?.namePyFirst)
                        );
                });
                const clinicGroupList: _ClinicGroupData[] = [];
                clinicGroupMap.forEach((groupData, groupName) => {
                    if (groupName != "") {
                        clinicGroupList.push(groupData);
                    }
                });
                if (clinicGroupMap.has("")) {
                    clinicGroupList.push(clinicGroupMap.get("")!);
                }

                const clinicDisplayItemDataList: ClinicDisplayItemData[] = [];
                clinicGroupList.forEach((group) => {
                    if (group.title === "") {
                        group.clinics.forEach((clinic) => {
                            clinicDisplayItemDataList.push(
                                new ClinicDisplayItemData(
                                    clinic.clinicId,
                                    clinic.title,
                                    clinic.clinic,
                                    ClinicDisplayItemType.singleClinic,
                                    clinic == _.last(group.clinics),
                                    clinic.namePy,
                                    clinic.namePyFirst
                                )
                            );
                        });
                    } else {
                        clinicDisplayItemDataList.push(
                            new ClinicDisplayItemData(undefined, group.title, undefined, ClinicDisplayItemType.groupTitle, false)
                        );
                        group.clinics.forEach((clinic) => {
                            clinicDisplayItemDataList.push(
                                new ClinicDisplayItemData(
                                    clinic.clinicId,
                                    clinic.title,
                                    clinic.clinic,
                                    ClinicDisplayItemType.normalClinic,
                                    clinic == _.last(group.clinics),
                                    clinic.namePy,
                                    clinic.namePyFirst
                                )
                            );
                        });
                    }
                });

                this.innerState.dataList = clinicDisplayItemDataList;
                this.innerState.matchDataList = _.cloneDeep(this.innerState.dataList);
                this.update();
            })
            .addToDisposableBag(this);

        this._loadDataTrigger.next(0);
    }

    private async *_mapEventUpdate(/*ignored: _EventUpdate*/): AsyncGenerator<State> {
        yield this.innerState.clone();
    }

    private async *_mapEventRequestSwitchClinic(event: _EventRequestSwitchClinic): AsyncGenerator<State> {
        const loading = new LoadingDialog("正在切换...");
        loading.show(1000);
        try {
            const rsp = await userCenter.postClinicSwitch(event.clinic.clinicId!);
            if (_.isEmpty(rsp.clinic?.modulesIdList())) {
                await loading.fail("没有权限");
                return;
            }

            //切换诊所后，触发deviceLogin,在这里会下发灰度标记，preparePluging再根据此标记判断是否要下载灰度或正式的升级包
            await abcNetDelegate.deviceLogin(true).catchIgnore();
            const pluginInfo = await pluginManager.preparePlugin("abcyun-hippy", { showLoading: false }).catchIgnore();
            await loading.hide();
            if (this.jumpUrl) {
                return ABCNavigator.navigateToPage(this.jumpUrl ?? URLProtocols.ABCURL_HOME, {
                    clearStack: true,
                }).then();
            }

            if (!pluginInfo || !pluginInfo.haveReload) {
                ABCNavigator.pop();
            }
        } catch (e) {
            await loading.fail("切换失败：" + errorSummary(e));
        }
    }

    @actionEvent(_EventUpdateSearchState)
    async *_mapEventUpdateSearchState(): AsyncGenerator<State> {
        this.innerState.showSearch = true;
        this.update();
    }

    @actionEvent(_EventFilterSearchContent)
    async *_mapEventFilterSearchContent(event: _EventFilterSearchContent): AsyncGenerator<State> {
        const keyWord = event?.text;
        this.innerState.matchDataList = !!keyWord
            ? this.innerState.dataList?.filter(
                  (t) =>
                      t.title?.toLocaleLowerCase()?.includes(keyWord?.toLocaleLowerCase()) ||
                      t.namePy?.toLocaleLowerCase().includes(keyWord?.toLocaleLowerCase()) ||
                      t.namePyFirst?.toLocaleLowerCase().includes(keyWord?.toLocaleLowerCase())
              )
            : this.innerState.dataList;
        this.update();
    }
    public update(): void {
        this.dispatch(new _EventUpdate());
    }

    public requestReloadData(): void {
        this._loadDataTrigger.next(0);
    }

    public requestSwitchClinic(data: ClinicDisplayItemData): void {
        this.dispatch(new _EventRequestSwitchClinic(data));
    }
    public requestFilterSearchContent(text: string): void {
        this.dispatch(new _EventFilterSearchContent(text));
    }
    public requestUpdateSearchState(): void {
        this.dispatch(new _EventUpdateSearchState());
    }
}

export { ClinicChangePageBloc, State };
