/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/7/11
 *
 * @description
 *
 */
import { BasePage, IconFontView } from "../base-ui";
import { View } from "@hippy/react";
import React from "react";
import { BottomNavigationBar, BottomNavigationBarItem } from "../base-ui/bottom-navigation-bar";
import { Color, Colors } from "../theme";
import { HomePage } from "./home-page";
import { UserCenterPage } from "../user-center/user-center-page";
import { NotificationPage } from "../notifications/notification-page";
import { bootLoader } from "../boot/boot-loader";
import { userCenter } from "../user-center";
import { upgradeDataManager, UpgradeInfo } from "../upgrade/upgrade";
import { MsgCategory, NotificationDataManager } from "../data/notification";
import WillPopListener from "../base-ui/views/will-pop-listener";
import { Toast } from "../base-ui/dialog/toast";
import { TimeUtils, UrlUtils } from "../common-base-module/utils";
import { AbcPlatformMix } from "../base-business/native-modules/abc-platform-mix";
import { statEvent } from "../base-business/stat/stat-manager";
import { urlDispatcher } from "../url-dispatcher/url-dispatcher";
import { URLProtocols } from "../url-dispatcher";

interface HomeTabPageProps {}

export class HomeTabPage extends BasePage {
    private _tabItems: _HomeTabItem[] = [
        {
            id: _TabId.tabIdHome,
            title: "首页",
            icon: "Home",
            badge: 0,

            pageBuilder: () => {
                return <HomePage />;
            },
        },
        {
            id: _TabId.tabIdMsg,
            title: "消息",
            icon: "my_message",
            badge: 0,
            pageBuilder: () => {
                return <NotificationPage />;
            },
        },
        {
            id: _TabId.tabIdUserCenter,
            title: "我",
            icon: "Me",
            badge: 0,
            pageBuilder: () => {
                return <UserCenterPage />;
            },
        },
    ];

    private _currentTabIndex: number;

    private _unreadMsgCount = 0;

    private _upgradeInfo?: UpgradeInfo | null;

    private _bottomNavigationBarRef?: BottomNavigationBar | null;
    private lastBackPressedTime?: Date;

    constructor(props: HomeTabPageProps) {
        super(props);
        this._currentTabIndex = _TabId.tabIdHome;
    }

    pageName(): string | undefined {
        return "HomePageTab";
    }

    componentDidMount(): void {
        super.componentDidMount();
        this._loadClinicConfig();

        const clinicChangeObserver = userCenter.sClinicChangeObserver.subscribe((/*data*/) => {
            this._loadClinicConfig();
        });
        this.addDisposable(clinicChangeObserver);

        const notificationDisposable = NotificationDataManager.sObserver.subscribe((rsp) => {
            const oldUnReadMsgCount = this._unreadMsgCount;
            this._unreadMsgCount = rsp.getNewMsgCount;

            if (oldUnReadMsgCount != this._unreadMsgCount) {
                const tabItem = this._tabItems.find((item) => item.id == _TabId.tabIdMsg);
                tabItem!.badge = this._unreadMsgCount;
                this.setState({});
            }
        });
        this.addDisposable(notificationDisposable);

        NotificationDataManager.getLatestMsgSummary();

        upgradeDataManager.upgradeObserver
            .subscribe((data) => {
                this._upgradeInfo = data;
                this._updateRedPoint();
            })
            .addToDisposableBag(this);

        //监听通知栏打开事件，对打开消息中心进行处理
        urlDispatcher.addListener((url) => {
            if (url.startsWith(URLProtocols.MSG_PAGE)) {
                this._switchToTab(_TabId.tabIdMsg);
                const params = UrlUtils.getUrlParams(url);
                if (params.size) {
                    if (params.get("category") == MsgCategory.categoryTodo.toString()) {
                        const { ABCNavigator } = require("../base-ui/views/abc-navigator");
                        const { NotificationListPage } = require("../notifications/notification-list-page");
                        ABCNavigator.navigateToPage(<NotificationListPage msgType={MsgCategory.categoryTodo} title={"待办通知"} />);
                    }
                }
                return true;
            }
            return false;
        });

        //触发一次更新
        upgradeDataManager.getUpgradeInfo();

        //用户更新后
        //全局弹窗注册弹窗
        userCenter.sUpdatedUserCenterObserver
            .subscribe(async () => {
                this._loadClinicConfig();
                const { ExpirePaymentDialog } = require("../views/expire-payment-dialog/expire-payment-dialog");
                await ExpirePaymentDialog.show();
                const { AbcPayAdDialog } = require("../views/login-success-dialog/abc-pay-ad-dialog");
                const { AbcDoubleElevenDialog } = require("../views/login-success-dialog/abc-double-eleven-dialog");
                const { AbcDoubleTwelfthDialog } = require("../views/login-success-dialog/abc-double-twelfth-dialog");
                // 11.5-11.30 期间弹双11活动弹窗，但是如果11月1日-11月30日期间，门店已下单则不弹双十一
                const isPlacedOrder = !!userCenter.mallOrderDetailRsp?.created
                    ? TimeUtils.isCorrespondingMonthRange(11, 1, 30, userCenter.mallOrderDetailRsp?.created)
                    : false;
                //  12月10-12月31 期间弹双12活动弹窗，但是如果这期间已下单则不弹双十二
                const isPlaceOrderByDoubleTwelfth = !!userCenter.mallOrderDetailRsp?.created
                    ? TimeUtils.isCorrespondingMonthRange(12, 10, 31, userCenter.mallOrderDetailRsp?.created)
                    : false;
                if (TimeUtils.isCorrespondingMonthRange(11, 5, 30) && !isPlacedOrder) {
                    await AbcDoubleElevenDialog.show();
                } else if (TimeUtils.isCorrespondingMonthRange(12, 10, 31) && !isPlaceOrderByDoubleTwelfth) {
                    await AbcDoubleTwelfthDialog.show();
                } else {
                    await AbcPayAdDialog.show();
                }
            })
            .addToDisposableBag(this);
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getBottomSafeAreaColor(): Color {
        return Colors.white;
    }

    onBackClick(): void {
        if (!this.lastBackPressedTime) {
            this.lastBackPressedTime = new Date();
            Toast.show("再按一次退出应用").then((res) => {
                if (!res) {
                    AbcPlatformMix.exitApp();
                    this.lastBackPressedTime = undefined;
                }
            });
            return;
        }

        const now = new Date();
        const seconds = TimeUtils.difference(this.lastBackPressedTime, now).inSeconds;

        if (seconds < 3) {
            AbcPlatformMix.exitApp();
            this.lastBackPressedTime = undefined;
            return;
        }

        this.lastBackPressedTime = now;
        Toast.show("再按一次退出应用").then();
    }

    renderContent(): JSX.Element | undefined {
        bootLoader.onHomePageShow();
        const items: BottomNavigationBarItem[] = this._tabItems.map((item, index) => {
            return {
                title: item.title,
                icon: <IconFontView key={index} name={item.icon} />,
                activeIcon: <IconFontView key={index} name={item.icon} color={Colors.mainColor} />,
                badge: item.badge,
            };
        });
        return (
            <View style={{ flex: 1 }}>
                <WillPopListener
                    onWillPop={() => {
                        this.onBackClick();
                    }}
                />
                <View style={{ flex: 1 }}>{this._tabItems[this._currentTabIndex].pageBuilder(this._tabItems[this._currentTabIndex])}</View>
                <BottomNavigationBar
                    ref={(ref) => {
                        this._bottomNavigationBarRef = ref;
                    }}
                    items={items}
                    currentIndex={this._currentTabIndex}
                    onSelectChanged={(index) => this._onTabItemChanged(index)}
                />
            </View>
        );
    }

    //选择了不同的页面
    private _onTabItemChanged(index: number) {
        this._currentTabIndex = index;
        statEvent("homepage_tab", { map: { tabid: index.toString() } });
        this.setState({});
    }

    //拉取诊所的基本页面配置
    private async _loadClinicConfig() {
        this._tabItems = [
            {
                id: _TabId.tabIdHome,
                title: "首页",
                icon: "Home",
                badge: 0,

                pageBuilder: () => {
                    return <HomePage />;
                },
            },
            {
                id: _TabId.tabIdMsg,
                title: "消息",
                icon: "my_message",
                badge: 0,

                pageBuilder: () => {
                    return <NotificationPage />;
                },
            },
            {
                id: _TabId.tabIdUserCenter,
                title: "我",
                icon: "Me",
                badge: 0,

                pageBuilder: () => {
                    return <UserCenterPage />;
                },
            },
        ];

        if (userCenter.clinicEdition?.isExpired) {
            this._tabItems = this._tabItems.filter((item) => item.id != _TabId.tabIdMsg);
        }

        this.forceUpdate();
    }

    private _updateRedPoint(): void {
        const tabItem = this._tabItems?.find((item) => item.id == _TabId.tabIdUserCenter);
        tabItem!.badge = this._upgradeInfo ? 1 : 0;

        this.setState({});
    }

    //切换到指定tab页面
    private _switchToTab(tabId: _TabId): void {
        const index = this._tabItems.findIndex((item) => item.id == tabId);
        if (index >= 0) this._bottomNavigationBarRef?.setCurrentIndex(index);
    }
}

enum _TabId {
    tabIdHome,
    tabIdMsg,
    tabIdUserCenter,
}

interface _HomeTabItem {
    id: _TabId;
    tabId?: number;
    title: string;
    icon: string;
    badge: number;

    pageBuilder(item?: _HomeTabItem): JSX.Element;
}
