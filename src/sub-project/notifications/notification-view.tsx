/**
 * create by deng<PERSON><PERSON>
 * desc: 消息中心卡片相关组件
 * create date 2020/7/23
 */
import React from "react";
import { Image, StyleSheet, Text, View } from "@hippy/react";
import { BaseComponent } from "../base-ui/base-component";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { DividerLine, SizedBox, Spacer } from "../base-ui";
import { ListSettingItem, ListSettingItemStyle } from "../base-ui/views/list-setting-item";
import { AbcView } from "../base-ui/views/abc-view";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { ABCUtils } from "../base-ui/utils/utils";
import { MessageListItem, MsgType } from "../data/notification";
import { userCenter } from "../user-center";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { WebviewPage } from "../base-ui/webview-page";
import { NumberUtils, TimeUtils } from "../common-base-module/utils";
import { InventoryAddListPage } from "../inventory/inventory-check/full-check/inventory-add-list-page";
import URLProtocols from "../url-dispatcher/url-protocols";
import { StatSummaryCardType } from "../statistics/data/statistics-bean";
import { RegistrationTabType } from "../registration/data/bean";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import abcI18Next from "../language/config";
import { RightArrowView } from "../base-ui/iconfont/iconfont-view";
import { GridView } from "../base-ui/views/grid-view";
import { FeatureAuthority } from "../user-center/data/constants";
import { isNil } from "lodash";
import { pxToDp } from "../base-ui/utils/ui-utils";

interface NotificationSystemCardViewProps {
    cardInfo?: MessageListItem;
}

//@ts-ignore
const notificationBaseStyle = StyleSheet.create({
    baseContainer: {
        borderWidth: 0.5,
        borderColor: Colors.P5,
        borderRadius: Sizes.dp6,
        paddingHorizontal: Sizes.listHorizontalMargin,
        backgroundColor: Colors.white,
    },
});

export class NotificationSystemCardView extends BaseComponent<NotificationSystemCardViewProps> {
    constructor(props: NotificationSystemCardViewProps) {
        super(props);
    }

    // 将“0”转化为“今”
    private zeroDayDisplayToday(title = ""): string {
        return title?.replace(/(?:^|[^0-9])(0)(?!\d)/g, "今");
    }

    render(): JSX.Element {
        const cardInfo = this.props.cardInfo;
        return (
            <AbcView
                style={[notificationBaseStyle.baseContainer, { paddingTop: Sizes.dp10 }]}
                onClick={() => {
                    // 如果链接中以abcyun开头则需要跳转小程序，以http开头跳转H5，如果link不存在且id存在，则需要发起请求获取富文本
                    const link = cardInfo?.messageBody?.data?.link;
                    if (!!link) {
                        if (link.startsWith("abcyun://")) {
                            ABCNavigator.navigateToPage(link);
                        } else {
                            ABCNavigator.navigateToPage(<WebviewPage uri={link} />).then();
                        }
                    } else {
                        if (!!cardInfo?.messageBody?.data?.id) {
                            ABCNavigator.navigateToPage(`${URLProtocols.SYSTEM_NOTICE}?id=${cardInfo?.messageBody?.data?.id}`);
                        }
                    }
                }}
            >
                <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp24 }]}>
                    {this.zeroDayDisplayToday(cardInfo?.messageBody?.title)}
                </Text>
                <SizedBox height={Sizes.dp8} />
                <Text style={TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20, color: Colors.t2 })} numberOfLines={3}>
                    {cardInfo?.messageBody?.content ?? ""}
                </Text>
                <SizedBox height={Sizes.dp18} />
                {!!cardInfo?.messageBody?.canJumpDetails && (
                    <View>
                        <DividerLine lineHeight={Sizes.dpHalf} />
                        <ListSettingItem
                            title={"详情"}
                            titleStyle={TextStyles.t14NT2.copyWith({ color: Colors.t2 })}
                            itemStyle={ListSettingItemStyle.expandIcon}
                            height={Sizes.dp38}
                        />
                    </View>
                )}
            </AbcView>
        );
    }
}

interface NotificationTodoCardViewProps {
    cardInfo?: MessageListItem;
}

export class NotificationTodoCardView extends BaseComponent<NotificationTodoCardViewProps> {
    constructor(props: NotificationTodoCardViewProps) {
        super(props);
    }
    private _onClick(): void {
        const cardInfo = this.props.cardInfo;
        if (cardInfo?.messageBody?.title == "盘点任务") {
            ABCNavigator.navigateToPage(<InventoryAddListPage />);
        } else if (cardInfo?.messageBody?.title == "随访任务") {
            ABCNavigator.navigateToPage(`${URLProtocols.PATIENT_REVISIT_DETAIL}?chainId=${cardInfo.chainId}&clinicId=${cardInfo.clinicId}`);
        } else if (cardInfo?.messageBody?.title == "预约提醒") {
            ABCNavigator.navigateToPage(`${cardInfo?.messageBody.action}`);
        } else if (cardInfo?.messageBody) {
            ABCNavigator.navigateToPage(`${cardInfo?.messageBody.action}`);
        }
    }

    render(): JSX.Element {
        const cardInfo = this.props.cardInfo;
        return (
            <AbcView
                style={[notificationBaseStyle.baseContainer, { paddingTop: Sizes.dp10 }]}
                onClick={() => {
                    this._onClick();
                }}
            >
                <Text style={[TextStyles.t16MT1, { lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                    {cardInfo?.messageBody?.title ?? ""}
                </Text>
                <SizedBox height={Sizes.dp8} />
                <Text style={TextStyles.t14NT2} numberOfLines={3}>
                    {cardInfo?.messageBody?.content ?? ""}
                </Text>
                <SizedBox height={Sizes.dp18} />
                <DividerLine lineHeight={0.5} />
                <ListSettingItem title={"详情"} titleStyle={TextStyles.t14NT2} itemStyle={ListSettingItemStyle.expandIcon} />
            </AbcView>
        );
    }
}

/**
 * 工作日报
 */
interface NotificationWorkStatProps {
    cardInfo?: MessageListItem;
}

export class NotificationWorkStat extends BaseComponent<NotificationWorkStatProps> {
    constructor(props: NotificationWorkStatProps) {
        super(props);
    }

    _renderInfo(title: string, content: string[]): JSX.Element {
        return (
            <View style={{ flexDirection: "row" }}>
                <Text style={[TextStyles.t12NB1, { color: "#C8A078", lineHeight: Sizes.dp20 }]}>{`${title}： `}</Text>
                <View style={{ flex: 1 }}>
                    {content.map((text, index) => {
                        return (
                            <Text key={index} style={[TextStyles.t12NB1, { color: "#C8A078", lineHeight: Sizes.dp20 }]}>
                                {text}
                            </Text>
                        );
                    })}
                </View>
            </View>
        );
    }

    private displayWeeksAndDaysCount(cardInfo?: MessageListItem): string {
        const countStat = cardInfo?.messageBody?.data?.countStat;

        const displayWeeksAndDayCount: string[] = [];
        TimeUtils.WeeksCountRecordKey.map((item) => {
            const content = countStat?.[item.key];
            if ((countStat?.[item.key] ?? 0) > 0) {
                displayWeeksAndDayCount.push(`${item.keyStr}${content}人`);
            }
        });

        return displayWeeksAndDayCount.join("，");
    }

    private _renderInventoryWarnView(): JSX.Element {
        const { cardInfo } = this.props || {};
        const { date, stockWarnShortageCount = 0, expiredWarnCount = 0, profitRatWarnCount = 0 } = cardInfo?.messageBody?.data || {};
        const inventoryWarnIndex = [
            {
                title: "库存不足",
                value: stockWarnShortageCount,
            },
            {
                title: "效期预警",
                value: expiredWarnCount,
            },
            {
                title: "毛利异常",
                value: profitRatWarnCount,
            },
        ];
        const warnInfo = JSON.stringify(cardInfo?.messageBody?.data);
        return (
            <AbcView
                style={[notificationBaseStyle.baseContainer, { paddingVertical: Sizes.dp12 }]}
                onClick={() => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.INVENTORY_WARN}?clinicId=${cardInfo?.clinicId}&chainId=${cardInfo?.chainId}&warnInfo=${warnInfo}`
                    );
                }}
            >
                <View style={ABCStyles.rowAlignCenter}>
                    <Text style={[TextStyles.t12NT6, { marginRight: Sizes.dp24 }]}>{`库存预警  ${date ?? ""}`}</Text>
                    <Spacer />
                    <Text style={[TextStyles.t12NT6, { flexShrink: 1 }]} numberOfLines={1}>
                        {cardInfo?.messageBody?.data?.clinicName ?? ""}
                    </Text>
                </View>
                <SizedBox height={Sizes.dp14} />
                <GridView itemHeight={Sizes.dp24} crossAxisCount={3} showNetLine={false} showBottomLine={false} mainAxisSpacing={0}>
                    {inventoryWarnIndex?.map((item, index) => {
                        return (
                            <View key={index} style={ABCStyles.rowAlignCenter}>
                                <Text style={TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24 })}>{item.title}</Text>
                                <SizedBox width={Sizes.dp2} />
                                <Text style={TextStyles.t16MY2.copyWith({ lineHeight: Sizes.dp24 })}>{item.value}</Text>
                            </View>
                        );
                    })}
                </GridView>
            </AbcView>
        );
    }

    render(): JSX.Element {
        const cardInfo = this.props.cardInfo;

        let _topTitle = "工作日报",
            _topDate = cardInfo?.messageBody?.data?.date,
            _title = "",
            _content = "",
            _onClick;
        const sourceType = cardInfo?.messageBody?.data?.countStat?.registrationType == RegistrationTabType.registration ? 0 : 1;

        switch (cardInfo?.messageBody?.data?.msgType) {
            case MsgType.doctorReport:
                _title = `接诊患者 ${cardInfo?.messageBody?.data?.outpatientCount} 人次`;
                _content = `开出处方 ${cardInfo?.messageBody?.data?.prescriptionCount} 张`;
                _onClick = () => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.outpatientDailyReport}&clinicId=${cardInfo?.clinicId}&chainId=${cardInfo?.chainId}&date=${cardInfo?.messageBody?.data?.date}`
                    );
                }; // 门诊日报
                break;
            case MsgType.cashierReport:
                _title = `完成收费 ${cardInfo?.messageBody?.data?.sheetCount ?? 0} 笔`;
                _content = `门诊 ${cardInfo?.messageBody?.data?.outpatientSheetCount ?? 0} 笔，零售 ${
                    cardInfo?.messageBody?.data?.directChargeSheetCount ?? 0
                } 笔，充值 ${cardInfo?.messageBody?.data?.memberRechargeSheetCount ?? 0} 笔`;
                _onClick = () => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.chargeDailyReport}&clinicId=${cardInfo?.clinicId}&chainId=${cardInfo?.chainId}&date=${cardInfo?.messageBody?.data?.date}`
                    ); // 收银日报
                };
                break;
            case MsgType.nurseReport:
                _title = `执行工作 ${
                    (cardInfo?.messageBody?.data?.registrationCount ?? 0) + (cardInfo.messageBody.data?.executeCount ?? 0)
                } 件`;
                _content = `挂号 ${cardInfo?.messageBody?.data?.registrationCount} 人次，治疗执行 ${cardInfo?.messageBody?.data?.executeCount} 人次`;
                break;
            case MsgType.therapyReport:
                _title = `执行项目 ${cardInfo?.messageBody?.data?.executeCount ?? 0}项`;
                _content = `项目实收 ${abcI18Next.t("￥")}${NumberUtils.formatMaxFixed(cardInfo?.messageBody?.data?.actualAmount ?? 0, 2)}`;
                _onClick = () => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.STAT_PERFORMANCE}?type=${StatSummaryCardType.treatmentAndPhysiotherapy}&clinicId=${cardInfo?.clinicId}&chainId=${cardInfo?.chainId}&date=${cardInfo?.messageBody?.data?.date}`
                    ); // 理疗师日报
                };
                break;
            case MsgType.reservedDailyReport:
                // 预约日报推送
                _topTitle = "今日预约";
                const countStat = cardInfo?.messageBody?.data?.countStat;
                _title = `${countStat?.count} 人预约`;
                _content = this.displayWeeksAndDaysCount(cardInfo);
                _onClick = () => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.REGISTRATION_TAB}?doctorId=${cardInfo?.messageBody?.data?.countStat?.employeeId}&clinicId=${cardInfo?.clinicId}&chainId=${cardInfo?.chainId}&startTime=${_topDate}&endTime=${_topDate}&tab=${sourceType}`
                    ); // 预约日报看板
                };
                break;
            case MsgType.reservedWeeklyReport:
                // 预约周报推送
                _topTitle = "下周预约";
                const stringTime = cardInfo?.messageBody?.data?.date;
                const date = new Date(Date.parse(stringTime ?? new Date().toString())); // 字符串转Date类型
                const getNextWeekStartDate = date;
                const getNextWeekEndDate = new Date(
                    getNextWeekStartDate.getFullYear(),
                    getNextWeekStartDate.getMonth(),
                    getNextWeekStartDate.getDate() + 6
                );
                _topDate = `${getNextWeekStartDate?.format("MM-dd")}~${getNextWeekEndDate?.format("MM-dd")}`;
                _title = `${cardInfo?.messageBody.data.countStat?.count} 人预约`;
                _content = this.displayWeeksAndDaysCount(cardInfo);
                _onClick = () => {
                    ABCNavigator.navigateToPage(
                        `${URLProtocols.REGISTRATION_TAB}?doctorId=${cardInfo?.messageBody?.data?.countStat?.employeeId}&clinicId=${
                            cardInfo?.clinicId
                        }&chainId=${cardInfo?.chainId}&startTime=${getNextWeekStartDate.format(
                            "yyyy-MM-dd"
                        )}&endTime=${getNextWeekStartDate.format("yyyy-MM-dd")}&tab=${sourceType}`
                    ); // 预约周报看板(日历筛选暂不支持时间范围 这里endTime默认截止到周一)
                };
                break;
        }
        if (cardInfo?.messageBody?.data?.msgType == MsgType.inventoryWarning) {
            return this._renderInventoryWarnView();
        }
        return (
            <AbcView style={[notificationBaseStyle.baseContainer, { paddingTop: Sizes.dp14 }]} onClick={_onClick}>
                <View style={ABCStyles.rowAlignCenter}>
                    <Text style={[TextStyles.t12NT6, { marginRight: Sizes.dp24 }]}>{`${_topTitle}  ${_topDate}`}</Text>
                    <Spacer />
                    <Text style={[TextStyles.t12NT6, { flexShrink: 1 }]} numberOfLines={1}>
                        {cardInfo?.messageBody?.data?.clinicName ?? ""}
                    </Text>
                </View>
                <SizedBox height={Sizes.dp14} />
                <View style={{ flexDirection: "row" }}>
                    <View style={{ flex: 1 }}>
                        <Text style={TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24 })}>{_title}</Text>
                        <SizedBox height={Sizes.dp5} />
                        <Text style={TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })}>{_content}</Text>
                    </View>
                    <View
                        style={{
                            overflow: "hidden",
                            borderWidth: 0.5,
                            borderColor: Colors.P5,
                            borderRadius: Sizes.dp3,
                            width: DeviceUtils.isAndroid() ? Sizes.dp40 : undefined,
                            height: DeviceUtils.isAndroid() ? Sizes.dp40 : undefined,
                        }}
                    >
                        {userCenter.employee?.headImgUrl ? (
                            <Image
                                source={{ uri: userCenter.employee?.headImgUrl ?? "" }}
                                style={{
                                    width: Sizes.dp40,
                                    height: Sizes.dp40,
                                }}
                            />
                        ) : (
                            <AssetImageView
                                name={"avatar_doctor"}
                                style={{
                                    width: Sizes.dp40,
                                    height: Sizes.dp40,
                                }}
                            />
                        )}
                    </View>
                </View>
                <SizedBox height={Sizes.dp13} />
                <View>
                    {!!cardInfo?.messageBody?.data?.shifts?.length &&
                        this._renderInfo(
                            "明天",
                            cardInfo?.messageBody?.data?.shifts?.map((item) => {
                                return item.displayDetail;
                            }) ?? []
                        )}
                    {!!cardInfo?.messageBody?.data?.appointmentCount &&
                        this._renderInfo("预约", [`${cardInfo?.messageBody?.data?.appointmentCount}人`])}
                </View>
                <SizedBox height={Sizes.dp12} />
            </AbcView>
        );
    }
}

interface NotificationBusinessStatProps {
    cardInfo?: MessageListItem;
}

export class NotificationBusinessStat extends BaseComponent<NotificationBusinessStatProps> {
    constructor(props: NotificationBusinessStatProps) {
        super(props);
    }

    _renderIncomeCompare(): JSX.Element {
        const messageInfo = this.props.cardInfo?.messageBody,
            messageData = messageInfo?.data,
            dailyUp = messageData?.dailyUp;
        //老带新活动日报
        const isOldNewActivity = messageData?.msgType == MsgType.oldBringNewDailyReport,
            isOrderCloudDaily = messageData?.msgType == MsgType.msgTypeDailyPharmacyEcOperateReport;
        if (isOldNewActivity && !messageData?.newAddPatientYesterdayCompare) return <View />;
        const iconName = (() => {
            if (isOldNewActivity || isOrderCloudDaily) {
                return messageData?.newAddPatientYesterdayCompareUp ? "trend_up" : "trend_down";
            } else {
                return dailyUp ? "trend_up" : "trend_down";
            }
        })();
        const message = (() => {
            if (isOldNewActivity) return messageData?.newAddPatientYesterdayCompare;
            else if (isOrderCloudDaily) return messageData?.totalAmountYesterdayCompare;
            return `总收入较昨日${dailyUp ? "上升" : "下降"} ${(messageData?.dailyUpPercent ?? 0)?.toFixed(1)}%`;
        })();
        return (
            <View style={[ABCStyles.rowAlignCenter, { height: Sizes.listItemHeight }]}>
                <AssetImageView name={iconName} style={{ width: Sizes.dp20, height: Sizes.dp20 }} />
                <Text style={TextStyles.t12NT2}>{message}</Text>
                <Spacer />
                {!isOldNewActivity && (
                    <View style={{ justifyContent: "center", marginTop: Sizes.dp3 }}>
                        <RightArrowView color={Colors.t3} />
                    </View>
                )}
            </View>
        );
    }
    private _keyIndicatorList(): { name: string; value: string }[] {
        const messageInfo = this.props.cardInfo?.messageBody,
            messageData = messageInfo?.data;
        //老带新活动日报
        const isOldNewActivity = messageData?.msgType == MsgType.oldBringNewDailyReport,
            isOrderCloudDaily = messageData?.msgType == MsgType.msgTypeDailyPharmacyEcOperateReport, //药店订单云日报;
            isDrugstoreButler = messageData?.isDrugstoreButler, // 药店管家日报
            isNormalHospital = messageData?.isHospital;
        let keyIndexList: { name: string; value: string }[] = [];
        if (isOldNewActivity) {
            keyIndexList = [
                {
                    name: "新增客户",
                    value: `${messageData?.newAddPatientCount ?? 0}人`,
                },
            ];
        } else if (isOrderCloudDaily) {
            keyIndexList = [
                {
                    name: "实收金额",
                    value: ABCUtils.formatPriceWithRMB(messageData?.actualPrice ?? 0, false),
                },
            ];
        } else if (isNormalHospital) {
            keyIndexList = [
                {
                    name: "总收入",
                    value: ABCUtils.formatPriceWithRMB(messageData?.totalFee ?? 0, false),
                },
                {
                    name: "预缴金",
                    value: ABCUtils.formatPriceWithRMB(messageData?.prepaidFee ?? 0, false),
                },
            ];
        } else if (isDrugstoreButler) {
            keyIndexList = [
                {
                    name: "营业收入",
                    value: ABCUtils.formatPriceWithRMB(messageData?.totalFee ?? 0, false),
                },
                {
                    name: "会员充值",
                    value: ABCUtils.formatPriceWithRMB(messageData?.rechargeFee ?? 0, false),
                },
                {
                    name: "营业毛利",
                    value: ABCUtils.formatPriceWithRMB(messageData?.grossProfit ?? 0, false),
                },
                {
                    name: "营业毛利率",
                    value: `${messageData?.grossProfitRate ?? 0}%`,
                },
            ];
        } else {
            keyIndexList = [
                {
                    name: "总收费",
                    value: ABCUtils.formatPriceWithRMB(messageData?.totalFee ?? 0, false),
                },
                {
                    name: "会员充值",
                    value: ABCUtils.formatPriceWithRMB(messageData?.rechargeFee ?? 0, false),
                },
            ];
        }
        return keyIndexList;
    }
    private _secondaryIndicatorList(): { name: string; value: string | number; show?: boolean }[] {
        const messageInfo = this.props.cardInfo?.messageBody,
            messageData = messageInfo?.data;
        //老带新活动日报
        const isOldNewActivity = messageData?.msgType == MsgType.oldBringNewDailyReport,
            isOrderCloudDaily = messageData?.msgType == MsgType.msgTypeDailyPharmacyEcOperateReport, //药店订单云日报;
            isDrugstoreButler = messageData?.isDrugstoreButler, // 药店管家日报
            isNormalHospital = messageData?.isHospital;
        let secondaryIndexList: { name: string; value: string | number; show?: boolean }[] = [];
        if (isOldNewActivity) {
            secondaryIndexList = [
                {
                    name: "到店人数",
                    value: messageData?.arrivalPeopleCount ?? 0,
                },
                {
                    name: "消费人数",
                    value: messageData?.payPeopleNumber ?? 0,
                },
                {
                    name: "消费金额",
                    value: `${abcI18Next.t("¥")} ${(messageData?.patientPayAmount ?? 0).toFixed(2)}`,
                },
            ];
        } else if (isOrderCloudDaily) {
            // 订单云的判断必须在药店之前，否则会被药店管家覆盖，因为订单云也是药店管家
            secondaryIndexList = [
                {
                    name: "订单数",
                    value: messageData?.orderNumber ?? 0,
                },
                {
                    name: "订单金额",
                    value: `${abcI18Next.t("¥")} ${(messageData?.orderAmount ?? 0)?.toFixed(2)}`,
                },
                {
                    name: "退款数",
                    value: messageData?.refundOrderNumber ?? 0,
                },
                {
                    name: "退款金额",
                    value: `${abcI18Next.t("¥")} ${(messageData?.refundOrderAmount ?? 0)?.toFixed(2)}`,
                },
            ];
        } else if (isDrugstoreButler) {
            secondaryIndexList = [
                {
                    name: "会员贡献收入",
                    value: ABCUtils.formatPriceWithRMB(messageData?.memberPayFee ?? 0, false),
                },
                {
                    name: "会员充值人次",
                    value: messageData?.memberRechargePeopleNumber ?? 0,
                },
                {
                    name: "消费人次",
                    value: messageData?.directCount ?? 0,
                },
                {
                    name: "会员消费人次",
                    value: messageData?.memberPayPeopleNumber ?? 0,
                },
            ];
        } else if (isNormalHospital) {
            secondaryIndexList = [
                {
                    name: "就诊人次",
                    value: messageData?.physicalExaminationCount ?? 0,
                },
                {
                    name: "住院人数",
                    value: messageData?.hospitalCount ?? 0,
                },
                {
                    name: "门诊收入",
                    value: `${abcI18Next.t("¥")} ${(messageData?.outpatientFee ?? 0)?.toFixed(2)}`,
                },
                {
                    name: "零售收入",
                    value: `${abcI18Next.t("¥")} ${(messageData?.directFee ?? 0)?.toFixed(2)}`,
                },
                {
                    name: "住院收入",
                    value: `${abcI18Next.t("¥")} ${(messageData?.hospitalFee ?? 0)?.toFixed(2)}`,
                },
                {
                    name: "体检收入",
                    value: `${abcI18Next.t("¥")} ${(messageData?.physicalExaminationFee ?? 0)?.toFixed(2)}`,
                    show: userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.PHYSICAL_EXAMINATION),
                },
            ];
        } else {
            secondaryIndexList = [
                {
                    name: "门诊客流",
                    value: messageData?.outpatientCount ?? 0,
                },
                {
                    name: "门诊收费",
                    value: `${abcI18Next.t("¥")} ${(messageData?.outpatientFee ?? 0)?.toFixed(2)}`,
                },
                {
                    name: "零售客流",
                    value: messageData?.directCount ?? 0,
                },
                {
                    name: "零售收费",
                    value: `${abcI18Next.t("¥")} ${(messageData?.directFee ?? 0)?.toFixed(2)}`,
                },
            ];
        }
        return secondaryIndexList;
    }
    private _renderTitleView(): JSX.Element {
        const messageInfo = this.props.cardInfo?.messageBody,
            messageData = messageInfo?.data;
        //老带新活动日报
        const isOldNewActivity = messageData?.msgType == MsgType.oldBringNewDailyReport,
            isOrderCloudDaily = messageData?.msgType == MsgType.msgTypeDailyPharmacyEcOperateReport;
        const title = (() => {
            if (isOldNewActivity || isOrderCloudDaily) return messageInfo?.title;
            return `${messageInfo?.title}  ${messageData?.date}`;
        })();
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t12NT2}>{title ?? ""}</Text>
                <Spacer />
                <Text style={TextStyles.t12NT2}>{`${messageData?.clinicName ?? ""}`}</Text>
            </View>
        );
    }
    private _keyIndicatorView(): JSX.Element {
        if (!this._keyIndicatorList()?.length) return <View />;
        return (
            <GridView crossAxisCount={2} crossAxisSpacing={Sizes.dp16} itemWidth={pxToDp(311)}>
                {this._keyIndicatorList()?.map((item, index) => {
                    return (
                        <View key={index} style={ABCStyles.rowAlignCenter}>
                            <Text style={TextStyles.t14MB}>{item?.name ?? ""}</Text>
                            <SizedBox width={Sizes.dp4} />
                            <Text style={TextStyles.t18MY2}>{item?.value}</Text>
                        </View>
                    );
                })}
            </GridView>
        );
    }
    private _secondaryIndexView(): JSX.Element {
        if (!this._secondaryIndicatorList().length) return <View />;
        return (
            <GridView crossAxisCount={2} crossAxisSpacing={Sizes.dp16} mainAxisSpacing={Sizes.dp6} itemWidth={pxToDp(311)}>
                {this._secondaryIndicatorList()
                    ?.filter((t) => (isNil(t.show) ? true : t.show))
                    ?.map((item, index) => {
                        return (
                            <View key={index} style={ABCStyles.rowAlignCenter}>
                                <Text style={TextStyles.t12NT2}>{item.name ?? ""}</Text>
                                <SizedBox width={Sizes.dp8} />
                                <Text style={TextStyles.t14NT2}>{item.value}</Text>
                            </View>
                        );
                    })}
            </GridView>
        );
    }

    render(): JSX.Element {
        const cardInfo = this.props.cardInfo;
        const messageInfo = this.props.cardInfo?.messageBody,
            messageData = messageInfo?.data;
        //老带新活动日报
        const isOldNewActivity = messageData?.msgType == MsgType.oldBringNewDailyReport,
            isOrderCloudDaily = messageData?.msgType == MsgType.msgTypeDailyPharmacyEcOperateReport;
        return (
            <AbcView
                style={[notificationBaseStyle.baseContainer, { paddingTop: Sizes.dp10 }]}
                onClick={() => {
                    if (isOldNewActivity) return;
                    const urlProtocol = isOrderCloudDaily ? URLProtocols.ORDER_CLOUD : URLProtocols.STAT_CLINIC_PERFORMANCE;
                    //TODO 区分是否当前门店管理员？查看详情：查看详情-不可修改日期
                    // 如果chainId与clinicId相同时，将chainId置为null，否则跳转的时候会找不到当前门店，导致报错
                    let chainId = cardInfo?.chainId;
                    if (cardInfo?.chainId == cardInfo?.clinicId) chainId = "";
                    ABCNavigator.navigateToPage(
                        `${urlProtocol}?chainId=${chainId}&clinicId=${cardInfo?.clinicId}&date=${messageData?.date}`
                    );
                }}
            >
                {this._renderTitleView()}
                <SizedBox height={Sizes.dp12} />
                {this._keyIndicatorView()}
                <SizedBox height={Sizes.dp14} />
                <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp12 }]}>{this._secondaryIndexView()}</View>
                <DividerLine lineHeight={Sizes.dpHalf} />
                {this._renderIncomeCompare()}
            </AbcView>
        );
    }
}
