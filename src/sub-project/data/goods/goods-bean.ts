import { dateToYyyyMMddString, fromJsonToDate, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { GoodsInfo, GoodsType } from "../../base-business/data/beans";

export class CutGoodsItem {
    goodsId?: string; //要拉取goods的Id
    packageCount?: number; //请求扣库的数量,负数表示要扣库（在开单场景）packageCount/pieceCount只可能有一个有值
    pieceCount?: number; //请求扣库的数量,负数表示要扣库（在开单场景）packageCount/pieceCount只可能有一个有值
}
export class GoodsStocksBatchesReq {
    clinicId?: string; //指定门店Id，不指定为全连锁
    @JsonProperty({ type: Array, clazz: CutGoodsItem })
    goodsList?: CutGoodsItem[];
    pharmacyNo?: number; //扣库了，必须要前端指定药房号
}
class SimpleGoodsLockInfo {
    lockId?: string; //锁库ID,需要注意可能同时存在多个一样lockId的记录，分多个批次锁了
    lockingPieceCount?: number; //客户端原始请求锁库的数量,如果有多个lockId一样，这些lockId的所有 lockingPieceCount/lockingPackageCount一样
    lockingPackageCount?: number; //户端原始请求锁库的数量,如果有多个lockId一样，这些lockId的所有 lockingPieceCount/lockingPackageCount一样
    lockLeftTotalPieceCount?: number; //本lockId，本batchId对应的锁库数量
    batchId?: string; //如果是按批次锁库，批次的ID信息
    clinicId?: string; //批次所属门店
    pharmacyType?: number; //批次所属药房类型
    pharmacyNo?: number; //药房号
}
class GoodsBatchesList {
    batchId?: string; //批次Id 数库库自增长ID，不是雪花算法Id
    clinicId?: string; //批次所属门店
    pharmacyType?: number;
    pharmacyNo?: number;
    @JsonProperty({ toJson: dateToYyyyMMddString })
    expiryDate?: Date;
    @JsonProperty({ toJson: dateToYyyyMMddString })
    productionDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date;
    supplierId?: string; //供应商
    supplierName?: string;
    //batchNo是 用户输入的可读的批次号
    // stockId是系统自动生成用于区分批次的号码
    // stockId = id：老的nodejs上是先插入stock记录，在插入成功后再把tockId = id 设置更新一次db。
    // 猜测这样设计的原因还是想保证批次ID的不变和唯一性。唯一性通过数据库自增唯一主键来保证。
    // 所以后面批次id尽量都用这个stockId(发现老的代码里面用用id写代码的都应该改正掉)
    batchNo?: string;
    packageCostPrice?: number; //批次价格成本相关
    leftCost?: number;
    priceMakeupPercent?: number;
    packagePrice?: number;
    priceType?: number;
    pieceCount?: number; //库存量相关 总库存
    packageCount?: number;
    dispGoodsCount?: string;
    stockPieceCount?: string; //门诊开单可售库存量: (总库存 - 禁售库存 - 锁定库存)
    stockPackageCount?: string;
    dispStockGoodsCount?: string;
    availablePackageCount?: number;
    availablePieceCount?: number;
    dispOutGoodsCount?: string;
    prohibitPieceCount?: string; //禁售库存: 过期停售+手动批次停售
    prohibitPackageCount?: string;
    dispProhibitGoodsCount?: string;
    lockingPieceCount?: number; //总的锁库数量
    lockingPackageCount?: number;
    dispLockingGoodsCount?: string;
    cutPieceCount?: number; //如果是预先扣库这两个字段的库存量是本批次上能被扣走的库存量  本批次会被扣的数量 正表示扣
    cutPackageCount?: number; //本批次会被扣的数量 正表示扣
    totalSalePrice?: number; //批次总售价 = cutPackageCount * packagePrice
    @JsonProperty({ type: Array, clazz: SimpleGoodsLockInfo })
    lockInfo?: SimpleGoodsLockInfo[]; //如果指定了lockId来查goods信息，返回这个lockId对应的批次，批次上的锁库信息
    status?: number; //批次状态
    expiredWarnFlag?: number; //过期预警标志
}
export class GoodsBatchesPretendCutItem {
    @JsonProperty({ type: Array, clazz: GoodsBatchesList })
    batchList?: GoodsBatchesList[]; //有库存批次信息
    goods?: GoodsInfo;
    packageCount?: number; //请求扣库的数量
    pieceCount?: number; //请求扣库的数量
    shortagePackageCount?: number; //请求扣库的缺货数量
    shortagePieceCount?: number; //请求扣库的缺货数量
}
export enum CreateGoodsScene {
    // 用户手动输入建档
    MANUAL = 1,
    // 入库单处快捷建档
    IN_STORAGE = 2,
    // 商城入库建档
    MALL_IN_STORAGE = 3,
    // 以下值系统库存药品搜索返回后台返回，建档直接填充即可
    //  通过药品库建档-查药名
    GOODS_LIB_SEARCH = 100,
    //  通过药品库建档-查条码
    GOODS_LIB_BARCODE = 101,
    //  通过药品库建档-查追溯码
    GOODS_LIB_TRACE = 102,
    //  实施jenkins导入建档
    JENKINS_IMPORT = 200,
}
export class JsonTypeItem {
    type?: GoodsType;
    subType?: number[];
}
