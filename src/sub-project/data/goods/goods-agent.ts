/**
 * 成都字节星球科技公司
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020-04-14
 *
 * @description
 * 商品数据模块请求
 */

import { ABCApiNetwork } from "../../net";
import {
    Age,
    GoodsInfo,
    GoodsInfoMaxCost,
    GoodsMultiPriceView,
    GoodsMultiPriceViewReq,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    GoodsTypeObj,
    OnlineTreatmentUnits,
    TraceableCodeList,
} from "../../base-business/data/beans";
import { dateToYyyyMMddString, fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { userCenter } from "../../user-center";
import { SupplierItem } from "../../inventory/data/inventory-draft";
import { AnyType } from "src/sub-project/common-base-module/common-types";
import {
    ExtendData,
    GetGoodsInventoryBatchInfoRsp,
    GoodsInventoryInfo,
    InventoryExtendDataItem,
    InventoryGoodsAdjustInfo,
} from "../../inventory/data/inventory-bean";
import { GoodsBatchesPretendCutItem, GoodsStocksBatchesReq, JsonTypeItem } from "./goods-bean";
import { cloneDeep } from "lodash";
import { TraceCodeCollectRsp, TraceCodeReq } from "../../base-business/data/trace-code-beans";

export type GoodsStaticType = string | number;

export class SmartSearchMedicineRsp {
    query?: {};
    @JsonProperty({
        type: Array,
        clazz: GoodsInfo,
    })
    list?: Array<GoodsInfo>;
}

class SearchWesternMedicine {
    query?: {};
    @JsonProperty({
        type: Array,
        clazz: GoodsInfo,
    })
    list?: Array<GoodsInfo>;
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    rows?: Array<GoodsInfo>;
}
class PretendBatchCutItem {
    batchId?: number;
    // 当前已经锁了多少。
    // 不是想要锁多少
    // 扣库的时候 如果有值优先在这种批次上扣
    pieceCount?: number;
    packageCount?: number;
}
class QueryGoodsItemGoodsId {
    goodsId?: string; // 批量查询2024.07增加了按加工类型分发药房，一个单据可能有多个处方，每个处方有各自加工类型（1个goods两个form不同加工类型
    keyId?: string;
    // 本次计划要扣减的量 如果为null，即将扣减批次的成本价
    // 否则将进行库存预扣减 算一个与扣减的平均成本价出来
    // 外围Goods上的库存量，不一定等于里面 pretendBatchCutItemList 加起来的库存量
    pieceCount?: number;
    packageCount?: number;
    // 指定批次数量查询  批次选择优先级 ：指定批次 > 指定追溯码 > 指定lockId
    // 1.指定批次后 不看指定追溯码 和 指定lockId
    // 2.指定追溯码和指定lockId可以同时指定
    @JsonProperty({ type: Array, clazz: PretendBatchCutItem })
    pretendBatchCutItemList?: PretendBatchCutItem[];
    // 指定追溯码数量来查goods
    // 这样设计的考虑是：如果让前面服务带code过来，goods去查code，这个量会有点大
    // 想单于通过追溯码来查goods，由前端控制了下
    noList?: string[];
    // 锁库Id   如果是套餐的签到，只有lockId
    lockId?: number;
}
export class QueryGoodsListItem {
    pharmacyType?: number; // 药房类型,不传默认为本地药房0
    pharmacyNo?: number;
    stockCheckSuggestBatches?: number; // 推荐盘点的批次，让尽量一个整批次出 参数 1，检查goods是否需要盘点批次
    sceneType?: number; // 多库房
    processType?: number;
    processSubType?: number;
    @JsonProperty({ type: Array, clazz: QueryGoodsItemGoodsId })
    goodsIds?: QueryGoodsItemGoodsId[];
}
export class SearchGoodsByIdsReq {
    @JsonProperty({ type: Array })
    goodsIds?: Array<string>;
    pharmacyNo?: number; // 药房号，可以不传默认为药品的默认药房null已经被占用用于表达使用默认药房库存，所以这里启用-1表示汇总，如果没有多药房会转成 0号药房
    clinicId?: string; // 查询的诊所Id 如果没指定就为http头里面的诊所Id
    pharmacyType?: number; // 药房类型,不传默认为本地药房0
    sceneType?: number; // 多库房
    withShebaoCode?: number; // 是否要拉取社保信息
    @JsonProperty({ type: Array, clazz: QueryGoodsListItem })
    queryGoodsList?: QueryGoodsListItem[]; // 批量查询2024.07增加了按加工类型分发药房，一个单据可能有多个处方，每个处方有各自加工类型
    itemGoodsFirst?: boolean; // 项目goods优先，会把收费项goods换掉
}

class SearchGoodsByIdRes {
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    list?: Array<GoodsInfo>;
}

export class SearchGoodsBySpecItem {
    keyId?: string;
    medicineCadn?: string;
    resultType?: number;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
}

class SearchGoodsBySpec {
    pharmacyNo?: number;
    @JsonProperty({ type: Array, clazz: SearchGoodsBySpecItem })
    goodsList?: Array<SearchGoodsBySpecItem>;
}

export class SearchAirPharmacyMedicinesRsp {
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    rows?: Array<GoodsInfo>;
}

export class GoodsSearchErrorCode {
    static errorCodeNotFind = 12009;
    static errorCodeDisable = 12027;
}

export class SupplierItemStatus {
    static disable = 0; //禁用
    static enable = 1; //启用
}

export class GoodsPurchaseInfo {
    expiredWarnFlag?: boolean;
    shortageWarnFlag?: boolean; // 库存预警
    negativeProfitWarnFlag?: boolean; //毛利预警
    turnoverDaysWarnFlag?: boolean; // 周转天数预警
    turnoverDays?: number; // 周转天数
    warnConfigTurnOverDays?: number; //预警周转天数
    profRat?: number; //毛利
    warnConfigPackageCount?: number; //预警库存
    packageCount?: number; //库存
    packageUnit?: string;
    pieceCount?: number;
    pieceUnit?: string;
    totalCost?: number;
    recentAvgSell?: number;

    /**
     * 拼接剩余库存信息, e.g: 余30盒5片
     */
    public displayStockInfo(): string {
        let stock = "";
        const packageCount = this.packageCount ?? 0;
        const pieceCount = this.pieceCount ?? 0;

        if (packageCount != 0) {
            stock = `${packageCount}${this.packageUnit}`;
        }

        if (pieceCount != 0) {
            stock = `${stock}${pieceCount}${this.pieceUnit}`;
        }

        return stock;
    }
}
export class InventoryInItem {
    goodsId?: string;
    useUnit?: string; //入库项的入库单上填写的原始入库单位
    useCount?: number; //入库项的入库单上填写的原始入库数量
    useUnitCostPrice?: number; //入库项的入库单上填写的入库成本价
    useTotalCostPrice?: number; //入库项的入库总成本,端上的这个字段做展示用
    batchNo?: string; //入库单上的用户输入的可读的批次号

    @JsonProperty({ toJson: dateToYyyyMMddString })
    expiryDate?: Date;

    @JsonProperty({ toJson: dateToYyyyMMddString })
    productionDate?: Date;
    batchId?: string; //退货出库的batchId
    packageCount?: number; //【退货出库】退货出库为退货的数量:客户端传正，后台会按负数存储
    pieceCount?: number; //【退货出库】退货出库为退货的数量:客户端传正，后台会按负数存储
    stockInId?: string; //退货出库的InId

    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];
    @JsonProperty({ type: InventoryExtendDataItem })
    extendData?: InventoryExtendDataItem;
}

export class InventoryInReq {
    clinicId?: string;
    supplierId?: string;
    comment?: string;
    outOrderNo?: string;
    @JsonProperty({ type: Array, clazz: InventoryInItem })
    list?: InventoryInItem[];
    inspectBy?: string;

    /**
     * 药房
     */
    pharmacyNo?: number;
    inOrderDraftId?: string;
    @JsonProperty({ type: ExtendData })
    extendData?: ExtendData;
}

export class SockInRspItem {
    status?: string;
    returnPieceCount?: string;
    returnPackageCount?: string;
    id?: number;
    goods?: GoodsInfo;
    piecePrice?: number;
    packagePrice?: number;
    createdUserId?: string;
    createdDate?: string;
    lastModifiedUserId?: string;
    lastModifiedDate?: string;
    goodsId?: string;
    useUnit?: string;
    useCount?: number;
    useUnitCostPrice?: number;
    useTotalCostPrice?: number;
    batchNo?: string;
    expiryDate?: string;
    productionDate?: string;
    pieceNum?: number;
    pieceCount?: number;
    packageCount?: number;
    packageCostPrice?: number;
    orderId?: number;
}

//入库状态
export class InventoryInStatus {
    static pass = 2; //入库通过
    static waitAgree = 0; //等待审批
    static waitConfirm = 1; //待确认
    static refused = 1; //已拒绝
}

export class SearchGoodInfoWithBatchItem {
    goodsId?: string;
    batchs?: number[];
}

export class SearchGoodInfoWithBatchReq {
    pharmacyNo?: number; // 药房号
    pharmacyType?: number; // 药房类型
    list?: SearchGoodInfoWithBatchItem[];
}

export class SearchGoodsWithPaginatingRsp {
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    list?: GoodsInfo[];
    offset?: number;
    total?: number;
}

export interface GoodsSecondaryClassificationItem {
    id: number;
    name: string;
    sort: number;
}

export interface GoodsInterfaceClassificationItem {
    goodsCMSpec: string;
    goodsSubType: number;
    goodsType: number;
    id: number;
    name: string;
    sort: number;
    customTypes: GoodsSecondaryClassificationItem[];
}

export interface GoodsClassificationList {
    list: GoodsInterfaceClassificationItem[];
}

export class GoodsStocksCostrang {
    inId?: string;
    packageCostPrice?: number;
    supplier?: string;
    supplierId?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    day?: Date;
}

export class GoodsStocksCostrangRsp {
    @JsonProperty({ type: Array, clazz: GoodsStocksCostrang })
    list?: GoodsStocksCostrang[];
    @JsonProperty({ type: GoodsStocksCostrang })
    max?: GoodsStocksCostrang;
    @JsonProperty({ type: GoodsStocksCostrang })
    min?: GoodsStocksCostrang;
}

class EmployeesConfig {
    id?: string;
    name?: string;
}

export class PharmacyListItem {
    chainId?: string;
    clinicId?: string;
    no?: number;
    name?: string;
    type?: number;
    stockCutType?: number;
    sort?: number;
    status?: number;
    isDeleted?: number;
    pharmacyGoodsType?: GoodsTypeObj;
    typeName?: string;

    /*----------------------------多库房新增字段---------------------------*/
    id?: string;
    remark?: number;
    enablePurchase?: number; // 采购入库开关
    enableDispense?: number; // 调剂发药
    enablePharmacyTrans?: number; // 领用
    enableProductionOut?: number; //生产出库
    enableTrans?: number; // 调拨
    enableDepartmentOut?: number; //科室消耗
    enableOtherOut?: number; //其他
    enableLossOut?: number; //报损
    enableCheck?: number; //盘点
    members?: number; //成员数
    defaultMember?: number;
    @JsonProperty({ type: Array, clazz: EmployeesConfig })
    employees?: EmployeesConfig[];
    orderCheckStatus?: number; //盘点是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderDepartmentOutStatus?: number; //科室消耗是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderDispenseStatus?: number; //发药是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderLossOutStatus?: number; //报损是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderOtherOutStatus?: number; //其他出库是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderPharmacyTransStatus?: number; //领用是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderPurchaseStatus?: number; //采购是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    orderTransStatus?: number; //调拨是否存在数据，0=不存在5=中间状态（disable后当天还展示tab）10=存在
    /*-------------------------end-多库房新增字段-end------------------------*/

    get isVirtual(): boolean {
        return this.type == 2;
    }

    get isAirPharmacy(): boolean {
        return this.type == 1;
    }

    get isNormalPharmacy(): boolean {
        return !this.type;
    }
    //前端自定义，用来展示
    __onlyShow?: boolean;

    /**
     * 采购入库模块是否展示
     */
    get enablePurchaseIn(): boolean {
        return this.enablePurchase === 1 || (this.enablePurchase == 0 && !!this.orderPurchaseStatus);
    }

    /**
     * 采购模块仅查看不可编辑
     */
    get enablePurchaseInOnlyView(): boolean {
        return this.status == 0 || (this.enablePurchase === 0 && !!this.orderPurchaseStatus);
    }

    /**
     * 领用模块是否展示
     */
    get enablePharmacyTransIn(): boolean {
        return this.enablePharmacyTrans === 1 || (this.enablePharmacyTrans == 0 && !!this.orderPharmacyTransStatus);
    }

    /**
     * 领用模块仅查看不可编辑
     */
    get enablePharmacyTransInOnlyView(): boolean {
        return this.status == 0 || (this.enablePharmacyTrans === 0 && !!this.orderPharmacyTransStatus);
    }

    /**
     * 调拨模块是否展示
     */
    get enableTransIn(): boolean {
        return this.enableTrans === 1 || (this.enableTrans == 0 && !!this.orderTransStatus);
    }

    /**
     * 调拨模块仅查看不可编辑
     */
    get enableTransInOnlyView(): boolean {
        return this.status == 0 || (this.enableTrans === 0 && !!this.orderTransStatus);
    }

    /**
     * 报损模块是否展示
     */
    get enableLossOutIn(): boolean {
        return this.enableLossOut === 1 || (this.enableLossOut == 0 && !!this.orderLossOutStatus);
    }

    /**
     * 报损模块仅查看不可编辑
     */
    get enableLossOutInOnlyView(): boolean {
        return this.status == 0 || (this.enableLossOut === 0 && !!this.orderLossOutStatus);
    }

    /**
     * 盘点模块是否展示
     */
    get enableCheckIn(): boolean {
        return this.enableCheck === 1 || (this.enableCheck == 0 && !!this.orderCheckStatus);
    }

    /**
     * 盘点模块仅查看不可编辑
     */
    get enableCheckInOnlyView(): boolean {
        return this.status == 0 || (this.enableCheck === 0 && !!this.orderCheckStatus);
    }
}

export class GetPharmacyListRsp {
    @JsonProperty({ type: Array, clazz: PharmacyListItem })
    rows?: PharmacyListItem[];
    total?: number;
    offset?: number;
    limit?: number;
}

export class ClinicWithPharmacyType {
    id?: string;
    name?: string;
    nodeType?: number;
    shortName?: string;
    addressDetail?: string;
    @JsonProperty({ type: Array, clazz: PharmacyListItem })
    pharmacyList?: PharmacyListItem[];
}

export class ClinicListWithPharmacyType {
    pharmacyType?: number;
    @JsonProperty({ type: Array, clazz: ClinicWithPharmacyType })
    list?: ClinicWithPharmacyType[];
}

export class GetClinicListByPharmacyTypeRsp {
    @JsonProperty({ type: Array, clazz: ClinicListWithPharmacyType })
    list?: ClinicListWithPharmacyType[];
}

export class GetClinicExamTreatListRsp {
    limit?: number;
    offset?: number;
    query?: null;
    @JsonProperty({ type: Array, clazz: GoodsInfo })
    rows?: GoodsInfo[];
    total?: number;
}

let _treatmentUnits: OnlineTreatmentUnits | undefined = undefined;

export class GoodsFeeTypeListItem {
    feeTypeId?: string; // 费用类型ID ，小于为10000系统，后台会尽量让系统id对齐goods的TypeId
    scopeId?: string; // 费用类型使用范围 位与 1 库存档案 2费用项目 4 挂号费 8 加工配送
    name?: string; // 费用名称
    disable?: number; // 停用状态: 1 停用
    innerFlag?: number; // 系统内置类型
    sort?: number; // 序号，升序
    scopeName?: string; // 费用类型使用范围:库存档案、费用项目、挂号费、加工配送
}

class GetGoodsFeeTypeListRsp {
    @JsonProperty({ type: Array, clazz: GoodsFeeTypeListItem })
    rows?: GoodsFeeTypeListItem[];
}

interface PostGoodsFindMedicinesByNameInfoOptionsReqItems {
    departmentId?: string;
    jsonTypeWithCustomTypeList?: { typeId?: number[] }[];
    list?: GoodsInfo[];
    pharmacyNo?: number;
    pharmacyType?: number;
    sceneType?: number; // 默认诊所
    wardAreaId?: number; // 病区
}
interface PostGoodsFindMedicinesByNameInfoOptions {
    chainId?: string;
    clinicId?: string;
    employeeId?: string;
    disable?: number;
    withDeleted?: number;
    keyId?: string;
    reqItems: PostGoodsFindMedicinesByNameInfoOptionsReqItems[];
}
class PostGoodsFindMedicinesByNameInfoOptionsListItem {
    medicineCadn?: string;
    resultType?: number;
    keyId?: string;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
}
class PostGoodsFindMedicinesByNameInfoOptionsList {
    pharmacyNo?: number;
    @JsonProperty({ type: Array, clazz: PostGoodsFindMedicinesByNameInfoOptionsListItem })
    goodsList?: PostGoodsFindMedicinesByNameInfoOptionsListItem[];
}
class PostGoodsFindMedicinesByNameInfoOptionsRsp {
    @JsonProperty({ type: Array, clazz: PostGoodsFindMedicinesByNameInfoOptionsList })
    list?: PostGoodsFindMedicinesByNameInfoOptionsList[];
}

export class GoodsAgent {
    /**
     * 根据关键词、类型、sub类型搜索商品
     * @param keyword 关键字
     * @param types 商品类型
     * @param subTypes 附属类型
     */
    static searchAllGoods(keyword: string, types?: number[], subTypes?: number[]): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get(`goods/search/all`, {
            queryParameters: {
                key: keyword,
                type: types,
                subType: subTypes ?? "",
                clinicId: userCenter.clinic?.clinicId,
            },
            clazz: SearchWesternMedicine,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 搜索检查项目
     * @param query 搜索参数
     * @param query.keyword 关键词
     * @param query.diagnosis 诊断
     * @param query.clinicId 诊所id
     */
    static searchExamination(query: { keyword?: string; diagnosis?: string; clinicId?: string }): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get(`goods/search/examinations`, {
            queryParameters: query,
            clazz: SearchWesternMedicine,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 搜索治疗项目
     * @param params 搜索参数
     * @param params.keyword 关键词
     * @param params.diagnosis 诊断
     * @param params.clinicId 诊所id
     */
    static searchTreatments(params: { keyword?: string; diagnosis?: string; clinicId?: string }): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get("goods/search/treatments", {
            queryParameters: params,
            clazz: SearchWesternMedicine,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 搜索商品项目
     * @param params
     */
    static searchStockGoods(params: {
        keyword: string;
        jsonType?: { type?: number; subType?: number[] }[];
        types?: number[];
        subTypes?: number[];
        diagnosis?: string;
        spec?: string;
        clinicId?: string;
        pharmacyNo?: string;
        pharmacyType?: string;
    }): Promise<GoodsInfo[]> {
        const _jsonType = JSON.stringify(params.jsonType);
        return ABCApiNetwork.get("goods/search/stock-goods", {
            queryParameters: {
                keyword: params.keyword ?? "",
                diagnosis: params.diagnosis ?? "",
                spec: params.spec ?? "",
                clinicId: params.clinicId ?? userCenter.clinic?.clinicId ?? "",
                jsonType: _jsonType,
                pharmacyNo: params.pharmacyNo,
                pharmacyType: params.pharmacyNo,
            },
            clazz: SearchWesternMedicine,
            clearUndefined: true,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 搜索西药项目
     * @param keyword
     * @param clinicId
     */
    static searchWesternMedicine(keyword: string, clinicId?: string): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get("goods/search/medicines/western", {
            queryParameters: {
                key: keyword || "",
                clinicId: clinicId || "",
            },
            clazz: SearchWesternMedicine,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 搜索中药项目
     * @param keyword
     * @param clinicId
     * @param spec
     * @param pharmacyNo
     */
    static async searchChineseMedicine(keyword: string, clinicId?: string, spec?: string, pharmacyNo?: number): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get("goods/search/medicines/chinese", {
            queryParameters: {
                key: keyword || "",
                spec: spec || "",
                clinicId: clinicId || userCenter.clinic?.clinicId,
                pharmacyNo: pharmacyNo,
            },
            clearUndefined: true,
            clazz: SearchWesternMedicine,
        }).then((rsp) => rsp.list || rsp.rows || []);
    }

    /**
     * 智能搜索接口
     */
    static async smartSearchMedicine(params: {
        age?: Age;
        searchByRoot?: number;
        sex?: string;
        chiefComplaint?: string; // 主诉
        diagnosis?: string; // 诊断
        goodsIds?: [];
        offset?: number;
        limit?: number;
        keyword?: string;
        clinicId?: string;
        jsonType?: { type?: number; subType?: number[] }[];
        spec?: string;
        pharmacyNo?: string;
        pharmacyType?: string;
        needAlias?: number; // 别名
        withDomainMedicine?: number; // 所属药房
        departmentId?: string;
        sceneType?: number; //1--门诊，2--住院
    }): Promise<GoodsInfo[]> {
        return ABCApiNetwork.post("goods/search", {
            body: {
                ...params,
            },
            clazz: SmartSearchMedicineRsp,
            clearUndefined: true,
        }).then((rsp) => rsp.list ?? []);
    }

    /**
     * 根据商品id查询当前完整的信息
     * @param params
     */
    static async searchGoodsByIds(params: SearchGoodsByIdsReq): Promise<Array<GoodsInfo>> {
        const res: SearchGoodsByIdRes = await ABCApiNetwork.post("goods/search/bygoodsIds", {
            body: params,
            clazz: SearchGoodsByIdRes,
            clearUndefined: true,
        });
        return res.list ?? [];
    }

    /**
     * 根据中药类型查询中药完整信息
     * @param params
     */
    static async searchChineseMedicineGoodsBySpec(params: {
        list: Array<{
            goodsId?: string;
            keyId?: string;
            manufacturer?: string;
            medicineCadn?: string;
            cMSpec?: string;
            pieceUnit?: string;
            extendSpec?: string; //规格
        }>;
        cMSpec?: string;
        pharmacyNo?: number;
    }): Promise<Array<SearchGoodsBySpecItem>> {
        const { cMSpec, list, pharmacyNo } = params || {};
        const rep: SearchGoodsBySpec = await ABCApiNetwork.post("goods/find-chinese-medicines", {
            body: { cMSpec, list, pharmacyNo: pharmacyNo ?? 0 },
            clazz: SearchGoodsBySpec,
            clearUndefined: true,
        });
        return (
            rep.goodsList?.map((it) => ({
                ...it,
                goods:
                    it.goods ??
                    JsonMapper.deserialize(GoodsInfo, {
                        name: it.medicineCadn,
                        medicineCadn: it.medicineCadn,
                        type: GoodsType.medicine,
                        subType: GoodsSubType.medicineChinese,
                    }),
            })) ?? []
        );
    }

    /**
     * 根据条形码搜索药品信息
     */
    static searchWithInStockByQRCode(params: {
        key: string;
        withStock?: string;
        onlyStock?: string;
        types?: number | number[];
        subTypes?: number | number[];
        cMSpec?: string | string[];
        disable?: number;
        clinicId?: string;
        inorderConfig?: string;
        pharmacyNo?: number;
        typeId?: string[];
        customTypeId?: string[];
    }): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get<{ list: GoodsInfo[] }>("goods/search", {
            queryParameters: {
                key: params.key,
                withStock: params.withStock,
                onlyStock: params.onlyStock,
                clinicId: params.clinicId ?? userCenter.clinic?.clinicId ?? "",
                type: params.types,
                subType: params.subTypes,
                cMSpec: params.cMSpec,
                disable: params.disable,
                inorderConfig: params.inorderConfig,
                pharmacyNo: params.pharmacyNo,
                typeId: params.typeId,
                customTypeId: params.customTypeId,
            },
            clearUndefined: true,
        }).then((rsp) => {
            return rsp.list.map((item) => JsonMapper.deserialize(GoodsInfo, item));
        });
    }

    static searchGoodsWithPaginating(params: {
        age?: Age;
        chiefComplaint?: string;
        clinicId?: string;
        diagnosis?: string;
        jsonType?: JsonTypeItem[];
        keyword?: string;
        limit?: number;
        offset?: number;
        sex?: string;
        pharmacyNo?: number;
        departmentId?: string;
        sceneType?: number; // 门诊、门诊住院（医院）都传1，医嘱项目那里传2
    }): Promise<SearchGoodsWithPaginatingRsp> {
        return ABCApiNetwork.post("goods/search", {
            body: { offset: 20, ...params },
            clazz: SearchGoodsWithPaginatingRsp,
            clearUndefined: true,
        });
    }

    /**
     * 搜索套餐
     * @param keyword
     */
    static searchPackage(keyword: string): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get<{ list: GoodsInfo[] }>("goods/query/composite", {
            queryParameters: {
                keyword: keyword || "",
                clinicId: userCenter.clinic?.clinicId,
            },
        }).then((rsp) => rsp.list.map((item) => JsonMapper.deserialize(GoodsInfo, item)));
    }

    /**
     * 创建物资
     */
    static createGoods(info: GoodsInfo, createGoodsScene?: number): Promise<GoodsInfo> {
        const { medicineNmpnStartExpiryDate, medicineNmpnEndExpiryDate, ...others } = info;
        return ABCApiNetwork.post("goods/", {
            clazz: GoodsInfo,
            body: {
                medicineNmpnStartExpiryDate: medicineNmpnStartExpiryDate?.format("yyyy-MM-dd"),
                medicineNmpnEndExpiryDate: medicineNmpnEndExpiryDate?.format("yyyy-MM-dd"),
                ...others,
                createGoodsScene,
            },
            clearUndefined: true,
        });
    }

    /**
     * 更新物资信息
     */
    static updateGoodsInfo(info: GoodsInfo): Promise<GoodsInfo> {
        info.manufacturerFull = info.manufacturerFull ?? info.manufacturer;
        const { medicineNmpnStartExpiryDate, medicineNmpnEndExpiryDate, ...others } = info;
        return ABCApiNetwork.put("goods/" + info.id, {
            clazz: GoodsInfo,
            body: {
                medicineNmpnStartExpiryDate: medicineNmpnStartExpiryDate?.format("yyyy-MM-dd"),
                medicineNmpnEndExpiryDate: medicineNmpnEndExpiryDate?.format("yyyy-MM-dd"),
                ...others,
            },
        });
    }

    // 西药、中成药、中药 forPurchase=1 pharmacyNo=0
    // 医疗器械 forPurchase=1 withSupplier=1
    //物资 - 固定资产、后勤材料、forPurchase=1 withSupplier=
    //商品 - 自制成品 forPurchase=1 withSupplier=1
    //商品 - 保健药品 保健食品 其他商品 forPurchase=1 withSupplier=
    static getGoodsInfo(
        id: string,
        params?: {
            withStock?: number;
            clinicId?: string;
            pharmacyType?: number;
            pharmacyNo?: number;
            forPurchase?: number;
            withSupplier?: number;
        }
    ): Promise<GoodsInventoryInfo> {
        return ABCApiNetwork.get("goods/" + id, {
            clazz: GoodsInventoryInfo,
            queryParameters: {
                withShebaoCode: 1,
                withMemberTypeInfo: userCenter.clinic?.isDrugstoreButler ? 1 : undefined, // 药店需要设置成1
                ...params,
            },
            clearUndefined: true,
        });
    }

    /**
     * 获取全部供应商列表
     * @deprecated
     */
    static getSupplierList(clinicId: string): Promise<SupplierItem[]> {
        return ABCApiNetwork.get<{ rows: SupplierItem[] }>("goods/supplier/search", {
            queryParameters: {
                clinicId: clinicId,
                keyword: "",
                offset: "",
                limit: "",
                status: "",
            },
        }).then((rsp) => rsp.rows?.map((item) => JsonMapper.deserialize(SupplierItem, item)) ?? []);
    }

    /**
     * 查询空中药房药品列表
     * @param keyword
     * @param typeId 药品分类ID：14（中药饮片）；15（中药颗粒）
     * @param vendorId 供应商门店ID
     * @param withDomainMedicine 查系统库
     * @param usageScopeId 使用范围ID
     * @param vendorUsageScopeId 供应商使用范围ID
     */
    static async searchAirPharmacyMedicines(
        keyword: string,
        typeId: number,
        vendorId: string,
        withDomainMedicine: number,
        usageScopeId: string,
        vendorUsageScopeId: string
    ): Promise<GoodsInfo[] | undefined> {
        return ABCApiNetwork.get("goods/search/medicine/bis", {
            queryParameters: {
                keyword,
                typeId,
                vendorId,
                vendorUsageScopeId,
                usageScopeId,
                withDomainMedicine,
            },
            clazz: SearchAirPharmacyMedicinesRsp,
        }).then((rsp) => rsp.rows);
    }

    /**
     * 查询盘点任务列表
     * @param taskName
     * @param create
     * @param statusName
     * @param taskId
     * @param withDomainMedicine 查系统库
     */
    static async searchInventoryTask(
        taskName: string, // 任务名
        taskId: string, // 任务ID
        create: string, // 任务时间
        statusName: string, // 状态名
        withDomainMedicine: number
    ): Promise<GoodsInfo[] | undefined> {
        return ABCApiNetwork.get("goods/stock/check/cowork/tasks", {
            queryParameters: {
                taskName: taskName,
                taskId: taskId,
                create: create,
                statusName: statusName,
                withDomainMedicine,
            },
            clazz: SearchAirPharmacyMedicinesRsp,
        }).then((rsp) => rsp.rows);
    }

    static async searchGoodInfoWithBatch(params: SearchGoodInfoWithBatchReq): Promise<AnyType> {
        return ABCApiNetwork.post("goods/stocks/batches", {
            body: params,
        }).then((rsp) => {
            //@ts-ignore
            rsp.items = rsp.items?.map((item: { batch: any }) => {
                return { ...item, ...item.batch };
            });
            return rsp;
        });
    }

    static async searchGoodInfoBatch(params: {
        clinicId?: string;
        batchId?: string;
        goodsId?: string;
        limit?: number;
        offset?: number;
        /**
         * 杭州代煎代配增加字段
         */
        pharmacyNo?: number;
    }): Promise<GetGoodsInventoryBatchInfoRsp> {
        return ABCApiNetwork.get("goods/stocks/batches", {
            queryParameters: { limit: 10, ...params },
            clazz: GetGoodsInventoryBatchInfoRsp,
            clearUndefined: true,
        });
    }

    static async getGoodsInfoMaxCost(id: string): Promise<GoodsInfoMaxCost> {
        return ABCApiNetwork.get(`goods/${id}/stocks/maxcost`);
    }

    static _goodsClassification?: GoodsInterfaceClassificationItem[];
    static _goodsClassificationWithStock?: GoodsInterfaceClassificationItem[];

    static async getGoodsClassification(withCache = true): Promise<GoodsInterfaceClassificationItem[] | undefined> {
        if (withCache && this._goodsClassification?.length) return this._goodsClassification;
        return ABCApiNetwork.get<GoodsClassificationList>("goods/sys/types", { queryParameters: { needCustomType: 1 } }).then((rsp) => {
            const list = cloneDeep(rsp.list) || [];
            this.updateGoodsClassificationNames(list);
            this._goodsClassification = list;
            return list;
        });
    }
    /**
     * 药店-中药饮片替换成配方饮片
     * @param list
     * @returns
     */
    private static updateGoodsClassificationNames(list: GoodsInterfaceClassificationItem[]): GoodsInterfaceClassificationItem[] {
        if (userCenter.clinic?.isDrugstoreButler) {
            list.forEach((item) => {
                if (item.id == GoodsTypeId.medicineChinesePiece) {
                    item.name = "配方饮片";
                }
            });
        }
        return list;
    }

    static async getGoodsClassificationWithStock(withCache = true): Promise<GoodsInterfaceClassificationItem[] | undefined> {
        if (withCache && this._goodsClassificationWithStock?.length) {
            return this.updateGoodsClassificationNames(this._goodsClassificationWithStock);
        }
        return ABCApiNetwork.get<GoodsClassificationList>("goods/sys/types", { queryParameters: { queryType: 1, needCustomType: 1 } }).then(
            (rsp) => {
                const list = cloneDeep(rsp.list) || [];
                this.updateGoodsClassificationNames(list);
                //每个分类下增加一个【未指定】的分类，其id对应为一级分类的id取反
                list.forEach((item) => {
                    item.customTypes?.push({
                        id: -item.id,
                        name: "未指定",
                        sort: item.customTypes?.length,
                    });
                });
                this._goodsClassificationWithStock = list;
                return list;
            }
        );
    }

    static transGoodsClassifyName(params: { typeId?: string[]; customTypeId?: string[] }): string | undefined {
        const str: string[] = [];
        const rsp = this._goodsClassification ?? this._goodsClassificationWithStock;
        rsp?.forEach((typeItem) => {
            if (params.typeId?.some((i) => i == typeItem.id.toString())) str.push(typeItem.name);
            typeItem?.customTypes?.forEach((subItem) => {
                if (params.customTypeId?.some((i) => i == subItem.id.toString())) str.push(subItem.name);
            });
        });
        if (!str.length) return undefined;
        if (str.length > 3) {
            return `${str.slice(0, 3).join("，")}等${str.length}种类型`;
        }

        return str.filter((i) => !!i).join("，");
    }

    static getGoodsStocksCostrang(id: string, clinicId?: string): Promise<GoodsStocksCostrangRsp> {
        return ABCApiNetwork.get(`goods/${id}/stocks/costrang`, {
            queryParameters: { clinicId },
            clazz: GoodsStocksCostrangRsp,
        });
    }

    static getGoodsInfoPurchaseInfo(id: string, clinicId?: string, pharmacyNo?: number): Promise<GoodsPurchaseInfo> {
        return ABCApiNetwork.get(`goods/${id}/purchase-warn-info`, {
            queryParameters: { clinicId, pharmacyNo: pharmacyNo },
            clazz: GoodsPurchaseInfo,
            clearUndefined: true,
        }).catch(() => new GoodsPurchaseInfo());
    }

    /**
     * 加载治疗理疗单位列表
     * @param useCache
     */
    static async getGoodsCustomUnit(useCache = true): Promise<OnlineTreatmentUnits | undefined> {
        if (useCache && _treatmentUnits) return _treatmentUnits;
        return ABCApiNetwork.get(`goods/custom-unit/3`, { clazz: OnlineTreatmentUnits })
            .then((rsp) => {
                _treatmentUnits = rsp;
                return rsp;
            })
            .catchIgnore();
    }

    /**
     * 加载门店的药房列表
     */
    static async getPharmacyList(): Promise<GetPharmacyListRsp> {
        return ABCApiNetwork.get("goods/pharmacy", { clazz: GetPharmacyListRsp });
    }

    /**
     * 加载门店的药房列表
     */
    static async getClinicListByPharmacyType(params?: { pharmacyType: number }): Promise<GetClinicListByPharmacyTypeRsp> {
        return ABCApiNetwork.get("goods/pharmacy/find-clinics-by-pharmacy-type", {
            queryParameters: params,
            clazz: GetClinicListByPharmacyTypeRsp,
        });
    }

    /**
     * 获取直接收费推荐药品
     */
    static async getClinicExamTreatList(clinicId: string): Promise<GoodsInfo[]> {
        return ABCApiNetwork.get("goods/query/exam-treat", {
            queryParameters: {
                clinicId,
            },
            clazz: GetClinicExamTreatListRsp,
        }).then((rsp) => rsp.rows ?? []);
    }

    /**
     * 获取费用类别
     * @param params
     * @params.innerFlag 是否只拉内置的 1 只拉内置 1 自定义 null全部
     * @params.scopeId 费用类型范围 null 0x01 库存档案 0x02费用项目 0x04 挂号费 0x08 加工配送
     */
    static async getGoodsFeeTypeList(params?: { innerFlag?: number; scopeId?: number }): Promise<GoodsFeeTypeListItem[]> {
        return ABCApiNetwork.get("goods/fee-types", {
            queryParameters: { ...params },
            clazz: GetGoodsFeeTypeListRsp,
            clearUndefined: true,
        }).then((rps) => rps.rows ?? []);
    }

    /**
     * [查询goods的批次信息]批次信息-用于药品资料处拉取库存批次信息
     * @param params
     */
    static async queryGoodsStocksBatchesInfo(params: GoodsStocksBatchesReq): Promise<GoodsBatchesPretendCutItem[]> {
        const rsp: { rows: GoodsBatchesPretendCutItem[] } = await ABCApiNetwork.post("goods/stocks/batches/pretend-cut", {
            body: params,
        });
        return rsp?.rows ?? [];
    }

    /**
     * 批量修改状态
     * @param params
     */
    static async postGoodsBizExtensionsInfo(params: {
        list: {
            goodsId: string;
            bizExtension: {
                billingType: number;
            };
        }[];
    }): Promise<void> {
        return ABCApiNetwork.post("goods/biz-extensions", {
            body: params,
        });
    }

    /**
     * 恢复商品档案
     * @param params
     */
    static recoverGoodsArchive(params: { items: { goodsId: string; name: string }[] }): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.put("goods/recover-goods-archive", {
            body: params,
        });
    }
    /**
     * 切换药房
     * @param params
     */
    static async postGoodsFindMedicinesByNameInfo(
        params: PostGoodsFindMedicinesByNameInfoOptions
    ): Promise<PostGoodsFindMedicinesByNameInfoOptionsRsp> {
        return ABCApiNetwork.post("goods/find-medicines-by-name", {
            body: params,
            clazz: PostGoodsFindMedicinesByNameInfoOptionsRsp,
        });
    }

    /**
     * 查询商品会员价
     * @param params
     * @returns
     */
    static async calculateGoodsMemberPrice(params: GoodsMultiPriceViewReq[]): Promise<GoodsMultiPriceView[]> {
        const rsp: { list: GoodsMultiPriceView[] } = await ABCApiNetwork.post("goods/calculate-goods-member-price", {
            body: { list: params },
            clearUndefined: true,
        });
        return rsp?.list?.map((item) => JsonMapper.deserialize(GoodsMultiPriceView, item)) ?? [];
    }

    static async calculateGoodsTypeMemberPrice(params: {
        memberTypeId?: string; // 会员类型ID
        goodsType?: number; // 商品类型
        goodsSubType?: number; // 商品子类型
        goodsCMSpec?: string; // 商品CMSpec
        customTypeId?: number; // goods上的二级分类
    }): Promise<{ memberTypeId?: string; discountValue?: number }> {
        return await ABCApiNetwork.post("goods/calculate-goods-type-member-price", {
            body: params,
            clearUndefined: true,
        });
    }
    /**
     * 查询诊疗项目中手术相关的申请单
     * @param params
     */
    static async querySurgeryApplicationForm(params: {
        id: string;
        withStock?: number;
        feeComposeType?: number;
        sceneType?: number;
        departmentId?: string;
    }): Promise<GoodsInfo> {
        const { id, withStock = 1, feeComposeType, sceneType = 1, departmentId } = params;
        return ABCApiNetwork.get(`goods/${id}`, {
            queryParameters: {
                withStock,
                feeComposeType,
                sceneType,
                departmentId,
            },
            clearUndefined: true,
            clazz: GoodsInfo,
        });
    }

    /**
     * 获取调价药品详情
     * @param orderItemId
     * @returns
     */
    static async fetchPriceItemInfo(orderItemId: string): Promise<InventoryGoodsAdjustInfo> {
        return ABCApiNetwork.get(`goods/prices/tips-items/${orderItemId}`, {
            clazz: InventoryGoodsAdjustInfo,
        });
    }

    /**
     * 批量获取追溯码应采集数量
     * 功能计算当前发药的【已采应采】，【拆零标记】，以及分摊采集的追溯码数量及单位（需告诉前端如何取值）
     */
    static async getCollectCodeCountList(params?: TraceCodeReq): Promise<TraceCodeCollectRsp[]> {
        const rsp: { list: TraceCodeCollectRsp[] } = await ABCApiNetwork.post("goods/calculate/traceable-code-count", {
            body: params,
            clearUndefined: true,
        });
        return rsp?.list ?? [];
    }
}
