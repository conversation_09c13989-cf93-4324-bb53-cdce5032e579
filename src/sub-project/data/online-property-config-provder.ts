/**
 * create by dengjie
 * desc:
 * create date 2020/5/19
 */
import { ApiMixService } from "./ApiMixService";
import { JsonMapper, JsonProperty } from "../common-base-module/json-mapper/json-mapper";
import { ChargeConfig } from "../charge/data/charge-beans";
import { ABCApiNetwork } from "../net";
import { userCenter } from "../user-center";
import { clinicSharedPreferences } from "../base-business/preferences/scoped-shared-preferences";
import { DisposableTracker } from "../common-base-module/cleanup/disposable";
import { LoginEvent, Roles } from "../user-center/user-center";
import { ChainBasePropertyConfig, ClinicTraceCodeConfig } from "./online-property-config-provder-bean";
import { FeatureAuthority } from "../user-center/data/constants";

class CustomUsage {
    after?: string;
    latin?: string;
    name?: string;
    type?: number;
}

class ClinicCustomUsagesConfig {
    @JsonProperty({ type: Array, clazz: CustomUsage })
    customUsages?: CustomUsage[];
    scope?: string;
    scopeId?: string;
}

export class EmployeesData {
    id?: string;
    name?: string;
    mobile?: string;
    countryCode?: string;
    status?: number;
    created?: string;
}

//医生 查看诊疗收入配置
export enum CheckOutpatientConfig {
    allowed = 1, // 允许查看个人诊疗收入
    notAllowed = 0, // 不允许查看个人诊疗收入
}

//医生 查看挂号收入配置
export enum CheckRegistrationConfig {
    allowed = 1, // 允许查看个人挂号收入
    notAllowed = 0, // 不允许查看个人挂号收入
}

// 收费员 查看账目配置
export enum CheckAccountConfig {
    allCheck = 2, // 允许查看门店全部账目
    personalCheck = 1, // 只允许查看个人经手账目
    notCheck = 0, // 不允许查看账目
}

// 查看药品物资成本 | 查看药品物资毛利 | 查看盘点药品价格 | 查看报损药品价格 | 查看领用药品价格 | 查看调拨药品价格
export enum GoodsProfitType {
    inventoryPermission = 0, // 有库存权限可查看
    isManager = 1, // 仅管理员权限可查看
    mustReserveEmployee = 2, // 指定成员可查看
}

// 操作药品物质价格
export enum goodsAdjustPriceType {
    goodsPermission = 0, // 有商品权限可查看
    mustReserveEmployee = 2, // 指定成员操作
    isManager = 3, // 管理员/运营人员可操作
}

// 操作药品物质价格
export enum GoodsArchivesType {
    goodsPermission = 0, // 有商品权限可查看
    mustReserveEmployee = 2, // 指定成员操作
    isManager = 3, // 管理员/店长/质量负责人/质量管理员/采购负责人可操作
}

/**
 * 查看药品物资成本
 */
export class InventoryGoodsCostConfig {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsCost?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 查看药品物资毛利
 */
export class InventoryGoodsProfitConfig {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsProfit?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 查看盘点药品价格
 */
export class InventoryCheckGoodsPrice {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsProfit?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 查看报损药品价格
 */
export class InventoryDamageGoodsPrice {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsProfit?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 查看领用药品价格
 */
export class InventoryObtainGoodsPrice {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsProfit?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 查看调拨药品价格
 */
export class InventoryTransGoodsPrice {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    goodsProfit?: GoodsProfitType; // 操作药品物质价格
}

/**
 * 操作药品物质价格
 */
export class InventoryGoodsAdjustPriceConfig {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    @JsonProperty({ type: Array, clazz: Roles })
    roles?: Roles[]; // 指定角色
    goodsAdjustPrice?: goodsAdjustPriceType; // 操作药品物质价格
}

/**
 * 操作药品物质价格
 */
export class InventoryGoodsArchivesConfig {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[]; // 指定成员
    @JsonProperty({ type: Array, clazz: Roles })
    roles?: Roles[]; // 指定角色
    goodsArchives?: GoodsArchivesType; // 操作药品物质价格
}

export class InventoryDataPermission {
    goodsCostConfig?: InventoryGoodsCostConfig; // 查看药品物资成本
    goodsProfitConfig?: InventoryGoodsProfitConfig; // 查看药品物资毛利

    checkGoodsPrice?: InventoryCheckGoodsPrice; // 查看盘点药品价格
    damageGoodsPrice?: InventoryDamageGoodsPrice; // 查看报损药品价格
    obtainGoodsPrice?: InventoryObtainGoodsPrice; // 查看领用药品价格
    transGoodsPrice?: InventoryTransGoodsPrice; // 查看调拨药品价格
    goodsAdjustPriceConfig?: InventoryGoodsAdjustPriceConfig; // 操作药品物质价格
    goodsArchivesConfig?: InventoryGoodsArchivesConfig; // 操作药品物质价格
}

/**
 * 数据权限-挂号预约
 */
export enum RegistrationValueType {
    registration = 0, // 有挂号预约权限可查看
    isManager = 1, // 仅管理员权限可查看
    mustReserveEmployee = 2, // 指定成员可查看
}
export class RegistrationPatientMobile {
    value?: RegistrationValueType;
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
}
export enum RegistrationModifyPayModeType {
    permissionModify = 0, // 有收费权限可修改
    isManager = 1, // 仅管理员权限可修改
    mustReserveEmployee = 2, // 指定成员可修改
}
export class RegistrationModifyPayMode {
    value?: RegistrationModifyPayModeType;
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
}
export enum RegistrationModifyRegistrationType {
    permissionModify = 0, // 有挂号预约权限可修改
    isManager = 1, // 仅管理员权限可修改
    mustReserveEmployee = 2, // 指定成员可修改
}
export class RegistrationModifyRegistration {
    value?: RegistrationModifyRegistrationType;
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
}
export class RegistrationDataPermission {
    medicalHistory?: number; // 查看患者就诊历史（1 允许查看 0 不允许查看）
    patientMobile?: RegistrationPatientMobile; // 查看患者手机号
    modifyPayMode?: RegistrationModifyPayMode; // 修改支付样式
    modifyRegistration?: RegistrationModifyRegistration; // 修改挂号预约
}

/**
 * 数据权限-收费
 */
export enum CashierPatientMobileValueType {
    cashier = 0, // 有收费权限可查看
    isManager = 1, // 仅管理员权限可查看
    mustReserveEmployee = 2, // 指定成员可查看
}
export enum CashierModifyValueType {
    cashier = 0, // 有收费权限可修改
    isManager = 1, // 仅管理员权限可修改
    mustReserveEmployee = 2, // 指定成员可修改
}
export class CashierModifyPayMode {
    value?: CashierModifyValueType;
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
}
export class CashierPatientMobile {
    value?: CashierPatientMobileValueType;
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
}
export class CashierDataPermission {
    goodsCostPrice?: number; // 收费员查看药品进价（1 允许查看药品进价（议价时便于参考） 0 不允许查看药品进价）
    patientHistory?: number; // 收费员查看就诊历史（1 允许查看 0 不允许查看）
    patientMobile?: CashierPatientMobile; // 查看患者手机号
    modifyPayMode?: CashierModifyPayMode; // 修改支付样式
}
export class CrmDataDefined {
    @JsonProperty({ type: Array, clazz: EmployeesData })
    employees?: EmployeesData[];
    value?: number; // 0 有患者权限可编辑/(【修改患者姓名、修改患者身份证号、修改患者档案号】代表全部成员可编辑) 1 管理员权限  2 指定成员可编辑
}
export class CrmDataPermission {
    doctorPatients?: number; // 医生查看患者信息
    executorPatients?: number; // 执行人查看患者信息
    @JsonProperty({ type: CrmDataDefined })
    modifyFirstFromAway?: CrmDataDefined; // 修改首诊来源
    @JsonProperty({ type: CrmDataDefined })
    modifyIdCard?: CrmDataDefined; // 修改患者身份证号
    @JsonProperty({ type: CrmDataDefined })
    modifyName?: CrmDataDefined; // 修改患者姓名
    @JsonProperty({ type: CrmDataDefined })
    modifySn?: CrmDataDefined; // 修改患者档案号
    @JsonProperty({ type: CrmDataDefined })
    patientMobile?: CrmDataDefined; // 查看患者手机号
    @JsonProperty({ type: CrmDataDefined })
    patientPayAmount?: CrmDataDefined; // 查看患者收费金额
}

export class DashboardDataPermission {
    chargerPermission?: number; // 收费员查看账目
    doctorOutpatientFee?: number; // 医生查看个人诊疗收入
    doctorRegistrationFee?: number; // 医生查看个人挂号收入
    kanbanPermission?: number; // 医生查看患者看板 (0:只允许查看自己的患者 1：允许查看当天所有患者)
}

export enum OutpatientDataPermissionGoodsPriceType {
    allowedAll = 1, //允许查看药品明细,总价
    allowedTotalPrice = 0, //只允许查看药品总价
    notAllowed = 2, //不允许查看药品明细,总价
}

export enum OutpatientDataPermissionHistoryPrescriptionType {
    onlyMyself = 0, //只允许查看自己开出的历史处方
    allowedAll = 1, //允许查看患者所有的历史处方
    notAllowed = 2, //不允许查看
}

export class OutpatientDataPermission {
    goodsPrice?: OutpatientDataPermissionGoodsPriceType;
    historyPrescription?: OutpatientDataPermissionHistoryPrescriptionType;
    prescriptionPrice?: number;
    totalPrice?: number;
}

export class DataPermission {
    @JsonProperty({ type: DashboardDataPermission })
    dashboard?: DashboardDataPermission;
    @JsonProperty({ type: OutpatientDataPermission })
    outpatient?: OutpatientDataPermission;
    @JsonProperty({ type: InventoryDataPermission })
    inventory?: InventoryDataPermission; // 库存数据权限
    @JsonProperty({ type: RegistrationDataPermission })
    registration?: RegistrationDataPermission; // 挂号预约数据权限
    @JsonProperty({ type: CashierDataPermission })
    cashier?: CashierDataPermission; // 收费数据权限
    @JsonProperty({ type: CrmDataPermission })
    crm?: CrmDataPermission; // 患者权限

    // 医生 不允许查看个人挂号收入
    get doctorNotAllowedCheckRegistration(): boolean {
        return this.dashboard?.doctorRegistrationFee == CheckRegistrationConfig.notAllowed;
    }

    // 收费员 不允许查看账目
    get cashierNotAllowedCheckAccount(): boolean {
        return this.dashboard?.chargerPermission == CheckAccountConfig.notCheck;
    }

    // 医生 不允许查看个人诊疗收入
    get doctorNotAllowedCheckOutpatient(): boolean {
        return !userCenter.clinic?.isManager && this.dashboard?.doctorOutpatientFee == CheckOutpatientConfig.notAllowed;
    }

    // 收费无修改支付方式权限提示文本
    get cashierModifyPayModeTypeStr(): string {
        let str = "";
        switch (this.cashier?.modifyPayMode?.value) {
            case CashierModifyValueType.cashier:
                str = "有收费权限可修改";
                break;
            case CashierModifyValueType.isManager:
                str = "仅管理员权限可修改";
                break;
            case CashierModifyValueType.mustReserveEmployee:
                str = "指定成员可修改";
        }
        return str;
    }

    // 挂号无修改支付方式权限提示文本
    get registrationModifyPayModeTypeStr(): string {
        let str = "";
        switch (this.registration?.modifyPayMode?.value) {
            case RegistrationModifyPayModeType.permissionModify:
                str = "有挂号预约权限可修改";
                break;
            case RegistrationModifyPayModeType.isManager:
                str = "仅管理员权限可修改";
                break;
            case RegistrationModifyPayModeType.mustReserveEmployee:
                str = "指定成员可修改";
        }
        return str;
    }

    get billingCardTitleStr(): string {
        let billingCardTitle = "";
        if ((!this.doctorNotAllowedCheckRegistration && !this.doctorNotAllowedCheckOutpatient) || userCenter.clinic?.isManager) {
            billingCardTitle = "开单金额"; // 同时允许查看个人挂号、诊疗收入
        } else if (!this.doctorNotAllowedCheckRegistration && this.doctorNotAllowedCheckOutpatient) {
            billingCardTitle = "挂号金额"; // 仅允许查看个人挂号收入
        } else if (this.doctorNotAllowedCheckRegistration && !this.doctorNotAllowedCheckOutpatient) {
            billingCardTitle = "诊疗金额"; // 仅允许查看个人诊疗收入
        }
        return billingCardTitle;
    }
}

export class ClinicDataPermissionRsp {
    @JsonProperty({ type: DataPermission })
    dataPermission?: DataPermission;
}

class DistributionScope {
    province?: string;
    city?: string;
    district?: string;
}

export class ClinicDispensingConfig {
    @JsonProperty({ type: Array, clazz: DistributionScope })
    distributionScope?: Array<DistributionScope>;
    toHome?: number;
    isDecoction?: number;
    isTakeMedicationTime?: number; //是否开启取药时间

    get isToHome(): boolean {
        return this.toHome == 1;
    }

    /**
     * 是否开启取药时间
     */
    get isOpenTakeMedicationTime(): boolean {
        return this.isTakeMedicationTime == 1;
    }
}

export class GetClinicDispensingConfigRsp {
    @JsonProperty({ type: ClinicDispensingConfig })
    dispensing?: ClinicDispensingConfig;
}

class SmartDispensing {
    isSmartDispensing?: number;
}

/**
 * 处方调配状态
 */
export enum PrescriptionCompoundStatus {
    close = 0,
    open = 1,
}

export class PharmacyConfig {
    showPriceType?: number;
    takeMedicineTimeConfig?: number; //??

    @JsonProperty({ type: SmartDispensing })
    smartDispensing?: SmartDispensing;

    prescriptionReview?: number;
    prescriptionCompound?: PrescriptionCompoundStatus; //处方调配
    wholeSheetOperateEnable?: number; // 整单发退药开关（开启后，发药单内药品只能全部发药/退药，不能选择发药/退药药品）
    /**
     * 是否开启处方审核
     */
    get showPrescriptionReview(): boolean {
        return (this?.prescriptionReview ?? 0) > 0;
    }

    /**
     * 是否开启处方调配
     */
    get showPrescriptionCompound(): boolean {
        return (this.prescriptionCompound ?? 0) > 0;
    }

    /**
     * 是否开启整单发退药
     */
    get showWholeSheetOperateEnable(): boolean {
        return (this.wholeSheetOperateEnable ?? 0) > 0;
    }
}

/**
 * 护士站配置
 */
export enum EnterListSettingsItemTypeEnum {
    NEED_DEDUCT = 1, // 需要划扣
    INFUSION_PRESCRIPTION = 2, // 输注处方
    ATOMIZING_PRESCRIPTION = 4, // 雾化处方
    PI_SHI = 8, // 皮试
    NURSE = 16, // 护理
    EXTERNAL_PRESCRIPTION = 32, //外用处方
}
export class EnterListSettings {
    chargeStatus?: number;
    itemType?: EnterListSettingsItemTypeEnum[];
}
export enum EnableExecuteItemType {
    All = 0, // 全部
    NEED_DEDUCT = 1, // 需要划扣
    INFUSION_PRESCRIPTION = 2, // 输注处方
    ATOMIZING_PRESCRIPTION = 4, // 雾化处方
    PI_SHI = 8, // 皮试
    NURSE = 16, // 护理
    EXTERNAL_PRESCRIPTION = 32, //外用处方
}
export class NurseStationConfig {
    listDisplaySetting?: number;
    canOpenGoodsTypes?: number[];
    enterListSettings?: EnterListSettings; //进入执行站的设置
    onlyExecuteAfterPaid?: number;
    enableExecuteItemType?: EnableExecuteItemType[]; //可执行的项目类型
    enableOpenSheet?: number;
    newListDisplaySetting?: number[];
}

class TreatRule {
    refundTime?: number;
    serviceTel?: string;
    serviceTime?: number; //咨询服务超时时间
    treatOnlineSwitch?: number;
}

class BaseMedicalRecordConfig {
    symptomTime?: number; //发病时间
}

export enum MedicalRecordConfigType {
    western,
    chinese,
}

export class MedicalRecordConfig extends BaseMedicalRecordConfig {
    type?: number; //MedicalRecordConfigType
    chiefComplaint?: number;
    chineseExamination?: number;
    diagnosis?: number;
    pastHistory?: number;
    personalHistory?: number;
    physicalExamination?: number;
    presentHistory?: number;
    syndrome?: number;
    therapy?: number;
    uploadImage?: number;
    familyHistory?: number;
    epidemiologicalHistory?: number;
    auxiliaryExamination?: number;
    auxiliaryExaminations?: number;
    obstetricalHistory?: number;
    chinesePrescription?: number; //方药
    oralExamination?: number; //口腔检查
    allergicHistory?: number; //过敏史
    syndromeTreatment?: number; //辨证治疗
    disposals?: number; //处置
    birthHistory?: number; //出生史
    tongue?: number; //舌象
    pulse?: number; //脉象
    prognosis?: number; // 预后
}

export class DentistryMedicalRecordConfig extends BaseMedicalRecordConfig {
    auxiliaryExaminations?: number; //辅助检查
    chiefComplaint?: number;
    dentistryExaminations?: number; //口腔检查
    diagnosis?: number;
    disposals?: number; //处置
    doctorAdvice?: number; //医嘱事项
    epidemiologicalHistory?: number;
    familyHistory?: number;
    obstetricalHistory?: number; //月经婚育史
    pastHistory?: number;
    personalHistory?: number;
    physicalExamination?: number;
    presentHistory?: number;
    treatmentPlans?: number; //治疗计划
    allergicHistory?: number; //过敏史
    birthHistory?: number; //出生史
}

export class OphthalmologyMedicalRecordConfig extends BaseMedicalRecordConfig {
    auxiliaryExaminations?: number;
    chiefComplaint?: number;
    diagnosis?: number;
    disposals?: number; //处置
    epidemiologicalHistory?: number;
    eyeExamination?: number; //眼部检查
    eyeExaminationProducts?: string[];
    familyHistory?: number;
    obstetricalHistory?: number;
    pastHistory?: number;
    personalHistory?: number;
    physicalExamination?: number;
    presentHistory?: number;
    treatmentPlans?: number; //治疗计划
    wearGlassesHistory?: number; //戴镜史
    allergicHistory?: number; //过敏史
}

export enum TreatmentUsageMethod {
    NoUsage = 0, // 不填写用法
    DailyTreatment = 1, // 填写每天治疗数量、治疗天数
    FullUsage = 2, // 填写完整用法（治疗频率、单次治疗数量、治疗天数）
}
export class DiagnosisTreatmentConfig {
    // inspection?: number;
    // examination?: number;
    // treatment?: number;
    // other?: number;
    // material?: number;
    // materialGoods?: number;
    // nursing?: number;
    // compose?: number;
    supportInputDays?: TreatmentUsageMethod; // 设置治疗理疗用法
    // eyeglasses?: number;
    // surgery?: number;
}

export class PrescriptionConfig {
    chineseSwitch?: number;
    infusionSwitch?: number;
    westernSwitch?: number;
    externalSwitch?: number;

    get showChineseSwitch(): boolean {
        return this.chineseSwitch != 0;
    }

    get showInfusionSwitch(): boolean {
        return this.infusionSwitch != 0;
    }

    get showWesternSwitch(): boolean {
        return this.westernSwitch != 0;
    }

    get showExternalSwitch(): boolean {
        return this.externalSwitch != 0;
    }
}

class ClinicMedicalRecordConfigDetail {
    required?: number;
    sort?: number;
    unmodifiable?: number;
}
class ClinicMedicalRecordConfigItem {
    [key: string]: ClinicMedicalRecordConfigDetail;
}
class ClinicMedicalRecordConfig {
    western?: ClinicMedicalRecordConfigItem;
    chinese?: ClinicMedicalRecordConfigItem;
    oral?: ClinicMedicalRecordConfigItem;
    ophthalmology?: ClinicMedicalRecordConfigItem;
    dentistry?: ClinicMedicalRecordConfigItem;
}

class ClinicPatientConfig {
    create?: ClinicMedicalRecordConfigItem;
}
export class GetClinicMedicalRecordConfig {
    @JsonProperty({ type: ClinicMedicalRecordConfig })
    medicalRecord?: ClinicMedicalRecordConfig;

    /**
     * 获取字段配置规则
     * @param options
     */
    getMedicalRecordConfigDetail(options: {
        type?: "western" | "chinese" | "oral" | "ophthalmology" | "dentistry";
        field: string;
    }): ClinicMedicalRecordConfigDetail | undefined {
        const { type = "western", field } = options;
        return this.medicalRecord?.[type]?.[field];
    }

    @JsonProperty({ type: ClinicPatientConfig })
    patient?: ClinicPatientConfig;
    @JsonProperty({ type: ClinicPatientConfig })
    registration?: ClinicPatientConfig;

    /**
     * 获取指定key-value
     * @param options
     */
    getFieldConfigDetail(options: {
        sourceKey: keyof Pick<GetClinicMedicalRecordConfig, "medicalRecord" | "patient" | "registration">;
        type: keyof (ClinicMedicalRecordConfig & ClinicPatientConfig);
        field: string;
    }): ClinicMedicalRecordConfigDetail | undefined {
        const { sourceKey, type, field } = options;
        //@ts-ignore
        return this?.[sourceKey]?.[type]?.[field];
    }
}

export class OutpatientConfig {
    //线上接口：主要限制数据必填项和排序
    @JsonProperty({ type: GetClinicMedicalRecordConfig })
    onlineConfig?: GetClinicMedicalRecordConfig;

    @JsonProperty({ type: MedicalRecordConfig })
    medicalRecord?: MedicalRecordConfig;

    @JsonProperty({ type: PrescriptionConfig })
    prescription?: PrescriptionConfig;

    @JsonProperty({ type: PrescriptionConfig })
    astPrescription?: PrescriptionConfig;

    @JsonProperty({ type: DentistryMedicalRecordConfig })
    dentistryMedicalRecord?: DentistryMedicalRecordConfig;

    //初复诊标记 1：初诊；2：复诊
    revisit = 1;

    //口腔复诊配置
    @JsonProperty({ type: DentistryMedicalRecordConfig })
    dentistryMedicalRecordRevisit?: DentistryMedicalRecordConfig;

    @JsonProperty({ type: OphthalmologyMedicalRecordConfig })
    ophthalmologyMedicalRecord?: OphthalmologyMedicalRecordConfig;

    // 诊疗项目配置
    @JsonProperty({ type: DiagnosisTreatmentConfig })
    diagnosisTreatment?: DiagnosisTreatmentConfig;

    //治疗理疗支持录入天数
    supportInputDays?: number;

    getCurrentOnlineMedicalRecordConfig(field: string): ClinicMedicalRecordConfigDetail | undefined {
        if (!this.onlineConfig) return undefined;
        let type: "dentistry" | "ophthalmology" | "western" | "chinese" | "oral" | undefined;
        if (userCenter.clinic?.isDentistryClinic) {
            type = "dentistry";
        } else if (userCenter.clinic?.isOphthalmologyClinic) {
            type = "ophthalmology";
        } else {
            if (this.medicalRecord?.type == 0) {
                type = "western";
            } else if (this.medicalRecord?.type == 1) {
                type = "chinese";
            } else if (this.medicalRecord?.type == 2) {
                if (userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.MULTI_MEDICAL_RECORD_ORAL)) {
                    type = "dentistry";
                } else {
                    type = "oral";
                }
            }
        }
        return this.onlineConfig.getMedicalRecordConfigDetail({ type, field });
    }
    get currentMedicalRecordConfig(): MedicalRecordConfig | DentistryMedicalRecordConfig | OphthalmologyMedicalRecordConfig | undefined {
        if (userCenter.clinic?.isDentistryClinic) {
            if (this.revisit == 1) {
                return this.dentistryMedicalRecord;
            } else {
                return this.dentistryMedicalRecordRevisit;
            }
        } else if (userCenter.clinic?.isOphthalmologyClinic) {
            return this.ophthalmologyMedicalRecord;
        } else {
            return this.medicalRecord;
        }
    }

    set currentMedicalRecordConfig(
        params: MedicalRecordConfig | DentistryMedicalRecordConfig | OphthalmologyMedicalRecordConfig | undefined
    ) {
        if (userCenter.clinic?.isDentistryClinic) {
            if (this.revisit == 1) {
                Object.assign(this.dentistryMedicalRecord, { ...params });
            } else {
                Object.assign(this.dentistryMedicalRecordRevisit, { ...params });
            }
        } else if (userCenter.clinic?.isOphthalmologyClinic) {
            Object.assign(this.ophthalmologyMedicalRecord, { ...params });
        } else {
            Object.assign(this.medicalRecord, { ...params });
        }
    }

    get showChiefComplaint(): boolean {
        return (this.currentMedicalRecordConfig?.chiefComplaint ?? 0) != 0;
    }

    get showChineseExamination(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.chineseExamination ?? 0) != 0
        );
    }

    get showTongue(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.tongue ?? 0) != 0
        );
    }

    get showPulse(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.pulse ?? 0) != 0
        );
    }

    //诊所管家---口腔检查
    get showOralExamination(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.oralExamination ?? 0) != 0
        );
    }

    get showAuxiliaryExaminationInfo(): boolean {
        return (
            (this.currentMedicalRecordConfig?.auxiliaryExaminations ?? 0) == 1 ||
            (this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
                (this.currentMedicalRecordConfig?.auxiliaryExamination ?? 0) == 1)
        );
    }

    get showDiagnosis(): boolean {
        return (this.currentMedicalRecordConfig?.diagnosis ?? 0) != 0;
    }

    get showPastHistory(): boolean {
        return (this.currentMedicalRecordConfig?.pastHistory ?? 0) != 0;
    }

    get showAllergicHistory(): boolean {
        return (this.currentMedicalRecordConfig?.allergicHistory ?? 0) != 0;
    }

    get showPersonalHistory(): boolean {
        return (this.currentMedicalRecordConfig?.personalHistory ?? 0) != 0;
    }

    get showBirthHistory(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            (this.currentMedicalRecordConfig instanceof MedicalRecordConfig ||
                this.currentMedicalRecordConfig instanceof DentistryMedicalRecordConfig) &&
            (this.currentMedicalRecordConfig?.birthHistory ?? 0) != 0
        );
    }

    get showPhysicalExamination(): boolean {
        return (this.currentMedicalRecordConfig?.physicalExamination ?? 0) != 0;
    }

    get showPresentHistory(): boolean {
        return (this.currentMedicalRecordConfig?.presentHistory ?? 0) != 0;
    }

    get showOnsetTime(): boolean {
        return (this.currentMedicalRecordConfig?.symptomTime ?? 0) != 0;
    }

    get showSyndrome(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.syndrome ?? 0) != 0
        );
    }

    get showTherapy(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.therapy ?? 0) != 0
        );
    }

    get showPrognosis(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.prognosis ?? 0) != 0
        );
    }

    get showChinesePrescriptionInfo(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.chinesePrescription ?? 0) != 0
        );
    }

    get showFamilyHistory(): boolean {
        return (this.currentMedicalRecordConfig?.familyHistory ?? 0) != 0;
    }

    get showEpidemiologicalHistory(): boolean {
        return (this.currentMedicalRecordConfig?.epidemiologicalHistory ?? 0) != 0;
    }

    get showEyeExamination(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof OphthalmologyMedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.eyeExamination ?? 0) != 0
        );
    }

    get showWearGlassesHistory(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof OphthalmologyMedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.wearGlassesHistory ?? 0) != 0
        );
    }

    get showDialecticalTreatment(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof MedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.syndromeTreatment ?? 0) != 0
        );
    }

    get chineseSwitch(): boolean {
        return this.prescription?.chineseSwitch != 0;
    }

    get infusionSwitch(): boolean {
        return this.prescription?.infusionSwitch != 0;
    }

    get westernSwitch(): boolean {
        return this.prescription?.westernSwitch != 0;
    }

    get showExternalSwitch(): boolean {
        return this.prescription?.externalSwitch != 0;
    }

    get showTreatmentPlans(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            !(this.currentMedicalRecordConfig instanceof MedicalRecordConfig) &&
            (this.currentMedicalRecordConfig?.treatmentPlans ?? 0) != 0
        );
    }

    get showDisposals(): boolean {
        return !!this.currentMedicalRecordConfig && (this.currentMedicalRecordConfig?.disposals ?? 0) != 0;
    }

    // 口腔格式下处置才允许多条
    get canAddMoreDisposals(): boolean {
        return !!this.currentMedicalRecordConfig && (this.currentMedicalRecordConfig as MedicalRecordConfig)?.type == 2;
    }

    get showObstetricalHistory(): boolean {
        return (this.currentMedicalRecordConfig?.obstetricalHistory ?? 0) != 0;
    }

    //口腔诊所--口腔检查
    get dentistryExaminations(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof DentistryMedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.dentistryExaminations ?? 0) != 0
        );
    }

    get doctorAdvice(): boolean {
        return (
            !!this.currentMedicalRecordConfig &&
            this.currentMedicalRecordConfig instanceof DentistryMedicalRecordConfig &&
            (this.currentMedicalRecordConfig?.doctorAdvice ?? 0) != 0
        );
    }

    asyncOnlineConfig2Local(type: "western" | "chinese" | "oral" | "ophthalmology" | "dentistry" = "western"): void {
        if (!this.onlineConfig) return;

        switch (type) {
            case "western":
            case "chinese":
            case "oral": {
                //普通门店
                Object.keys(this.medicalRecord ?? {}).forEach((key) => {
                    const _config = this.onlineConfig?.getMedicalRecordConfigDetail({ type, field: key });
                    if (_config && !!_config.required) {
                        //@ts-ignore
                        this.medicalRecord![key] = 1;
                    }
                });
                break;
            }
            case "ophthalmology": {
                //眼科诊所
                Object.keys(this.ophthalmologyMedicalRecord ?? {}).forEach((key) => {
                    const _config = this.onlineConfig?.getMedicalRecordConfigDetail({ type, field: key });
                    if (_config && !!_config.required) {
                        //@ts-ignore
                        this.medicalRecord![key] = 1;
                    }
                });
                break;
            }
            case "dentistry": {
                //口腔门店
                Object.keys(this.dentistryMedicalRecord ?? {}).forEach((key) => {
                    const _config = this.onlineConfig?.getMedicalRecordConfigDetail({ type, field: key });
                    if (_config && !!_config.required) {
                        //@ts-ignore
                        this.medicalRecord![key] = 1;
                    }
                });
                break;
            }
        }
    }
}

export interface ChainBasicOutpatientSetting {
    requiredPastHistory: number;
    requiredPresentHistory: number;
    requiredEpidemiologicalHistory: number;
    requiredSyndrome?: number;
    requiredChineseExamination?: number;
    prescriptionFocusDosage?: number;
    isChinesePrescriptionShowSortNo?: number;
}

export interface ChainBasicOutpatient {
    outpatient: ChainBasicOutpatientSetting;
    scope: string;
    scopeId: string;
}

class ChainTreatOnlineTreatRuleConfig {
    refundTime?: number;
    serviceTel?: string;
    serviceTime?: number;
    treatOnlineSwitch?: number;
}

class ChainTreatOnlineRule {
    treatRule?: ChainTreatOnlineTreatRuleConfig;
    scope?: string;
    scopeId?: string;
}

class _InnerData {
    @JsonProperty({ type: ClinicDispensingConfig })
    dispensingConfig?: ClinicDispensingConfig;

    @JsonProperty({ type: TreatRule })
    treatRule?: TreatRule;

    @JsonProperty({ type: ChargeConfig })
    chargeConfig?: ChargeConfig;

    @JsonProperty({ type: DataPermission })
    dataPermission?: DataPermission;

    @JsonProperty({ type: PharmacyConfig })
    pharmacyConfig?: PharmacyConfig;

    @JsonProperty({ type: OutpatientConfig })
    outpatientConfig?: OutpatientConfig;

    @JsonProperty({ type: NurseStationConfig })
    nurseStationConfig?: NurseStationConfig;

    chainBasicOutpatientSetting?: ChainBasicOutpatient;
}

class GetNurseExecuteCrossRsp {
    enableCross?: number;
}

class GetPatientSnEditConfig {
    disablePatientSnEdit?: number;
    scope?: string;
    scopeId?: string;
}

const kOutpatientConfigKey = "OutpatientConfigKey";
const kOutpatientLastConfigKey = "OutpatientLastConfigKey";

class ClinicBasicSettingTempate {
    prescriptionCatalogTier?: number;
}
class ClinicBasicSettingMall {
    paidPopup?: number;
}
class ClinicBasicSettingShenZhen {
    isEnablePinganCity?: number;
}
enum OutpatientSettingUpdateLimit {
    Infinity = 0,
    MouthOf1 = 1,
    DayOf14 = 2,
    DayOf7 = 3,
    DayOf3 = 4,
    DayOf1 = 5,
}
class ClinicBasicSettingOutpatientSetting {
    chinesePrescriptionSupportMix?: number;
    covid19Detection?: number;
    covid19Keyword?: string[];
    infectiousDiseases?: number;
    infusion?: number;
    nebulizer?: number;
    canChangeRevisited?: number;
    isOpenContinueDiagnoseWithoutReg?: number; // 回诊免挂号
    dangerPrescriptionMustSign?: number; // 风险处方强制双签 默认不打开 0：不打开 1：打开
    medicalRecordUpdateLimit?: OutpatientSettingUpdateLimit;
    prescriptionUpdateLimit?: OutpatientSettingUpdateLimit;
    isOpenEditPrescriptionDiagnosed?: number; // 是否开启完诊后新增医嘱
    isOpenEditMedicalRecordDiagnosed?: number; // 是否开启完诊后修改病历
    get medicalRecordUpdateLimitTime(): number {
        return this.outpatientSettingUpdateLimitTime(this.medicalRecordUpdateLimit);
    }
    get prescriptionUpdateLimitTime(): number {
        return this.outpatientSettingUpdateLimitTime(this.prescriptionUpdateLimit);
    }
    private outpatientSettingUpdateLimitTime(updateLimit?: OutpatientSettingUpdateLimit): number {
        switch (updateLimit) {
            case OutpatientSettingUpdateLimit.DayOf1:
                return 1;
            case OutpatientSettingUpdateLimit.DayOf3:
                return 3;
            case OutpatientSettingUpdateLimit.DayOf7:
                return 7;
            case OutpatientSettingUpdateLimit.DayOf14:
                return 14;
            case OutpatientSettingUpdateLimit.MouthOf1:
                return 30;
            default:
                return 0;
        }
    }
}
class ClinicBasicSettingOutpatient {
    directRegOrderNoStrategy?: number;
    @JsonProperty({ type: ClinicBasicSettingOutpatientSetting })
    settings?: ClinicBasicSettingOutpatientSetting;
    shareType?: number;
}
class HospitalExecuteAdvice {
    afterDispensed?: number;
}
class ClinicBasicSettingHospital {
    executeAdvice?: HospitalExecuteAdvice;
}
class ClinicBasicSettingCrmSetting {
    needRealAuth?: number;
}
class ClinicBasicSettingCrm {
    settings?: ClinicBasicSettingCrmSetting;
}

class ClinicBasicSettingCharge {
    directSaleEnable?: number;
}

class ClinicBasicSettingFeeNameDisplay {
    registrationFee?: string; // 挂号费
}
// 获取门店基本配置
export class GetClinicBasicSetting {
    template?: ClinicBasicSettingTempate;
    isEnableCA?: number; // 门店是否开启CA签名 0：否 1：是
    scanRegisterEpidemiological?: number;
    isEnableWuhanHealthyCard?: number;
    isEnableReadSocialCard?: number;
    isEnableShebaoSettle?: number; // 是否开通清算单 0：否 1：是
    mall?: ClinicBasicSettingMall;
    shenzhen?: ClinicBasicSettingShenZhen;
    @JsonProperty({ type: ClinicBasicSettingOutpatient })
    outpatient?: ClinicBasicSettingOutpatient;
    hospital?: ClinicBasicSettingHospital;
    region?: string;
    crm?: ClinicBasicSettingCrm;
    charge?: ClinicBasicSettingCharge;
    feeNameDisplay?: ClinicBasicSettingFeeNameDisplay;

    get registrationFeeStr(): string {
        return this.feeNameDisplay?.registrationFee ?? "";
    }
}

export class OnlinePropertyConfigProvider extends DisposableTracker {
    static instance = new OnlinePropertyConfigProvider();
    _innerData: _InnerData = new _InnerData();

    constructor() {
        super();
        userCenter.loginObserver
            .subscribe((typeEvent) => {
                if (typeEvent.type === LoginEvent.loginOut) {
                    this._innerData = new _InnerData();
                }
            })
            .addToDisposableBag(this);

        userCenter.sClinicChangeObserver
            .subscribe(() => {
                this._innerData = new _InnerData();
            })
            .addToDisposableBag(this);
    }

    ///获取诊所收费配置
    async getChargeConfig(cache = true): Promise<ChargeConfig> {
        if (this._innerData?.chargeConfig != null && cache) return this._innerData.chargeConfig;

        this._innerData!.chargeConfig = await ABCApiNetwork.get("charge/config/available", { clazz: ChargeConfig });
        return this._innerData!.chargeConfig!;
    }

    getChargeConfigSync(): ChargeConfig | undefined {
        return this._innerData.chargeConfig;
    }

    async getClinicDataPermission(): Promise<DataPermission | undefined> {
        // if (this._innerData.dataPermission) return this._innerData.dataPermission;
        const rsp = await ABCApiNetwork.get("clinics/data-permission", { clazz: DataPermission }).catchIgnore();
        this._innerData.dataPermission = rsp;
        return rsp;
    }

    async getClinicDispensingConfig(): Promise<ClinicDispensingConfig> {
        const clinic = userCenter.clinic!;
        const config = await ApiMixService.getProperty("clinic", clinic.clinicId, "treatOnline.dispensing", GetClinicDispensingConfigRsp);

        return config.dispensing!;
    }

    /// 获取药房配置
    async getPharmacyConfig(): Promise<PharmacyConfig> {
        if (this._innerData?.pharmacyConfig != null) return this._innerData.pharmacyConfig;
        const clinic = userCenter.clinic!;
        this._innerData.pharmacyConfig = await ApiMixService.getPropertyV3(
            "clinic",
            clinic.clinicId,
            "dispensing",
            PharmacyConfig
        ).catchIgnore();
        return this._innerData!.pharmacyConfig!;
    }

    /// 拉取销售员是否为必填项配置
    public getChargeRequiredSeller(): Promise<boolean> {
        const clinic = userCenter.clinic!;
        return ApiMixService.getProperty<{ chargeRequiredSeller: number }>(
            "chain",
            clinic.chainId ?? "",
            "chainBasic.chargeRequiredSeller"
        ).then((rsp) => {
            return rsp.chargeRequiredSeller == 1;
        });
    }

    /**
     * 获取当前诊所总部的门诊单必填项目设置设置
     */
    async getChainBasicOutpatientSetting(): Promise<ChainBasicOutpatient> {
        if (this._innerData?.chainBasicOutpatientSetting) return this._innerData.chainBasicOutpatientSetting;
        const requiredConfig = await ApiMixService.getProperty<ChainBasicOutpatient>(
            "chain",
            userCenter.clinic!.chainId,
            "chainBasic.outpatient"
        );
        const settingConfig = await this.getOutpatientConfig();
        if (requiredConfig.outpatient.requiredPastHistory) {
            settingConfig.medicalRecord!.pastHistory = 1;
        }
        if (requiredConfig.outpatient.requiredPresentHistory) {
            settingConfig.medicalRecord!.presentHistory = 1;
        }
        if (requiredConfig.outpatient.requiredEpidemiologicalHistory) {
            settingConfig.medicalRecord!.epidemiologicalHistory = 1;
        }
        this._innerData.chainBasicOutpatientSetting = requiredConfig;
        this.updateOutpatientConfig({ scope: "", config: settingConfig });
        return requiredConfig;
    }

    /**
     * 获取执行站配置项
     */
    async getNurseStationConfig(): Promise<NurseStationConfig> {
        // if (this._innerData?.nurseStationConfig != null) return this._innerData.nurseStationConfig;
        const clinic = userCenter.clinic!;
        this._innerData.nurseStationConfig = await ApiMixService.getPropertyV3(
            "clinic",
            clinic.clinicId,
            "clinicNurseSettings",
            NurseStationConfig
        ).catchIgnore();
        return this._innerData!.nurseStationConfig!;
    }

    async getOutpatientConfig(): Promise<OutpatientConfig> {
        const obj = clinicSharedPreferences.getObject(kOutpatientConfigKey);
        const isDentistryClinic = userCenter.clinic?.isDentistryClinic;
        let _rspObj;
        //走后台配置
        const config = await this.getClinicMedicalRecordConfig().catchIgnore();
        /**
         * @description 手动加入配置项
         */
        if (obj) {
            if (!obj.hasOwnProperty("astPrescription")) {
                obj.astPrescription = { infusionSwitch: 1, westernSwitch: 0 };
            }
            //处方类型配置重置
            if (!obj.hasOwnProperty("prescription")) {
                obj.prescription = {
                    chineseSwitch: isDentistryClinic ? 0 : 1,
                    infusionSwitch: isDentistryClinic ? 0 : 1,
                    westernSwitch: 1,
                    externalSwitch: isDentistryClinic ? 0 : 1,
                };
            }
            //口腔配置重置
            if (!obj.hasOwnProperty("dentistryMedicalRecord")) {
                obj.dentistryMedicalRecord = Object.assign(
                    {
                        chiefComplaint: 1,
                        presentHistory: 1,
                        pastHistory: 1,
                        dentistryExaminations: 1,
                        disposals: 1,
                        diagnosis: 1,
                    },
                    obj.medicalRecord
                );
            }
            //口腔复诊配置重置
            if (!obj.hasOwnProperty("dentistryMedicalRecordRevisit")) {
                obj.dentistryMedicalRecordRevisit = Object.assign(
                    {
                        chiefComplaint: 1,
                        presentHistory: 1,
                        pastHistory: 1,
                        dentistryExaminations: 1,
                        disposals: 1,
                        diagnosis: 1,
                    },
                    obj.medicalRecord
                );
            }
            //眼科配置重置
            if (!obj.hasOwnProperty("ophthalmologyMedicalRecord")) {
                obj.ophthalmologyMedicalRecord = Object.assign(
                    {
                        chiefComplaint: 1,
                        presentHistory: 1,
                        pastHistory: 1,
                        wearGlassesHistory: 1,
                        eyeExamination: 1,
                        diagnosis: 1,
                    },
                    obj.medicalRecord
                );
            }
            //诊断治疗配置重置
            if (!obj.hasOwnProperty("diagnosisTreatment")) {
                obj.diagnosisTreatment = {
                    supportInputDays: 0,
                };
            }
            _rspObj = JsonMapper.deserialize(OutpatientConfig, {
                ...obj,
                onlineConfig: config,
                medicalRecord: {
                    personalHistory: obj?.medicalRecord?.personalHistory ?? 1,
                    physicalExamination: obj.medicalRecord?.physicalExamination ?? 1,
                    ...obj.medicalRecord,
                },
                prescription: {
                    chineseSwitch: obj?.prescription?.chineseSwitch ?? (isDentistryClinic ? 0 : 1),
                    infusionSwitch: obj?.prescription?.infusionSwitch ?? (isDentistryClinic ? 0 : 1),
                    westernSwitch: obj?.prescription?.westernSwitch ?? 1,
                    externalSwitch: obj?.prescription?.externalSwitch ?? (isDentistryClinic ? 0 : 1),
                },
                dentistryMedicalRecord: {
                    chiefComplaint: obj?.dentistryMedicalRecord?.chiefComplaint ?? 1,
                    presentHistory: obj?.dentistryMedicalRecord?.presentHistory ?? 1,
                    pastHistory: obj?.dentistryMedicalRecord?.pastHistory ?? 1,
                    dentistryExaminations: obj?.dentistryMedicalRecord?.dentistryExaminations ?? 1,
                    disposals: obj?.dentistryMedicalRecord?.disposals ?? 1,
                    diagnosis: obj?.dentistryMedicalRecord?.diagnosis ?? 1,
                    ...obj.dentistryMedicalRecord,
                },
                ophthalmologyMedicalRecord: {
                    chiefComplaint: obj?.ophthalmologyMedicalRecord?.chiefComplaint ?? 1,
                    presentHistory: obj?.ophthalmologyMedicalRecord?.presentHistory ?? 1,
                    pastHistory: obj?.ophthalmologyMedicalRecord?.pastHistory ?? 1,
                    wearGlassesHistory: obj?.ophthalmologyMedicalRecord?.wearGlassesHistory ?? 1,
                    eyeExamination: obj?.ophthalmologyMedicalRecord?.eyeExamination ?? 1,
                    diagnosis: obj?.ophthalmologyMedicalRecord?.diagnosis ?? 1,
                    ...obj.ophthalmologyMedicalRecord,
                },
            });
        } else {
            _rspObj = JsonMapper.deserialize(OutpatientConfig, {
                onlineConfig: config,
                medicalRecord: {
                    type: MedicalRecordConfigType.western,
                    chiefComplaint: 1,
                    symptomTime: 0,
                    chineseExamination: 0,
                    diagnosis: 1,
                    pastHistory: 0,
                    physicalExamination: 1,
                    presentHistory: 0,
                    syndrome: 0,
                    therapy: 0,
                    epidemiologicalHistory: 0,
                    familyHistory: 0,
                    personalHistory: 0,
                    syndromeTreatment: 0,
                    tongue: 0,
                    pulse: 0,
                    prognosis: 0,
                },
                // @ts-ignore
                prescription: {
                    chineseSwitch: isDentistryClinic ? 0 : 1,
                    infusionSwitch: isDentistryClinic ? 0 : 1,
                    westernSwitch: 1,
                    externalSwitch: isDentistryClinic ? 0 : 1,
                },
                // @ts-ignore
                astPrescription: {
                    infusionSwitch: 1,
                    westernSwitch: 0,
                },
                dentistryMedicalRecord: {
                    chiefComplaint: 1,
                    presentHistory: 1,
                    pastHistory: 1,
                    dentistryExaminations: 1,
                    disposals: 1,
                    diagnosis: 1,
                    auxiliaryExaminations: 0,
                    doctorAdvice: 0,
                    epidemiologicalHistory: 0,
                    familyHistory: 0,
                    obstetricalHistory: 0,
                    personalHistory: 0,
                    physicalExamination: 0,
                    treatmentPlans: 0,
                },
                ophthalmologyMedicalRecord: {
                    chiefComplaint: 1,
                    presentHistory: 1,
                    pastHistory: 1,
                    wearGlassesHistory: 1,
                    eyeExamination: 1,
                    diagnosis: 1,
                    auxiliaryExaminations: 0,
                    disposals: 0,
                    epidemiologicalHistory: 0,
                    familyHistory: 0,
                    obstetricalHistory: 0,
                    personalHistory: 0,
                    physicalExamination: 0,
                    treatmentPlans: 0,
                },
                diagnosisTreatment: {
                    // compose: 0,
                    // examination: 0,
                    // eyeglasses: 0,
                    // inspection: 0,
                    // material: 0,
                    // materialGoods: 0,
                    // nursing: 0,
                    // other: 0,
                    supportInputDays: 0,
                    // surgery: 0,
                    // treatment: 0,
                },
            });
        }
        //复制后台必填项配置为选中
        if (
            userCenter.clinic?.isDentistryClinic ||
            userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.MULTI_MEDICAL_RECORD_ORAL)
        ) {
            this._setChineseExaminationConfig(_rspObj, config, "dentistry");
        } else if (userCenter.clinic?.isOphthalmologyClinic) {
            this._setChineseExaminationConfig(_rspObj, config, "ophthalmology");
        } else {
            let _type: "western" | "chinese" | "oral" | undefined;
            if (_rspObj.medicalRecord?.type == 0) {
                _type = "western";
            } else if (_rspObj.medicalRecord?.type == 1) {
                _type = "chinese";
            } else if (_rspObj.medicalRecord?.type == 2) {
                _type = "oral";
            }
            // // 如果配置项中存在【望闻切诊】字段是必填项，那么【舌象、脉象】要勾选
            // const _config = config?.getMedicalRecordConfigDetail({ type: _type, field: "chineseExamination" });
            // let isChecked = false;
            // if (_config && !!_config.required) {
            //     isChecked = true;
            // }
            // _rspObj.medicalRecord!.chineseExamination = (isChecked ? 1 : _rspObj?.medicalRecord?.chineseExamination) ?? 0;
            // _rspObj.medicalRecord!.tongue = (isChecked ? 1 : _rspObj?.medicalRecord?.tongue) ?? 0;
            // _rspObj.medicalRecord!.pulse = (isChecked ? 1 : _rspObj?.medicalRecord?.pulse) ?? 0;
            // 原配置【舌象】【脉象】任意一个必填，升级后【望闻切诊】必填
            const _config1 = config?.getMedicalRecordConfigDetail({ type: _type, field: "tongue" });
            const _config2 = config?.getMedicalRecordConfigDetail({ type: _type, field: "pulse" });
            const isChecked = (_config1 && _config1?.required === 1) || (_config2 && _config2?.required === 1);

            _rspObj.medicalRecord!.chineseExamination = (isChecked ? 1 : _rspObj?.medicalRecord?.chineseExamination) ?? 0;
            _rspObj.asyncOnlineConfig2Local(_type);
        }
        return _rspObj;
    }

    private _setChineseExaminationConfig(
        _rspObj: OutpatientConfig,
        config: GetClinicMedicalRecordConfig | undefined,
        type: "dentistry" | "ophthalmology" | "western" | "chinese" | "oral"
    ): void {
        // 口腔、眼科门店严格意义上是没有中医望闻切诊，舌象、脉象等字段的，但是有个判断【多病历口腔-专业口腔】是走的口腔病历设置（是没有望闻问切诊的），
        // 但是本地的medicalRecord会有之前存储的chineseExamination为选中状态，需要重置
        // const _config = config?.getMedicalRecordConfigDetail({ type, field: "chineseExamination" });
        // const isChecked = _config?.required === 1;

        // _rspObj.medicalRecord!.tongue = (isChecked ? 1 : _rspObj.medicalRecord?.tongue) ?? 0;
        // _rspObj.medicalRecord!.pulse = (isChecked ? 1 : _rspObj.medicalRecord?.pulse) ?? 0;
        // _rspObj.medicalRecord!.chineseExamination = (isChecked ? 1 : _rspObj.medicalRecord?.chineseExamination) ?? 0;

        // 原配置【舌象】【脉象】任意一个必填，升级后【望闻切诊】必填
        const _config1 = config?.getMedicalRecordConfigDetail({ type, field: "tongue" });
        const _config2 = config?.getMedicalRecordConfigDetail({ type, field: "pulse" });
        const isChecked = (_config1 && _config1?.required === 1) || (_config2 && _config2?.required === 1);

        _rspObj.medicalRecord!.chineseExamination = (isChecked ? 1 : _rspObj?.medicalRecord?.chineseExamination) ?? 0;
        _rspObj.asyncOnlineConfig2Local(type);
    }
    //更新门诊设置
    async updateOutpatientConfig(options: { scope: string; config: OutpatientConfig }): Promise<OutpatientConfig> {
        //复制后台必填项配置为选中
        let _type: "dentistry" | "ophthalmology" | "western" | "chinese" | "oral" | undefined;
        if (userCenter.clinic?.isDentistryClinic) {
            _type = "dentistry";
        } else if (userCenter.clinic?.isOphthalmologyClinic) {
            _type = "ophthalmology";
        } else {
            if (options.config.medicalRecord?.type == 0) {
                _type = "western";
            } else if (options.config.medicalRecord?.type == 1) {
                _type = "chinese";
            } else if (options.config.medicalRecord?.type == 2) {
                if (userCenter.clinicEdition?.checkFeaturePurcheased(FeatureAuthority.MULTI_MEDICAL_RECORD_ORAL)) {
                    _type = "dentistry";
                } else {
                    _type = "oral";
                }
            }
        }
        options.config.asyncOnlineConfig2Local(_type);
        clinicSharedPreferences.setObject(kOutpatientConfigKey, options.config);

        const _lastConfigObj = clinicSharedPreferences.getObject(kOutpatientLastConfigKey);
        let lastConfig: Map<string, OutpatientConfig> = new Map<string, OutpatientConfig>();
        if (!!_lastConfigObj) {
            lastConfig = new Map<string, OutpatientConfig>(JSON.parse(_lastConfigObj));
        }
        lastConfig.set(_type ?? "", options.config);
        clinicSharedPreferences.setObject(kOutpatientLastConfigKey, JSON.stringify(Array.from(lastConfig)));

        return options.config;
    }

    getLastOutpatientConfig(type: "western" | "chinese" | "oral"): OutpatientConfig | undefined {
        const _lastConfigObj = clinicSharedPreferences.getObject(kOutpatientLastConfigKey);
        let lastConfig: Map<string, OutpatientConfig> = new Map<string, OutpatientConfig>();
        if (!!_lastConfigObj) {
            lastConfig = new Map<string, OutpatientConfig>(JSON.parse(_lastConfigObj));
        }
        return lastConfig.get(type);
    }

    async getTreatRule(): Promise<TreatRule | undefined> {
        if (this._innerData.treatRule != null) return this._innerData.treatRule;

        const clinic = await userCenter.clinic;

        this._innerData.treatRule = await ApiMixService.getProperty(
            "chain",
            clinic?.chainId ?? "",
            "treatOnline.treatRule",
            _InnerData
        ).then((rsp) => rsp.treatRule);

        return this._innerData.treatRule;
    }

    private _nurseExecuteCross?: GetNurseExecuteCrossRsp;

    //获取连锁跨店执行的开关
    async getNurseExecuteCross(cache = false): Promise<boolean> {
        if (cache && this._nurseExecuteCross) {
            return this._nurseExecuteCross.enableCross == 1;
        }
        this._nurseExecuteCross = await ApiMixService.getPropertyV3("chain", "", "nurseExecute", GetNurseExecuteCrossRsp);
        return this._nurseExecuteCross.enableCross == 1;
    }

    async getChainBasicPropertyConfig(): Promise<ChainBasePropertyConfig> {
        return ApiMixService.getProperty("chain", "", "chainBasic", ChainBasePropertyConfig);
    }

    async getChainTreatOnlineRuleConfig(): Promise<ChainTreatOnlineRule> {
        return ApiMixService.getProperty("chain", "", "treatOnline.treatRule", ChainTreatOnlineRule);
    }

    _patientSnEditConfig?: GetPatientSnEditConfig;
    /**
     * 拉取患者修改是否可修改档案号
     */
    async getPatientSnCanEditStatus(cache = false): Promise<boolean> {
        if (cache && !!this._patientSnEditConfig) {
            return !this._patientSnEditConfig.disablePatientSnEdit;
        }
        this._patientSnEditConfig = await ApiMixService.getProperty(
            "chain",
            "",
            "crm.patient.disablePatientSnEdit",
            GetPatientSnEditConfig
        );
        return !this._patientSnEditConfig.disablePatientSnEdit;
    }

    /**
     * 获取病历新冠症状提示开关
     */
    async getClinicOutpatientCovid19DetectionSetting(): Promise<number> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.outpatient.settings.covid19Detection");
    }

    /**
     * 获取病历新冠症状提示关键字
     */
    async getClinicOutpatientCovid19DetectionKeywords(): Promise<string[]> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.outpatient.settings.covid19Keyword");
    }

    /**
     * 获取传染病提醒开关配置
     */
    async getClinicOutpatientInfectiousDiseasesSetting(): Promise<number> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.outpatient.settings.infectiousDiseases");
    }

    /**
     * 获取初复诊开关配置
     */
    async getClinicOutpatientCanChangeRevisitedSetting(): Promise<number> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.outpatient.settings.canChangeRevisited");
    }

    /**
     * 获取诊所基础配置
     */
    async getClinicBasicSetting(): Promise<GetClinicBasicSetting> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic", GetClinicBasicSetting);
    }

    /**
     * 获取诊所门诊病历必填项选项
     */
    async getClinicMedicalRecordConfig(): Promise<GetClinicMedicalRecordConfig> {
        return await ApiMixService.getPropertyV3("clinic", "", "field", GetClinicMedicalRecordConfig);
    }
    /**
     * 获取清算单是否开启的配置(成都地区)
     */
    async getSheBaoSettleConfig(): Promise<number> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.isEnableShebaoSettle");
    }

    /**
     * 获取诊所自定义用法配置
     */
    async getClinicCustomUsages(): Promise<ClinicCustomUsagesConfig> {
        return await ApiMixService.getProperty<ClinicCustomUsagesConfig>("clinic", "", "outpatient.customUsages");
    }

    /**
     * 追溯码采集场景配置
     */
    async getClinicTraceCodeConfig(): Promise<ClinicTraceCodeConfig> {
        return await ApiMixService.getPropertyV3("clinic", "", "traceCodeConfig", ClinicTraceCodeConfig);
    }

    /**
     * 获取收费-医保-全额退费配置
     */
    async getClinicChargeShebaoAllRefundSetting(): Promise<number> {
        return await ApiMixService.getPropertyV3("clinic", "", "clinicBasic.charge.shebao.allRefund");
    }
}
