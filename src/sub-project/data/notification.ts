/**
 * create by deng<PERSON>e
 * desc:
 * create date 2020/7/21
 */
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { BehaviorSubject } from "rxjs";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../common-base-module/json-mapper/json-mapper";
import { ABCApiError, ABCApiNetwork } from "../net";
import { LogUtils } from "../common-base-module/log";
import { ABCUtils } from "../base-ui/utils/utils";
import _ from "lodash";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { userCenter } from "../user-center";
import { HisType } from "../user-center/data/bean";

export enum MsgActionsType {
    create, // 创建
    delete, // 删除
    update, // 更新
}

export enum MessageBodyDetailType {
    socialNotice = 100, // 医保公告
    systemUpdateNoticePre = 101, // 更新预告
    systemUpdateNotice = 100, // 更新内容

    WEAPP_ICP = 30000, // 备案通知提醒：【重要通知】腾讯官方要求，所有小程序必须尽快完成认证和备案，否则将被下架处理
    WEAPP_VERIFY = 31000, // 小程序认证
    WEAPP_PUBLISH = 32000, // 小程序发布
}

export class MsgCategory {
    static categorySystem = 1; //系统通知
    static categoryTodo = 2; //待办事项
    static categoryWorkReport = 3; //工作日报
    static categoryStatIncome = 4; //经营日报

    static CMSChannelAllPerson = 100001; // CMS通道类型-发送到所有人
    static CMSChannelAllSpecifyPerson = 100003; // CMS通道类型-发送给指定人
}

export class MsgType {
    static revenuesReport = 1; //运营日报-收入及客流
    static doctorReport = 2; //医生日报
    static cashierReport = 3; //收费员日报
    static nurseReport = 4; //护士日报
    static therapyReport = 5; //理疗师日报
    static reservedDailyReport = 6; // 预约日报
    static reservedWeeklyReport = 7; // 预约周报
    static oldBringNewDailyReport = 8; //老带新活动日报
    static msgTypeDailyPharmacyEcOperateReport = 9; //订单云-经营日报

    static appointmentReminder = 10000; //预约推送
    static dailyReportNotification = 20000; //日报推送

    static inventoryWarning = 10; // 库存预警日报
}

export class MessageItem {
    id?: string;
    msgId?: string;
    chainId?: string;
    clinicId?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    title?: string;
    content?: string;
    jsonView?: string;

    //本字段客户端使用
    @JsonProperty({ ignore: true })
    widget?: JSX.Element;
}

class LatestMsgSummaryItem {
    category?: number;
    newMessageCount?: number;

    @JsonProperty({
        fromJson(json) {
            return JsonMapper.deserialize(MessageListItem, json);
        },
    })
    message?: MessageListItem;
}

export class GetLatestMsgSummaryRsp {
    @JsonProperty({ type: Array, clazz: LatestMsgSummaryItem })
    messages?: Array<LatestMsgSummaryItem>;

    get getNewMsgCount(): number {
        let count = 0;

        this.messages
            ?.filter((message) => {
                // 药店管家（忽略待办事项、工作日报红点提醒）
                userCenter.clinic?.isDrugstoreButler
                    ? message.category != MsgCategory.categoryTodo || message.category != MsgCategory.categoryWorkReport
                    : false;
            })
            .forEach((item) => {
                count += item.newMessageCount ?? 0;
            });

        return count;
    }

    getItemByCategoryType(type?: number): LatestMsgSummaryItem | undefined {
        return this.messages?.find((item) => item.category == type);
    }
}

export class GetMsgListRsp {
    category?: number;
    offset?: number;
    limit?: number;
    total?: number;

    @JsonProperty({ type: Array, clazz: MessageItem })
    messageList?: Array<MessageItem>;
}

class MsgCategoryReq {
    category?: number;

    lastGetDate?: number | null;

    constructor(value0: number, value1: number | null) {
        this.category = value0;
        this.lastGetDate = value1;
    }
}

//自定义转换类型,兼容之前老数据String类型
function _toEffectClinics(json: any): Array<string> {
    if (_.isString(json)) {
        return json.split(",");
    } else if (_.isArray(json)) {
        return json.map((item) => item as string);
    }
    return [];
}

class NotificationContentJson {
    name?: string;
    nmpn?: string;
    detail?: string;
    effect?: string;
    specAfter?: string;
    specBefore?: string;

    @JsonProperty({ fromJson: _toEffectClinics })
    effectClinics?: Array<string>;
}

class NotificationData {
    id?: number;
    type?: number; //NotificationType
    status?: number; //0 未读，9已读
    from?: string;
    fromName?: string;
    title?: string;
    abstract?: string;
    content?: string;
    action?: string;
    actionParams?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;

    @JsonProperty({ type: NotificationContentJson })
    contentJson?: NotificationContentJson;

    get hasRead(): boolean {
        return this.status != 0;
    }
}

class GetNotificationsResult {
    @JsonProperty({ type: Array, clazz: NotificationData })
    list?: Array<NotificationData>;
    count?: number;
    unreadCount?: number;
    skip?: number;
    limit?: number;
}

class ShiftsItemDetail {
    name?: string;
    endTime?: string;
    isDefault?: number;
    startTime?: string;
    departmentName?: string;

    get displayDetail(): string {
        return `${this.name}(${this.startTime} - ${this.endTime})  ${this.departmentName ?? ""}`;
    }
}

class MessageContentDetail {
    title?: string;
    content?: string;
    android?: any;
}
export class MessageBodyDateCountStat {
    // 今日预约
    count?: number;
    clinicId?: string;
    employeeId?: string;
    nightCount?: number;
    morningCount?: number;
    afternoonCount?: number;

    // 下周预约
    mondayCount?: number;
    tuesdayCount?: number;
    wednesdayCount?: number;
    thursdayCount?: number;
    fridayCount?: number;
    saturdayCount?: number;
    sundayCount?: number;
    registrationType?: number; //区分来源于门诊挂号预约、理疗预约（0---门诊挂号）
}
export class MessageCardDetail {
    id?: string;
    icon?: string;
    link?: string;
    type?: number;
    content?: string;
    title?: string;
    readNum?: string;
    describe?: string;
    startTime?: Date;
    endTime?: Date;
    employeeId?: string;
    messageIds?: string;
    employeeName?: string;

    date?: string;
    clinicName?: string;
    outpatientCount?: number;
    appointmentCount?: number;
    prescriptionCount?: number;

    msgType?: MsgType;

    executeCount?: number;
    registrationCount?: number;

    //经营日报
    totalFee?: number; //营业收费
    rechargeFee?: number; //会员充值
    outpatientFee?: number; //门诊收费
    directFee?: number; //零售收费
    directCount?: number; //零售客流
    dailyUpPercent?: number; //浮动比例
    dailyUp?: boolean; //上浮下降
    sheetCount?: number;
    outpatientSheetCount?: number;
    directChargeSheetCount?: number;
    memberRechargeSheetCount?: number;

    actualAmount?: number;

    @JsonProperty({ type: Array, clazz: ShiftsItemDetail })
    shifts?: Array<ShiftsItemDetail>;

    countStat?: MessageBodyDateCountStat; // 预约日报周报推送

    //老带新活动日报
    referrerVisitorCount?: number; //推荐访客数
    newAddPatientCount?: number; //新增客户数
    arrivalPeopleCount?: number; //到店人数
    payPeopleNumber?: number; //消费人数
    patientPayAmount?: number; //消费金额
    newAddPatientYesterdayCompare?: string; //新增客户昨日同比
    totalAmountYesterdayCompare?: string; //订单云昨日同比
    chainId?: string; //连锁id
    isRise?: number; //1：上升，0：下降

    memberRechargePeopleNumber?: number; // 会员充值人次
    memberPayPeopleNumber?: number; // 会员消费人次
    memberPayFee?: number; //会员贡献收入
    //新增客户昨日同比上升
    get newAddPatientYesterdayCompareUp(): boolean {
        return this.isRise == 1;
    }
    hisType?: HisType;
    get isDrugstoreButler(): boolean {
        return this.hisType == HisType.drugstoreButler;
    }
    get isHospital(): boolean {
        return this.hisType == HisType.normalHospital;
    }
    physicalExaminationFee?: number; //体检费用
    hospitalFee?: number; //住院费用
    prepaidFee?: number; //预缴金
    physicalExaminationCount?: number; //体检人数(就诊人次)
    hospitalCount?: number; //住院人数
    orderNumber?: number; //订单数
    orderAmount?: number; //订单金额
    refundOrderNumber?: number; //退款订单数
    refundOrderAmount?: number; //退款订单金额
    actualPrice?: number; //实收金额

    //  库存预警
    profitRatWarnCount?: number; // 毛利率异常预警数
    expiredWarnCount?: number; // 效期预警数
    stockWarnShortageCount?: number; // 周转天数 ＋库存预警数量
    grossProfit?: number; // 营业毛利
    grossProfitRate?: number; // 营业毛利率
}

class MessageBodyDetail {
    title?: string;
    action?: string;
    canPush?: boolean;
    content?: string;
    @JsonProperty({ type: MessageCardDetail })
    data?: MessageCardDetail;

    link?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    startTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    endTime?: Date;
    type?: MessageBodyDetailType;

    // 展示卡片底部可跳转详情(不可跳转的类型不显示详情>)
    get canJumpDetails(): boolean {
        return !(
            this.type == MessageBodyDetailType.WEAPP_ICP ||
            this.type == MessageBodyDetailType.WEAPP_VERIFY ||
            this.type == MessageBodyDetailType.WEAPP_PUBLISH
        );
    }
}

export class MessageListItem {
    id?: string;
    msgId?: string;
    chainId?: string;
    clinicId?: string;
    clinicName?: string;

    @JsonProperty({ type: MessageContentDetail })
    messageContent?: MessageContentDetail;

    sendStatus?: number;
    messageChannel?: number;
    receiptId?: string;

    @JsonProperty({ type: MessageBodyDetail })
    messageBody?: MessageBodyDetail;

    targetType?: number;
    taskTotal?: number;
    taskSuccess?: number;
    taskFailed?: number;
    messageCount?: number;
    serviceProvider?: number;
    apiRsp?: number;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdBy?: number;
    lastModified?: number;
    detailReadCount?: number;
    readCount?: number;
    targetId?: number;
    targetName?: number;
    createdByName?: number;
    errCode?: number;
    errMsg?: string;
    sendTime?: number;
    @JsonProperty({ type: MsgCategory })
    category?: MsgCategory;
}

export class SystemMessageNotifyMsg {
    id?: string;
    action?: MsgActionsType;

    @JsonProperty({ type: MessageListItem })
    messageBody?: MessageListItem;
}

export class GetNewMsgListRsp {
    offset?: number;
    limit?: number;
    category?: number;
    total?: number;
    newMessageCount?: number;

    @JsonProperty({ type: Array, clazz: MessageListItem })
    messageList?: Array<MessageListItem>;
}

/**
 * 获取/更新 员工消息通知配置
 */

export class GetEmployeeMessageNotificationConfigItem {
    employeeId?: string; // 更新员工消息时使用

    msgType?: number;
    name?: string;
    desc?: string;
    xingeSwitch?: number;
}
export class GetEmployeeMessageNotificationConfigRsp {
    @JsonProperty({ type: Array, clazz: GetEmployeeMessageNotificationConfigItem })
    items?: Array<GetEmployeeMessageNotificationConfigItem>;
}

export class NotificationDataManager {
    static PREF_LAST_FETCH_MSG_TIME = "last_featch_first_fetch_msg_count_time";

    static sObserver: BehaviorSubject<GetLatestMsgSummaryRsp> = new BehaviorSubject<GetLatestMsgSummaryRsp>(new GetLatestMsgSummaryRsp());

    static async deviceRegister(token: string, pushChannelId: number): Promise<boolean> {
        return ABCApiNetwork.post("msg/xg/register", {
            useBody: true,
            body: {
                deviceToken: token,
                platform: DeviceUtils.isAndroid() ? 1 : 2, //平台类型 1安卓 2 ios
                userType: 1, //用户类型 1 toB 2 toC 可选
                deviceType: 1, //设备类型 1 mobile 2 pad 可选,
                pushChannel: pushChannelId ?? 4, //1 xg 2 oppo 3 vivo 4 tpns
            },
        });
    }

    static async deviceUnRegister(token: string): Promise<boolean> {
        const rsp: any = await ABCApiNetwork.delete("msg/xg/unregister", {
            queryParameters: {
                deviceToken: token,
                deviceType: 1, //设备类型 1 mobile 2 pad 可选
            },
        });

        return rsp.status.code == 200;
    }

    static async getNotificationDataList(offset?: number): Promise<GetNotificationsResult> {
        LogUtils.d("getNotificationDataList");
        return ABCApiNetwork.get("msg/list", {
            queryParameters: {
                skip: offset?.toString(),
                limit: 10?.toString(),
            },
        });
    }

    ///单条消息标记为已读
    static async markNotificationDataAsRead(id?: number): Promise<boolean> {
        const rsp = await ABCApiNetwork.put<{ code: number }>(`msg/${id}/read`);
        return rsp.code == 200;
    }

    ///全部消息标记为已读
    static async markAllNotificationDataAsRead(category?: number): Promise<boolean> {
        let ret = false;
        LogUtils.d("全部消息标记为已读 markAllNotificationDataAsRead = " + category);
        const rsp = await ABCApiNetwork.put<{ code: number }>("msg/markAllRead", {
            useRsp: true,
            queryParameters: {
                category: category,
            },
        });
        if (rsp.code == 200) {
            ret = true;
        }

        return ret;
    }

    static getUnReadMsgCount(beginDate?: Date): Promise<number> {
        return ABCApiNetwork.get("msg/unread/count", {
            queryParameters: {
                beginDate: beginDate == null ? "" : new Date().format(beginDate.toString()),
            },
        });
    }

    //获取每日收入列表
    static async getLatestMsgSummary(): Promise<GetLatestMsgSummaryRsp> {
        const lastTime1 = await sharedPreferences.getInt(this.getLastFetchMsgCategoryTimeKey(MsgCategory.categorySystem));
        const lastTime2 = await sharedPreferences.getInt(this.getLastFetchMsgCategoryTimeKey(MsgCategory.categoryTodo));
        const lastTime3 = await sharedPreferences.getInt(this.getLastFetchMsgCategoryTimeKey(MsgCategory.categoryWorkReport));
        const lastTime4 = await sharedPreferences.getInt(this.getLastFetchMsgCategoryTimeKey(MsgCategory.categoryStatIncome));

        const categories = [
            new MsgCategoryReq(MsgCategory.categorySystem, lastTime1 != null ? lastTime1 : null),
            new MsgCategoryReq(MsgCategory.categoryTodo, lastTime2 != null ? lastTime2 : null),
            new MsgCategoryReq(MsgCategory.categoryWorkReport, lastTime3 != null ? lastTime3 : null),
            new MsgCategoryReq(MsgCategory.categoryStatIncome, lastTime4 != null ? lastTime4 : null),
        ];

        if (ABCUtils.isEmpty(categories)) {
            throw new ABCApiError(0, "categories参数不能为空");
        }

        const rsp = await ABCApiNetwork.post<LatestMsgSummaryItem[]>("msg/latest/summary", {
            body: categories,
        });

        const _rsp: GetLatestMsgSummaryRsp = JsonMapper.deserialize(GetLatestMsgSummaryRsp, { messages: rsp });

        LogUtils.d("获取每日收入列表 GetLatestMsgSummaryRsp = " + JSON.stringify(_rsp));
        this.sObserver.next(_rsp);

        return _rsp;
    }

    //获取列表详细数据
    static async getNewMsgList(params: { category: number; offset?: number; limit?: number }): Promise<GetNewMsgListRsp> {
        const { category, offset, limit } = params;
        return ABCApiNetwork.get("msg/list", {
            queryParameters: {
                category: category,
                offset: offset ?? 0,
                limit: limit ?? 30,
            },
            clazz: GetNewMsgListRsp,
        });
    }

    static getLastFetchMsgCategoryTimeKey(category?: number): string {
        return `last_fetch_msg_category_unread_count_time_${category}`;
    }

    //更新上次查看时间
    public static updateCategoryReadTime(category?: number): void {
        sharedPreferences.setInt(NotificationDataManager.getLastFetchMsgCategoryTimeKey(category), Date.now());
        const item = this.sObserver.value?.getItemByCategoryType(category);
        if (item != null) {
            item.newMessageCount = 0;
            LogUtils.d(" 更新上次查看时间 static updateCategoryReadTime " + JSON.stringify(this.sObserver.value));
            this.sObserver.next(this.sObserver.value);
        }
    }

    // 获取员工消息通知配置
    static getEmployeeMessageNotificationConfig(employeeId?: string): Promise<GetEmployeeMessageNotificationConfigRsp> {
        return ABCApiNetwork.get(`msg/employee/${employeeId}/config`, {
            queryParameters: {
                id: employeeId,
            },
            clazz: GetEmployeeMessageNotificationConfigRsp,
        });
    }

    // 更新员工消息通知配置
    static postEmployeeMessageNotificationConfig(params: {
        employeeId?: string;
        msgType?: number;
        name?: string;
        xingeSwitch?: number;
    }): Promise<GetEmployeeMessageNotificationConfigRsp> {
        const { employeeId, msgType, name, xingeSwitch } = params;
        return ABCApiNetwork.post(`msg/employee/config`, {
            body: {
                employeeId: employeeId,
                msgType: msgType,
                name: name,
                xingeSwitch: xingeSwitch,
            },
            clazz: GetEmployeeMessageNotificationConfigRsp,
        });
    }
}
