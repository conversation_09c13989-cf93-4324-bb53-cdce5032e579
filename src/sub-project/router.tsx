/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/7/16
 */

import React from "react";
import { Route } from "./url-dispatcher/router-decorator";
import URLProtocols from "./url-dispatcher/url-protocols";
import { UrlRoute } from "./url-dispatcher/url-router";
import { UrlUtils } from "./common-base-module/utils";
import { Version } from "./base-ui/utils/version-utils";
import { AppInfo } from "./base-business/config/app-info";
import { showConfirmDialog } from "./base-ui/dialog/dialog-builder";
import { View } from "@hippy/react";
import { environment, ServerEnvType } from "./base-business/config/environment";
import URL from "url";
import { userCenter } from "./user-center";
import { WxApi } from "./base-business/wxapi/wx-api";
import { wxLoginHelper } from "./login/wx-login-helper";
import { reportPointApi, StatisticalPointKeyConstants } from "./views/statistical-points";
import { ABCNavigator } from "./base-ui/views/abc-navigator";

@Route({ path: URLProtocols.ABC_DEBUG_LOG })
export class AbcDebugLogRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        const { AppLogPage } = require("./app-log-page");
        return <AppLogPage />;
    }
}

@Route({ path: URLProtocols.ABC_UI_COMPONENTS })
export class AbcUIRoute implements UrlRoute {
    handleUrl(/*action: string*/): JSX.Element {
        const { AppUIPage } = require("./app-ui-page");
        return <AppUIPage />;
    }
}

@Route({ path: URLProtocols.ABC_WEBVIEW_V2 })
@Route({ path: URLProtocols.ABC_WEBVIEW })
export class AbcWebviewRoute implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const params = UrlUtils.getUrlParams(action);
        let url = params.get("url");
        const title = params.get("title");
        [...params.keys()].forEach((key) => {
            if (key != "url" && key != "title") {
                const _value = params.get(key);
                if (!!_value) {
                    url += "&";
                    url += key + "=";
                    url += _value;
                }
            }
        });

        //兼容后台不返回http情况
        if (!url?.startsWith("http")) {
            url = "https://" + url;
        }

        const { WebviewPage } = require("./base-ui/webview-page");
        const lessThan252 = new Version(AppInfo.appVersion).compareTo(new Version("2.8.0")) < 0;
        if (url?.includes("/share/order-mobile-renewal") && lessThan252) {
            showConfirmDialog("跳转缴费页面失败", "该软件版本过低，请先进行升级", undefined, undefined, undefined, "rgba(0,0,0,0.8)");
            return <View />;
        }
        return <WebviewPage uri={url ?? ""} title={title} />;
    }
}

/**
 * 跳转小程序
 */
@Route({ path: URLProtocols.JUMP_LINK })
export class AbcJumpLink implements UrlRoute {
    handleUrl(action: string): JSX.Element {
        const params = UrlUtils.getUrlParams(action);
        let url = params.get("url");
        [...params.keys()].forEach((key) => {
            if (key != "url") {
                const _value = params.get(key);
                if (!!_value) {
                    url += "&";
                    url += key + "=";
                    url += _value;
                }
            }
        });
        const pathMatch = url?.match(/^([^?]+)/);
        const pathUrl = pathMatch ? pathMatch[0] : "";

        // 使用正则表达式匹配查询参数部分，并解析成对象
        const queryParams = {};
        const queryParamsMatch = url?.match(/\?([^#]*)/);
        if (queryParamsMatch) {
            const queryString = queryParamsMatch[1];
            const pairs = queryString.split("&");
            pairs.forEach((pair) => {
                let [key, value] = pair.split("=");
                key = key.replace(/'/g, ""); // 去除键周围的单引号
                value = value.replace(/'/g, ""); // 去除值周围的单引号
                // @ts-ignore
                queryParams[key] = decodeURIComponent(value); // 解码URI组件（如果需要）
            });
        }
        Object.assign(queryParams, {
            clinicId: userCenter.clinic?.clinicId,
            toHome: 1,
        });

        const greaterThan270 = new Version(AppInfo.appVersion).compareTo(new Version("2.7.0.0100")) > 0;
        const weChatInstalled = wxLoginHelper._weChatInstalled;
        if (!greaterThan270) {
            showConfirmDialog("", "该软件版本过低，请先进行升级", undefined, undefined, undefined, "rgba(0,0,0,0.8)");
        } else if (!weChatInstalled) {
            showConfirmDialog("", "请先安装微信", undefined, undefined, undefined, "rgba(0,0,0,0.8)");
        } else {
            reportPointApi({ key: StatisticalPointKeyConstants.Push_Notice_SystemMessage });
            const _userName = environment.serverEnvType == ServerEnvType.normal ? "gh_8780ede6c1fd" : "gh_a9fda1ce6263";
            // 自动带入门店信息，不用再选择一次
            const path = URL.format({
                pathname: pathUrl ?? "pages/goods/goods-home/index",
                query: queryParams,
            });
            WxApi.launchMiniProgram({
                userName: _userName,
                miniProgramType: environment.serverEnvType == ServerEnvType.normal ? 0 : 1,
                path: path,
            }).then(() => {
                // 解决从小程序回到应用时，页面会有遮罩，有些机型卡死问题
                ABCNavigator.pop();
            });
        }

        return <View />;
    }
}
