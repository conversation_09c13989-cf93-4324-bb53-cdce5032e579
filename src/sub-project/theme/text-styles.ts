/**
 * Created by he<PERSON><PERSON> on 2020/3/11.
 */
import FontSize from "./font-sizes";
import FontWeight from "./font-weights";
import Colors, { Color } from "./colors";

export class TextStyle {
    fontSize: number;
    fontWeight: string;
    color: Color;
    lineHeight?: number;

    constructor(fontSize: number, fontWeight: string, color: Color, lineHeight?: number) {
        this.fontSize = fontSize;
        this.fontWeight = fontWeight;
        this.color = color;
        this.lineHeight = lineHeight;
    }

    copyWith(options: Partial<TextStyle>): TextStyle {
        const fontSize = options.fontSize ?? this.fontSize;
        const fontWeight = options.fontWeight ?? this.fontWeight;
        const color = options.color ?? this.color;
        const lineHeight = options.lineHeight ?? this.lineHeight;

        return new TextStyle(fontSize, fontWeight, color, lineHeight);
    }
}

class TextStyles {
    mainTitleStyle = new TextStyle(FontSize.size16, FontWeight.medium, Colors.mainTitleColor);
    subTitleStyle = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T6);
    subTitleStyle2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T4);
    subTitleStyle3 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.t3);

    t8NM = new TextStyle(FontSize.size8, FontWeight.normal, Colors.mainColor);

    t10NW = new TextStyle(FontSize.size10, FontWeight.normal, Colors.white);
    t10NS2 = new TextStyle(FontSize.size10, FontWeight.normal, Colors.S2);
    t10NT2 = new TextStyle(FontSize.size10, FontWeight.normal, Colors.T2);
    t10NT3 = new TextStyle(FontSize.size10, FontWeight.normal, Colors.T3);
    t10NT5 = new TextStyle(FontSize.size10, FontWeight.normal, Colors.T5);

    t11NT2 = new TextStyle(FontSize.size11, FontWeight.normal, Colors.T2);
    t11NT3 = new TextStyle(FontSize.size11, FontWeight.normal, Colors.T3);
    t11NG1 = new TextStyle(FontSize.size11, FontWeight.normal, Colors.G1);
    t11NG2 = new TextStyle(FontSize.size11, FontWeight.normal, Colors.G2);
    t11NT6 = new TextStyle(FontSize.size11, FontWeight.normal, Colors.T6);
    t11NT4 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T4);
    t11MT1 = new TextStyle(FontSize.size11, FontWeight.medium, Colors.T1);

    t12NR2 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.R2);
    t12NW = new TextStyle(FontSize.size12, FontWeight.normal, Colors.white);
    t12NS2 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.S2);
    t12MT1 = new TextStyle(FontSize.size12, FontWeight.medium, Colors.T1);
    t12NT1 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T1);
    t12NT2 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T2);
    t12NT3 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T3);
    t12NT4 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T4);
    t12NT5 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T5);
    t12NT6 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.T6);
    t12NY1 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.Y1);
    t12NY2 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.Y2);
    t12BB = new TextStyle(FontSize.size12, FontWeight.bold, Colors.black);
    t12BW = new TextStyle(FontSize.size12, FontWeight.bold, Colors.white);
    t12BT1 = new TextStyle(FontSize.size12, FontWeight.bold, Colors.T1);
    t12NB1 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.B1);
    t12NB = new TextStyle(FontSize.size12, FontWeight.normal, Colors.black);
    t12NM = new TextStyle(FontSize.size12, FontWeight.normal, Colors.mainColor);
    t12NM2 = new TextStyle(FontSize.size12, FontWeight.normal, Colors.theme2);

    t13NT9 = new TextStyle(FontSize.size13, FontWeight.normal, Colors.T9);
    t13NT1 = new TextStyle(FontSize.size13, FontWeight.normal, Colors.t1);
    t13NT2 = new TextStyle(FontSize.size13, FontWeight.normal, Colors.t2);
    t13NY2 = new TextStyle(FontSize.size13, FontWeight.normal, Colors.Y2);
    t13NM = new TextStyle(FontSize.size13, FontWeight.normal, Colors.mainColor);

    t14MT0 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.T0);
    t14MT1 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.T1);
    t14MT3 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.t3);
    t14MS2 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.S2);
    t14MM = new TextStyle(FontSize.size14, FontWeight.medium, Colors.mainColor);
    t14MM2 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.theme2);
    t14MB = new TextStyle(FontSize.size14, FontWeight.medium, Colors.black);
    t14BM = new TextStyle(FontSize.size14, FontWeight.bold, Colors.mainColor);
    t14BB = new TextStyle(FontSize.size14, FontWeight.bold, Colors.black);
    t14MW = new TextStyle(FontSize.size14, FontWeight.medium, Colors.white);

    t14MY2 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.Y2);
    t14MB1 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.B1);
    t14MT2 = new TextStyle(FontSize.size14, FontWeight.medium, Colors.T2);
    t14NT0 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T0);
    t14NT2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T2);
    t14Nt2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.t2);
    t14NT3 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T3);
    t14NT4 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T4);
    t14NB = new TextStyle(FontSize.size14, FontWeight.normal, Colors.black);
    t14NS2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.S2);
    t14NB1 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.B1);
    t14NB2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.B2);
    t14NY1 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.Y1);
    t14NY2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.Y2);
    t14NM = new TextStyle(FontSize.size14, FontWeight.normal, Colors.mainColor);
    t14NM2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.theme2);
    t14NT1 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T1);
    t14NW = new TextStyle(FontSize.size14, FontWeight.normal, Colors.white);
    t14NT6 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T6);
    t14NT9 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.T9);
    t14NG2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.G2);
    t14NR2 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.R2);
    t14NFG = new TextStyle(FontSize.size14, FontWeight.medium, Colors.freshGreen);

    t14NP1 = new TextStyle(FontSize.size14, FontWeight.normal, Colors.P1);

    t15NT1 = new TextStyle(FontSize.size15, FontWeight.normal, Colors.T1);
    t15MT1 = new TextStyle(FontSize.size15, FontWeight.medium, Colors.T1);

    t16MB = new TextStyle(FontSize.size16, FontWeight.medium, Colors.black);
    t16MW = new TextStyle(FontSize.size16, FontWeight.medium, Colors.white);
    t16MB2 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.B2);
    t16MB1 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.B1);
    t16MY2 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.Y2);
    t16MT0 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T0);
    t16MT1 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T1);
    t16MT2 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T2);
    t16MT3 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T3);
    t16MT4 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T4);
    t16MM = new TextStyle(FontSize.size16, FontWeight.medium, Colors.mainColor);
    t16MT8 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T8);
    t16MT9 = new TextStyle(FontSize.size16, FontWeight.medium, Colors.T9);
    t16NT3 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T3);
    t16NT6 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T6);
    t16NG2 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.G2);
    t16NT2 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T2);
    t16NY2 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.Y2);
    t16NB = new TextStyle(FontSize.size16, FontWeight.normal, Colors.black);
    t16NB1 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.B1);
    t16NT0 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T0);
    t16NT1 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T1);
    t16NW = new TextStyle(FontSize.size16, FontWeight.normal, Colors.white);
    t16NT = new TextStyle(FontSize.size16, FontWeight.normal, Colors.theme2);
    t16MT = new TextStyle(FontSize.size16, FontWeight.medium, Colors.theme2);
    t16NR = new TextStyle(FontSize.size16, FontWeight.normal, Colors.red);
    t16NR2 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.R2);
    t16NM = new TextStyle(FontSize.size16, FontWeight.normal, Colors.mainColor);
    t16NT4 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T4);
    t16NT9 = new TextStyle(FontSize.size16, FontWeight.normal, Colors.T9);
    t16BW = new TextStyle(FontSize.size16, FontWeight.bold, Colors.white);
    t16BB = new TextStyle(FontSize.size16, FontWeight.bold, Colors.black);

    t18BB = new TextStyle(FontSize.size18, FontWeight.bold, Colors.black);
    t18MB = new TextStyle(FontSize.size18, FontWeight.medium, Colors.black);
    t18NT1 = new TextStyle(FontSize.size18, FontWeight.normal, Colors.T1);
    t18MT1 = new TextStyle(FontSize.size18, FontWeight.medium, Colors.T1);
    t18MT2 = new TextStyle(FontSize.size18, FontWeight.medium, Colors.T2);
    t18MT4 = new TextStyle(FontSize.size18, FontWeight.medium, Colors.T4);
    t18MY2 = new TextStyle(FontSize.size18, FontWeight.medium, Colors.Y2);
    t18MW = new TextStyle(FontSize.size18, FontWeight.medium, Colors.white);
    t18MM = new TextStyle(FontSize.size18, FontWeight.medium, Colors.mainColor);
    t18NM = new TextStyle(FontSize.size18, FontWeight.normal, Colors.mainColor);
    t18NT2 = new TextStyle(FontSize.size18, FontWeight.normal, Colors.T2);
    t18NT4 = new TextStyle(FontSize.size18, FontWeight.normal, Colors.T4);
    t18NW = new TextStyle(FontSize.size18, FontWeight.normal, Colors.white);
    t18NB = new TextStyle(FontSize.size18, FontWeight.normal, Colors.black);

    t20NY2 = new TextStyle(FontSize.size20, FontWeight.normal, Colors.Y2);
    t20MY2 = new TextStyle(FontSize.size20, FontWeight.medium, Colors.Y2);
    t20MB = new TextStyle(FontSize.size20, FontWeight.medium, Colors.black);
    t20BB = new TextStyle(FontSize.size20, FontWeight.bold, Colors.black);
    t20BW = new TextStyle(FontSize.size20, FontWeight.bold, Colors.white);
    t20BM = new TextStyle(FontSize.size20, FontWeight.bold, Colors.mainColor);
    t20BT1 = new TextStyle(FontSize.size20, FontWeight.bold, Colors.T1);

    t20MT2 = new TextStyle(FontSize.size20, FontWeight.medium, Colors.T2);
    t20MW = new TextStyle(FontSize.size20, FontWeight.medium, Colors.white);
    t22NW = new TextStyle(FontSize.size22, FontWeight.normal, Colors.white);
    t22MW = new TextStyle(FontSize.size22, FontWeight.medium, Colors.white);
    t22MM = new TextStyle(FontSize.size22, FontWeight.medium, Colors.mainColor);
    t22MB = new TextStyle(FontSize.size22, FontWeight.medium, Colors.black);
    t22MT2 = new TextStyle(FontSize.size22, FontWeight.medium, Colors.T2);
    t22NT1 = new TextStyle(FontSize.size22, FontWeight.normal, Colors.T1);
    t22BW = new TextStyle(FontSize.size22, FontWeight.bold, Colors.white);
}

const textStyles = new TextStyles();
export default textStyles;

function refreshTextStyle(): void {
    Object.assign(textStyles, new TextStyles());
}

const DINAlternate = "DIN Alternate";
const KarlaRegulars = "Karla-Regular";
const DINMittelschriftAlternate = "DINMittelschrift LT Alternate";
// const DINAlternate = "DINAlternate";
export { DINAlternate, refreshTextStyle, DINMittelschriftAlternate, KarlaRegulars };
