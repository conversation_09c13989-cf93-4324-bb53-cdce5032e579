/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-23
 *
 * @description
 */

import { setColors } from "./colors";
import { ChineseMedicineColor } from "./chinese-medicine-color";
import { refreshTextStyle } from "./text-styles";
import { Colors } from "./default-colors";
import { Subject } from "rxjs";
import { setAbcTheme } from "@app/theme";

export enum ThemeType {
    normal,
    chineseMedicine,
}

export const ThemeNames = ["清新绿", "国风红"];

const PREF_THEME_INDEX = "theme_index";

export class ThemeManager {
    static currentTheme: ThemeType = ThemeType.normal;
    static themeObserver = new Subject<ThemeType>();

    public static init(): void {
        const { sharedPreferences } = require("../base-business/preferences/shared-preferences");
        const themeIndex = sharedPreferences.getInt(PREF_THEME_INDEX);

        if (themeIndex != undefined) {
            //启动时，初始化不需要通知皮肤变化
            ThemeManager.setTheme(themeIndex, false);
        }
    }

    public static setTheme(type: ThemeType, notifyThemeChanged = true): void {
        if (ThemeManager.currentTheme == type) return;

        const { sharedPreferences } = require("../base-business/preferences/shared-preferences");
        sharedPreferences.setInt(PREF_THEME_INDEX, type);

        if (type == ThemeType.chineseMedicine) {
            setColors(new ChineseMedicineColor());
        } else if (type == ThemeType.normal) {
            setColors(new Colors());
        }
        ThemeManager.currentTheme = type;
        setAbcTheme(type);
        refreshTextStyle();

        if (notifyThemeChanged) ThemeManager.themeObserver.next(type);
    }

    public static getCurrentTheme(): ThemeType {
        return ThemeManager.currentTheme;
    }

    public static getCurrentThemeName(): string {
        return ThemeNames[ThemeManager.currentTheme];
    }
}
