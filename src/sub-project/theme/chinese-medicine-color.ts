/**
 * Created by he<PERSON><PERSON> on 2020/3/11.
 */

import { Colors } from "./default-colors";

export declare type Color = string;

export class ChineseMedicineColor extends Colors {
    black = "#000";
    white = "#fff";
    red = "#f00";

    transparent = "transparent";
    window_bg = "#F2F2F2";

    toast_bg = "#333333F2"; // Toast背景色

    sheBaoTag_bg = "rgba(0, 0, 0, 0.03)"; // 社保标签背景色
    sheBaoCardOnTouch_bg = "#EEEEEE"; // 社保卡片点击状态态背景色

    theme1 = "#AA5637";
    theme2 = "#E28F4C";
    theme3 = "#E17F5B";
    theme4 = "#FADED3";
    theme5 = "#F6C9B8";
    theme6 = "#F3B8A3";

    theme2Mask8 = "rgba(198, 110, 78, 0.08)";
    theme2Mask12 = "rgba(198, 110, 78, 0.12)";

    B1 = "#005ED9";
    B2 = "#2680F7";
    B3 = "#5199F8";
    B4 = "#E9F2FE";
    B5 = "#D4E6FD";
    B6 = "#85BAFF";

    S1 = "#000000";
    S2 = "#FFFFFF";
    S3 = "#2680F7";
    S4 = "#385068";

    // 文字色
    T1 = "#000000";
    T2 = "#7A8794";
    T3 = "#96A4B3";
    T4 = "#A0B1C4";
    T8 = "#3C95C4";
    T9 = "#626D77";

    // 界面色
    P1 = "#CED0DA";

    //  P2 :'#FFCED0DA';
    P3 = "#DADBE0";
    P4 = "#EFF3F6";
    P5 = "#E6EAEE";
    P6 = "#C1C9D0";
    P7 = "#EBF5FF";

    // 装饰色
    R1 = "#E52D5B";
    R2 = "#FF3333";
    R3 = "#FF5B84";
    R4 = "#FFEAEF";
    R5 = "#FFD6E0";
    R6 = "#F04A3E";

    Y1 = "#E5892D";
    Y2 = "#FF9933";
    Y3 = "#FFAD5B";
    Y4 = "#FFF4EA";
    Y5 = "#FFEBD6";

    F6 = "#FBF6F4";

    G1 = "#08A446";
    G2 = "#0EBA52";
    G3 = "#23CF67";
    G4 = "#E3FCED";
    G5 = "#BBF2D1";

    //报表配色 -- chart 配色参照
    C1 = "#FD9800";
    C2 = "#0A8CEA";
    C3 = "#067CE0E";
    C4 = "#FF6464";
    C5 = "#FEC166";
    C6 = "#6CBAF2";

    D2 = "#F5F7FB";

    // errorBorder = this.R2;
    errorBorder = "#FF9933";
    errorBorderBg = "rgba(255,153,51,0.08)"; // 代码重构底部线条

    mainColor = "#C66E4E";
    mainColorMask30 = "rgba(198,110,78,0.30)";
    mainColorMask60 = "rgba(198,110,78,0.60)";
    bottomBtnDisable = "#e6eaee";
    lightMainColor = "#E0F6EF";

    mainTitleColor = "#000000";

    dividerLineColor = "#F0F0F0";

    contentBgColor = "white";
    maskBg = "#0000005c";
    cardYellowBackgroundColor = "#FFFDEC";

    priceColor = "#FF9933";
    popMenuBg = "#414244";

    //重构颜色
    mainColor_05 = `${this.mainColor}0D`;
    mainColor_08 = `${this.mainColor}14`;
    mainColor_20 = `${this.mainColor}33`;

    theme2_05 = `${this.theme2}0D`;
    theme2_08 = `${this.theme2}08`;
    theme2_10 = `${this.theme2}10`;

    //背景色
    bg1 = "#F8F8F8";

    //文字色
    t1 = "#333333";
    t2 = "#777777";
    t3 = "#AAABB3";
    t4 = "#cccccc";

    //重构颜色end
    tagBg = "#FBF4F1";
    //按钮点击态
    mainColorPress = "#9E583E";
    femalePatientColor = "#FF6082"; // 女性患者图标颜色
    malePatientColor = "#58A0FF"; // 男性患者图标颜色
    freshGreen = "#08BB88"; // 清新绿

    statTopBg = "#C56E4D"; // 统计上方背景色
}
