/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2024/3/8
 * @Copyright 成都字节流科技有限公司© 2024
 */
import React from "react";
import { BasePage, Tab, Tabs } from "../base-ui";
import { Color, Colors, Sizes, TextStyles } from "../theme";
import { StyleSheet, View } from "@hippy/react";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { DeviceUtils } from "../base-ui/utils/device-utils";

const AbcModuleTabPageStyles = StyleSheet.create({
    tabsStyle: {
        flex: 1,
        justifyContent: "center",
        backgroundColor: Colors.panelBg,
        height: Sizes.dp44,
    },
    tabStyle: {
        marginRight: Sizes.dp44,
        ...TextStyles.t16NT1.copyWith({ color: "rgba(51,51,51,0.7)" }),
    },
});

export interface ModuleTabItem {
    id: number;
    title: string;
    badge?: boolean;
    contentRender: (item: ModuleTabItem) => JSX.Element;
}

export class AbcModuleTabPage<P, S extends {}, VT> extends BasePage<P, S> {
    protected currentTabIndex: number;
    protected listViewRefs: Map<number, VT> = new Map<number, VT>();
    protected tabPageRef?: Tabs | null;
    protected tabsViewProps = {};

    constructor(p: P) {
        super(p);
        this.currentTabIndex = 0;
    }

    get listViewRef(): VT | undefined {
        return this.listViewRefs.get(this.currentTabIndex);
    }

    getAppBar(): JSX.Element | undefined {
        return undefined;
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    renderContent(): JSX.Element | undefined {
        const tabContent = this.renderTabContent();
        return (
            <View style={{ position: "relative", flex: 1 }}>
                <Tabs
                    ref={(ref) => {
                        this.tabPageRef = ref;
                    }}
                    initialPage={this.currentTabIndex}
                    lineColor={tabContent.length > 1 ? undefined : Colors.transparent}
                    tabsStyle={AbcModuleTabPageStyles.tabsStyle}
                    tabStyle={AbcModuleTabPageStyles.tabStyle}
                    leftSuffix={this.renderLeftSuffix.bind(this)}
                    rightSuffix={this.renderRightSuffix.bind(this)}
                    onChange={this.handleChangeTab.bind(this)}
                    {...this.tabsViewProps}
                >
                    {tabContent}
                </Tabs>
            </View>
        );
    }

    handleChangeTab(index: number): void {
        this.currentTabIndex = index;
        this.setState({});
    }

    protected renderLeftSuffix(hasMR = true): JSX.Element {
        return (
            <IconFontView
                name={"back"}
                size={Sizes.dp20}
                color={Colors.T1}
                style={{
                    ...Sizes.paddingLTRB(DeviceUtils.isOhos() ? 0 : Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10),
                    marginLeft: DeviceUtils.isOhos() ? Sizes.dp16 : 0,
                    marginRight: hasMR ? Sizes.dp27 : 0,
                }}
                onClick={() => {
                    ABCNavigator.pop();
                }}
            />
        );
    }

    protected renderRightSuffix(): JSX.Element {
        return (
            <View
                style={[
                    Sizes.paddingLTRB(Sizes.dp10, Sizes.dp5, Sizes.dp12, Sizes.dp5),
                    {
                        width: Sizes.dp63,
                        height: Sizes.dp30,
                        marginRight: Sizes.dp16,
                    },
                ]}
            />
        );
    }

    protected createModuleTabList(): ModuleTabItem[] {
        return [];
    }

    protected renderTabContent(): JSX.Element[] {
        return this.createModuleTabList().map((item, index) => (
            <Tab key={index} title={item.title} badge={item.badge}>
                {item.contentRender(item)}
            </Tab>
        ));
    }
}
