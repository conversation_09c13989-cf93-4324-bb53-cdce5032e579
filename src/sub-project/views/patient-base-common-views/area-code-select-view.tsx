/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description 身份证类型选择视图
 * <AUTHOR>
 * @CreateDate 2025/01/22
 * @Copyright 成都字节流科技有限公司 2025
 */

import React from "react";
import { Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView } from "../../base-ui";
import { AbcView } from "../../base-ui/views/abc-view";
import { showIdCardTypePicker } from "../../base-ui/picker/id-card-type-picker";

interface IdCardTypeSelectViewProps {
    value?: string;
    disabled?: boolean;
    onChange?: (value: string) => void;
}

const IdCardTypeSelectView = (props?: IdCardTypeSelectViewProps): JSX.Element => {
    const { value, disabled = false, onChange } = props ?? {};

    const handleSelectIdCardType = () => {
        if (disabled) return;
        showIdCardTypePicker({ value: value ?? "身份证" }).then((value) => {
            onChange && !!value && onChange(value);
        });
    };

    return (
        <AbcView
            style={[ABCStyles.rowAlignCenter]}
            onClick={() => {
                handleSelectIdCardType();
            }}
        >
            <Text style={[{ marginRight: Sizes.dp4 }, TextStyles.t16NT2.copyWith({ color: Colors.t2 })]}>
                {value?.substr(0, 6) ?? "身份证"}
            </Text>
            <IconFontView name={"arrow_down"} size={Sizes.dp16} color={Colors.T6} />
        </AbcView>
    );
};

export default IdCardTypeSelectView;
