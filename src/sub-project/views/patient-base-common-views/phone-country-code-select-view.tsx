/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description
 * <AUTHOR>
 * @CreateDate 2023/7/21
 * @Copyright 成都字节流科技有限公司© 2023
 */

import React from "react";
import { Text } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView } from "../../base-ui";
import { AbcView } from "../../base-ui/views/abc-view";
import { showPhoneCountryPicker } from "../../base-ui/picker/phone-country-picker";

interface PhoneCountryCodeSelectViewProps {
    code?: string;
    disabled?: boolean;

    onChange?: (code: string) => void;
}

const PhoneCountryCodeSelectView = (props?: PhoneCountryCodeSelectViewProps): JSX.Element => {
    const { code, disabled = false, onChange } = props ?? {};

    const handleSelectCountryCode = () => {
        if (disabled) return;
        showPhoneCountryPicker({ code: code ?? "86" }).then((code) => {
            onChange && !!code && onChange(code);
        });
    };

    return (
        <AbcView
            style={[ABCStyles.rowAlignCenter]}
            onClick={() => {
                handleSelectCountryCode();
            }}
        >
            <Text style={[{ marginRight: Sizes.dp4 }, TextStyles.t16NT2.copyWith({ color: Colors.t2 })]}>+{code ?? "86"}</Text>
            <IconFontView name={"arrow_down"} size={Sizes.dp16} color={Colors.T6} />
        </AbcView>
    );
};

export default PhoneCountryCodeSelectView;
