/**
 * create by deng<PERSON><PERSON>
 * desc:
 * create date 2020/5/21
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { HistoryPermissionModuleType, MemberInfo, Patient } from "../base-business/data/beans";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { SizedBox } from "../base-ui";
import { BaseComponent } from "../base-ui/base-component";
import { AbcView } from "../base-ui/views/abc-view";
import IconFontView, { RightArrowView } from "../base-ui/iconfont/iconfont-view";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import PatientDetailEditPage from "../outpatient/patient-edit-page/patient-edit-page";
import { OutpatientHistoryDialogResult } from "../outpatient/outpatient-history-dialog";
import OutpatientHistoryPage, { OutpatientHistoryPagType } from "../outpatient/outpatient-history-page";
import _ from "lodash";
import { PatientSearchPage } from "../outpatient/patient-edit-page/patient-search-page";
import { PatientInfoEditComponentsConfig } from "../outpatient/patient-edit-page/patient-edit-data";
import { CrmAgent } from "../patients/data/crm-agent";
import { showBottomPanel } from "../base-ui/abc-app-library/panel";
import { PatientInfoMethod } from "../base-business/data/patient-beans";

interface PatientInfoViewProps {
    patientInfo?: Patient;
    memberInfo?: MemberInfo;
    statusImg?: string;
    bottomLine?: boolean;
    rightSuffix?: () => JSX.Element;
    editMode?: boolean;
    hintSex?: string;
    showPhoneNumber?: boolean; // 是否可访问已执行单据详情 （如果没有权则限隐藏手机号）
    type?: HistoryPermissionModuleType; // 模块类型
    canSeePatientMobileInRegister?: boolean; //挂号预约-可以查看患者手机号
    canSeePatientMobileInOutpatient?: boolean; //门诊-可以查看患者手机号
    canSeePatientMobileInCashier?: boolean; //收费-可以查看患者手机号
    canSeePatientMobileInPharmacy?: boolean; //    药房-可以查看患者手机号

    onClick?: () => void;
}

export class PatientInfoView extends React.Component<PatientInfoViewProps, any> {
    constructor(props: PatientInfoViewProps) {
        super(props);
    }

    static defaultProps = {
        bottomLine: true,
        editMode: false,
        showPhoneNumber: true,
    };

    _renderUpView(): JSX.Element {
        const { editMode } = this.props;

        const memberTypeName =
            this.props.patientInfo?.memberInfo?.memberTypeInfo?.memberTypeName ?? this.props.memberInfo?.memberType?.name ?? "";
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <Text
                    style={[editMode && _.isEmpty(this.props.patientInfo?.name) ? TextStyles.t18MT4 : TextStyles.t18MB, { flexShrink: 1 }]}
                    numberOfLines={1}
                >
                    {this.props.patientInfo?.name ? this.props.patientInfo?.name : editMode ? "姓名" : "匿名患者"}
                </Text>
                <SizedBox width={Sizes.dp16} />
                {this.props.patientInfo?.isMember ? (
                    <View style={{ flexDirection: "row" }}>
                        <AssetImageView
                            name={"charge_invoice_patient_member"}
                            style={{
                                marginLeft: Sizes.dp6,
                                width: Sizes.dp16,
                                height: Sizes.dp16,
                            }}
                        />
                        <Text style={[TextStyles.t14NT1, { marginLeft: Sizes.dp4 }]}>{memberTypeName}</Text>
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }

    _renderDownView(): JSX.Element {
        const { patientInfo, showPhoneNumber, ...others } = this.props;
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NT2}>{patientInfo?.sex ?? this.props.hintSex ?? ""}</Text>
                <SizedBox width={Sizes.dp12} />
                <Text style={TextStyles.t16NT2}>{patientInfo?.age?.displayAge ?? "-岁"}</Text>
                <SizedBox width={Sizes.dp12} />
                {!!showPhoneNumber && (
                    <Text style={TextStyles.t16NT2}>
                        {PatientInfoMethod.canSeePatientMobile({ ...others })
                            ? patientInfo?.mobile ?? ""
                            : PatientInfoMethod.encryptThePhoneNumber(patientInfo?.mobile)}
                    </Text>
                )}
            </View>
        );
    }

    _renderRightSuffix(): JSX.Element {
        const { rightSuffix } = this.props;
        if (rightSuffix) return rightSuffix();
        else return <View />;
    }

    render(): JSX.Element {
        const { bottomLine, onClick } = this.props;
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    {
                        height: Sizes.dp76,
                        justifyContent: "space-between",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                        backgroundColor: Colors.white,
                        borderBottomColor: Colors.window_bg,
                        borderBottomWidth: 1,
                        marginBottom: bottomLine ? Sizes.dp10 : 0,
                    },
                ]}
                onClick={onClick}
            >
                <View style={{ flexShrink: 1 }}>
                    {this._renderUpView()}
                    <SizedBox height={Sizes.dp6} />
                    {this._renderDownView()}
                </View>
                <View>{this._renderRightSuffix()}</View>
            </View>
        );
    }
}

interface ModifyPatientInfoIconProps extends PatientInfoEditComponentsConfig {
    enableEdit?: boolean;
    onChange?: (patient: Patient) => void;
}

export class ModifyPatientInfoIcon extends BaseComponent<ModifyPatientInfoIconProps> {
    constructor(props: ModifyPatientInfoIconProps) {
        super(props);
    }

    async _onClickEditInfo(): Promise<void> {
        const { patient, onChange, enableEdit, patientSwitchable } = this.props;
        if (!enableEdit) return;
        let _patient: Patient | undefined = patient;
        if (patient?.id)
            await CrmAgent.getPatientById(patient?.id).then((patientRsp: Patient) => {
                _patient = _.merge(patient, patientRsp);
            });
        const _editInfo: Patient = await ABCNavigator.navigateToPage(
            <PatientDetailEditPage {...this.props} patient={_patient} __switchPatient={patientSwitchable} />
        );
        if (_editInfo) {
            onChange?.(_editInfo);
        }
    }

    render(): JSX.Element {
        const { enableEdit, patient } = this.props;
        return (
            <AbcView onClick={this._onClickEditInfo.bind(this)} style={{ alignItems: "center", justifyContent: "center" }}>
                <IconFontView
                    style={Sizes.paddingLTRB(Sizes.dp6, Sizes.dp6)}
                    name="profile"
                    color={enableEdit ? Colors.mainColor : Colors.P6}
                    size={Sizes.dp20}
                />
                {!!patient?.isMember && (
                    <AssetImageView
                        name={"charge_invoice_patient_member"}
                        style={{
                            position: "absolute",
                            right: Sizes.dp6,
                            bottom: Sizes.dp9,
                            width: Sizes.dp12,
                            height: Sizes.dp12,
                        }}
                    />
                )}
            </AbcView>
        );
    }
}

interface ConsultationHistoryIconProps {
    patient?: Patient;
    enableEdit?: boolean;

    hideCopyHistory?: boolean; //是否隐藏复制历史病历按钮，在enableEdit为true时才生效

    treatOnlineClinicId?: string; //网诊单中复制使用医生所在诊所

    onChange?: (result: OutpatientHistoryDialogResult) => void;
    isCanCheckPrescription?: boolean;
}

export class ConsultationHistoryIcon extends BaseComponent<ConsultationHistoryIconProps> {
    async _navToHistoryPage(): Promise<void> {
        const { patient, enableEdit, hideCopyHistory, onChange, treatOnlineClinicId, isCanCheckPrescription } = this.props;
        if (!patient?.id) return;
        const result: OutpatientHistoryDialogResult = await showBottomPanel(
            <OutpatientHistoryPage
                // patientId={patient?.id ?? ""}
                patient={patient}
                type={OutpatientHistoryPagType.visitHistory}
                viewOnly={!enableEdit}
                hideCopyHistory={hideCopyHistory}
                treatOnlineClinicId={treatOnlineClinicId}
                isCanCheckPrescription={isCanCheckPrescription}
            />,
            { topMaskHeight: Sizes.dp160 }
        );
        if (result) {
            onChange?.(result);
        }
    }

    render(): JSX.Element {
        const { patient } = this.props;
        return (
            <AbcView
                style={{ alignItems: "center", justifyContent: "center", width: Sizes.dp44 }}
                onClick={() => {
                    this._navToHistoryPage().then();
                }}
            >
                <IconFontView
                    style={Sizes.paddingLTRB(Sizes.dp6, Sizes.dp6)}
                    name={"history"}
                    size={Sizes.dp20}
                    color={patient?.id ? Colors.mainColor : Colors.P6}
                />
            </AbcView>
        );
    }
}

interface PatientInfoViewWithModifyAndHistoryProps extends PatientInfoEditComponentsConfig {
    copyEnable?: boolean; //default true
    bottomLine?: boolean;
    hintSex?: string;
    editable?: boolean; //default true
    isCanCheckPrescription?: boolean; //是否有可以查看处方的权限

    showHistoryBtn?: boolean; // 是否可查看就诊历史历史
    showEditBtn?: boolean; // 是否显示修改按钮
    type?: HistoryPermissionModuleType;
    canSeePatientMobileInRegister?: boolean; //挂号预约-可以查看患者手机号
    canSeePatientMobileInOutpatient?: boolean; //门诊-可以查看患者手机号
    canSeePatientMobileInCashier?: boolean; //收费-可以查看患者手机号
    canSeePatientMobileInPharmacy?: boolean; //    药房-可以查看患者手机号

    onChanged?(patient: Patient): void;

    onCopyHistory?(result: OutpatientHistoryDialogResult): void;
}

/**
 * 患者信息编辑，附带更换患者和处方复制功能
 */
export class PatientInfoViewWithModifyAndHistory extends BaseComponent<PatientInfoViewWithModifyAndHistoryProps> {
    static defaultProps: PatientInfoViewWithModifyAndHistoryProps = {
        editable: true,
        showHistoryBtn: true,
        showEditBtn: true,
    };

    render(): JSX.Element {
        const { patient, copyEnable = true, bottomLine = true, hintSex, editable, showHistoryBtn, showEditBtn, ...others } = this.props;
        return (
            <PatientInfoView
                patientInfo={patient}
                editMode={editable}
                bottomLine={bottomLine}
                hintSex={hintSex}
                onClick={async () => {
                    if (!editable || !_.isEmpty(patient?.name)) return;
                    const patientInfo: Patient = await ABCNavigator.navigateToPage(<PatientSearchPage {...this.props} />);

                    if (patientInfo) {
                        this.props.onChanged?.(patientInfo);
                    }
                }}
                rightSuffix={() => {
                    const nameEmpty = _.isEmpty(patient?.name);
                    if (!editable) return <View />;
                    if (nameEmpty) return <RightArrowView />;

                    return (
                        <View style={ABCStyles.rowAlignCenter}>
                            {!!showEditBtn && (
                                <ModifyPatientInfoIcon
                                    {...this.props}
                                    patient={patient}
                                    enableEdit={true}
                                    onChange={(patient) => {
                                        this.props.onChanged?.(patient);
                                    }}
                                />
                            )}
                            {!!showHistoryBtn && (
                                <ConsultationHistoryIcon
                                    patient={patient}
                                    enableEdit={copyEnable}
                                    hideCopyHistory={true}
                                    onChange={this.props.onCopyHistory}
                                />
                            )}
                        </View>
                    );
                }}
                {...others}
            />
        );
    }
}
