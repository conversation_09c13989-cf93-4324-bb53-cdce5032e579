import { BaseComponent } from "../../base-ui/base-component";
import { employeeSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { userCenter } from "../../user-center";
import abcOverlay, { OverlayViewKey } from "../../base-ui/views/abc-overlay";
import React from "react";
import { ABCStyles, Colors, Sizes } from "../../theme";
import { View } from "@hippy/react";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { IconFontView } from "../../base-ui";
import { AbcView } from "../../base-ui/views/abc-view";
import { environment, ServerEnvType } from "../../base-business/config/environment";
import URL from "url";
import { WxApi } from "../../base-business/wxapi/wx-api";
const double_eleven_ad = "double_eleven_ad";
export class AbcDoubleElevenDialog extends BaseComponent {
    static async show(): Promise<void> {
        const canShowAd = employeeSharedPreferences.getInt(double_eleven_ad);
        const clinic = userCenter.clinic;
        //有采购权限的成员
        if (!clinic?.isPurchasing || canShowAd == 1) return;
        return abcOverlay.show(<AbcDoubleElevenDialog />, OverlayViewKey.abcDoubleElevenDialog);
    }
    handleClickCancel(): void {
        employeeSharedPreferences.setInt(double_eleven_ad, 1);
        abcOverlay.hide(OverlayViewKey.abcDoubleElevenDialog);
    }
    jumpToZhiCaiMall(): void {
        employeeSharedPreferences.setInt(double_eleven_ad, 1);
        abcOverlay.hide(OverlayViewKey.abcDoubleElevenDialog);
        const _userName = environment.serverEnvType == ServerEnvType.normal ? "gh_8780ede6c1fd" : "gh_a9fda1ce6263";
        // 自动带入门店信息，不用再选择一次
        const path = URL.format({
            pathname: "pages/goods/goods-validate/index",
            query: {
                clinicId: userCenter.clinic?.clinicId,
                hiddenDialog: 1,
            },
        });

        WxApi.launchMiniProgram({
            userName: _userName,
            miniProgramType: environment.serverEnvType == ServerEnvType.normal ? 0 : 1,
            path: path,
        });
    }
    render(): JSX.Element {
        return (
            <View style={[ABCStyles.absoluteFill, { backgroundColor: Colors.maskBg, zIndex: 1 }]} onClick={() => ({})}>
                <View style={{ flexGrow: 1, alignItems: "center", justifyContent: "center", zIndex: 2 }}>
                    <AbcView style={{ position: "relative" }} onClick={() => this.jumpToZhiCaiMall()}>
                        <AssetImageView
                            style={{ width: pxToDp(300), height: pxToDp(415) }}
                            name={"double_eleven_ad"}
                            resizeMode={"contain"}
                        />
                        <IconFontView
                            name={"close-color"}
                            color={"rgba(255,255,255,0.6)"}
                            size={Sizes.dp20}
                            style={{ width: Sizes.dp20, height: Sizes.dp20, position: "absolute", top: 26, right: 16 }}
                            onClick={this.handleClickCancel.bind(this)}
                        />
                    </AbcView>
                </View>
            </View>
        );
    }
}
