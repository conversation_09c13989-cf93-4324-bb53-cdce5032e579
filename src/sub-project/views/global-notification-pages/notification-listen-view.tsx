/**
 * create by den<PERSON><PERSON><PERSON>
 * @Description 全局消息监听组件
 * <AUTHOR>
 * @CreateDate 2022/12/13
 * @Copyright 成都字节流科技有限公司© 2022
 */

import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { interval, merge, Subject, Subscription, timer } from "rxjs";
import abcOverlay from "../../base-ui/views/abc-overlay";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { AssetImageView } from "../../base-ui/views/asset-image-view";
import { IconFontView, SizedBox } from "../../base-ui";
import AbcMotion from "../../base-ui/abc-app-library/transform/abc-motion";
import _ from "lodash";
import { onlineMessageManager } from "../../base-business/msg/online-message-manager";
import { MessageBodyDetailType, SystemMessageNotifyMsg } from "../../data/notification";
import { BaseComponent } from "../../base-ui/base-component";
import { DisposableTracker } from "../../common-base-module/cleanup/disposable";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { AbcView } from "../../base-ui/views/abc-view";
import { AbcWebview } from "../../base-ui/abc-webview";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { Version } from "../../base-ui/utils/version-utils";
import { AppInfo } from "../../base-business/config/app-info";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { imService } from "../../outpatient/data/im";
import { OutpatientChatNotification } from "./views/outpatient-chat-notification";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { URLProtocols } from "../../url-dispatcher";
import appState from "../../common-base-module/app-state/app-state";
import { OnlinePropertyConfigProvider } from "../../data/online-property-config-provder";
import { IMSceneType } from "../../outpatient/data/im-beans";
import { EditionRenewalNotification } from "./views/edition-renewal-notification";
import { EditionRenewalMsg } from "../../data/edition-renewal-bean";
import { UIUtils } from "../../base-ui/utils";
import { PermissionPurposeNotification } from "./views/permission-purpose-notification";
import { PermissionEvent } from "../../common-base-module/permission/permission-event";

const editionRenewalKey = "editionRenewalNotificationKey";
const permissionPurposeNotificationKey = "permissionPurposeNotificationKey";
export class NotificationListenView extends DisposableTracker {
    static allNotificationUpdateObserver = new Subject();
    globalNotificationList: SystemMessageNotifyMsg[] = [];
    private intervalUpdate4000 = interval(4000);
    private intervalUpdate4000Observer?: Subscription;
    private isOnlineClinic = false;

    constructor() {
        super();
        OnlinePropertyConfigProvider.instance.getChainTreatOnlineRuleConfig().then((rsp) => {
            this.isOnlineClinic = !!rsp?.treatRule?.treatOnlineSwitch;
        });
        onlineMessageManager.systemMessageNotifyObserver
            .subscribe((data: SystemMessageNotifyMsg) => {
                if (
                    [
                        MessageBodyDetailType.socialNotice,
                        MessageBodyDetailType.systemUpdateNotice,
                        MessageBodyDetailType.systemUpdateNoticePre,
                    ].includes(data.messageBody?.messageBody?.data?.type ?? 0) &&
                    !abcOverlay.keys.includes(data.id ?? "")
                ) {
                    this.createNotificationListenItemViews(data);
                }
            })
            .addToDisposableBag(this);

        imService.allNewMessageObserver.subscribe((msgData) => {
            //无网诊权限或者下线
            if (!this.isOnlineClinic) return;
            if (!appState.foregroundState.value) return;
            //患者沟通消息不提醒
            if (msgData.sceneType == IMSceneType.patientConsultation) return;
            const _key = msgData.conversationId ?? "";
            // 判断弹窗重复
            const hasOverlay = abcOverlay.keys.includes(_key);
            // 判断当前是否在网诊页面
            const isOutpatientChatPage = ABCNavigator.getAllRoutes().some((route) => {
                const { routeName } = route;
                return (
                    routeName.includes("OutpatientChatPage") || // 网诊聊天页面
                    routeName.startsWith(URLProtocols.OUTPATIENT_TAB) || // 网诊页面
                    routeName.startsWith(URLProtocols.OUTPATIENT_NET_OUTPATIENT) || // 打开指定网诊单
                    routeName.startsWith(URLProtocols.OUTPATIENT_NETSHEET) // 打开网诊单
                );
            });
            if (!hasOverlay && !isOutpatientChatPage) {
                abcOverlay.show(<OutpatientChatNotification data={msgData} />, _key);
            }
        });

        onlineMessageManager.editionRenewalFeeObserver
            .subscribe((data) => {
                const isPaymentPage = ABCNavigator.getAllRoutes().some((route) => {
                    const { routeName } = route;
                    return routeName.startsWith(URLProtocols.ABC_WEBVIEW); //缴费页面
                });
                if (!abcOverlay.keys.includes(editionRenewalKey) && !isPaymentPage) {
                    this.createEditionRenewalNotificationViews(data);
                }
            })
            .addToDisposableBag(this);

        // 相机、图片、媒体、音频权限等说明
        PermissionEvent.PermissionObservable.subscribe((data) => {
            if (!abcOverlay.keys.includes(permissionPurposeNotificationKey)) {
                this.createPermissionPurposeNotificationViews(data);
            }
        }).addToDisposableBag(this);

        this.init4000Timer();
    }

    init4000Timer(): void {
        this.intervalUpdate4000Observer?.unsubscribe();
        this.intervalUpdate4000Observer = undefined;

        this.intervalUpdate4000Observer = this.intervalUpdate4000.subscribe(() => {
            NotificationListenView.allNotificationUpdateObserver.next();
        });
        this.addDisposable(this.intervalUpdate4000Observer);
    }

    createNotificationListenItemViews(data: SystemMessageNotifyMsg): void {
        this.init4000Timer();

        const { id: overlayKey } = data;
        this.globalNotificationList.push(data);

        abcOverlay.show(
            <NotificationListenItemView
                index={this.globalNotificationList.length}
                msgData={data}
                onCancel={(_key) => {
                    if (!this.globalNotificationList.length) return;
                    const key = _key ?? this.globalNotificationList[0].id!;
                    abcOverlay.hide(key);
                    _.remove(this.globalNotificationList, (item) => item.id == key);
                }}
            />,
            overlayKey ?? ""
        );
    }

    createEditionRenewalNotificationViews(data: EditionRenewalMsg): void {
        this.init4000Timer();
        abcOverlay.show(
            <EditionRenewalNotification
                msgData={data}
                onCancel={(_key) => {
                    abcOverlay.hide(_key ?? editionRenewalKey);
                }}
            />,
            editionRenewalKey
        );
    }
    createPermissionPurposeNotificationViews(type: number): void {
        abcOverlay.show(
            <PermissionPurposeNotification
                type={type}
                onCancel={(_key) => {
                    abcOverlay.hide(_key ?? permissionPurposeNotificationKey);
                }}
            />,
            permissionPurposeNotificationKey
        );
    }
}

interface NotificationListenItemViewProps {
    index: number;
    msgData: SystemMessageNotifyMsg;
    onCancel(key?: string): void;
}

class NotificationListenItemView extends BaseComponent<NotificationListenItemViewProps> {
    private rectTop: number;
    private _AbcMotionRef?: AbcMotion | null;
    constructor(props: NotificationListenItemViewProps) {
        super(props);
        this.rectTop = props.index * Sizes.dp84 - Sizes.dp84 + UIUtils.safeStatusHeight() + Sizes.dp8;
    }

    componentDidMount(): void {
        merge(timer(4000), NotificationListenView.allNotificationUpdateObserver).subscribe(() => {
            return;
            if (this.rectTop == Sizes.dp84) {
                const { msgData } = this.props;
                this.props.onCancel(msgData.id);
            } else {
                this.rectTop -= Sizes.dp84;
                this.setState({});
            }
        });
    }

    handleShowDetail(): void {
        const { msgData } = this.props,
            { title, content } = msgData.messageBody?.messageBody?.data ?? {};
        const greaterThan252_01 = DeviceUtils.isAndroid() || new Version(AppInfo.appVersion).compareTo(new Version("2.5.2.0100")) > 0;
        showConfirmDialog(
            title ?? "",
            (
                <ScrollView>
                    {greaterThan252_01 ? <AbcWebview style={{ height: pxToDp(320) }} source={{ html: content }} /> : <Text>{content}</Text>}
                </ScrollView>
            ) ?? "",
            "我知道了"
        );
    }

    render(): JSX.Element {
        const { msgData } = this.props,
            { title, content } = msgData.messageBody?.messageBody?.data ?? {};
        const _content = content?.replace(/<.*?>/g, "");
        return (
            <View style={[{ position: "absolute", top: this.rectTop, left: 0, right: 0, backgroundColor: Colors.transparent }]}>
                <AbcMotion
                    ref={(ref) => (this._AbcMotionRef = ref)}
                    animation={{
                        opacity: {
                            value: [0, 1],
                            repeatCount: 1,
                            duration: 400,
                        },
                    }}
                >
                    <AbcView
                        style={[
                            { marginHorizontal: Sizes.dp16, padding: Sizes.dp16 },
                            { backgroundColor: Colors.white, borderRadius: Sizes.dp5 },
                        ]}
                        onClick={() => {
                            this.props.onCancel(msgData.id);
                            abcOverlay.hide(msgData.id ?? "");
                            this.handleShowDetail();
                        }}
                    >
                        <View
                            style={[{ position: "absolute", right: Sizes.dp8, top: Sizes.dp8, width: Sizes.dp20, height: Sizes.dp20 }]}
                            onClick={() => {
                                this._AbcMotionRef?.destroy().then(() => {
                                    this.props.onCancel(msgData.id);
                                    abcOverlay.hide(msgData.id ?? "");
                                });
                            }}
                        >
                            <IconFontView name={"cross_small"} size={Sizes.dp18} color={Colors.T4} />
                        </View>
                        <View style={[ABCStyles.rowAlignCenter]}>
                            <AssetImageView name={"trumpet"} style={{ width: Sizes.dp16, height: Sizes.dp16, marginRight: Sizes.dp8 }} />
                            <Text style={[TextStyles.t16MT1]}>{title ?? "更新公告"}</Text>
                        </View>
                        <SizedBox height={Sizes.dp12} />
                        <View>
                            <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp16 })]} numberOfLines={2}>
                                {_content}
                            </Text>
                        </View>
                    </AbcView>
                </AbcMotion>
            </View>
        );
    }
}
