# def _updateUpgradeItem(url, file, grayFlag):
#     print("_updateUpgradeItem url = " + url + ", file = " + file)
#     f = None
#     content = None
#     with open(file, 'r') as f:
#         content = json.load(f)
#         content['grayFlag'] = grayFlag
#         content['zone'] = buildZone
#         content = json.dumps(content)
#
#     print("_updateUpgradeItem url = " + url + ", content = " + content)
#
#     headers = {"content-type": "application/json"}
#     r = requests.put(url, data=content,headers=headers)
#     print("_updateUpgradeItem rsp code:" + str(r.status_code))
#     print("_updateUpgradeItem, rsp content = " + r.content)
#     if r.status_code != 200:
#         raise Exception("_updateUpgradeItem 更改配置失败")
#
#
# def _updateUpgradeInfo():
#     if buildEnv == "dev" or buildEnv == "test":
#         if buildAndroid:
#             _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "0")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "1")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "2")
#         if buildIOS:
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "0")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "1")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "2")
#         if buildIOS9:
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "0")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "1")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "2")
#         if buildOhos:
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "0")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "1")
#             _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "2")
#     elif buildEnv == "gray":
#         _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "1")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "1")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "1")
#     elif buildEnv == "prod":
#         _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "0")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "0")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "0")
#     elif buildEnv == "pre":
#         _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "2")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "2")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "2")
#         _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "2")
