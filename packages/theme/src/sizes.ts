import { pxToDp } from "@app/utils";
import _ from "lodash";

class Sizes {
    dpHalf = pxToDp(0.5);
    dp0!: number;
    dp1!: number;
    dp2!: number;
    dp3!: number;
    dp4!: number;
    dp5!: number;
    dp6!: number;
    dp7!: number;
    dp8!: number;
    dp9!: number;
    dp10!: number;
    dp11!: number;
    dp12!: number;
    dp13!: number;
    dp14!: number;
    dp15!: number;
    dp16!: number;
    dp17!: number;
    dp18!: number;
    dp19!: number;
    dp20!: number;
    dp21!: number;
    dp22!: number;
    dp23!: number;
    dp24!: number;
    dp25!: number;
    dp26!: number;
    dp27!: number;
    dp28!: number;
    dp29!: number;
    dp30!: number;
    dp31!: number;
    dp32!: number;
    dp33!: number;
    dp34!: number;
    dp35!: number;
    dp36!: number;
    dp37!: number;
    dp38!: number;
    dp39!: number;
    dp40!: number;
    dp41!: number;
    dp42!: number;
    dp43!: number;
    dp44!: number;
    dp45!: number;
    dp46!: number;
    dp47!: number;
    dp48!: number;
    dp49!: number;
    dp50!: number;
    dp51!: number;
    dp52!: number;
    dp53!: number;
    dp54!: number;
    dp55!: number;
    dp56!: number;
    dp57!: number;
    dp58!: number;
    dp59!: number;
    dp60!: number;
    dp61!: number;
    dp62!: number;
    dp63!: number;
    dp64!: number;
    dp65!: number;
    dp66!: number;
    dp67!: number;
    dp68!: number;
    dp69!: number;
    dp70!: number;
    dp71!: number;
    dp72!: number;
    dp73!: number;
    dp74!: number;
    dp75!: number;
    dp76!: number;
    dp77!: number;
    dp78!: number;
    dp79!: number;
    dp80!: number;
    dp81!: number;
    dp82!: number;
    dp83!: number;
    dp84!: number;
    dp85!: number;
    dp86!: number;
    dp87!: number;
    dp88!: number;
    dp89!: number;
    dp90!: number;
    dp91!: number;
    dp92!: number;
    dp93!: number;
    dp94!: number;
    dp95!: number;
    dp96!: number;
    dp97!: number;
    dp98!: number;
    dp99!: number;
    dp100!: number;
    dp101!: number;
    dp102!: number;
    dp103!: number;
    dp104!: number;
    dp105!: number;
    dp106!: number;
    dp107!: number;
    dp108!: number;
    dp109!: number;
    dp110!: number;
    dp111!: number;
    dp112!: number;
    dp113!: number;
    dp114!: number;
    dp115!: number;
    dp116!: number;
    dp117!: number;
    dp118!: number;
    dp119!: number;
    dp120!: number;
    dp121!: number;
    dp122!: number;
    dp123!: number;
    dp124!: number;
    dp125!: number;
    dp126!: number;
    dp127!: number;
    dp128!: number;
    dp129!: number;
    dp130!: number;
    dp131!: number;
    dp132!: number;
    dp133!: number;
    dp134!: number;
    dp135!: number;
    dp136!: number;
    dp137!: number;
    dp138!: number;
    dp139!: number;
    dp140!: number;
    dp148!: number;
    dp149!: number;
    dp160 = pxToDp(160);
    dp180 = pxToDp(180);
    dp213 = pxToDp(213);
    dp217!: number;
    dp268 = pxToDp(268);
    dp270 = pxToDp(270);
    dp325!: number;
    dp343 = pxToDp(343);
    dp300 = pxToDp(300);
    dp391 = pxToDp(391);
    dp460 = pxToDp(460);

    listItemHeight = pxToDp(48);
    toolbarHeight = pxToDp(68);
    listHorizontalMargin = pxToDp(16);
    listVerticalMargin = pxToDp(8);
    listBorderMargin = {
        marginLeft: this.listHorizontalMargin,
        marginRight: this.listHorizontalMargin,
    };
    listBorderPadding = {
        paddingLeft: this.listHorizontalMargin,
        paddingRight: this.listHorizontalMargin,
    };

    paddingLTRB(...argument: number[]):
        | { padding: number }
        | {
              paddingLeft: number;
              paddingTop: number;
              paddingRight: number;
              paddingBottom: number;
          } {
        if (argument.length == 1) {
            return {
                padding: argument[0],
            };
        }
        if (argument.length == 2) {
            return {
                paddingLeft: argument[0],
                paddingTop: argument[1],
                paddingRight: argument[0],
                paddingBottom: argument[1],
            };
        }
        return {
            paddingLeft: argument[0],
            paddingTop: argument[1],
            paddingRight: argument[2],
            paddingBottom: argument[3],
        };
    }

    marginLTRB(...argument: number[]) {
        if (arguments.length == 1) {
            return {
                marginLeft: argument[0],
                marginTop: argument[0],
                marginRight: argument[0],
                marginBottom: argument[0],
            };
        }
        if (arguments.length == 2) {
            return {
                marginLeft: argument[0],
                marginTop: argument[1],
                marginRight: argument[0],
                marginBottom: argument[1],
            };
        }
        return {
            marginLeft: argument[0],
            marginTop: argument[1],
            marginRight: argument[2],
            marginBottom: argument[3],
        };
    }

    flattenBorderStyles(...argument: number[]) {
        if (arguments.length == 1) {
            return {
                borderLeftWidth: argument[0],
                borderTopWidth: argument[0],
                borderRightWidth: argument[0],
                borderBottomWidth: argument[0],
            };
        }
        if (arguments.length == 2) {
            return {
                borderLeftWidth: argument[0],
                borderTopWidth: argument[1],
                borderRightWidth: argument[0],
                borderBottomWidth: argument[1],
            };
        }
        return {
            borderLeftWidth: argument[0],
            borderTopWidth: argument[1],
            borderRightWidth: argument[2],
            borderBottomWidth: argument[3],
        };
    }

    constructor() {
        _.range(0, 160).forEach((item) => {
            //@ts-ignore
            this[`dp${item}`] = pxToDp(item);
        });
    }
}

const sizes = new Sizes();
export default sizes;
