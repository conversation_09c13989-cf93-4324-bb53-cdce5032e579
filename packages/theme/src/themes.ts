/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-23
 *
 * @description
 */

import { setColors } from "./colors";
import { ChineseMedicineColor } from "./chinese-medicine-color";
import { refreshTextStyle } from "./text-styles";
import { Colors } from "./default-colors";
import { Subject } from "rxjs";
import { getSharedPreferences } from "./index";

export enum ThemeType {
    normal,
    chineseMedicine,
}

export const ThemeNames = ["清新绿", "国风红"];

const PREF_THEME_INDEX = "theme_index";

export class ThemeManager {
    static currentTheme: ThemeType = ThemeType.normal;
    static themeObserver = new Subject<ThemeType>();
    public static setTheme(type: ThemeType, notifyThemeChanged = true): void {
        if (ThemeManager.currentTheme == type) return;

        getSharedPreferences()?.setInt(PREF_THEME_INDEX, type);

        if (type == ThemeType.chineseMedicine) {
            setColors(new ChineseMedicineColor());
        } else if (type == ThemeType.normal) {
            setColors(new Colors());
        }
        ThemeManager.currentTheme = type;
        refreshTextStyle();

        if (notifyThemeChanged) ThemeManager.themeObserver.next(type);
    }

    public static getCurrentTheme(): ThemeType {
        return ThemeManager.currentTheme;
    }

    public static getCurrentThemeName(): string {
        return ThemeNames[ThemeManager.currentTheme];
    }
}
