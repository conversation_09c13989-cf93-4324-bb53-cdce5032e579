const path = require("path");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");

const isProduction = process.env.NODE_ENV === "production";

module.exports = {
    mode: process.env.NODE_ENV === "production" ? "production" : "development",
    entry: "./src/index.ts",
    output: {
        path: path.resolve(__dirname, "dist"),
        filename: "index.js",
        library: {
            name: "abcMobileUI",
            type: "umd",
        },
        globalObject: "this",
        clean: isProduction,
    },
    resolve: {
        extensions: [".ts", ".tsx", ".js", ".jsx", ".json"],
        alias: {
            "@components": path.resolve(__dirname, "src/components"),
            "@app/theme": path.resolve(__dirname, "../theme/dist"),
            "@app/utils": path.resolve(__dirname, "../utils/dist"),
        },
    },
    externals: {
        react: {
            commonjs: "react",
            commonjs2: "react",
            amd: "React",
            root: "React",
        },
        "react-dom": {
            commonjs: "react-dom",
            commonjs2: "react-dom",
            amd: "ReactDOM",
            root: "ReactDOM",
        },
        typescript: "commonjs typescript",
        "@hippy/react": "commonjs2 @hippy/react",
        "@app/theme": {
            commonjs: "@app/theme",
            commonjs2: "@app/theme",
            amd: "@app/theme",
            root: "@app/theme",
        },
    },
    module: {
        rules: [
            {
                test: /\.(ts|tsx)$/,
                use: [
                    {
                        loader: "ts-loader",
                        options: {
                            transpileOnly: true,
                        },
                    },
                ],
                exclude: /node_modules/,
            },
        ],
    },
    plugins: isProduction ? [new CleanWebpackPlugin()] : [],
};
