/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/30
 *
 * @description
 */

import { ClickableProps, LayoutableProps, Style, Text, View } from "@hippy/react";
import React from "react";
import { Const } from "@app/utils";
import { AbcIconfont } from "../index";

interface AbcTextProps extends LayoutableProps, ClickableProps {
    airTestKey?: string;
    /**
     * Used to truncate the text with an ellipsis after computing the text layout,
     * including line wrapping, such that the total number of lines does not exceed this number.
     * This prop is commonly used with `ellipsizeMode`.
     */
    numberOfLines?: number;
    /**
     * Determines what the opacity of the wrapped view.
     */
    opacity?: number;
    /**
     * When numberOfLines is set, this prop defines how text will be truncated.
     * numberOfLines must be set in conjunction with this prop.
     * This can be one of the following values:
     *
     * * head - The line is displayed so that the end fits in the container
     *          and the missing text at the beginning of the line is indicated by an ellipsis glyph.
     *          e.g., "...wxyz
     * * middle - The line is displayed so that the beginning and
     *            end fit in the container and the missing text in the middle is indicated
     *            by an ellipsis glyph.
     *            e.g., "ab...yz"
     * * tail - The line is displayed so that the beginning fits in the container
     *          and the missing text at the end of the line is indicated by an ellipsis glyph.
     *          e.g., "abcd..."
     * * clip - Lines are not drawn past the edge of the text container.
     *
     * The default is `tail`.
     */
    ellipsizeMode?: "head" | "middle" | "tail" | "clip";
    children: number | string | string[];
    text?: string;
    style?: Style | Style[];
}

export class AbcText extends React.Component<AbcTextProps> {
    private _lastClickTime?: Date;

    public render(): JSX.Element {
        let { onClick } = this.props;
        const { children, airTestKey } = this.props;
        const _airTestKey = airTestKey ?? this._createAirTestKey();

        if (onClick) {
            onClick = this._onClick.bind(this);
        }
        return (
            <Text accessibilityLabel={_airTestKey} {...this.props} onClick={onClick}>
                {children}
            </Text>
        );
    }

    findStringInChildNodes = (children: React.ReactChild, searchString?: string) => {
        let str: string | undefined = undefined;
        React.Children.forEach(children, (child) => {
            if (!!str) return;
            if (child && typeof child === "object" && child.props) {
                str = this.findStringInChildNodes(child.props.children, searchString);
            } else if (typeof child === "string") {
                if (searchString && child.includes(searchString)) {
                    str = child;
                } else {
                    str = child;
                }
            }
        });
        return str;
    };

    private _createAirTestKey(): string | undefined {
        const children = this.props.children;
        //@ts-ignore
        const airTestKey = this.findStringInChildNodes(children);
        return airTestKey;
    }

    _onClick(): void {
        if (!this._lastClickTime || new Date().getTime() - this._lastClickTime.getTime() > Const.clickDebounceTime) {
            this.props.onClick?.();
        }
        this._lastClickTime = new Date();
    }
}

export class AbcMoreText extends AbcText {
    private showMore = false;

    constructor(props: AbcTextProps) {
        super(props);
    }

    private _onChangeShowMore(): void {
        this.showMore = !this.showMore;
        this.setState({});
    }

    render(): JSX.Element {
        let { onClick } = this.props;
        const { children, numberOfLines, style, ...others } = this.props;
        if (onClick) {
            onClick = this._onClick.bind(this);
        }
        return (
            <View style={[{ flexDirection: "row" }]}>
                {this.showMore ? (
                    <Text {...others} style={[{ flexShrink: 1, ...style }]} onClick={onClick} numberOfLines={undefined}>
                        {children}
                    </Text>
                ) : (
                    <Text {...others} style={[{ flexShrink: 1, ...style }]} onClick={onClick} numberOfLines={numberOfLines ?? 1}>
                        {children}
                    </Text>
                )}

                <View style={{ flexDirection: "row" }} onClick={this._onChangeShowMore.bind(this)}>
                    <View>{AbcIconfont.dropDown()}</View>
                    <Text
                        style={{
                            fontSize: 12,
                            fontWeight: "normal",
                            color: "#CED0DA",
                        }}
                    >
                        {this.showMore ? "更多" : "收起"}
                    </Text>
                </View>
            </View>
        );
    }
}
