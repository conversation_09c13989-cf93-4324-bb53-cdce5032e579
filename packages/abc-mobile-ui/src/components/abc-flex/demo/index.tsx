import React from "react";
import { View } from "@hippy/react";
import { Sizes, Colors } from "@app/theme";
import { AbcText } from "../../abc-text/index";
import { AbcFlex } from "../index";

export function AbcFlexDemo() {
    const itemStyle = {
        width: Sizes.dp60,
        height: Sizes.dp48,
        color: Colors.white,
        backgroundColor: Colors.mainColor,
    };

    const list = [1, 2, 3].map((item, index) => {
        return (
            <View key={item} style={[itemStyle, { backgroundColor: index % 2 === 0 ? Colors.mainColor : Colors.mainColorMask30 }]}>
                <AbcFlex style={{ flex: 1 }} justify={"center"} align={"center"}>
                    {item}
                </AbcFlex>
            </View>
        );
    });

    return (
        <View>
            <View>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>基础用法 (direction) </AbcText>
                <View style={{ height: Sizes.dp48 }}>
                    <AbcFlex style={{ flex: 1 }}>{list}</AbcFlex>
                </View>

                <View style={{ marginTop: Sizes.dp16 }}>
                    <AbcFlex style={{ flex: 1 }} vertical={true}>
                        {list}
                    </AbcFlex>
                </View>
            </View>

            <View style={{ marginTop: Sizes.dp24 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>主轴对齐方式（justify）</AbcText>
                <AbcFlex style={{ flex: 1, height: Sizes.dp48 }} justify="flex-start">
                    {list}
                </AbcFlex>

                <AbcFlex style={{ marginTop: Sizes.dp16, flex: 1, height: Sizes.dp48 }} justify="center">
                    {list}
                </AbcFlex>

                <AbcFlex style={{ marginTop: Sizes.dp16, flex: 1, height: Sizes.dp48 }} justify="flex-end">
                    {list}
                </AbcFlex>

                <AbcFlex style={{ marginTop: Sizes.dp16, flex: 1, height: Sizes.dp48 }} justify="space-between">
                    {list}
                </AbcFlex>
                <AbcFlex style={{ marginTop: Sizes.dp16, flex: 1, height: Sizes.dp48 }} justify="space-around">
                    {list}
                </AbcFlex>
            </View>

            <View style={{ marginTop: Sizes.dp24 }}>
                <AbcText style={{ color: Colors.T1, marginBottom: Sizes.dp20 }}>wrap</AbcText>
                <AbcFlex style={{ flex: 1, height: Sizes.dp48 }} wrap="wrap">
                    {[...list, ...list]}
                </AbcFlex>
            </View>
        </View>
    );
}
