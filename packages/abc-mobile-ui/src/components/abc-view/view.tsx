/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/5/30
 *
 * @description 对原始View进行包装,用于解决快速点击的问题
 */
import React from "react";
import { ClickableProps, LayoutableProps, Style, TouchableProps, View } from "@hippy/react";
import { Fiber as Fiber_$0 } from "react-reconciler";
import { Const } from "@app/utils";
// import { AbcTextInput } from "./abc-text-input";
import { ABCStyles, Colors, flattenStyles, Sizes } from "@app/theme";
import _ from "lodash";

interface AbcViewProps extends LayoutableProps, ClickableProps, TouchableProps {
    airTestKey?: string;
    /**
     * Overrides the text that's read by the screen reader when the user interacts with the element.
     * By default, the label is constructed by traversing all the children and accumulating
     * all the Text nodes separated by space.
     */
    accessibilityLabel?: string;
    /**
     * When `true`, indicates that the view is an accessibility element.
     * By default, all the touchable elements are accessible.
     */
    accessible?: boolean;
    /**
     * Views that are only used to layout their children or otherwise don't draw anything may be
     * automatically removed from the native hierarchy as an optimization.
     * Set this property to `false` to disable this optimization
     * and ensure that this `View` exists in the native view hierarchy.
     */
    collapsable?: false;
    /**
     * Specifies what should happen if content overflows an container's box.
     *
     * Default: iOS is 'visible', android is 'hidden'.
     */
    overflow?: "visible" | "hidden";
    focusable?: boolean;
    requestFocus?: boolean;
    nextFocusDownId?: string | Fiber_$0;
    nextFocusUpId?: string | Fiber_$0;
    nextFocusLeftId?: string | Fiber_$0;
    nextFocusRightId?: string | Fiber_$0;
    style?: Style | Style[];
    continuousClick?: boolean;

    /**
     * The focus event occurs when the component is focused.
     *
     * @param {Object} evt - Focus event data
     * @param {boolean} evt.focus - Focus status
     */
    onFocus?(evt: FocusEvent): void;

    blurTextInputWhenClick?: boolean; //false

    tagName?: string;
}

let sLastClickTime: Date | undefined = undefined;
export class AbcView extends React.Component<AbcViewProps> {
    private getModifiedStyle(style: Style | Style[] | undefined): Style | undefined {
        if (!style) return style;
        const _style = flattenStyles(style);
        const newStyle = _.cloneDeep(_style);
        if (newStyle.borderBottomWidth === 0.5) {
            newStyle.isShowBottomLine = true;
            delete newStyle.borderBottomWidth;
        }
        return newStyle;
    }

    /**
     * 将style进行归类
     * @param originalStyle -- 原始style
     * @param boxStyle --- 外层容器Style
     * @param filed --- 具体的类型字段
     * @private
     */
    private classifyStyle(originalStyle: Style, boxStyle: Style, filed: string): void {
        if (originalStyle[filed]) {
            boxStyle[filed] = originalStyle[filed];
            delete originalStyle[filed];
        }
    }

    // 将style中的样式进行归类，flex、width、height、marginLeft, marginRight, marginHorizontal归为containerStyle，剩下的分为contentStyle
    private adjustStyle(styleItem: Style | undefined):
        | {
              containerStyle?: Style; // 外层容器样式
              contentStyle?: Style; // 内容样式
          }
        | undefined {
        if (!styleItem) return;
        const newStyle = _.cloneDeep(styleItem);
        const containerStyle: Style = {};
        if (newStyle.flex) {
            containerStyle.flex = newStyle.flex;
            delete newStyle.flex;
        }
        this.classifyStyle(newStyle, containerStyle, "flex");
        this.classifyStyle(newStyle, containerStyle, "width");
        //  如果ABCStyles.rowAlignCenter和height同时存在，则不能将高度给外层容器
        if (newStyle.height && (!newStyle.flexDirection || !newStyle.alignItems)) {
            this.classifyStyle(newStyle, containerStyle, "height");
        }
        this.classifyStyle(newStyle, containerStyle, "minHeight");
        this.classifyStyle(newStyle, containerStyle, "marginLeft");
        this.classifyStyle(newStyle, containerStyle, "marginRight");
        this.classifyStyle(newStyle, containerStyle, "marginHorizontal");
        this.classifyStyle(newStyle, containerStyle, "marginTop");
        this.classifyStyle(newStyle, containerStyle, "marginBottom");

        return {
            containerStyle,
            contentStyle: newStyle,
        };
    }

    render(): JSX.Element {
        let { onClick } = this.props;
        const { airTestKey } = this.props;
        if (onClick) onClick = this._onClick.bind(this);
        let accessibilityLabel = airTestKey;
        if (onClick) {
            accessibilityLabel = this._createAccessibilityLabel();
        }
        // 识别props中的style中是否还有ABCStyles.bottomLine,如果有则需要将isShowBottomLine设置为true，并将style中的ABCStyles.bottomLine删除
        const _modifiedStyle = this.getModifiedStyle(this.props?.style);
        const isShowBottomLine = _modifiedStyle?.isShowBottomLine ?? false;
        const { containerStyle, contentStyle } = this.adjustStyle(_modifiedStyle) || {};
        if (!isShowBottomLine) {
            return (
                <View
                    accessibilityLabel={accessibilityLabel}
                    // @ts-ignore
                    collapsable={!accessibilityLabel}
                    {...this.props}
                    onClick={onClick}
                >
                    {this.props.children}
                </View>
            );
        }
        return (
            <View style={[containerStyle]}>
                <View
                    accessibilityLabel={accessibilityLabel}
                    // @ts-ignore
                    collapsable={!accessibilityLabel}
                    {...this.props}
                    style={{ ...contentStyle }}
                    onClick={onClick}
                >
                    {this.props.children}
                </View>
                {isShowBottomLine && <BottomLine bottomStyle={contentStyle} />}
            </View>
        );
    }

    findStringInChildNodes = (children: React.ReactChild, searchString?: string): string | undefined => {
        let str: string | undefined = undefined;
        React.Children.forEach(children, (child) => {
            if (!!str) return;
            if (child && typeof child === "object" && child.props) {
                str = this.findStringInChildNodes(child.props.children, searchString);
            } else if (typeof child === "string") {
                if (searchString && child.includes(searchString)) {
                    str = child;
                } else {
                    str = child;
                }
            }
        });
        return str;
    };

    private _createAccessibilityLabel(): string | undefined {
        const children = this.props.children;
        //@ts-ignore
        const accessibilityLabel = this.props.airTestKey ?? this.findStringInChildNodes(children);
        return accessibilityLabel;
        //@ts-ignore
        // if (!!accessibilityLabel) return unescape(accessibilityLabel?.replace(/\\u/g, "%u"));
    }

    private _onClick() {
        const { continuousClick, blurTextInputWhenClick = false } = this.props;
        if (blurTextInputWhenClick) {
            // AbcTextInput.focusInput?.blur();
        }
        if (continuousClick) {
            this.props.onClick?.();
        } else if (!sLastClickTime || new Date().getTime() - sLastClickTime.getTime() > Const.clickDebounceTime) {
            this.props.onClick?.();
        }

        sLastClickTime = new Date();
    }
}

interface BottomLineProps {
    bottomStyle?: Style;
}
export const BottomLine: React.FC<BottomLineProps> = ({ bottomStyle }) => {
    if (!bottomStyle) return <View />;
    const { borderColor, ...otherProps } = bottomStyle;
    return (
        <View
            style={{
                ...otherProps,
                height: Sizes.dp1,
                backgroundColor: "transparent",
                ...ABCStyles.topLine,
                borderColor: borderColor ?? Colors.dividerLineColor,
            }}
        />
    );
};
