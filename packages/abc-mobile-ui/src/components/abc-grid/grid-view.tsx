/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-01
 *
 * @description
 */
import React from "react";
import { Style, StyleSheet, View } from "@hippy/react";
import _ from "lodash";
import { Colors, Sizes } from "@app/theme";
import { DeviceUtils } from "@app/utils";

// @ts-ignore
const styles = StyleSheet.create({
    rowStyle: { flexDirection: "row", justifyContent: "space-between" },
    rightBorderLine: {
        borderRightWidth: Sizes.dpHalf,
        borderRightColor: Colors.dividerLineColor,
        borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
    },
    bottomBorderLine: {
        borderBottomWidth: Sizes.dpHalf,
        borderBottomColor: Colors.dividerLineColor,
        borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
    },
});

interface GridViewProps {
    crossAxisCount: number; //每行个数
    itemWidth?: number;
    itemHeight?: number;
    mainAxisSpacing?: number; //行间隔
    crossAxisSpacing?: number; //横向item间间隔
    // childAspectRatio: number | 1;// width/height
    children: JSX.Element[];
    style?: Style | Style[];
    showNetLine?: boolean; //default false
    showBottomLine?: boolean; //default false
    netLineStyle?: Style;
    rowStyle?: Style;
}

export class AbcGrid extends React.Component<GridViewProps> {
    static defaultProps = {
        // childAspectRatio: 1,
        mainAxisSpacing: 0,

        showNetLine: false,
        showBottomLine: false,
    };

    constructor(props: GridViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            rowStyle,
            children,
            crossAxisCount,
            crossAxisSpacing,
            itemWidth,
            itemHeight,
            mainAxisSpacing,
            showNetLine,
            showBottomLine,
            netLineStyle,
        } = this.props;
        const fixedItemWidth = itemWidth != undefined;
        const itemStyle = {
            width: itemWidth,
            height: itemHeight,
            flex: fixedItemWidth ? itemWidth : 1,
        };
        let keyIndex = 0;
        const totalLine = _.ceil(children.length / crossAxisCount);
        const rows = _.chunk(children, crossAxisCount).map((array, lineIndex) => {
            const items = array.map((item, index) => {
                let needRightLine = showNetLine;
                let needBottomLine = showNetLine;
                if (index % crossAxisCount === crossAxisCount - 1) needRightLine = false;

                const line = lineIndex + 1;
                if (line == totalLine && !showBottomLine) needBottomLine = false;

                return (
                    <View
                        key={keyIndex++}
                        style={[
                            itemStyle,
                            !fixedItemWidth && index != 0 ? { marginLeft: crossAxisSpacing } : {},
                            needRightLine ? styles.rightBorderLine : {},
                            needBottomLine ? netLineStyle ?? styles.bottomBorderLine : {},
                        ]}
                    >
                        {item}
                    </View>
                );
            });

            const emptyCount = crossAxisCount - items.length;
            for (let i = 0; i < emptyCount; ++i) {
                items.push(
                    <View key={keyIndex++} style={[itemStyle, !fixedItemWidth && keyIndex != 0 ? { marginLeft: crossAxisSpacing } : {}]} />
                );
            }

            return (
                <View key={keyIndex++} style={[styles.rowStyle, rowStyle, { marginTop: lineIndex != 0 ? mainAxisSpacing : undefined }]}>
                    {items}
                </View>
            );
        });
        return <View style={this.props.style}>{rows}</View>;
    }
}
