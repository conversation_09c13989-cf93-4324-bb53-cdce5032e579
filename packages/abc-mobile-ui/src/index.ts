type AnyType = any;
interface ComponentErrorHandler {
    (error: AnyType, errorInfo: AnyType): void;
}

type ComponentErrorHandlerFn = () => ComponentErrorHandler | null;

interface optionsInterface {
    log?: any;
    languageObserver?: any;
    getComponentErrorHandler?: ComponentErrorHandlerFn;
}

let _logInstance: any = null;
let _languageObserver: any = null;
let _getComponentErrorHandler: ComponentErrorHandlerFn | null = null;

export function init(options: optionsInterface): void {
    if (options?.log) {
        _logInstance = options.log;
    }

    if (options?.languageObserver) {
        _languageObserver = options?.languageObserver;
    }

    if (options?.getComponentErrorHandler) {
        _getComponentErrorHandler = options?.getComponentErrorHandler;
    }
}

export function getLog(): any {
    return _logInstance;
}

export function getLanguageObserver(): any {
    return _languageObserver;
}

export function getErrorHandler(): ComponentErrorHandlerFn | null {
    return _getComponentErrorHandler;
}

export * from "./components";
