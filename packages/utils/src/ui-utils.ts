import { callNative, Dimensions, PixelRatio, Platform, UIManagerModule } from "@hippy/react";
import Utils from "./utils";
import { Fiber } from "react-reconciler";
import { Component } from "react";
import _ from "lodash";
import { DeviceUtils } from "./device-utils";
import { getAppInfo } from "./index";

export interface LayoutContent {
    x: number;
    y: number;
    width: number;
    height: number;
    statusBarHeight?: number;
}

type RefType = string | Fiber | Element | Component | undefined | null;

class UiUtils {
    static getScreenWidth(): number {
        // 以短的那条边做屏幕宽度
        const screenWidth = Dimensions.get("screen").width;
        const screenHeight = Dimensions.get("screen").height;
        const width = screenWidth > screenHeight ? screenHeight : screenWidth;
        return Math.floor(width);
    }

    static getScreenHeight(): number {
        // 以短的那条边做屏幕宽度
        const screenWidth = Dimensions.get("screen").width;
        const screenHeight = Dimensions.get("screen").height;
        const width = screenWidth > screenHeight ? screenWidth : screenHeight;
        return Math.floor(width);
    }

    static isiPhoneX(): boolean {
        let rst = false;
        if (Platform.OS === "android") return rst;
        const { height } = Dimensions.get("window");
        if (height >= 812 && PixelRatio.get() >= 2) {
            rst = true;
        }
        return rst;
    }

    static safeAreaBottomHeight(): number {
        if (Utils.isiPhoneX()) return 32;
        return 0;
    }

    /**
     * 计算子view相对于window的相对位置
     * @param child
     */
    static measureInWindow(child: RefType): Promise<LayoutContent> {
        if (!child) return Promise.reject("measureInWindow node不能为空");
        // @ts-ignore
        return UIManagerModule.measureInAppWindow(child).then((layout: LayoutContent) => {
            const statusBarHeight = layout.statusBarHeight ?? 0;
            if (getAppInfo()?.androidFullScreen ?? false) layout.y += statusBarHeight;

            return layout;
        });
    }

    /**
     * 计算子view相对于祖先View里的相对位置
     * @param ref
     * @param ancestor
     */
    static measureInAncestor(ref: RefType, ancestor: RefType): Promise<LayoutContent> {
        // @ts-ignore
        const nodeId = UIManagerModule.getNodeIdByRef(ref);
        // @ts-ignore
        const ancestorId = UIManagerModule.getNodeIdByRef(ancestor);
        return new Promise((resolve, reject) => {
            if (!nodeId) {
                return reject(new Error("measureInAncestor cannot get nodeId for ref = " + ref));
            }
            if (!ancestorId) {
                return reject(new Error("measureInAncestor cannot get nodeId for ancestor = " + ancestor));
            }

            return callNative("UIManagerModule", "measureInAncestor", nodeId, ancestorId, (layout: LayoutContent | string) => {
                if (_.isString(layout)) {
                    if (layout === "this view is null") {
                        return reject(new Error("Android cannot get the node"));
                    }

                    return reject(new Error("layout is string: " + layout));
                }
                return resolve(layout as LayoutContent);
            });
        });
    }

    static safeStatusHeight(): number {
        if (DeviceUtils.isIOS() || getAppInfo()?.androidFullScreen) return Dimensions.get("screen").statusBarHeight;

        return 0;
    }

    static getSafeContentHeight(): number {
        return this.getScreenHeight() - this.safeStatusHeight() - this.safeAreaBottomHeight();
    }
    static androidSafeAreaBottomHeight(): number {
        if (!DeviceUtils.isAndroid()) return 0;

        // 检测是否有导航栏
        const windowHeight = Dimensions.get("window").height;
        const screenHeight = Dimensions.get("screen").height;
        const difference = screenHeight - windowHeight;

        // 如果差值大于某个阈值，认为设备有导航栏
        if (difference > 40) {
            return difference;
        }

        // 对于没有导航栏的设备，返回一个较小的值
        return 40;
    }
}

export default UiUtils;

//设计稿按375设计的
// const kDesignScale = UiUtils.getScreenWidth() / 375;
const kDesignScale = Math.min(UiUtils.getScreenWidth(), 525) / 375;

export function pxToDp(px: number, fixedNum = 4): number {
    // 0.5像素的时候，会导致有些边线不清晰
    if (px == 0.5) {
        return Number((px * kDesignScale).toFixed(1));
    }
    return Number((px * kDesignScale).toFixed(fixedNum));
}
