export { default as UiUtils, LayoutContent, pxToDp } from "./ui-utils";
export { default as Utils } from "./utils";

export { Const } from "./consts";
export { DeviceUtils } from "./device-utils";
export { Version } from "./version-utils";
export { applyMixins } from "./mixin-utils";

let _appInfo: any = null;
interface optionsInterface {
    appInfo?: any;
}
export function utilsInit(options: optionsInterface): void {
    if (options?.appInfo) {
        _appInfo = options.appInfo;
    }
}

export function getAppInfo(): any {
    return _appInfo;
}
